
# 交联代码定制需求指南

为了帮您仿照现有交联代码开发类似功能的脚本，请提供以下信息：

## 1. 基本系统描述
- 交联体系类型(环氧树脂、聚氨酯、硅氧烷等)
- 单体分子描述(结构、功能基团)
- 交联剂分子描述(结构、功能基团)
- 体系中的其他组分(如溶剂、催化剂)

## 2. 反应机理详情
- 交联反应类型(加成、缩合、开环等)
- 是否涉及副产物生成与去除
- 受热行为特性(退火需求、温度敏感性)
- 特殊反应步骤或条件

## 3. 反应参数设置
- 目标交联度/转化率
- 反应距离范围(起始、步长、最大值)
- 单个原子是否可多次参与反应
- 是否允许分子内交联

## 4. 模拟控制参数
- 力场选择(COMPASS、PCFF等)
- 动力学模拟参数(温度、压力、时间)
- 优化方法偏好
- 约束类型设置
- 期望模拟时长或计算资源限制

## 5. 自定义特性需求
- 需要新增的特殊反应类型
- 额外的结构分析需求
- 自定义输出数据格式
- 交联过程中的特殊处理规则

## 6. 输入文件格式与准备
- 原子命名规则(如反应位点如何标记)
- 初始结构的特殊要求
- 所需原子力场参数是否需要补充

## 7. 输出需求
- 需要哪些类型的分析数据
- 是否需要特定的可视化输出
- 中间结构保存方案
- 统计报告格式偏好

提供以上信息后，我可以根据现有交联代码为您定制一个满足特定需求的新脚本，保留原代码的核心功能同时实现您所需的特性修改或扩展。
