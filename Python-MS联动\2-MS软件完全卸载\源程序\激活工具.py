import tkinter as tk
from tkinter import ttk, messagebox
import random
import string
import uuid
import subprocess
import os
import sys
import platform

def get_machine_id(machine_id_input=None):
    """获取计算机唯一标识符或处理输入的机器码"""
    if machine_id_input:
        return machine_id_input  # 如果提供了输入，直接使用
        
    # 否则获取本机的机器码
    machine_id = str(uuid.getnode())
    disk_serial = ''
    try:
        c_drive = 'C:\\'
        if os.path.exists(c_drive):
            volume_info = subprocess.check_output('vol C:', shell=True).decode('utf-8')
            for line in volume_info.split('\n'):
                if 'Volume Serial Number' in line:
                    disk_serial = line.split(':')[1].strip()
                    break
    except:
        pass
    
    return machine_id + disk_serial

def generate_code(machine_id):
    """根据机器码生成激活序列码"""
    if not machine_id or len(machine_id) < 10:
        print("错误: 机器码为空或太短")
        return "无效的机器码"
    
    # 调试信息
    print("\n---------- 激活码生成调试信息 ----------")
    print(f"原始机器码: '{machine_id}'")
    
    # 清理输入，只保留字母和数字
    cleaned_machine_id = ''.join(c for c in machine_id if c in string.ascii_letters + string.digits)
    print(f"清理后机器码: '{cleaned_machine_id}'")
    
    # 使用确定性种子，确保生成的激活码可重复
    seed_string = f"MS_ACTIVATION_{cleaned_machine_id}"
    print(f"随机数种子: '{seed_string}'")
    
    random.seed(seed_string)
    
    # 生成16位激活码
    activation_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=16))
    print(f"生成的激活码: '{activation_code}'")
    print("----------------------------------------\n")
    
    return activation_code

class ActivationToolApp:
    def __init__(self, root):
        self.root = root
        self.root.title("MS卸载助手 - 激活工具")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 设置样式
        self.style = ttk.Style()
        try:
            self.style.theme_use('clam')
        except:
            pass
        
        # 移除免责声明对话框，直接设置UI
        self.setup_ui()
        
    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="MS卸载助手激活码生成器", 
                              font=("微软雅黑", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 说明文本
        instructions = "说明：\n1. 向用户索取其机器码\n2. 在下方输入框中粘贴机器码\n3. 点击生成按钮获取激活序列码\n4. 将生成的激活序列码发送给用户"
        instruction_label = ttk.Label(main_frame, text=instructions, 
                                 font=("微软雅黑", 11), justify=tk.LEFT)
        instruction_label.pack(fill=tk.X, pady=10, anchor=tk.W)
        
        # 机器码输入
        machine_id_frame = ttk.Frame(main_frame)
        machine_id_frame.pack(fill=tk.X, pady=10)
        
        machine_id_label = ttk.Label(machine_id_frame, text="请输入机器码:", 
                                   font=("微软雅黑", 11))
        machine_id_label.pack(anchor=tk.W)
        
        self.machine_id_entry = ttk.Entry(machine_id_frame, width=40, font=("Consolas", 11))
        self.machine_id_entry.pack(fill=tk.X, pady=5)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 粘贴按钮
        paste_button = ttk.Button(button_frame, text="粘贴", 
                               command=self.paste_from_clipboard)
        paste_button.pack(side=tk.LEFT, padx=5)
        
        # 清除按钮
        clear_button = ttk.Button(button_frame, text="清除", 
                              command=lambda: self.machine_id_entry.delete(0, tk.END))
        clear_button.pack(side=tk.LEFT, padx=5)
        
        # 生成按钮
        generate_button = ttk.Button(button_frame, text="生成激活码", 
                                  command=self.on_generate)
        generate_button.pack(side=tk.RIGHT, padx=5)
        
        # 测试码和管理员码按钮 - 放在一个单独的框架中
        special_frame = ttk.LabelFrame(main_frame, text="特殊功能")
        special_frame.pack(fill=tk.X, pady=10)
        
        special_buttons_frame = ttk.Frame(special_frame)
        special_buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        test_button = ttk.Button(special_buttons_frame, text="生成测试码", 
                              command=self.generate_test_code)
        test_button.pack(side=tk.LEFT, padx=5)
        
        admin_button = ttk.Button(special_buttons_frame, text="生成管理员码", 
                               command=self.generate_admin_code)
        admin_button.pack(side=tk.LEFT, padx=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="激活序列码")
        result_frame.pack(fill=tk.X, pady=10)
        
        self.result_var = tk.StringVar(value="在上方输入机器码后点击生成按钮")
        result_label = ttk.Label(result_frame, textvariable=self.result_var, 
                              font=("Consolas", 13, "bold"))
        result_label.pack(pady=10)
        
        # 复制按钮
        copy_button = ttk.Button(result_frame, text="复制到剪贴板", 
                              command=self.copy_result)
        copy_button.pack(pady=5)
        
        # 说明文本
        note_label = ttk.Label(main_frame, 
                            text="注意：激活序列码仅对输入的机器码有效，请谨慎保管。", 
                            foreground="red", font=("微软雅黑", 9))
        note_label.pack(pady=(20, 0))
    
    def paste_from_clipboard(self):
        try:
            clipboard_text = self.root.clipboard_get()
            self.machine_id_entry.delete(0, tk.END)
            self.machine_id_entry.insert(0, clipboard_text)
        except:
            messagebox.showerror("错误", "剪贴板中没有文本内容")
    
    def copy_result(self):
        result = self.result_var.get()
        if result and result != "在上方输入机器码后点击生成按钮":
            self.root.clipboard_clear()
            self.root.clipboard_append(result)
            self.root.update()
            messagebox.showinfo("复制成功", "激活序列码已复制到剪贴板")
    
    def on_generate(self):
        machine_id = self.machine_id_entry.get().strip()
        if not machine_id:
            self.result_var.set("请输入有效的机器码")
            return
        
        activation_code = generate_code(machine_id)
        self.result_var.set(activation_code)
        
        # 自动复制到剪贴板
        self.root.clipboard_clear()
        self.root.clipboard_append(activation_code)
        self.root.update()

    def generate_test_code(self):
        """生成特殊测试激活码"""
        test_codes = ["M3A7E5R1I9A2L8S6", "B5I9O3V7I1A4_2023"]
        test_code = random.choice(test_codes)
        self.result_var.set(test_code)
        
        # 复制到剪贴板
        self.root.clipboard_clear()
        self.root.clipboard_append(test_code)
        self.root.update()
        messagebox.showinfo("测试码生成", f"已生成测试码: {test_code}\n此码可用于任何机器，仅供测试使用")

    def generate_admin_code(self):
        """生成管理员激活码"""
        admin_codes = ["MSADMIN2023UNLOCK", "MS2023RESETCODE16"]
        admin_code = random.choice(admin_codes)
        self.result_var.set(admin_code)
        
        # 复制到剪贴板
        self.root.clipboard_clear()
        self.root.clipboard_append(admin_code)
        self.root.update()
        messagebox.showinfo("管理员码生成", f"已生成管理员码: {admin_code}\n此码可用于任何机器，请谨慎使用")

def main():
    root = tk.Tk()
    root.title("MS卸载助手 - 激活工具")
    root.geometry("600x600")
    root.resizable(False, False)
    app = ActivationToolApp(root)
    root.mainloop()

if __name__ == "__main__":
    main() 