import tkinter as tk
from tkinter import ttk, messagebox
import random
import string
import uuid
import subprocess
import os
import sys

def get_machine_id(machine_id_input=None):
    """获取计算机唯一标识符或处理输入的机器码"""
    if machine_id_input:
        return machine_id_input  # 如果提供了输入，直接使用
        
    # 否则获取本机的机器码
    machine_id = str(uuid.getnode())
    disk_serial = ''
    try:
        c_drive = 'C:\\'
        if os.path.exists(c_drive):
            volume_info = subprocess.check_output('vol C:', shell=True).decode('utf-8')
            for line in volume_info.split('\n'):
                if 'Volume Serial Number' in line:
                    disk_serial = line.split(':')[1].strip()
                    break
    except:
        pass
    
    return machine_id + disk_serial

def generate_reset_code(machine_id):
    """生成重置码"""
    if not machine_id or len(machine_id) < 10:
        return "无效的机器码"
    
    # 清理输入，只保留字母和数字
    machine_id = ''.join(c for c in machine_id if c in string.ascii_letters + string.digits)
    
    # 使用确定性种子，确保生成的重置码可重复
    random.seed(f"MS_RESET_{machine_id}")
    
    # 生成10位重置码
    reset_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
    return reset_code

class ResetCodeGeneratorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("MS卸载助手 - 重置码生成器")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 设置样式
        self.style = ttk.Style()
        try:
            self.style.theme_use('clam')
        except:
            pass
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="MS卸载助手重置码生成器", 
                              font=("微软雅黑", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 说明文本
        instructions = "说明：\n1. 向用户索取其机器码\n2. 在下方输入框中粘贴机器码\n3. 点击生成按钮获取重置码\n4. 将生成的重置码发送给用户"
        instruction_label = ttk.Label(main_frame, text=instructions, 
                                 font=("微软雅黑", 11), justify=tk.LEFT)
        instruction_label.pack(fill=tk.X, pady=10, anchor=tk.W)
        
        # 机器码输入
        machine_id_frame = ttk.Frame(main_frame)
        machine_id_frame.pack(fill=tk.X, pady=10)
        
        machine_id_label = ttk.Label(machine_id_frame, text="请输入机器码:", 
                                   font=("微软雅黑", 11))
        machine_id_label.pack(anchor=tk.W)
        
        self.machine_id_entry = ttk.Entry(machine_id_frame, width=40, font=("Consolas", 11))
        self.machine_id_entry.pack(fill=tk.X, pady=5)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 粘贴按钮
        paste_button = ttk.Button(button_frame, text="粘贴", 
                               command=self.paste_from_clipboard)
        paste_button.pack(side=tk.LEFT, padx=5)
        
        # 清除按钮
        clear_button = ttk.Button(button_frame, text="清除", 
                              command=lambda: self.machine_id_entry.delete(0, tk.END))
        clear_button.pack(side=tk.LEFT, padx=5)
        
        # 生成按钮
        generate_button = ttk.Button(button_frame, text="生成重置码", 
                                  command=self.on_generate)
        generate_button.pack(side=tk.RIGHT, padx=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="重置码")
        result_frame.pack(fill=tk.X, pady=10)
        
        self.result_var = tk.StringVar(value="在上方输入机器码后点击生成按钮")
        result_label = ttk.Label(result_frame, textvariable=self.result_var, 
                              font=("Consolas", 13, "bold"))
        result_label.pack(pady=10)
        
        # 复制按钮
        copy_button = ttk.Button(result_frame, text="复制到剪贴板", 
                              command=self.copy_result)
        copy_button.pack(pady=5)
        
        # 说明文本
        note_label = ttk.Label(main_frame, 
                            text="注意：重置码仅对输入的机器码有效，请谨慎保管。", 
                            foreground="red", font=("微软雅黑", 9))
        note_label.pack(pady=(20, 0))
        
        # 调试区域（默认隐藏）
        self.debug_frame = ttk.LabelFrame(main_frame, text="调试信息")
        self.debug_var = tk.StringVar(value="无调试信息")
        debug_label = ttk.Label(self.debug_frame, textvariable=self.debug_var, 
                             font=("Consolas", 10))
        debug_label.pack(pady=5)
        
        # 添加调试按钮（隐藏在角落）
        debug_button = ttk.Button(main_frame, text="D", width=2, 
                               command=self.toggle_debug)
        debug_button.place(relx=1.0, rely=1.0, anchor=tk.SE)
    
    def paste_from_clipboard(self):
        try:
            clipboard_text = self.root.clipboard_get()
            self.machine_id_entry.delete(0, tk.END)
            self.machine_id_entry.insert(0, clipboard_text)
        except:
            messagebox.showerror("错误", "剪贴板中没有文本内容")
    
    def copy_result(self):
        result = self.result_var.get()
        if result and result != "在上方输入机器码后点击生成按钮":
            self.root.clipboard_clear()
            self.root.clipboard_append(result)
            self.root.update()
            messagebox.showinfo("复制成功", "重置码已复制到剪贴板")
    
    def on_generate(self):
        machine_id = self.machine_id_entry.get().strip()
        if not machine_id:
            self.result_var.set("请输入有效的机器码")
            return
        
        reset_code = generate_reset_code(machine_id)
        self.result_var.set(reset_code)
        
        # 调试信息
        self.debug_var.set(f"机器码: {machine_id}\n"
                          f"清理后: {''.join(c for c in machine_id if c in string.ascii_letters + string.digits)}\n"
                          f"种子: MS_RESET_{machine_id}")
        
        # 自动复制到剪贴板
        self.root.clipboard_clear()
        self.root.clipboard_append(reset_code)
        self.root.update()
    
    def toggle_debug(self):
        """切换调试信息的显示状态"""
        if self.debug_frame.winfo_ismapped():
            self.debug_frame.pack_forget()
        else:
            self.debug_frame.pack(fill=tk.X, pady=10)

def main():
    root = tk.Tk()
    app = ResetCodeGeneratorApp(root)
    root.mainloop()

if __name__ == "__main__":
    main() 