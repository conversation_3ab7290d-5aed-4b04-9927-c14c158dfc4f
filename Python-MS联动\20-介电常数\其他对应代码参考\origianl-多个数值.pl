################################################################################
#!perl                                                                         #
#                                                                              #
# DIELECTRIC CONSTANT (NON-EQUILIBRIUM)                                        #
#                                                                              #
# Author: <PERSON><PERSON><PERSON> (Accelrys)                                         #
# Version: 1.0                                                                 #
# Tested on: Materials Studio 5.5                                              #
# Required modules: Materials Visualizer, Forcite Plus                         #
#                                                                              #
# This script calculates the dielectric constant from the total dipole moment, #
# resulting from an electric field:                                            #
#                                                                              #
#              <M>                                                             #
# eps = 1 + ----------                                                         #
#           <V> E eps0                                                         #
#                                                                              #
#  eps:  dielectric constant                                                   #
#  M:    total dipole moment (in the direction of the field)                   #
#  E:    electric field strength                                               #
#  V:    volume of the box                                                     #
#  eps0: vacuum permittivity                                                   #
#                                                                              #
################################################################################
use strict;
use MaterialsScript qw(:all);

use constant SPEED_OF_LIGHT => 299792458; # m/s
use constant ELEMENTARY_CHARGE => 1.602176565E-19; # C
use constant ANGSTROM => 1E-10; # m
use constant EA => ELEMENTARY_CHARGE*ANGSTROM; # data model unit for dipoles
use constant DEBYE => 1E-21/SPEED_OF_LIGHT; # C*m
use constant PI => 3.14159265358979323846264338327950288419716939937510;
use constant MAGNETIC_CONSTANT => 4*PI*1E-7; # s^2*V/C/m
use constant VACUUM_PERMITTIVITY => 1/MAGNETIC_CONSTANT/SPEED_OF_LIGHT/SPEED_OF_LIGHT; # C/V/m
use constant BOLTZMANN => 1.3806488E-23; # J/K

# the supported directions for the electric field
use constant { A => 0, B => 1, C => 2};

# column indices for the results
use constant { FIELD => 0, DIPOLE => 1, DIELECTRIC => 2};
use constant { TIME => 0, DIPOLE_X => 1, DIPOLE_Y => 2, DIPOLE_Z => 3};

{
################################################################################
#  BEGIN USER INPUT                                                            #
################################################################################

# the trajectory to be analysed
my $doc = $Documents{"acetonitrile.xsd"};

# script settings
my $settings = Settings(
	NumStepsEq => 100000, # number of equilibration steps
	NumStepsProd => 50000, # number of production steps
	NumFrames => 1000, # number of frames to collect for analysis
	ElectricFieldMin => 0, # minimum electric field (in V/A)
	ElectricFieldMax => 0.01, #  maximum electric field (in V/A)
	NumElectricFieldSteps => 10, # number of runs between min and max
	ElectricFieldDirection => C, # direction of the electric field
	Temperature => 298, # temperature (in K)
	CurrentForcefield => "COMPASS"# # force field to use
);
################################################################################
#  END USER  INPUT                                                             #
################################################################################

Run($doc,$settings);
}
printf "Time taken: %d seconds", time()-$^T;

################################################################################
sub Run
{
	my ($doc,$settings) = @_;

	# create a hash of the settings array for easy access
	my %settings = Array2Hash($settings);

	# construct the field vector (assuming triclinic cell)
	my $electricFieldDirection = Point(X => 0, Y => 0, Z => 0);
	$electricFieldDirection->X++ if $settings{ElectricFieldDirection} == A;
	$electricFieldDirection->Y++ if $settings{ElectricFieldDirection} == B;
	$electricFieldDirection->Z++ if $settings{ElectricFieldDirection} == C;
		
	# set up MD simulation
	Modules->Forcite->ChangeSettings([
		ElectricFieldX => $electricFieldDirection->X,
		ElectricFieldY => $electricFieldDirection->Y,
		ElectricFieldZ => $electricFieldDirection->Z,
		CurrentForcefield => $settings{CurrentForcefield},
		Ensemble3D => "NVT",
		Temperature => $settings{Temperature},
		Thermostat => "Berendsen",
		BerendsenThermostatDecayConstant => 0.1, # ps
		WriteLevel => "Silent"]);
	
	# study table to report the results
	my $std = Documents->New($doc->Name . ".std");
	ClearStudyTable($std);
	
	my $resultsSheet = $std->ActiveSheet;
	$resultsSheet->Title = "Results";
	$resultsSheet->ColumnHeading(FIELD) = "electric field (V/A)";
	$resultsSheet->ColumnHeading(DIPOLE) = "total dipole (D)";
	$resultsSheet->ColumnHeading(DIELECTRIC) = "dielectric constant";
	
	# run over all field strengths
	my $eBin = ($settings{ElectricFieldMax}-$settings{ElectricFieldMin})/$settings{NumElectricFieldSteps};
	for (my $iRun = 0; $iRun <= $settings{NumElectricFieldSteps}; $iRun++) 
	{
		my $electricFieldStrength = $settings{ElectricFieldMin}+$iRun*$eBin;
		
		# sheet for frame data
		my $sheet = $std->InsertSheet();
		$sheet->Title = sprintf("E = %.3f V/A", $electricFieldStrength);
		$sheet->ColumnHeading(TIME) = "time (ps)";
		$sheet->ColumnHeading(DIPOLE_X) = "dipole x (D)";
		$sheet->ColumnHeading(DIPOLE_Y) = "dipole y (D)";
		$sheet->ColumnHeading(DIPOLE_Z) = "dipole z (D)";
		
		Modules->Forcite->ChangeSettings([ElectricFieldStrength => $electricFieldStrength]);
			
		# equilibration
		my $resultsEq = Modules->Forcite->Dynamics->Run($doc, Settings(
			NumberOfSteps => $settings{NumStepsEq}, 
			TrajectoryFrequency => $settings{NumStepsEq}));
		
		# production
		my $resultsProd = Modules->Forcite->Dynamics->Run($resultsEq->Trajectory, Settings(
			TrajectoryRestart => "Yes", 
			NumberOfSteps => $settings{NumStepsProd}, 
			TrajectoryFrequency => $settings{NumStepsProd}/$settings{NumFrames}));
		
		# calculate the total dipole moment in the direction of the field.
		my $xtd = $resultsProd->Trajectory;
		my $molecules = $xtd->UnitCell->Molecules;
		my $numMolecules = $molecules->Count;
		my $dipole = 0;
		my $volume = 0;
		my $trj = $xtd->Trajectory;
		my $numFrames = $trj->NumFrames;
		for (my $i = 0; $i < $numFrames; $i++)
		{
			$trj->CurrentFrame = $i+1;
	 		
	 		my $totalDipoleMoment = TotalDipoleMoment($molecules); # D
			
			$sheet->InsertRow if $i >= $sheet->RowCount;
			$sheet->Cell($i,TIME) = $trj->FrameTime;
			$sheet->Cell($i,DIPOLE_X) = $totalDipoleMoment->X;
			$sheet->Cell($i,DIPOLE_Y) = $totalDipoleMoment->Y;
			$sheet->Cell($i,DIPOLE_Z) = $totalDipoleMoment->Z;
			
			$dipole += DotProduct($electricFieldDirection,$totalDipoleMoment);
			$volume += $doc->Lattice3D->CellVolume;# A^3		
		}
		$dipole /= $numFrames;
		$volume /= $numFrames;
		
		$resultsSheet->InsertRow if $iRun >= $resultsSheet->RowCount;
		$resultsSheet->Cell($iRun,FIELD) = $electricFieldStrength; # V/A
		$resultsSheet->Cell($iRun,DIPOLE) = $dipole; # D
		
		
		# fit the results so far
		if($iRun > 0)
		{
			# get the slope (in D/(V/A) = D*A/V)
			my $slope = LeastSquareFit($resultsSheet, 0, $iRun, FIELD, DIPOLE);
		
			#convert to SI units
			$slope *= ANGSTROM*DEBYE; # C*m^2/V			
			$volume *= ANGSTROM*ANGSTROM*ANGSTROM; # m^3
	
			my $diElectricConstant = $slope/$volume; # C*m^2/V/m^3 = C/V/m
			$diElectricConstant /= VACUUM_PERMITTIVITY;
			$diElectricConstant += 1;
			
			$resultsSheet->Cell($iRun,DIELECTRIC) = $diElectricConstant;
		}
		
		$xtd->Delete;
		Documents->SaveAll;
	}
}

sub TotalDipoleMoment
{
	my ($molecules) = @_;
	
	my $dX = 0;
	my $dY = 0;
	my $dZ = 0;
	foreach my $molecule (@$molecules)
	{
		my $dipoleMoment = $molecule->DipoleMoment; # e*A
		$dX += $dipoleMoment->X;
		$dY += $dipoleMoment->Y;
		$dZ += $dipoleMoment->Z;
	}
	# convert from e*A to D
	$dX *= EA/DEBYE;
	$dY *= EA/DEBYE;
	$dZ *= EA/DEBYE;

	return Point(X => $dX, Y => $dY, Z => $dZ);
}

sub DotProduct
{
	my ($a,$b) = @_;
	return $a->X*$b->X+$a->Y*$b->Y+$a->Z*$b->Z;
}

sub LeastSquareFit
{
	my ($sheet, $begin, $end, $xCol, $yCol) = @_;

	# get the average x and y
	my $xAv = 0;
	my $yAv = 0;
	for(my $iLayer = $begin; $iLayer <= $end; $iLayer++)
	{
		$xAv += $sheet->Cell($iLayer,$xCol);
		$yAv += $sheet->Cell($iLayer,$yCol);
	}
	my $N = $end-$begin+1;
	$xAv /= $N;
	$yAv /= $N;
	
	# get the covariances
	my $xx = 0;
	my $xy = 0;
	for(my $iLayer = $begin; $iLayer <= $end; $iLayer++)
	{
		my $xDiff = $sheet->Cell($iLayer,$xCol)-$xAv;
		my $yDiff = $sheet->Cell($iLayer,$yCol)-$yAv;
		$xx += $xDiff*$xDiff;
		$xy += $xDiff*$yDiff;
	}

	# in the unlikely event that all samples are the same, slope will be zero
	my $slope = ($xx == 0)? 0. : $xy/$xx;

	return $slope;
}

################################################################################
#  creates a hash from an array with key-value elements                        #
################################################################################
sub Array2Hash
{
	# make hash
	my ($settings) = @_;
	my @array = @$settings;
	my %hash;
	for(my $i = 0; $i < scalar(@array); $i +=2)
	{
		my $key = $array[$i];
		my $value = $array[$i+1];
		$hash{$key} = $value;
	}
	return %hash;
}

################################################################################
#  clear studytable from empty rows                                            #
################################################################################
sub ClearStudyTable
{
	my($std) = @_;
	
	# delete the rows and columns of the active sheet
	do
	{
		$std->DeleteRow(0);
	}
	while($std->RowCount > 0);
	
	do
	{
		$std->DeleteColumn(0);
	}
	while($std->ColumnCount > 1);	# need at least one column (infinite loop otherwise!)
}