use Cwd;![[Pasted image 20240123135625.png]]

open my $out, '>', "Centroid.txt";
my $cwd = getcwd;
![[Pasted image 20240123144144.png]]

print {$out}"Centroid is at";
PrintPoint($out, $center);
close($out);
Documents->Import("$cwd/Centroid.txt");
sub PrintPoint
{
    my ($out, $point) = @_;
    printf {$out} "[%.2f, %.2f, %.2f]\n", $point->X, $point->Y, $point->Z;
}
![[Pasted image 20240123144153.png]]


这段代码是用Perl编写的，主要用于打开一个文件并获取当前工作目录。下面是对每一行代码的详细解释：

### 1. `open my $out, '>', "Disatnce_Angle_Torsion.txt";`

- **`open`**: 这是Perl中的一个内置函数，用于打开文件。
- **`my $out`**: 这声明了一个新的变量 `$out`，用于存储文件句柄。`my` 是Perl中的词法作用域声明，表示这个变量只在当前代码块中有效。
- **`'>'`**: 这是文件打开模式。`>` 表示以写入模式打开文件。如果文件不存在，Perl会创建它；如果文件已经存在，Perl会清空文件内容。
- **`"Disatnce_Angle_Torsion.txt"`**: 这是要打开的文件名。文件名是 `Disatnce_Angle_Torsion.txt`。

这行代码的作用是打开一个名为 `Disatnce_Angle_Torsion.txt` 的文件，并以写入模式打开它。文件句柄存储在 `$out` 变量中，后续可以通过 `$out` 来写入文件。

### 2. `my $cwd = getcwd;`

- **`my $cwd`**: 这声明了一个新的变量 `$cwd`，用于存储当前工作目录的路径。
- **`getcwd`**: 这是Perl中的一个内置函数，用于获取当前工作目录的路径。`getcwd` 返回一个字符串，表示当前工作目录的完整路径。

这行代码的作用是获取当前工作目录的路径，并将其存储在 `$cwd` 变量中。

### 总结

- 第一行代码打开了一个名为 `Disatnce_Angle_Torsion.txt` 的文件，并以写入模式打开它。
- 第二行代码获取了当前工作目录的路径，并将其存储在 `$cwd` 变量中。

这两行代码通常用于准备向文件写入数据，并记录当前的工作目录。