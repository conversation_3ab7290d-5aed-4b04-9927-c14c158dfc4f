import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings("ignore")  # 忽略警告

# 配置matplotlib支持中文显示
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.rcParams['font.size'] = 10  # 设置字体大小
    plt.style.use('ggplot')  # 使用ggplot样式
except Exception as e:
    print(f"配置matplotlib字体时出错: {e}")

class MLApp:
    def __init__(self, root):
        """初始化应用程序"""
        self.root = root
        self.root.title("线性模型分析工具")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # 初始化数据和变量
        self.data = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.model = None
        self.predictions = None
        self.feature_names = []
        
        # 创建UI变量
        self.target_var = tk.StringVar()
        self.test_size_var = tk.DoubleVar(value=0.2)
        self.model_var = tk.StringVar(value="线性回归")
        self.alpha_var = tk.DoubleVar(value=1.0)
        self.l1_ratio_var = tk.DoubleVar(value=0.5)
        self.max_iter_var = tk.IntVar(value=1000)
        self.scale_var = tk.BooleanVar(value=True)
        self.viz_type_var = tk.StringVar(value="预测vs实际值")
        
        # 初始化状态栏变量
        self.status_var = tk.StringVar(value="就绪")
        
        # 创建界面组件
        self.create_widgets()
        
    def create_widgets(self):
        """创建主界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="5")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建左右分隔面板
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧控制面板
        left_frame = ttk.Frame(paned, padding="10")
        left_frame.pack(fill=tk.BOTH, expand=True)
        paned.add(left_frame, weight=1)
        
        # 右侧结果面板
        right_frame = ttk.Frame(paned, padding="10")
        right_frame.pack(fill=tk.BOTH, expand=True)
        paned.add(right_frame, weight=2)
        
        # 设置各部分内容
        self.setup_left_panel(left_frame)
        self.setup_right_panel(right_frame)
        
        # 创建底部状态栏
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建底部固定的运行按钮框架
        bottom_frame = tk.Frame(self.root, bg="#f0f0f0", height=60)
        bottom_frame.pack(side=tk.BOTTOM, fill=tk.X)
        bottom_frame.pack_propagate(False)
        
        # 添加提示文本
        hint_label = tk.Label(
            bottom_frame, 
            text="完成所有设置后，点击此按钮运行模型 →", 
            font=("Arial", 12),
            bg="#f0f0f0"
        )
        hint_label.pack(side=tk.LEFT, padx=20)
        
        # 底部运行按钮
        self.run_button = tk.Button(
            bottom_frame,
            text="运行模型",
            command=self.run_model,
            bg="#FF5722",  # 橙色背景
            fg="white",    # 白色文字
            font=("Arial", 14, "bold"),
            width=15,
            height=1,
            relief=tk.RAISED,
            bd=3
        )
        self.run_button.pack(side=tk.RIGHT, padx=20, pady=10)
        
    def setup_left_panel(self, parent):
        """设置左侧控制面板"""
        # 数据加载区域
        data_frame = ttk.LabelFrame(parent, text="数据加载", padding="5")
        data_frame.pack(fill=tk.X, pady=5)
        
        # 加载数据按钮
        load_button = ttk.Button(data_frame, text="加载数据文件", command=self.load_data)
        load_button.pack(fill=tk.X, pady=5)
        
        # 文件名标签
        self.file_label = ttk.Label(data_frame, text="未选择文件")
        self.file_label.pack(fill=tk.X, pady=5)
        
        # 数据处理区域
        process_frame = ttk.LabelFrame(parent, text="数据处理", padding="5")
        process_frame.pack(fill=tk.X, pady=5)
        
        # 目标变量选择
        ttk.Label(process_frame, text="选择目标变量:").pack(anchor=tk.W, pady=2)
        self.target_combo = ttk.Combobox(process_frame, textvariable=self.target_var, state="readonly")
        self.target_combo.pack(fill=tk.X, pady=2)
        self.target_combo.bind("<<ComboboxSelected>>", self.update_features)
        
        # 特征选择
        ttk.Label(process_frame, text="选择特征变量:").pack(anchor=tk.W, pady=2)
        
        # 创建带滚动条的列表框
        feature_frame = ttk.Frame(process_frame)
        feature_frame.pack(fill=tk.BOTH, expand=True, pady=2)
        
        scrollbar = ttk.Scrollbar(feature_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.feature_listbox = tk.Listbox(feature_frame, selectmode=tk.MULTIPLE, height=6)
        self.feature_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.feature_listbox.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.feature_listbox.yview)
        
        # 特征选择按钮
        feature_btn_frame = ttk.Frame(process_frame)
        feature_btn_frame.pack(fill=tk.X, pady=2)
        
        ttk.Button(feature_btn_frame, text="选择所有特征", command=self.select_all_features).pack(side=tk.LEFT, padx=2)
        ttk.Button(feature_btn_frame, text="清除所有特征", command=self.clear_all_features).pack(side=tk.LEFT, padx=2)
        
        # 测试集比例滑块
        ttk.Label(process_frame, text="测试集比例:").pack(anchor=tk.W, pady=2)
        test_scale = ttk.Scale(process_frame, variable=self.test_size_var, from_=0.1, to=0.5, orient=tk.HORIZONTAL)
        test_scale.pack(fill=tk.X, pady=2)
        
        # 数据标准化选项
        scale_check = ttk.Checkbutton(process_frame, text="标准化数据", variable=self.scale_var)
        scale_check.pack(anchor=tk.W, pady=2)
        
        # 算法选择区域
        algo_frame = ttk.LabelFrame(parent, text="算法选择", padding="5")
        algo_frame.pack(fill=tk.X, pady=5)
        
        models = ["线性回归", "岭回归", "Lasso回归", "弹性网络"]
        for model in models:
            ttk.Radiobutton(algo_frame, text=model, variable=self.model_var, value=model, 
                          command=self.update_param_frame).pack(anchor=tk.W, pady=2)
        
        # 参数设置区域
        self.param_frame = ttk.LabelFrame(parent, text="参数设置", padding="5")
        self.param_frame.pack(fill=tk.X, pady=5)
        
        # 初始化参数框架
        self.update_param_frame()
        
        # 运行按钮（左侧）
        run_button = ttk.Button(parent, text="运行模型", command=self.run_model)
        run_button.pack(fill=tk.X, pady=10)
        
    def setup_right_panel(self, parent):
        """设置右侧结果面板"""
        # 结果区域
        result_frame = ttk.LabelFrame(parent, text="模型结果", padding="5")
        result_frame.pack(fill=tk.X, pady=5)
        
        # 性能指标区域
        self.metrics_frame = ttk.Frame(result_frame)
        self.metrics_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(self.metrics_frame, text="等待模型运行...").pack()
        
        # 可视化区域
        viz_frame = ttk.LabelFrame(parent, text="可视化", padding="5")
        viz_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 可视化类型选择
        viz_type_frame = ttk.Frame(viz_frame)
        viz_type_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(viz_type_frame, text="选择可视化类型:").pack(side=tk.LEFT, padx=5)
        viz_types = ["预测vs实际值", "特征重要性", "残差图"]
        self.viz_combo = ttk.Combobox(viz_type_frame, textvariable=self.viz_type_var, values=viz_types, state="readonly")
        self.viz_combo.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.viz_combo.bind("<<ComboboxSelected>>", self.update_visualization)
        
        # 图表区域
        self.fig_frame = ttk.Frame(viz_frame)
        self.fig_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建空白图表
        self.fig = plt.Figure(figsize=(6, 5), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.fig_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 导出结果按钮
        export_button = ttk.Button(parent, text="导出结果", command=self.export_results)
        export_button.pack(anchor=tk.E, pady=10) 

    def load_data(self):
        """加载数据文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx;*.xls"), ("所有文件", "*.*")]
        )
        
        if not file_path:
            return
            
        try:
            self.status_var.set("正在加载数据...")
            self.root.update_idletasks()
            
            # 根据文件类型加载数据
            if file_path.endswith('.csv'):
                self.data = pd.read_csv(file_path)
            elif file_path.endswith(('.xlsx', '.xls')):
                self.data = pd.read_excel(file_path)
            else:
                messagebox.showwarning("未知文件类型", "请选择CSV或Excel文件")
                return
                
            # 更新文件名标签
            file_name = os.path.basename(file_path)
            self.file_label.config(text=f"已加载: {file_name}")
            
            # 更新目标变量选择
            self.target_combo['values'] = list(self.data.columns)
            if len(self.data.columns) > 0:
                self.target_combo.current(len(self.data.columns) - 1)  # 默认选择最后一列
                self.update_features()
                
            messagebox.showinfo("数据加载成功", f"成功加载数据，包含 {self.data.shape[0]} 行和 {self.data.shape[1]} 列")
            self.status_var.set("数据加载完成")
        except Exception as e:
            messagebox.showerror("数据加载错误", f"加载数据时出错:\n{str(e)}")
            self.status_var.set("数据加载失败")
            
    def update_features(self, event=None):
        """更新特征列表"""
        if self.data is None:
            return
            
        # 清空特征列表
        self.feature_listbox.delete(0, tk.END)
        
        # 获取目标变量
        target = self.target_var.get()
        
        # 更新特征列表（排除目标变量）
        self.feature_names = [col for col in self.data.columns if col != target]
        for feature in self.feature_names:
            self.feature_listbox.insert(tk.END, feature)
            
    def select_all_features(self):
        """选择所有特征"""
        if self.data is None:
            return
            
        # 选择所有特征
        self.feature_listbox.select_set(0, tk.END)
        
    def clear_all_features(self):
        """清除所有特征选择"""
        self.feature_listbox.selection_clear(0, tk.END)
        
    def update_param_frame(self):
        """根据选择的模型更新参数设置区域"""
        # 清空参数区域
        for widget in self.param_frame.winfo_children():
            widget.destroy()
            
        model = self.model_var.get()
        
        if model == "线性回归":
            ttk.Label(self.param_frame, text="线性回归无需额外参数").pack(pady=5)
            
        elif model == "岭回归":
            ttk.Label(self.param_frame, text="正则化系数 (alpha):").pack(anchor=tk.W, pady=2)
            alpha_scale = ttk.Scale(self.param_frame, variable=self.alpha_var, from_=0.01, to=10.0, orient=tk.HORIZONTAL)
            alpha_scale.pack(fill=tk.X, pady=2)
            alpha_label = ttk.Label(self.param_frame, textvariable=tk.StringVar(value=f"当前值: {self.alpha_var.get():.2f}"))
            alpha_label.pack(anchor=tk.W, pady=2)
            
            alpha_scale.config(command=lambda value: alpha_label.config(text=f"当前值: {float(value):.2f}"))
            
        elif model == "Lasso回归":
            ttk.Label(self.param_frame, text="正则化系数 (alpha):").pack(anchor=tk.W, pady=2)
            alpha_scale = ttk.Scale(self.param_frame, variable=self.alpha_var, from_=0.01, to=10.0, orient=tk.HORIZONTAL)
            alpha_scale.pack(fill=tk.X, pady=2)
            alpha_label = ttk.Label(self.param_frame, textvariable=tk.StringVar(value=f"当前值: {self.alpha_var.get():.2f}"))
            alpha_label.pack(anchor=tk.W, pady=2)
            
            alpha_scale.config(command=lambda value: alpha_label.config(text=f"当前值: {float(value):.2f}"))
            
            ttk.Label(self.param_frame, text="最大迭代次数:").pack(anchor=tk.W, pady=2)
            iter_scale = ttk.Scale(self.param_frame, variable=self.max_iter_var, from_=100, to=10000, orient=tk.HORIZONTAL)
            iter_scale.pack(fill=tk.X, pady=2)
            iter_label = ttk.Label(self.param_frame, textvariable=tk.StringVar(value=f"当前值: {self.max_iter_var.get()}"))
            iter_label.pack(anchor=tk.W, pady=2)
            
            iter_scale.config(command=lambda value: iter_label.config(text=f"当前值: {int(float(value))}"))
            
        elif model == "弹性网络":
            ttk.Label(self.param_frame, text="正则化系数 (alpha):").pack(anchor=tk.W, pady=2)
            alpha_scale = ttk.Scale(self.param_frame, variable=self.alpha_var, from_=0.01, to=10.0, orient=tk.HORIZONTAL)
            alpha_scale.pack(fill=tk.X, pady=2)
            alpha_label = ttk.Label(self.param_frame, textvariable=tk.StringVar(value=f"当前值: {self.alpha_var.get():.2f}"))
            alpha_label.pack(anchor=tk.W, pady=2)
            
            alpha_scale.config(command=lambda value: alpha_label.config(text=f"当前值: {float(value):.2f}"))
            
            ttk.Label(self.param_frame, text="L1比率:").pack(anchor=tk.W, pady=2)
            l1_scale = ttk.Scale(self.param_frame, variable=self.l1_ratio_var, from_=0.0, to=1.0, orient=tk.HORIZONTAL)
            l1_scale.pack(fill=tk.X, pady=2)
            l1_label = ttk.Label(self.param_frame, textvariable=tk.StringVar(value=f"当前值: {self.l1_ratio_var.get():.2f}"))
            l1_label.pack(anchor=tk.W, pady=2)
            
            l1_scale.config(command=lambda value: l1_label.config(text=f"当前值: {float(value):.2f}"))
            
            ttk.Label(self.param_frame, text="最大迭代次数:").pack(anchor=tk.W, pady=2)
            iter_scale = ttk.Scale(self.param_frame, variable=self.max_iter_var, from_=100, to=10000, orient=tk.HORIZONTAL)
            iter_scale.pack(fill=tk.X, pady=2)
            iter_label = ttk.Label(self.param_frame, textvariable=tk.StringVar(value=f"当前值: {self.max_iter_var.get()}"))
            iter_label.pack(anchor=tk.W, pady=2)
            
            iter_scale.config(command=lambda value: iter_label.config(text=f"当前值: {int(float(value))}"))
            
    def run_model(self):
        """运行模型"""
        if self.data is None:
            messagebox.showwarning("数据缺失", "请先加载数据")
            return
            
        # 获取目标变量
        target_name = self.target_var.get()
        if not target_name:
            messagebox.showwarning("目标变量缺失", "请选择目标变量")
            return
            
        # 获取选择的特征
        selected_indices = self.feature_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("特征缺失", "请至少选择一个特征")
            return
            
        selected_features = [self.feature_names[i] for i in selected_indices]
        
        try:
            self.status_var.set("正在处理数据...")
            self.root.update_idletasks()
            
            # 准备数据
            X = self.data[selected_features]
            y = self.data[target_name]
            
            # 处理可能的缺失值
            X = X.fillna(X.mean())
            y = y.fillna(y.mean())
            
            # 分割训练集和测试集
            test_size = self.test_size_var.get()
            self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
                X, y, test_size=test_size, random_state=42
            )
            
            # 数据标准化
            if self.scale_var.get():
                scaler = StandardScaler()
                self.X_train = scaler.fit_transform(self.X_train)
                self.X_test = scaler.transform(self.X_test)
                # 保存特征名，因为标准化后会丢失
                self.X_train_df = pd.DataFrame(self.X_train, columns=selected_features)
                self.X_test_df = pd.DataFrame(self.X_test, columns=selected_features)
            else:
                self.X_train_df = self.X_train
                self.X_test_df = self.X_test
            
            # 创建并训练模型
            self.status_var.set("正在训练模型...")
            self.root.update_idletasks()
            
            model_type = self.model_var.get()
            
            if model_type == "线性回归":
                self.model = LinearRegression()
            elif model_type == "岭回归":
                self.model = Ridge(alpha=self.alpha_var.get())
            elif model_type == "Lasso回归":
                self.model = Lasso(alpha=self.alpha_var.get(), max_iter=self.max_iter_var.get())
            elif model_type == "弹性网络":
                self.model = ElasticNet(
                    alpha=self.alpha_var.get(),
                    l1_ratio=self.l1_ratio_var.get(),
                    max_iter=self.max_iter_var.get()
                )
                
            # 训练模型
            self.model.fit(self.X_train, self.y_train)
            
            # 预测
            self.y_train_pred = self.model.predict(self.X_train)
            self.y_test_pred = self.model.predict(self.X_test)
            
            # 计算性能指标
            self.train_mse = mean_squared_error(self.y_train, self.y_train_pred)
            self.test_mse = mean_squared_error(self.y_test, self.y_test_pred)
            self.train_r2 = r2_score(self.y_train, self.y_train_pred)
            self.test_r2 = r2_score(self.y_test, self.y_test_pred)
            
            # 更新性能指标显示
            self.update_metrics()
            
            # 更新可视化
            self.update_visualization()
            
            # 存储预测结果
            self.predictions = {
                'y_train': self.y_train,
                'y_train_pred': self.y_train_pred,
                'y_test': self.y_test,
                'y_test_pred': self.y_test_pred
            }
            
            self.status_var.set("模型训练和预测完成")
            messagebox.showinfo("模型训练完成", "模型训练和预测已完成")
            
        except Exception as e:
            messagebox.showerror("模型运行错误", f"模型运行时出错:\n{str(e)}")
            self.status_var.set("模型运行失败")
            
    def update_metrics(self):
        """更新性能指标显示"""
        # 清空指标区域
        for widget in self.metrics_frame.winfo_children():
            widget.destroy()
            
        # 创建表格显示指标
        metrics_table = ttk.Frame(self.metrics_frame)
        metrics_table.pack(fill=tk.BOTH, expand=True)
        
        # 创建表头
        ttk.Label(metrics_table, text="", width=10).grid(row=0, column=0, padx=2, pady=2)
        ttk.Label(metrics_table, text="训练集", width=15, font=("Arial", 10, "bold")).grid(row=0, column=1, padx=2, pady=2)
        ttk.Label(metrics_table, text="测试集", width=15, font=("Arial", 10, "bold")).grid(row=0, column=2, padx=2, pady=2)
        
        # 添加MSE指标
        ttk.Label(metrics_table, text="MSE", width=10, font=("Arial", 10, "bold")).grid(row=1, column=0, padx=2, pady=2)
        ttk.Label(metrics_table, text=f"{self.train_mse:.4f}", width=15).grid(row=1, column=1, padx=2, pady=2)
        ttk.Label(metrics_table, text=f"{self.test_mse:.4f}", width=15).grid(row=1, column=2, padx=2, pady=2)
        
        # 添加R²指标
        ttk.Label(metrics_table, text="R²", width=10, font=("Arial", 10, "bold")).grid(row=2, column=0, padx=2, pady=2)
        ttk.Label(metrics_table, text=f"{self.train_r2:.4f}", width=15).grid(row=2, column=1, padx=2, pady=2)
        ttk.Label(metrics_table, text=f"{self.test_r2:.4f}", width=15).grid(row=2, column=2, padx=2, pady=2)
        
        # 添加指标解释
        explanation = ttk.Frame(self.metrics_frame)
        explanation.pack(fill=tk.X, pady=5)
        
        ttk.Label(explanation, text="指标说明:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        ttk.Label(explanation, text="MSE (均方误差): 值越小越好").pack(anchor=tk.W)
        ttk.Label(explanation, text="R² (决定系数): 值越接近1越好").pack(anchor=tk.W)
        
    def update_visualization(self, event=None):
        """更新可视化图表"""
        if self.model is None:
            return
            
        viz_type = self.viz_type_var.get()
        
        # 清空图表
        self.fig.clear()
        ax = self.fig.add_subplot(111)
        
        if viz_type == "预测vs实际值":
            # 绘制训练集散点图
            ax.scatter(self.y_train, self.y_train_pred, color='blue', alpha=0.5, label='训练集')
            # 绘制测试集散点图
            ax.scatter(self.y_test, self.y_test_pred, color='red', alpha=0.5, label='测试集')
            
            # 绘制理想线
            min_val = min(min(self.y_train), min(self.y_test))
            max_val = max(max(self.y_train), max(self.y_test))
            ax.plot([min_val, max_val], [min_val, max_val], 'k--', lw=2)
            
            ax.set_xlabel('实际值')
            ax.set_ylabel('预测值')
            ax.set_title('预测值 vs 实际值')
            ax.legend()
            
        elif viz_type == "特征重要性":
            # 获取模型系数
            if hasattr(self.model, 'coef_'):
                # 获取特征名称
                if self.scale_var.get():
                    feature_names = self.X_train_df.columns
                else:
                    feature_names = self.X_train.columns
                    
                # 排序系数
                coefs = self.model.coef_
                if not isinstance(coefs, np.ndarray) and hasattr(coefs, 'toarray'):
                    coefs = coefs.toarray()[0]
                    
                coef_df = pd.DataFrame({'特征': feature_names, '系数': coefs})
                coef_df = coef_df.reindex(coef_df['系数'].abs().sort_values(ascending=False).index)
                
                # 绘制条形图
                ax.barh(coef_df['特征'], coef_df['系数'])
                ax.set_xlabel('系数值')
                ax.set_ylabel('特征')
                ax.set_title('特征重要性 (系数)')
                
                # 为负值添加不同颜色
                bars = ax.containers[0]
                for bar, coef in zip(bars, coef_df['系数']):
                    bar.set_color('blue' if coef > 0 else 'red')
            else:
                ax.text(0.5, 0.5, "该模型不支持系数可视化", ha='center', va='center')
                
        elif viz_type == "残差图":
            # 计算残差
            train_residuals = self.y_train - self.y_train_pred
            test_residuals = self.y_test - self.y_test_pred
            
            # 绘制训练集残差散点图
            ax.scatter(self.y_train_pred, train_residuals, color='blue', alpha=0.5, label='训练集')
            # 绘制测试集残差散点图
            ax.scatter(self.y_test_pred, test_residuals, color='red', alpha=0.5, label='测试集')
            
            # 添加水平线
            ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
            
            ax.set_xlabel('预测值')
            ax.set_ylabel('残差')
            ax.set_title('残差图')
            ax.legend()
            
        # 调整布局
        self.fig.tight_layout()
        
        # 更新画布
        self.canvas.draw()
        
    def export_results(self):
        """导出预测结果"""
        if self.predictions is None:
            messagebox.showwarning("无结果", "请先运行模型")
            return
            
        # 获取保存路径
        file_path = filedialog.asksaveasfilename(
            title="保存预测结果",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx")]
        )
        
        if not file_path:
            return
            
        try:
            # 创建结果DataFrame
            results_df = pd.DataFrame({
                '数据集': ['训练集'] * len(self.predictions['y_train']) + ['测试集'] * len(self.predictions['y_test']),
                '实际值': pd.concat([self.predictions['y_train'], self.predictions['y_test']]).reset_index(drop=True),
                '预测值': np.concatenate([self.predictions['y_train_pred'], self.predictions['y_test_pred']])
            })
            
            # 根据文件类型保存
            if file_path.endswith('.csv'):
                results_df.to_csv(file_path, index=False)
            elif file_path.endswith('.xlsx'):
                results_df.to_excel(file_path, index=False)
                
            messagebox.showinfo("导出成功", f"预测结果已保存到: {file_path}")
            
        except Exception as e:
            messagebox.showerror("导出错误", f"导出结果时出错:\n{str(e)}")
            
if __name__ == "__main__":
    root = tk.Tk()
    app = MLApp(root)
    root.mainloop() 