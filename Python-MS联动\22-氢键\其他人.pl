#!perl

use strict;
use warnings;
use MaterialsScript qw(:all);

# Purpose: Calculate H-bond number between two parts in the XTD.

# Author: Luminary
# Current version: 1.0

# Update log 
# 2021-10-05 Version: 1.0: The first version released


###### Begin User INPUT ####################

my $doc = $Documents{"S10.xtd"};
my $statsDoc = Documents->New("HBondStats.std");
$statsDoc->ColumnHeading(0) = "all studied molecules";
$statsDoc->ColumnHeading(1) = "H-bond in all studied";

$statsDoc->ColumnHeading(2) = "part 1 cell";
$statsDoc->ColumnHeading(3) = "H-bond in part 1";

$statsDoc->ColumnHeading(4) = "part 2 cell";
$statsDoc->ColumnHeading(5) = "H-bond in part 2";

$statsDoc->ColumnHeading(6) = "H-bond between part 1 and 2";



my $numFrames = $doc->Trajectory->NumFrames;
for (my $counter = 1; $counter <= $numFrames ; ++$counter) {
$doc->Trajectory->CurrentFrame = $counter;

my $alldoc = Documents->New("all.xsd");
my $part1 = Documents->New("part1.xsd");
my $part2 = Documents->New("part2.xsd");



$alldoc->CopyFrom($doc);
#$alldoc->UnitCell->Sets("Delete")->Atoms->Delete;


$part1->CopyFrom($doc);
$part1->UnitCell->Sets("Part 2")->Atoms->Delete;
#$part1->UnitCell->Sets("Delete")->Atoms->Delete;



$part2->CopyFrom($doc);
$part2->UnitCell->Sets("Part 1")->Atoms->Delete;
#$part2->UnitCell->Sets("Delete")->Atoms->Delete;



#Initialize variables for the stats calculations
my $totalLength = 0;
my $minLength   = 99999.9; #Arbitrary Big Num
my $maxLength   = 0;
my $row = 0;
my $row1 = 0;
my $row2 = 0;

#Get all the HBonds in the UnitCell



Tools->BondCalculation->HBonds->ClearDonors;
Tools->BondCalculation->HBonds->ClearAcceptors;



my @speciesList = ("N", "O","Cl");
foreach my $element (@speciesList){
    Tools->BondCalculation->HBonds->AddDonor($element);
    Tools->BondCalculation->HBonds->AddAcceptor($element);
}
Tools->BondCalculation->HBonds->Calculate($alldoc,Settings(MaxHydrogenAcceptorDistance => "2.5"));
Tools->BondCalculation->HBonds->Calculate($part1,Settings(MaxHydrogenAcceptorDistance => "2.5"));
Tools->BondCalculation->HBonds->Calculate($part2,Settings(MaxHydrogenAcceptorDistance => "2.5"));


#--------------------------Calculate H bonds in whole cell -------------------------------------------

my $hbonds = $alldoc->UnitCell->HydrogenBonds;


#Create a new Study Table for the results


foreach my $hbond (@$hbonds) {
  
    ++$row;
}

$statsDoc->Cell($counter-1,0) = $alldoc;
$statsDoc->Cell($counter-1,1) = $row;

$alldoc->Discard;


#--------------------------Calculate H bonds in part 1 cell -------------------------------------------

my $hbonds1 = $part1->UnitCell->HydrogenBonds;


#Create a new Study Table for the results


foreach my $hbond1 (@$hbonds1) {
  
    ++$row1;
}

$statsDoc->Cell($counter-1,2) = $part1;
$statsDoc->Cell($counter-1,3) = $row1;

$part1->Discard;


#--------------------------Calculate H bonds in part 2 cell -------------------------------------------

my $hbonds2 = $part2->UnitCell->HydrogenBonds;


#Create a new Study Table for the results


foreach my $hbond2 (@$hbonds2) {
  
    ++$row2;
}

$statsDoc->Cell($counter-1,4) = $part2;
$statsDoc->Cell($counter-1,5) = $row2;

$part2->Discard;

my $Hbond_bewteen = $row - ($row1 + $row2);
$statsDoc->Cell($counter-1,6) = $Hbond_bewteen;


}
