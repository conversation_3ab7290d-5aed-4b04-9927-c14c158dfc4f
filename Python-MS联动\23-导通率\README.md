# 导通率计算脚本使用说明

本目录包含用于计算材料导通率的Materials Studio Perl脚本。这些脚本可以分析MD模拟轨迹文件，并计算导电填料之间的导通率。

## 背景原理

在分子动力学(MD)模拟中，导通率A被定义为：

```
导通率A = 导通的构型数 / 总构型数
```

当两个导电填料之间的距离小于隧穿距离TD时，认为它们导通。根据量子化学理论，隧穿距离TD通常设置为3Å。虽然导通率A的量级变化不大，但实际复合材料的电导率在渗透阈值附近可有10-100个数量级的变化。

## 脚本功能

本目录包含两个主要脚本：

1. **original.pl** - 基础导通率计算脚本，计算整体导通率
2. **directional_conductivity.pl** - 扩展脚本，可计算X、Y、Z三个方向的导通率

## 使用方法

### 准备工作

1. 确保您有一个Materials Studio的MD模拟轨迹文件（.xtd格式）
2. 将脚本文件复制到您的Materials Studio项目目录中

### 运行脚本

1. 在Materials Studio中打开您的轨迹文件
2. 在Materials Studio的工具栏中选择 File -> Run Script
3. 选择脚本文件（original.pl或directional_conductivity.pl）
4. 点击运行

### 配置参数

您可以根据需要修改脚本开头的参数：

```perl
# 配置参数
my $inputFileName = "Target";  # 轨迹文件基本名称(.xtd)
my $tunnelDistance = 3.0;      # 隧穿距离TD(Å)，当两个导电填料之间距离小于此值时认为导通
my $timeInterval = 0.01;       # 每个构型的输出时间间隔(ps)
my $totalFrames = 5000;        # 需要统计的总构型数
my $conductiveFiller = "C";    # 导电填料原子类型(默认为碳原子，可根据需要修改)
```

- **$inputFileName**: 您的轨迹文件名（不包括.xtd扩展名）
- **$tunnelDistance**: 隧穿距离，单位为埃(Å)
- **$timeInterval**: 轨迹文件中相邻帧的时间间隔
- **$totalFrames**: 要处理的帧数量
- **$conductiveFiller**: 导电填料的原子类型（如C表示碳原子）

### 输出文件

脚本运行后会生成以下文件：

1. **<轨迹文件名>_Conductivity.std** - 包含每一帧的导通状态和统计数据的表格
2. **<轨迹文件名>_Conductivity_Log.txt** - 计算过程的日志文件

对于方向性导通率脚本，输出文件为：

1. **<轨迹文件名>_Directional_Conductivity.std** - 包含各方向导通状态的表格
2. **<轨迹文件名>_Directional_Conductivity_Log.txt** - 计算过程的日志文件

## 工作原理

脚本的主要工作流程如下：

1. 读取轨迹文件的每一帧
2. 对于每一帧：
   - 识别所有导电填料原子
   - 计算原子间距离
   - 当距离小于隧穿距离时，将原子合并到同一团簇
   - 分析团簇的大小和连通性
   - 判断是否形成贯通团簇（导通）
3. 统计导通的帧数并计算导通率

方向性导通率脚本额外分析了团簇在X、Y、Z三个方向上的分布，以确定在特定方向上是否导通。

## 注意事项

1. 脚本默认使用碳原子(C)作为导电填料，如果您的系统使用其他原子类型，请修改`$conductiveFiller`参数
2. 处理大型轨迹文件时可能需要较长时间，请耐心等待
3. 确保您的轨迹文件已经导入到Materials Studio并且格式正确

## 示例结果解读

导通率A的值在0到1之间，其中：
- A = 0表示没有任何构型导通
- A = 1表示所有构型都导通
- 中间值表示部分构型导通

通常，当A > 0.5时，可以认为材料整体具有良好的导电性。 