"""
文件读取模块
支持多种格式的CASTEP介电数据文件读取
"""

import numpy as np
import pandas as pd
import os
from typing import Tuple, Optional, Dict, Any
from ..utils.validators import DataValidator

class CastepFileReader:
    """CASTEP文件读取器"""
    
    def __init__(self):
        self.validator = DataValidator()
    
    def read_epsilon_file(self, file_path: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        读取CASTEP .epsilon文件或其他格式的介电数据文件
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: (能量, 介电函数实部, 介电函数虚部)
        """
        # 验证文件路径
        self.validator.validate_file_path(file_path)
        
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.epsilon':
            return self._read_epsilon_format(file_path)
        elif file_ext in ['.csv', '.txt']:
            return self._read_text_format(file_path)
        elif file_ext in ['.xlsx', '.xls']:
            return self._read_excel_format(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")
    
    def _read_epsilon_format(self, file_path: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        读取CASTEP原生.epsilon格式文件
        
        Args:
            file_path (str): .epsilon文件路径
            
        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: (能量, ε₁, ε₂)
        """
        try:
            # 尝试读取数据，跳过注释行
            data = np.loadtxt(file_path, comments='#')
            
            if data.shape[1] < 3:
                raise ValueError("文件列数不足，至少需要3列(Energy, ε₁, ε₂)")
            
            # 标准格式：Energy(eV), ε₁, ε₂
            energy = data[:, 0]
            epsilon_1 = data[:, 1]
            epsilon_2 = data[:, 2]
            
            return energy, epsilon_1, epsilon_2
            
        except Exception as e:
            raise ValueError(f"读取.epsilon文件失败: {str(e)}")
    
    def _read_text_format(self, file_path: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        读取文本格式文件(CSV/TXT)
        
        Args:
            file_path (str): 文本文件路径
            
        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: (能量, ε₁, ε₂)
        """
        try:
            # 尝试多种分隔符
            separators = ['\t', ',', ' ', ';']
            data = None
            
            for sep in separators:
                try:
                    if file_path.endswith('.csv'):
                        data = pd.read_csv(file_path, sep=sep, comment='#', header=None)
                    else:
                        data = pd.read_csv(file_path, sep=sep, comment='#', header=None, delim_whitespace=(sep==' '))
                    
                    if data.shape[1] >= 3:
                        break
                except:
                    continue
            
            if data is None or data.shape[1] < 3:
                raise ValueError("无法解析文件格式或列数不足")
            
            # 转换为numpy数组
            data_array = data.values
            
            # 自动检测列格式
            energy, epsilon_1, epsilon_2 = self._detect_column_format(data_array)
            
            return energy, epsilon_1, epsilon_2
            
        except Exception as e:
            raise ValueError(f"读取文本文件失败: {str(e)}")
    
    def _read_excel_format(self, file_path: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        读取Excel格式文件
        
        Args:
            file_path (str): Excel文件路径
            
        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: (能量, ε₁, ε₂)
        """
        try:
            # 读取Excel文件
            data = pd.read_excel(file_path, header=None)
            
            if data.shape[1] < 3:
                raise ValueError("Excel文件列数不足，至少需要3列")
            
            # 转换为numpy数组
            data_array = data.values
            
            # 自动检测列格式
            energy, epsilon_1, epsilon_2 = self._detect_column_format(data_array)
            
            return energy, epsilon_1, epsilon_2
            
        except Exception as e:
            raise ValueError(f"读取Excel文件失败: {str(e)}")
    
    def _detect_column_format(self, data: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        自动检测数据列格式
        
        支持的格式：
        1. 3列: Energy, ε₁, ε₂
        2. 4列: Energy, ε₁, Energy(重复), ε₂
        
        Args:
            data (np.ndarray): 原始数据数组
            
        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: (能量, ε₁, ε₂)
        """
        n_cols = data.shape[1]
        
        if n_cols == 3:
            # 标准3列格式
            energy = data[:, 0]
            epsilon_1 = data[:, 1]
            epsilon_2 = data[:, 2]
        elif n_cols >= 4:
            # 4列或更多列格式，检查是否有重复的能量列
            energy_col1 = data[:, 0]
            energy_col3 = data[:, 2]
            
            # 检查第1列和第3列是否相似（可能是重复的能量列）
            if np.allclose(energy_col1, energy_col3, rtol=1e-6):
                # 格式：Energy, ε₁, Energy(重复), ε₂
                energy = energy_col1
                epsilon_1 = data[:, 1]
                epsilon_2 = data[:, 3]
            else:
                # 假设前3列是标准格式
                energy = energy_col1
                epsilon_1 = data[:, 1]
                epsilon_2 = data[:, 2]
        else:
            raise ValueError(f"不支持的列数: {n_cols}")
        
        return energy, epsilon_1, epsilon_2
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件基本信息
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            Dict[str, Any]: 文件信息字典
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        file_stat = os.stat(file_path)
        file_ext = os.path.splitext(file_path)[1].lower()
        
        info = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'file_extension': file_ext,
            'file_size': file_stat.st_size,
            'modification_time': file_stat.st_mtime
        }
        
        # 尝试获取数据行数
        try:
            if file_ext == '.epsilon':
                with open(file_path, 'r') as f:
                    lines = f.readlines()
                    data_lines = [line for line in lines if not line.strip().startswith('#') and line.strip()]
                    info['data_rows'] = len(data_lines)
            elif file_ext in ['.csv', '.txt']:
                data = pd.read_csv(file_path, comment='#')
                info['data_rows'] = len(data)
            elif file_ext in ['.xlsx', '.xls']:
                data = pd.read_excel(file_path)
                info['data_rows'] = len(data)
        except:
            info['data_rows'] = 'Unknown'
        
        return info

def read_castep_data(file_path: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    便捷函数：读取CASTEP介电数据文件
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        Tuple[np.ndarray, np.ndarray, np.ndarray]: (能量, ε₁, ε₂)
    """
    reader = CastepFileReader()
    return reader.read_epsilon_file(file_path)
