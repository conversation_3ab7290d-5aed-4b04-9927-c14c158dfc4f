#!perl

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);
use warnings;
##################################################################################################################
# perl                                                                                                            #
#                                                                                                                 #
# Author: DMPB                                                                                                    #
# Version: 1.3                                                                                                    #
# Testing Environment: Materials Studio 2020                                                                      #
#                                                                                                                 #
# Required Module: Materials Visualizer                                                                           #
#                                                                                                                 #
# Description:                                                                                                    #
# This script is used to fix atoms within specified distances from the upper and lower Z-direction boundaries     #
# in a periodic structure. Top and bottom boundaries can have different fixed distances. This is particularly     #
# useful for simulating material interfaces, surface adsorption, shear simulations, and other scenarios where     #
# fixing crystal boundaries is needed, while allowing interior atoms to move freely.                              #
#                                                                                                                 #
# Date: 2024-06-25                                                                                                #
# Update: 2024-06-26 - Added Top and Bottom boundary classifications                                              #
# Update: 2024-06-27 - Added support for different Top and Bottom boundary distances                              #
##################################################################################################################

##################################################################################################################
#                                     START OF USER INPUT SECTION                                                 #
##################################################################################################################

# Basic configuration parameters
# Get the currently open document
my $doc = $Documents{"test.xsd"};
my $bottomBoundaryDistance = 3.0; # Distance from bottom boundary within which atoms will be fixed (default 3.0Å)
my $topBoundaryDistance = 3.0;    # Distance from top boundary within which atoms will be fixed (default 3.0Å)
my $fixDirections = "XYZ";        # Directions to fix: "X", "Y", "Z", "XY", "XZ", "YZ", or "XYZ"
my $createSets = "yes";           # Whether to create Sets containing fixed atoms: "yes" or "no"
my $fixedSetName = "Fixed_Atoms"; # Name for all fixed atoms set
my $topSetName = "Top";           # Name for upper boundary fixed atoms set
my $bottomSetName = "Bottom";     # Name for lower boundary fixed atoms set
my $logFileName = "fix_atoms_log.txt"; # Log file name

##################################################################################################################
#                                      END OF USER INPUT SECTION                                                  #
##################################################################################################################

# Create output log file
my $logFile = Documents->New($logFileName);

# Output welcome message to log
$logFile->Append("\n======================================================\n");
$logFile->Append("       Periodic Structure Boundary Atom Fixing Tool v1.3\n");
$logFile->Append("======================================================\n\n");
$logFile->Append("Using currently open document: " . $doc->Name . "\n");

# Verify document has periodic structure
if (!$doc->UnitCell) {
    $logFile->Append("Error: Current document has no periodic unit cell information. Please ensure you are using a periodic structure.\n");
    die "Error: Current document has no periodic unit cell information. Please ensure you are using a periodic structure.\n";
}

# Report script parameters
$logFile->Append("\nScript Configuration Parameters:\n");
$logFile->Append("- Bottom boundary distance: $bottomBoundaryDistance Å\n");
$logFile->Append("- Top boundary distance: $topBoundaryDistance Å\n");
$logFile->Append("- Fixed directions: $fixDirections\n");
$logFile->Append("- Create atom sets: $createSets\n");
if ($createSets eq "yes") {
    $logFile->Append("- All fixed atoms set name: $fixedSetName\n");
    $logFile->Append("- Top boundary atoms set name: $topSetName\n");
    $logFile->Append("- Bottom boundary atoms set name: $bottomSetName\n");
}

# Get cell parameters
my $minZ = 0;
my $maxZ = $doc->SymmetryDefinition->LengthC;

$logFile->Append("\nCell Parameters - Z direction length: $maxZ Å\n");

# Calculate Cartesian coordinate ranges
my $lowerBound = $minZ + $bottomBoundaryDistance;
my $upperBound = $maxZ - $topBoundaryDistance;

$logFile->Append("\nWill fix atoms within the following ranges:\n");
$logFile->Append("- Lower boundary region (Bottom): $minZ to $lowerBound Å ($bottomBoundaryDistance Å wide)\n");
$logFile->Append("- Upper boundary region (Top): $upperBound to $maxZ Å ($topBoundaryDistance Å wide)\n");

# Create atom collections to store atoms to be fixed
my @atomsToFix;     # All atoms to be fixed
my @topAtoms;       # Atoms in the upper boundary (Top)
my @bottomAtoms;    # Atoms in the lower boundary (Bottom)
my $totalAtoms = 0;
my $fixedCount = 0;
my $topCount = 0;
my $bottomCount = 0;

# Get all atoms
my $atoms = $doc->UnitCell->Atoms;
$totalAtoms = @$atoms;

# Iterate through all atoms to find those meeting the criteria
$logFile->Append("\nAnalyzing atom positions...\n");

foreach my $atom (@$atoms) {
    # Get atom's Cartesian coordinates
    my $atomZ = $atom->XYZ->Z;
    
    # Check if atom is within boundary range (Cartesian coordinates)
    if ($atomZ <= $lowerBound) {
        # Bottom boundary
        push @atomsToFix, $atom;
        push @bottomAtoms, $atom;
        $fixedCount++;
        $bottomCount++;
    } elsif ($atomZ >= $upperBound) {
        # Top boundary
        push @atomsToFix, $atom;
        push @topAtoms, $atom;
        $fixedCount++;
        $topCount++;
    }
}

# Exit if no atoms are found meeting the criteria
if ($fixedCount == 0) {
    $logFile->Append("\nWarning: No atoms found within the specified boundary range. Please check your boundary distance parameter or crystal structure.\n");
    $logFile->Save();
    exit;
}

$logFile->Append("\nFound $fixedCount atoms to be fixed (total atoms: $totalAtoms)\n");
$logFile->Append("- Top boundary atoms: $topCount\n");
$logFile->Append("- Bottom boundary atoms: $bottomCount\n");

# Create new Sets if requested
if ($createSets eq "yes") {
    # Check if Sets with the same names already exist, and remove them if so
    eval {
        if ($doc->UnitCell->Sets($fixedSetName)) {
            $doc->UnitCell->Sets($fixedSetName)->Delete;
            $logFile->Append("Removed existing '$fixedSetName' atom set\n");
        }
    };
    
    eval {
        if ($doc->UnitCell->Sets($topSetName)) {
            $doc->UnitCell->Sets($topSetName)->Delete;
            $logFile->Append("Removed existing '$topSetName' atom set\n");
        }
    };
    
    eval {
        if ($doc->UnitCell->Sets($bottomSetName)) {
            $doc->UnitCell->Sets($bottomSetName)->Delete;
            $logFile->Append("Removed existing '$bottomSetName' atom set\n");
        }
    };
    
    # Create new Sets and add atoms directly
    $doc->UnitCell->CreateSet($fixedSetName, \@atomsToFix);
    $doc->UnitCell->CreateSet($topSetName, \@topAtoms);
    $doc->UnitCell->CreateSet($bottomSetName, \@bottomAtoms);
    
    # Hide the sets from the display
    eval {
        $doc->UnitCell->Sets($fixedSetName)->IsHidden = 1;
        $doc->UnitCell->Sets($topSetName)->IsHidden = 1;
        $doc->UnitCell->Sets($bottomSetName)->IsHidden = 1;
        
        # 验证是否成功隐藏
        my $isFixedHidden = $doc->UnitCell->Sets($fixedSetName)->IsHidden ? "是" : "否";
        my $isTopHidden = $doc->UnitCell->Sets($topSetName)->IsHidden ? "是" : "否";
        my $isBottomHidden = $doc->UnitCell->Sets($bottomSetName)->IsHidden ? "是" : "否";
        
        $logFile->Append("验证隐藏状态 - $fixedSetName: $isFixedHidden, $topSetName: $isTopHidden, $bottomSetName: $isBottomHidden\n");
    };
    if ($@) {
        $logFile->Append("警告: 无法隐藏集合名称。错误: $@\n");
    } else {
        $logFile->Append("成功隐藏集合名称\n");
    }
    
    $logFile->Append("创建的原子集合 (已隐藏显示):\n");
    $logFile->Append("- '$fixedSetName' 包含 $fixedCount 个原子\n");
    $logFile->Append("- '$topSetName' 包含 $topCount 个原子\n");
    $logFile->Append("- '$bottomSetName' 包含 $bottomCount 个原子\n");
}

# Fix the selected atoms
$logFile->Append("\nFixing atoms...\n");

# Fix atoms directly based on creation method
if ($createSets eq "yes") {
    # If Sets were created, use atoms from the Set
    my $atomsToFixSet = $doc->UnitCell->Sets($fixedSetName)->Atoms;
    $atomsToFixSet->Fix($fixDirections);
    $logFile->Append("Successfully fixed $fixDirections directions for $fixedCount atoms\n");
    
    # Also log the top and bottom counts (these atoms are already fixed through the main set)
    $logFile->Append("- Top boundary atoms fixed: $topCount\n");
    $logFile->Append("- Bottom boundary atoms fixed: $bottomCount\n");
} else {
    # If no Sets were created, fix the atoms directly
    foreach my $atom (@atomsToFix) {
        $atom->Fix($fixDirections);
    }
    $logFile->Append("Successfully fixed $fixDirections directions for $fixedCount atoms\n");
}

# Save the document
$doc->Save;
$logFile->Append("Saved document with fixed atoms\n");

$logFile->Append("\n======================================================\n");
$logFile->Append("            Script Execution Completed\n");
$logFile->Append("======================================================\n");
$logFile->Append("Summary:\n");
$logFile->Append("- Total atoms: $totalAtoms\n");
$logFile->Append("- Fixed atoms: $fixedCount\n");
$logFile->Append("  - Top boundary atoms ($topBoundaryDistance Å): $topCount\n");
$logFile->Append("  - Bottom boundary atoms ($bottomBoundaryDistance Å): $bottomCount\n");
$logFile->Append("- Fixed ratio: " . sprintf("%.2f%%", ($fixedCount / $totalAtoms) * 100) . "\n");
$logFile->Append("======================================================\n");

# Save log file
$logFile->Save();

# Output completion information to console
print "Script execution completed, fixed $fixedCount atoms ($topCount top, $bottomCount bottom). Detailed log saved to $logFileName file.\n";
