"""
数据验证工具模块
提供输入数据的验证和清理功能
"""

import numpy as np
import os
from typing import Tuple, Optional, Union
from .constants import (
    SUPPORTED_EXTENSIONS, 
    MIN_FIT_POINTS, 
    MAX_ENERGY_RANGE,
    MIN_ENERGY_STEP,
    NUMERICAL_TOLERANCE
)

class DataValidator:
    """数据验证器类"""
    
    @staticmethod
    def validate_file_path(file_path: str) -> bool:
        """
        验证文件路径是否有效
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            bool: 文件是否存在且格式支持
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in SUPPORTED_EXTENSIONS:
            raise ValueError(f"不支持的文件格式: {file_ext}. 支持的格式: {SUPPORTED_EXTENSIONS}")
        
        return True
    
    @staticmethod
    def validate_energy_data(energy: np.ndarray) -> Tuple[bool, str]:
        """
        验证能量数据的有效性
        
        Args:
            energy (np.ndarray): 能量数组
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if len(energy) == 0:
            return False, "能量数据为空"
        
        if np.any(np.isnan(energy)) or np.any(np.isinf(energy)):
            return False, "能量数据包含NaN或无穷大值"
        
        if np.any(energy < 0):
            return False, "能量数据包含负值"
        
        if np.max(energy) > MAX_ENERGY_RANGE:
            return False, f"能量范围超出限制 ({MAX_ENERGY_RANGE} eV)"
        
        # 检查能量步长
        if len(energy) > 1:
            energy_diff = np.diff(energy)
            if np.any(energy_diff <= 0):
                return False, "能量数据不是单调递增的"
            
            min_step = np.min(energy_diff)
            if min_step < MIN_ENERGY_STEP:
                return False, f"能量步长过小 (< {MIN_ENERGY_STEP} eV)"
        
        return True, ""
    
    @staticmethod
    def validate_epsilon_data(epsilon_2: np.ndarray) -> Tuple[bool, str]:
        """
        验证介电函数虚部数据的有效性
        
        Args:
            epsilon_2 (np.ndarray): 介电函数虚部数组
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if len(epsilon_2) == 0:
            return False, "介电函数虚部数据为空"
        
        if np.any(np.isnan(epsilon_2)) or np.any(np.isinf(epsilon_2)):
            return False, "介电函数虚部数据包含NaN或无穷大值"
        
        # 检查是否所有值都为零或负值
        if np.all(epsilon_2 <= NUMERICAL_TOLERANCE):
            return False, "介电函数虚部数据全部为零或负值"
        
        return True, ""
    
    @staticmethod
    def validate_data_consistency(energy: np.ndarray, epsilon_2: np.ndarray) -> Tuple[bool, str]:
        """
        验证能量和介电函数数据的一致性
        
        Args:
            energy (np.ndarray): 能量数组
            epsilon_2 (np.ndarray): 介电函数虚部数组
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if len(energy) != len(epsilon_2):
            return False, f"能量数据长度({len(energy)})与介电函数数据长度({len(epsilon_2)})不匹配"
        
        if len(energy) < MIN_FIT_POINTS:
            return False, f"数据点数量({len(energy)})少于最小要求({MIN_FIT_POINTS})"
        
        return True, ""
    
    @staticmethod
    def validate_fit_parameters(fit_points: int, data_length: int) -> Tuple[bool, str]:
        """
        验证拟合参数的有效性
        
        Args:
            fit_points (int): 拟合点数
            data_length (int): 数据总长度
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if fit_points < MIN_FIT_POINTS:
            return False, f"拟合点数({fit_points})少于最小要求({MIN_FIT_POINTS})"
        
        if fit_points > data_length:
            return False, f"拟合点数({fit_points})超过数据总长度({data_length})"
        
        if fit_points > data_length // 2:
            return False, f"拟合点数({fit_points})过多，建议不超过数据总长度的一半"
        
        return True, ""
    
    @staticmethod
    def clean_data(energy: np.ndarray, epsilon_2: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        清理数据，移除无效值
        
        Args:
            energy (np.ndarray): 能量数组
            epsilon_2 (np.ndarray): 介电函数虚部数组
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: 清理后的(能量, 介电函数虚部)数组
        """
        # 创建有效数据的掩码
        valid_mask = (
            np.isfinite(energy) & 
            np.isfinite(epsilon_2) & 
            (energy >= 0) & 
            (epsilon_2 >= 0)
        )
        
        # 应用掩码
        clean_energy = energy[valid_mask]
        clean_epsilon_2 = epsilon_2[valid_mask]
        
        # 按能量排序
        sort_indices = np.argsort(clean_energy)
        clean_energy = clean_energy[sort_indices]
        clean_epsilon_2 = clean_epsilon_2[sort_indices]
        
        return clean_energy, clean_epsilon_2

def validate_all_data(energy: np.ndarray, epsilon_2: np.ndarray, 
                     fit_points: Optional[int] = None) -> Tuple[bool, str]:
    """
    综合验证所有数据
    
    Args:
        energy (np.ndarray): 能量数组
        epsilon_2 (np.ndarray): 介电函数虚部数组
        fit_points (Optional[int]): 拟合点数
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    validator = DataValidator()
    
    # 验证能量数据
    is_valid, error_msg = validator.validate_energy_data(energy)
    if not is_valid:
        return False, f"能量数据验证失败: {error_msg}"
    
    # 验证介电函数数据
    is_valid, error_msg = validator.validate_epsilon_data(epsilon_2)
    if not is_valid:
        return False, f"介电函数数据验证失败: {error_msg}"
    
    # 验证数据一致性
    is_valid, error_msg = validator.validate_data_consistency(energy, epsilon_2)
    if not is_valid:
        return False, f"数据一致性验证失败: {error_msg}"
    
    # 验证拟合参数（如果提供）
    if fit_points is not None:
        is_valid, error_msg = validator.validate_fit_parameters(fit_points, len(energy))
        if not is_valid:
            return False, f"拟合参数验证失败: {error_msg}"
    
    return True, "数据验证通过"
