use strict;
use Getopt::Long;
use MaterialsScript qw(:all);
##################################################################################################################
# perl                                                                                                            #
#                                                                                                                 #
# Author: IMATSOFT.DM                                                                                             #
# Version: 1.0                                                                                                    #
# Tested on: Materials Studio 2020                                                                                #
#                                                                                                                 #
# Required modules: Materials Visualizer, Forcite                                                                 #
#                                                                                                                 #
# Description:                                                                                                    #
# This script runs MD simulations on 'Layer_0.xsd' for multiple steps, saves trajectories, moves atoms in         #
# 'Target' set,                                                                                                   #
# and compiles results in 'final.xtd', using Forcite module.                                                      #        
#                                                                                                                 #
# Date: 2024-01-11                                                                                                #
##################################################################################################################

# Define variables with more descriptive names
my $baseDocumentName = 'Layer';
my $numberOfMoveSteps = 10;
my $moveDistance = -0.50;
my $finalTrajectoryDocument = Documents->New('final.xtd');
my $SetsName = "Target";

for (my $currentStep = 0; $currentStep <= $numberOfMoveSteps; ++$currentStep) {
    # Open the document
    my $currentDocument = $Documents{"${baseDocumentName}_${currentStep}.xsd"};
    
    # Run the Molecular Dynamics (MD) simulation
    my $mdSimulationResults = Modules->Forcite->Dynamics->Run(
        $currentDocument,
        Settings(
            Quality => "Medium",
            CurrentForcefield => 'COMPASSIII',
            AssignForcefieldTypes => 'No',
            ChargeAssignment => 'Use current',
            Ensemble3D => 'NVT',
            Temperature => 300,
            TimeStep => 1,
            Thermostat => 'Velocity Scale',
            Use3DPeriodicvdWAtomLongRangeCorrection => 'No',
            NumberOfSteps => 1000,
            TrajectoryFrequency => 1000,
            EnergyDeviation => 1e+024,
            InitialVelocities => 'Current',
            WriteForces => 'Yes',
            WriteLevel => 'Silent'
        )
    );
    
    # Create a new trajectory document and save it
    my $currentTrajectory = Documents->New("${baseDocumentName}_${currentStep}.xtd");
    $currentTrajectory->Trajectory->AppendFramesFrom($mdSimulationResults->Trajectory);
    $currentTrajectory->Save;
    
    # Create a new document and copy the structure of the last step
    $currentTrajectory->Trajectory->CurrentFrame = $currentTrajectory->Trajectory->EndFrame;
    my $nextDocument = Documents->New("${baseDocumentName}_".($currentStep + 1).".xsd");
    $nextDocument->CopyFrom($currentTrajectory);
    $nextDocument->Save;
    
    # Move the position of the atoms
    my $wallAtoms = $nextDocument->AsymmetricUnit->Sets($SetsName)->Atoms;
    $wallAtoms->Translate(Point(Z => $moveDistance));
    $nextDocument->Save;
    
    # Create a temporary document and copy from the next document
    my $tempDocument = Documents->New("Temp.xsd");
    $tempDocument->CopyFrom($nextDocument);
    # Unfix the atoms in the temporary document
    my $tempAtoms = $tempDocument->UnitCell->Sets($SetsName)->Atoms;
    $tempAtoms->Unfix("XYZ");
    # Append the last step's structure to the final trajectory
    $finalTrajectoryDocument->Trajectory->AppendFramesFrom($tempDocument);
    $finalTrajectoryDocument->Save;
    # Delete the temporary document
    $tempDocument->Delete;
    $mdSimulationResults->Trajectory->Delete;
    $currentDocument->Delete;
    
    # Delete unnecessary documents
    $Documents{"${baseDocumentName}_${currentStep} (2).xtd"}->Delete;
}