"""
电导率计算核心模块
实现从介电函数到光学导率和直流电导率的计算
"""

import numpy as np
from typing import Tuple, Optional, Dict, Any
from scipy import optimize
from ..utils.constants import PhysicalConstants, DEFAULT_FIT_POINTS, DEFAULT_ENERGY_THRESHOLD
from ..utils.validators import validate_all_data, DataValidator

class ConductivityCalculator:
    """电导率计算器类"""
    
    def __init__(self):
        self.constants = PhysicalConstants()
        self.validator = DataValidator()
        self.results = {}
    
    def calculate_optical_conductivity(self, energy: np.ndarray, epsilon_2: np.ndarray) -> <PERSON><PERSON>[np.ndarray, np.ndarray]:
        """
        计算光学导率 σ(ω) = ω·ε₂(ω)·ε₀
        
        Args:
            energy (np.ndarray): 能量数组 (eV)
            epsilon_2 (np.ndarray): 介电函数虚部数组
            
        Returns:
            Tuple[np.ndarray, np.ndarray]: (角频率(rad/s), 光学导率(S/m))
        """
        # 数据验证
        is_valid, error_msg = validate_all_data(energy, epsilon_2)
        if not is_valid:
            raise ValueError(f"数据验证失败: {error_msg}")
        
        # 清理数据
        clean_energy, clean_epsilon_2 = self.validator.clean_data(energy, epsilon_2)
        
        # 转换能量为角频率
        omega = self.constants.eV_to_rad_per_s(clean_energy)
        
        # 计算光学导率
        sigma_omega = self.constants.conductivity_formula(omega, clean_epsilon_2)
        
        # 存储结果
        self.results['energy'] = clean_energy
        self.results['omega'] = omega
        self.results['epsilon_2'] = clean_epsilon_2
        self.results['sigma_omega'] = sigma_omega
        
        return omega, sigma_omega
    
    def extrapolate_dc_conductivity(self, energy: np.ndarray, sigma_omega: np.ndarray, 
                                  fit_points: int = DEFAULT_FIT_POINTS,
                                  energy_threshold: float = DEFAULT_ENERGY_THRESHOLD,
                                  method: str = 'linear') -> Dict[str, Any]:
        """
        通过低频外推计算直流电导率
        
        Args:
            energy (np.ndarray): 能量数组 (eV)
            sigma_omega (np.ndarray): 光学导率数组 (S/m)
            fit_points (int): 用于拟合的低能点数
            energy_threshold (float): 能量阈值 (eV)
            method (str): 拟合方法 ('linear', 'polynomial', 'exponential')
            
        Returns:
            Dict[str, Any]: 包含直流电导率和拟合信息的字典
        """
        # 验证拟合参数
        is_valid, error_msg = self.validator.validate_fit_parameters(fit_points, len(energy))
        if not is_valid:
            raise ValueError(error_msg)
        
        # 选择低能数据点
        low_energy_mask = energy <= energy_threshold
        low_energy_indices = np.where(low_energy_mask)[0]
        
        if len(low_energy_indices) < fit_points:
            # 如果阈值内点数不足，使用前fit_points个点
            fit_indices = np.arange(min(fit_points, len(energy)))
        else:
            # 使用阈值内的前fit_points个点
            fit_indices = low_energy_indices[:fit_points]
        
        fit_energy = energy[fit_indices]
        fit_sigma = sigma_omega[fit_indices]
        
        # 执行拟合
        if method == 'linear':
            result = self._linear_extrapolation(fit_energy, fit_sigma)
        elif method == 'polynomial':
            result = self._polynomial_extrapolation(fit_energy, fit_sigma)
        elif method == 'exponential':
            result = self._exponential_extrapolation(fit_energy, fit_sigma)
        else:
            raise ValueError(f"不支持的拟合方法: {method}")
        
        # 添加拟合信息
        result.update({
            'fit_points': fit_points,
            'fit_energy_range': (fit_energy[0], fit_energy[-1]),
            'fit_indices': fit_indices,
            'method': method,
            'energy_threshold': energy_threshold
        })
        
        # 存储结果
        self.results['dc_conductivity'] = result
        
        return result
    
    def _linear_extrapolation(self, energy: np.ndarray, sigma: np.ndarray) -> Dict[str, Any]:
        """
        线性外推到零频率
        
        Args:
            energy (np.ndarray): 拟合用能量数组
            sigma (np.ndarray): 拟合用导率数组
            
        Returns:
            Dict[str, Any]: 拟合结果字典
        """
        # 线性拟合: σ = a * E + b
        coeffs = np.polyfit(energy, sigma, 1)
        slope, intercept = coeffs
        
        # 直流电导率为截距
        sigma_dc = intercept
        
        # 计算拟合质量
        sigma_fit = np.polyval(coeffs, energy)
        r_squared = self._calculate_r_squared(sigma, sigma_fit)
        
        # 计算拟合误差
        fit_error = np.std(sigma - sigma_fit)
        
        return {
            'sigma_dc': sigma_dc,
            'slope': slope,
            'intercept': intercept,
            'r_squared': r_squared,
            'fit_error': fit_error,
            'coefficients': coeffs,
            'fit_function': lambda x: np.polyval(coeffs, x)
        }
    
    def _polynomial_extrapolation(self, energy: np.ndarray, sigma: np.ndarray, degree: int = 2) -> Dict[str, Any]:
        """
        多项式外推到零频率
        
        Args:
            energy (np.ndarray): 拟合用能量数组
            sigma (np.ndarray): 拟合用导率数组
            degree (int): 多项式阶数
            
        Returns:
            Dict[str, Any]: 拟合结果字典
        """
        # 多项式拟合
        coeffs = np.polyfit(energy, sigma, degree)
        
        # 直流电导率为常数项
        sigma_dc = coeffs[-1]
        
        # 计算拟合质量
        sigma_fit = np.polyval(coeffs, energy)
        r_squared = self._calculate_r_squared(sigma, sigma_fit)
        
        # 计算拟合误差
        fit_error = np.std(sigma - sigma_fit)
        
        return {
            'sigma_dc': sigma_dc,
            'r_squared': r_squared,
            'fit_error': fit_error,
            'coefficients': coeffs,
            'degree': degree,
            'fit_function': lambda x: np.polyval(coeffs, x)
        }
    
    def _exponential_extrapolation(self, energy: np.ndarray, sigma: np.ndarray) -> Dict[str, Any]:
        """
        指数函数外推到零频率
        
        Args:
            energy (np.ndarray): 拟合用能量数组
            sigma (np.ndarray): 拟合用导率数组
            
        Returns:
            Dict[str, Any]: 拟合结果字典
        """
        # 指数拟合: σ = a * exp(b * E) + c
        def exp_func(x, a, b, c):
            return a * np.exp(b * x) + c
        
        try:
            # 初始猜测
            p0 = [sigma[0], -1.0, sigma[-1]]
            popt, pcov = optimize.curve_fit(exp_func, energy, sigma, p0=p0)
            
            a, b, c = popt
            sigma_dc = c  # 当E->0时的极限值
            
            # 计算拟合质量
            sigma_fit = exp_func(energy, *popt)
            r_squared = self._calculate_r_squared(sigma, sigma_fit)
            
            # 计算拟合误差
            fit_error = np.std(sigma - sigma_fit)
            
            return {
                'sigma_dc': sigma_dc,
                'r_squared': r_squared,
                'fit_error': fit_error,
                'coefficients': popt,
                'covariance': pcov,
                'fit_function': lambda x: exp_func(x, *popt)
            }
            
        except Exception as e:
            # 如果指数拟合失败，回退到线性拟合
            print(f"指数拟合失败，回退到线性拟合: {str(e)}")
            return self._linear_extrapolation(energy, sigma)
    
    def _calculate_r_squared(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """
        计算决定系数 R²
        
        Args:
            y_true (np.ndarray): 真实值
            y_pred (np.ndarray): 预测值
            
        Returns:
            float: R²值
        """
        ss_res = np.sum((y_true - y_pred) ** 2)
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        
        if ss_tot == 0:
            return 1.0 if ss_res == 0 else 0.0
        
        return 1 - (ss_res / ss_tot)
    
    def get_calculation_summary(self) -> Dict[str, Any]:
        """
        获取计算结果摘要
        
        Returns:
            Dict[str, Any]: 计算摘要字典
        """
        if not self.results:
            return {"error": "尚未进行计算"}
        
        summary = {
            'data_points': len(self.results.get('energy', [])),
            'energy_range': (
                np.min(self.results.get('energy', [])),
                np.max(self.results.get('energy', []))
            ) if 'energy' in self.results else None,
            'sigma_range': (
                np.min(self.results.get('sigma_omega', [])),
                np.max(self.results.get('sigma_omega', []))
            ) if 'sigma_omega' in self.results else None
        }
        
        if 'dc_conductivity' in self.results:
            dc_result = self.results['dc_conductivity']
            summary.update({
                'sigma_dc': dc_result.get('sigma_dc'),
                'fit_method': dc_result.get('method'),
                'fit_quality': dc_result.get('r_squared'),
                'fit_error': dc_result.get('fit_error')
            })
        
        return summary
