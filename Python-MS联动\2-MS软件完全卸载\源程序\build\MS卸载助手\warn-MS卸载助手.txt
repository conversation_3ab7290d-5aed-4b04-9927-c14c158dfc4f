
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), psutil (optional), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional), posixpath (optional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), pkg_resources._vendor.packaging._manylinux (delayed, optional)
missing module named collections.Sequence - imported by collections (conditional), pyautogui (conditional), setuptools._vendor.ordered_set (optional)
missing module named collections.MutableSet - imported by collections (optional), setuptools._vendor.ordered_set (optional)
missing module named 'setuptools.extern.jaraco' - imported by setuptools._reqs (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level), setuptools._vendor.jaraco.text (top-level)
missing module named setuptools.extern.importlib_resources - imported by setuptools.extern (conditional), setuptools._importlib (conditional), setuptools._vendor.jaraco.text (optional)
missing module named setuptools.extern.tomli - imported by setuptools.extern (delayed), setuptools.config.pyprojecttoml (delayed)
missing module named setuptools.extern.importlib_metadata - imported by setuptools.extern (conditional), setuptools._importlib (conditional)
missing module named setuptools.extern.packaging - imported by setuptools.extern (top-level), setuptools._normalization (top-level), setuptools.command.egg_info (top-level)
missing module named 'setuptools.extern.more_itertools' - imported by setuptools.dist (top-level), setuptools._itertools (top-level), setuptools._entry_points (top-level), setuptools.config.expand (delayed), setuptools.config.pyprojecttoml (delayed), setuptools.msvc (top-level), setuptools._vendor.jaraco.functools (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named fcntl - imported by subprocess (optional), xmlrpc.server (optional)
missing module named termios - imported by getpass (optional), tty (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by site (delayed, optional), rlcompleter (optional), cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional)
missing module named jnius - imported by pkg_resources._vendor.platformdirs.android (delayed, optional), platformdirs.android (delayed, optional)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.platformdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named imp - imported by pkg_resources (optional), cffi.verifier (conditional), cffi._imp_emulation (optional)
missing module named 'setuptools.extern.packaging.requirements' - imported by setuptools._reqs (top-level), setuptools._core_metadata (top-level), setuptools.config.setupcfg (top-level), setuptools.command._requirestxt (top-level)
missing module named 'setuptools.extern.packaging.utils' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.tags' - imported by setuptools.wheel (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named importlib_metadata - imported by setuptools._importlib (delayed, optional)
missing module named 'setuptools.extern.packaging.version' - imported by setuptools._core_metadata (top-level), setuptools.depends (top-level), setuptools.dist (top-level), setuptools.config.setupcfg (top-level), setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.specifiers' - imported by setuptools.dist (top-level), setuptools.config.setupcfg (top-level), setuptools.config._apply_pyprojecttoml (delayed)
missing module named 'setuptools.extern.packaging.markers' - imported by setuptools._core_metadata (top-level), setuptools.dist (top-level), setuptools.config.setupcfg (top-level)
missing module named 'setuptools.extern.ordered_set' - imported by setuptools.dist (top-level)
missing module named pyimod02_importers - imported by C:\Users\<USER>\miniconda3\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed), C:\Users\<USER>\miniconda3\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional)
missing module named StringIO - imported by six (conditional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named PIL._imagingcms - imported by PIL (optional), PIL.ImageCms (optional)
excluded module named numpy - imported by PIL.Image (delayed, conditional, optional), pyscreeze (optional), comtypes._npsupport (delayed, conditional)
missing module named _dummy_thread - imported by cffi.lock (conditional, optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named PIL._imagingagg - imported by PIL (delayed, conditional, optional), PIL.ImageDraw (delayed, conditional, optional)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
missing module named 'Xlib.ext' - imported by pyautogui._pyautogui_x11 (top-level), pywinauto.linux.keyboard (top-level), pywinauto.mouse (conditional)
missing module named Xlib - imported by mouseinfo (conditional), pyautogui._pyautogui_x11 (top-level), pywinauto.linux.keyboard (top-level), pywinauto.mouse (conditional)
missing module named 'Xlib.display' - imported by pyautogui._pyautogui_x11 (top-level), pywinauto.linux.keyboard (top-level), pywinauto.mouse (conditional)
missing module named logger - imported by pywinauto.actionlogger (optional)
missing module named ctypes._CArgObject - imported by ctypes (conditional), comtypes.automation (conditional), comtypes._comobject (conditional)
missing module named ctypes._FuncPointer - imported by ctypes (conditional), comtypes._vtbl (conditional)
missing module named 'numpy.ctypeslib' - imported by comtypes._npsupport (delayed, optional)
missing module named ctypes._CData - imported by ctypes (conditional), comtypes (conditional)
missing module named extra_tests - imported by pywinauto.tests (delayed, optional)
missing module named 'Xlib.XK' - imported by pyautogui._pyautogui_x11 (top-level), pywinauto.linux.keyboard (top-level)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named AppKit - imported by pyperclip (delayed, conditional, optional), pyautogui._pyautogui_osx (top-level)
missing module named Quartz - imported by pygetwindow._pygetwindow_macos (top-level), pyautogui._pyautogui_osx (optional)
missing module named Tkinter - imported by pymsgbox (conditional, optional), mouseinfo (conditional, optional)
missing module named 'rubicon.objc' - imported by mouseinfo (conditional)
missing module named rubicon - imported by mouseinfo (conditional)
missing module named Foundation - imported by pyperclip (delayed, conditional, optional)
missing module named qtpy - imported by pyperclip (delayed, conditional, optional)
missing module named cv2 - imported by pyscreeze (optional)
