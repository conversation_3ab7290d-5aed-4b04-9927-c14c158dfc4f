##################################################################################################################
# perl                                                                                                            #
#                                                                                                                 #
# Author: IMATSOFT.DM                                                                                             #
# Version: 2.0                                                                                                    #
# Tested on: Materials Studio 2020                                                                                #
#                                                                                                                 #
# Required modules: Materials Visualizer, Forcite                                                                 #
#                                                                                                                 #
# Description:                                                                                                    #
# This script calculates the interaction energy between two defined sets of atoms (SetA and SetB)                 #
# using the COMPASSIII force field. For each frame in the trajectory, the script computes the total potential,    #
# van der Waals, and electrostatic energies for both the full system and each set of atoms separately. It then    #
# calculates the interaction energy as the difference between the total energy and the sum of energies of the     #
# individual sets. The results are saved in an STD file.                                                          #
#                                                                                                                 #
# Date: 2023-03-27                                                                                                #
##################################################################################################################

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);

#################################################################################################################
#                                         BEGIN USER INPUT                                                      #
#################################################################################################################

# Load the structure document
my $doc = Documents->ActiveDocument;
my %Args;
GetOptions(\%Args, "SetA=s", "SetB=s", "Forcefield=s",
"Quilty=s", "Strat_trj=i", "End_trj=i", "VDW=s", "Elec=s", "SaveStructures=s");

# Define the atom sets for energy calculations
my $setA = $Args{SetA};
my $setB = $Args{SetB};
my $Forcefield = $Args{Forcefield};
my $Quilty = $Args{Quilty};
my $Strat_trj = $Args{Strat_trj};
my $end_trj = $Args{End_trj};
my $VDW = $Args{VDW};
my $Elec = $Args{Elec};
my $SaveStructures = $Args{SaveStructures} || "No"; # Default to "No" if not provided

#################################################################################################################
#                                         END USER INPUT                                                        #
#################################################################################################################

# Create an output table to store the calculated energies
my $table = Documents->New("Eint_TwoPart.std");
my $activesheet = $table->ActiveSheet;

# Set the column headings for the output table
$activesheet->ColumnHeading(0) = "Time_ps";
$activesheet->ColumnHeading(1) = "Total_Structure";
$activesheet->ColumnHeading(2) = "E_Total_Kcal/mol";
$activesheet->ColumnHeading(3) = "E_Total_VDW_Kcal/mol";
$activesheet->ColumnHeading(4) = "E_Total_Elec_Kcal/mol";
$activesheet->ColumnHeading(5) = "SetA_Structure";
$activesheet->ColumnHeading(6) = "E_SetA_Kcal/mol";
$activesheet->ColumnHeading(7) = "E_SetA_VDW_Kcal/mol";
$activesheet->ColumnHeading(8) = "E_SetA_Elec_Kcal/mol";
$activesheet->ColumnHeading(9) = "SetB_Structure";
$activesheet->ColumnHeading(10) = "E_SetB_Kcal/mol";
$activesheet->ColumnHeading(11) = "E_SetB_VDW_Kcal/mol";
$activesheet->ColumnHeading(12) = "E_SetB_Elec_Kcal/mol";
$activesheet->ColumnHeading(13) = "Eint_Pot_Kcal/mol";
$activesheet->ColumnHeading(14) = "Eint_Vdw_Kcal/mol";
$activesheet->ColumnHeading(15) = "Eint_Elec_Kcal/mol";

# Retrieve the trajectory object from the document
my $trajectory = $doc->Trajectory;

# Loop through each frame in the trajectory
for (my $i = $Strat_trj; $i <= $end_trj; ++$i) {
    $trajectory->CurrentFrame = $i;
    
    # Calculate the potential energy for the entire system
    my $temp_All = Documents->New("temp_all.xsd");
    $temp_All->CopyFrom($doc);
    
    my $results_All = Modules->Forcite->Energy->Run($temp_All, Settings(
        Quality => $Quilty,
        '****************************' => $VDW,
        '3DPeriodicElectrostaticSummationMethod' => $Elec,
        CurrentForcefield => $Forcefield,
        AssignForcefieldTypes => 'No',
        ChargeAssignment => 'Use current'
    ));

    my $EPot_All = $temp_All->PotentialEnergy;
    my $EVdw_All = $temp_All->VanDerWaalsEnergy;
    my $Elec_All = $temp_All->ElectrostaticEnergy;
    
    # Calculate the potential energy for SetA
    my $temp_SetA = Documents->New("temp_SetA.xsd");
    $temp_SetA->CopyFrom($doc);
    $temp_SetA->UnitCell->Sets($setB)->Atoms->Delete;
    
    my $results_SetA = Modules->Forcite->Energy->Run($temp_SetA, Settings(
        Quality => $Quilty,
        '****************************' => $VDW,
        '3DPeriodicElectrostaticSummationMethod' => $Elec,
        CurrentForcefield => $Forcefield,
        AssignForcefieldTypes => 'No',
        ChargeAssignment => 'Use current'
    ));

    my $EPot_SetA = $temp_SetA->PotentialEnergy;
    my $EVdw_SetA = $temp_SetA->VanDerWaalsEnergy;
    my $EElec_SetA = $temp_SetA->ElectrostaticEnergy;
    
    # Calculate the potential energy for SetB
    my $temp_SetB = Documents->New("temp_SetB.xsd");
    $temp_SetB->CopyFrom($doc);
    $temp_SetB->UnitCell->Sets($setA)->Atoms->Delete;
    
    my $results_SetB = Modules->Forcite->Energy->Run($temp_SetB, Settings(
        Quality => $Quilty,
        '****************************' => $VDW,
        '3DPeriodicElectrostaticSummationMethod' => $Elec,
        CurrentForcefield => $Forcefield,
        AssignForcefieldTypes => 'No',
        ChargeAssignment => 'Use current'
    ));

    my $EPot_SetB = $temp_SetB->PotentialEnergy;
    my $EVdw_SetB = $temp_SetB->VanDerWaalsEnergy;
    my $EElec_SetB = $temp_SetB->ElectrostaticEnergy;
    
    # Calculate the interaction energy
    my $Eint_Potential = $EPot_All - ($EPot_SetA + $EPot_SetB);
    my $Eint_Vdw = $EVdw_All - ($EVdw_SetA + $EVdw_SetB);
    my $Eint_Elec = $Elec_All - ($EElec_SetA + $EElec_SetB);
    
    # Output the results to the table
    $activesheet->Cell($i - 1, 0) = $trajectory->FrameTime;
    
    # Save structures to table only if SaveStructures is set to "Yes"
    if ($SaveStructures eq "Yes") {
        $activesheet->Cell($i - 1, 1) = $temp_All;
        $activesheet->Cell($i - 1, 5) = $temp_SetA;
        $activesheet->Cell($i - 1, 9) = $temp_SetB;
    }
    
    # Always save energy values
    $activesheet->Cell($i - 1, 2) = $EPot_All;
    $activesheet->Cell($i - 1, 3) = $EVdw_All;
    $activesheet->Cell($i - 1, 4) = $Elec_All;
    $activesheet->Cell($i - 1, 6) = $EPot_SetA;
    $activesheet->Cell($i - 1, 7) = $EVdw_SetA;
    $activesheet->Cell($i - 1, 8) = $EElec_SetA;
    $activesheet->Cell($i - 1, 10) = $EPot_SetB;
    $activesheet->Cell($i - 1, 11) = $EVdw_SetB;
    $activesheet->Cell($i - 1, 12) = $EElec_SetB;
    $activesheet->Cell($i - 1, 13) = $Eint_Potential;
    $activesheet->Cell($i - 1, 14) = $Eint_Vdw;
    $activesheet->Cell($i - 1, 15) = $Eint_Elec;
    
    # Delete the temporary structures
    $temp_All->Delete;
    $temp_SetA->Delete;
    $temp_SetB->Delete;
}

# Save the output table
$table->Save();