#!/usr/bin/env python3
"""
CASTEP电导率计算器测试脚本
使用现有的介电数据进行测试
"""

import sys
import os
import numpy as np

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from castep_conductivity_calculator import CastepConductivityApp

def test_with_sample_data():
    """使用示例数据测试计算器"""
    print("CASTEP电导率计算器测试")
    print("=" * 50)
    
    # 检查是否存在介电数据文件
    data_file = "介电数据.md"
    if not os.path.exists(data_file):
        print(f"错误: 找不到数据文件 {data_file}")
        return False
    
    # 创建应用程序实例
    app = CastepConductivityApp()
    
    try:
        # 测试数据加载
        print("1. 测试数据加载...")
        if not app.load_data(data_file):
            print("   ❌ 数据加载失败")
            return False
        print("   ✅ 数据加载成功")
        
        # 测试光学导率计算
        print("2. 测试光学导率计算...")
        if not app.calculate_conductivity():
            print("   ❌ 光学导率计算失败")
            return False
        print("   ✅ 光学导率计算成功")
        
        # 测试直流电导率计算
        print("3. 测试直流电导率计算...")
        if not app.calculate_dc_conductivity(fit_points=5, method='linear'):
            print("   ❌ 直流电导率计算失败")
            return False
        print("   ✅ 直流电导率计算成功")
        
        # 测试结果导出
        print("4. 测试结果导出...")
        if not app.export_results("test_results.csv", export_all=True):
            print("   ❌ 结果导出失败")
            return False
        print("   ✅ 结果导出成功")
        
        # 打印摘要
        app.print_summary()
        
        print("\n🎉 所有测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        return False

def create_sample_epsilon_file():
    """创建示例.epsilon文件用于测试"""
    print("创建示例.epsilon文件...")
    
    # 生成示例数据
    energy = np.linspace(0.01, 10.0, 200)
    epsilon_1 = 10.0 + 2.0 * np.exp(-energy/2.0) * np.cos(energy)
    epsilon_2 = 0.1 * energy * np.exp(-energy/3.0) + 0.01
    
    # 写入文件
    with open("sample.epsilon", "w") as f:
        f.write("# Sample CASTEP epsilon file for testing\n")
        f.write("# Energy(eV)  Epsilon_1  Epsilon_2\n")
        for i in range(len(energy)):
            f.write(f"{energy[i]:.6f}  {epsilon_1[i]:.6f}  {epsilon_2[i]:.6f}\n")
    
    print("✅ 示例文件 sample.epsilon 已创建")
    return "sample.epsilon"

def test_with_generated_data():
    """使用生成的示例数据测试"""
    print("使用生成的示例数据测试")
    print("=" * 50)
    
    # 创建示例文件
    sample_file = create_sample_epsilon_file()
    
    # 创建应用程序实例
    app = CastepConductivityApp()
    
    try:
        # 测试完整流程
        print("1. 加载示例数据...")
        if not app.load_data(sample_file):
            return False
        
        print("2. 计算光学导率...")
        if not app.calculate_conductivity():
            return False
        
        print("3. 计算直流电导率...")
        if not app.calculate_dc_conductivity():
            return False
        
        print("4. 导出结果...")
        if not app.export_results("sample_results.csv"):
            return False
        
        # 打印结果
        app.print_summary()
        
        print("\n🎉 示例数据测试成功!")
        
        # 清理临时文件
        if os.path.exists(sample_file):
            os.remove(sample_file)
            print(f"已清理临时文件: {sample_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("CASTEP电导率计算器测试套件")
    print("=" * 60)
    
    # 首先尝试使用现有的介电数据文件
    if os.path.exists("介电数据.md"):
        print("发现现有介电数据文件，开始测试...")
        if test_with_sample_data():
            print("\n✅ 使用现有数据的测试完成")
        else:
            print("\n❌ 使用现有数据的测试失败")
    else:
        print("未找到现有介电数据文件")
    
    print("\n" + "-" * 60)
    
    # 使用生成的示例数据测试
    print("使用生成的示例数据测试...")
    if test_with_generated_data():
        print("\n✅ 使用生成数据的测试完成")
    else:
        print("\n❌ 使用生成数据的测试失败")
    
    print("\n" + "=" * 60)
    print("测试套件运行完成")

if __name__ == "__main__":
    main()
