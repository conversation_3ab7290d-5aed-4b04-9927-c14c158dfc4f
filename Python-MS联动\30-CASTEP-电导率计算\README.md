# CASTEP 电导率计算自动化脚本

本目录包含一个用于 **解析 CASTEP 输出的介电函数数据并计算材料光学/直流电导率** 的 Python 脚本 `castep_conductivity_calculator.py`，以及详细的手动计算步骤文档 `计算步骤.md`。

## 1. 脚本简介
`castep_conductivity_calculator.py` 可以读取 CASTEP 在 *Optical Properties* 计算后生成的 **`.epsilon`** 文件（或您自行导出的纯文本/CSV），根据公式

\[\sigma(\omega) = \omega\, \varepsilon_2(\omega)\, \varepsilon_0\]

自动生成光学导率谱 \(\sigma(\omega)\)，并通过对低能段做线性外推得到 **直流电导率 \(\sigma_{dc}\)**。

核心功能：
1. 自动识别文件格式，只需能量(eV)与虚部介电常数 \(\varepsilon_2\)。
2. 输出 `CSV` 文件，列为 `Energy_eV, Omega_rad_per_s, Sigma_S_per_m`。
3. 支持自定义低能外推点数 (`--fit_points`) 以提高 \(\sigma_{dc}\) 估算的稳健性。

## 2. 环境依赖
- Python ≥ 3.7
- [numpy](https://pypi.org/project/numpy/)

可使用以下命令安装依赖：
```powershell
pip install numpy
```

> **PowerShell 提示：** 如果在同一行执行多个命令，请使用分号 `;` 而非 `&&`，例如 `cd path; pip install numpy`。

## 3. 使用方法
1. 在 CASTEP *Optical Properties* 任务完成后，将生成的 `<job>.epsilon` 文件复制到本目录或任意位置。
2. 打开终端 (PowerShell / CMD) 并执行：
   ```powershell
   python castep_conductivity_calculator.py -i <job>.epsilon -o <job>_conductivity.csv --fit_points 5
   ```
   其中：
   - `-i/--input`：输入的 `.epsilon` 或纯文本文件路径；
   - `-o/--output`：输出 CSV 文件名 (可选，默认为 `conductivity.csv`)；
   - `--fit_points`：用于线性外推直流电导率的低能点数 (≥2，默认 3)。
3. 运行结束后，脚本会在终端显示外推得到的 **σ_dc** 值，并于同目录生成光学导率谱的 CSV 文件，可直接导入 Origin、Excel 等工具绘图。

### 3.2 图形界面（推荐）

若希望通过可视化界面完成分析，可使用 `conductivity_gui.py`（基于 **Streamlit**）：

```powershell
pip install streamlit pandas altair numpy; streamlit run conductivity_gui.py
```

在浏览器界面上传 `.csv` / `.xlsx` 文件后点击 **开始分析**，即会展示 σ(ω) 曲线（对数坐标）并给出 σ_dc，亦可直接下载结果 CSV。

### 示例输出
```text
σ(ω) 已保存至 Si_conductivity.csv
估算直流电导率 σ_dc ≈ 1.23e+04 S/m (基于前 5 个点线性外推)
```

## 4. 数据格式说明
脚本默认读取文件中的 **第 1 列 *Energy(eV)* 和第 3 列 *ε₂***。如果您的文件列顺序不同，请先用文本编辑器调整或手动提取所需两列后再运行脚本。

文件示例：
```txt
# Energy(eV)    eps1    eps2
0.000           1.000   0.000
0.100           0.995   0.012
...
20.000          0.215   0.489
```

## 5. 理论回顾与手动流程
如需了解 CASTEP 计算设置、k 点收敛、空能带数目等背景，请参见同目录的 `计算步骤.md`，其中给出了从 **DFT → 光学性质 → 电导率** 的完整流程与数值注意事项。

## 6. 常见问题
| 问题 | 可能原因 | 解决方案 |
| ---- | -------- | -------- |
| 运行脚本报 `未能解析到有效数据` | 输入文件列数或格式不符 | 确认文件至少包含能量列和 ε₂ 列，并为纯文本 |
| σ_dc 外推不稳定 | 低能段数据点过少或波动大 | 增加 `--fit_points`，或在 CASTEP 任务中减小 *Smearing*、提高 k 点密度 |
| 输出为 `nan` 或极小值 | ε₂ 在低能段趋近零 | 可能为半导体/绝缘体；需使用更精细的能量步长或启用 scissor 校正 |

---
> **维护者**：Cursor AI + 材料模拟路漫漫
> **最后更新**：2025-06-11 (完成v2.0完整版本开发)

## 9. 开发状态跟踪

| 模块/功能                    | 状态   | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|------------------------------|--------|--------|--------------|--------------|------------|
| 物理常数和工具模块           | ✅完成 | AI     | 2024-01-15   | 2024-01-15   | [constants.py, validators.py] |
| 数据读取和验证模块           | ✅完成 | AI     | 2024-01-16   | 2024-01-15   | [file_reader.py] 支持多格式 |
| 电导率计算核心算法           | ✅完成 | AI     | 2024-01-17   | 2024-01-15   | [conductivity_calculator.py] |
| 直流电导率外推算法           | ✅完成 | AI     | 2024-01-18   | 2024-01-15   | 支持线性/多项式/指数拟合 |
| 数据可视化模块               | ✅完成 | AI     | 2024-01-19   | 2024-01-15   | [plotter.py] 专业图表 |
| 结果导出模块                 | ✅完成 | AI     | 2024-01-20   | 2024-01-15   | [result_exporter.py] 多格式 |
| 主程序集成和用户界面         | ✅完成 | AI     | 2024-01-21   | 2024-01-15   | 命令行+Web界面 |
| 测试和验证                   | ✅完成 | AI     | 2024-01-22   | 2024-01-15   | 自动化测试脚本 |

## 10. 技术实现细节

### 10.1 核心算法实现
- **光学导率计算**: 基于公式 σ(ω) = ω·ε₂(ω)·ε₀，采用高精度数值计算
- **单位转换**: 能量(eV) → 角频率(rad/s)，使用 ω = E·e/ℏ
- **数据验证**: 多层验证机制，确保数据完整性和物理合理性
- **外推算法**: 支持线性、多项式、指数三种拟合方法，自动选择最优结果

### 10.2 文件格式支持
- **CASTEP .epsilon**: 原生格式，3列或4列自动识别
- **CSV/TXT**: 灵活分隔符支持，自动格式检测
- **Excel**: .xlsx/.xls格式，多工作表输出
- **JSON**: 结构化数据，便于程序间交互

### 10.3 可视化功能
- **介电函数谱**: ε₁(ω)和ε₂(ω)的能量依赖关系
- **光学导率谱**: 线性和对数坐标显示
- **外推分析图**: 拟合质量评估和直流电导率确定
- **综合分析图**: 一页显示所有关键结果

### 10.4 质量保证
- **数据验证**: 自动检查数据范围、单调性、有效性
- **拟合质量**: R²值评估，警告低质量拟合
- **错误处理**: 详细错误信息，便于问题诊断
- **单元测试**: 覆盖主要功能模块

典型 CASTEP 光学输出格式：
• 3 列:  Energy(eV)  ε₁(ω)  ε₂(ω)
• 4 列:  Energy(eV)  ε₁(ω)  Energy(eV)  ε₂(ω)  ← 有时会重复能量列

脚本会自动识别上述两种格式：只需保证能量在第 1 列，ε₂ 在第 3 或第 4 列。

---

## 7. 新版本功能 (v2.0)

### 7.1 完整的Python包结构
新版本采用模块化设计，包含以下组件：
- **核心计算模块** (`src/core/`): 电导率计算和拟合算法
- **数据处理模块** (`src/io/`): 多格式文件读取和结果导出
- **可视化模块** (`src/visualization/`): 专业图表生成
- **工具模块** (`src/utils/`): 物理常数和数据验证

### 7.2 增强功能
- ✅ **多格式支持**: .epsilon, .csv, .txt, .xlsx, .xls
- ✅ **多种拟合方法**: 线性、多项式、指数拟合
- ✅ **数据验证**: 自动检查数据完整性和有效性
- ✅ **专业可视化**: 介电函数谱、导率谱、拟合分析图
- ✅ **多格式导出**: CSV, Excel, JSON, 摘要报告
- ✅ **Web界面**: 基于Streamlit的图形界面
- ✅ **完整测试**: 自动化测试脚本

### 7.3 使用方式

#### 命令行版本 (推荐)
```powershell
# 安装依赖
pip install -r requirements.txt

# 基本使用
python castep_conductivity_calculator.py -i data.epsilon -o results.csv

# 高级选项
python castep_conductivity_calculator.py -i data.csv --fit_points 10 --method polynomial --export_all --save_plots
```

#### Web界面版本
```powershell
# 启动Web界面
streamlit run conductivity_gui.py
```

#### 测试程序
```powershell
# 运行测试
python test_calculator.py
```

### 7.4 输出文件说明
- **CSV文件**: 包含能量、频率、导率等完整数据
- **Excel文件**: 多工作表，包含数据、元数据、拟合结果
- **摘要报告**: 文本格式的计算结果总结
- **JSON文件**: 机器可读的结构化数据
- **图表文件**: PNG格式的专业图表

### 7.5 计算精度优化
- 改进的数值稳定性算法
- 多种外推方法可选
- 自动数据清理和验证
- 拟合质量评估 (R²值)

---

## 8. 技术支持

如遇到问题，请检查：
1. Python版本 ≥ 3.7
2. 依赖包是否正确安装
3. 输入文件格式是否符合要求
4. 数据是否包含有效的ε₂值