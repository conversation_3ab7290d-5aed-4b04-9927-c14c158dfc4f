#!perl

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);

# Author: Luminary
# Current version: 2.0

# Update log 

# 2020-12-25 Version: 1.0: The first version released
# 2022-06-13 Version: 2.0: The V2.0 released




####################################################################################
# Begin user editable settings

my $xtdDoc = "PE";	# Name of the trajectory

my $startFrame = 1;		# Start frame in the trajectory
my $endFrame = 501;		# Final frame in the trajectory
my $frameStepSize = 1;		# Step size between frames



# End user editable settings
####################################################################################


# Create a study table to hold the results

my $study = Documents->New("$xtdDoc"."_FFV.std");
$study->ColumnHeading(0) = "Frame No.";
$study->ColumnHeading(1) = "Actualmodel";
$study->ColumnHeading(2) = "Total Occupied Volume (A^3)";
$study->ColumnHeading(3) = "Total surface Area (A^2)";
$study->ColumnHeading(4) = "Total Volume (A^3)";
$study->ColumnHeading(5) = "FFV (%)";


# Load the trajectory document
my $doc = $Documents{"$xtdDoc.xtd"};

# Go through the frames, make a copy, calculate the field and put the data in the study table


my $numFrames = $endFrame;
for (my $Counter = $startFrame; $Counter <= $numFrames; $Counter+=$frameStepSize) {

	# Create a copy of the frame
	
	my $tempDoc = Documents->New("$xtdDoc"."_$Counter.xsd");
	$doc->Trajectory->CurrentFrame = $Counter;
	my $TotalVolume =$doc->SymmetrySystem->Volume;
	

        my $fieldConnolly = Tools->AtomVolumesSurfaces->Connolly->Calculate($doc, Settings(
	GridInterval => 0.4, 
	ConnollyRadius => 1.0000, 
	VDWScaleFactor => 1.0000));
        $fieldConnolly->Style = "None";
        my $isosurfaceConnolly = $fieldConnolly->CreateIsosurface([
	IsoValue => 0.0, 
	HasFlippedNormals  => "No"]);
	
	
	$tempDoc->CopyFrom($doc);
	
	my $OccVolume = $isosurfaceConnolly->EnclosedVolume;
	my $freesurface = $isosurfaceConnolly->SurfaceArea;
	
	$study->Cell($Counter-1, 0) = $Counter;
	$study->Cell($Counter-1, 1) = $tempDoc;
	$study->Cell($Counter-1, 2) = $OccVolume;
	$study->Cell($Counter-1, 3) = $freesurface;
	$study->Cell($Counter-1, 4) = $TotalVolume;
	my $FFV=100-($OccVolume/$TotalVolume*100);
	$study->Cell($Counter-1, 5) = $FFV;
	
	
	
	
	
	$tempDoc->Discard;
	
}
	
	
	
