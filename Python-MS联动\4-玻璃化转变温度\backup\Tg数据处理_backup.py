﻿import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.ticker import MaxNLocator
from matplotlib import style
from scipy.signal import savgol_filter
from scipy.optimize import curve_fit
import pandas as pd
import os
import seaborn as sns
import tkinter as tk
from tkinter import filedialog, ttk, messagebox, scrolledtext
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.patches import Circle
import sys

# 璁剧疆鍏ㄥ眬缁樺浘鏍峰紡
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_context("notebook", font_scale=1.1)
plt.rcParams['font.sans-serif'] = ['SimHei']  # 璁剧疆涓枃瀛椾綋
plt.rcParams['axes.unicode_minus'] = False    # 瑙ｅ喅璐熷彿鏄剧ず闂

# 淇瀛椾綋闂 - 鏇挎崲涓婃爣鲁
def format_volume_unit(with_superscript=False):
    """鏍规嵁瀛椾綋鏀寔鎯呭喌杩斿洖浣撶Н鍗曚綅"""
    if with_superscript:
        return "cm鲁/g"
    else:
        return "cm3/g"

# 璁剧疆鏄惁浣跨敤涓婃爣
USE_SUPERSCRIPT = False
VOLUME_UNIT = format_volume_unit(USE_SUPERSCRIPT)
DENSITY_UNIT = "g/cm3" if not USE_SUPERSCRIPT else "g/cm鲁"

# 鍙嫋鍔ㄧ殑Tg鐐圭被
class DraggableTg:
    def __init__(self, ax, x, y, temperatures, densities, on_update_callback=None, y_min=None, y_max=None):
        self.ax = ax
        self.temperatures = temperatures
        self.densities = densities
        self.x = x
        self.y = y
        self.y_min = y_min
        self.y_max = y_max
        
        # 涓嶅啀鍒涘缓绾㈣壊Tg鐐癸紝鍙娇鐢ㄥ瀭鐩磋櫄绾?        # 浣跨敤铏氱嚎鏍囪Tg浣嶇疆
        min_temp, max_temp = min(temperatures), max(temperatures)
        self.tg_line = self.ax.axvline(x=x, color='black', linestyle='--', linewidth=1.0, alpha=0.7, zorder=4)
        
        # 鏂囨湰鏍囩
        self.text = ax.text(x+5, min(densities) + 0.9*(max(densities)-min(densities)),
                            f'Tg = {x:.0f}K', fontsize=12, color='darkred',
                            bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5'))
        
        self.press = None
        self.background = None
        self.connect()
        self.on_update_callback = on_update_callback
        
        # 淇濆瓨楂樻俯鍜屼綆娓╂嫙鍚堢嚎鐨勫紩鐢?        self.high_line = None
        self.low_line = None
        
        # 娣诲姞鐢ㄤ簬鎷熷悎绾跨鐐规嫋鍔ㄧ殑鍙橀噺
        self.fit_line_points = []  # 鐢ㄤ簬瀛樺偍鎷熷悎绾跨鐐圭殑鐐瑰璞?        self.active_point = None   # 褰撳墠姝ｅ湪鎷栧姩鐨勭偣
        self.high_temp_range = None  # 楂樻俯鎷熷悎绾胯寖鍥?        self.low_temp_range = None   # 浣庢俯鎷熷悎绾胯寖鍥?        self.show_control_points = True  # 鏄惁鏄剧ず鎺у埗鐐?        self.calc_intercept = None  # 璁＄畻鍑虹殑浜ょ偣浣嶇疆
        
        # 缂撳瓨鎷熷悎鍙傛暟锛屽噺灏戣绠楅鐜?        self.cached_high_params = None
        self.cached_low_params = None
        self.last_fit_position = None
        self.drag_threshold = 0.5  # 鎷栧姩闃堝€硷紙娓╁害鍗曚綅锛?        
        # 棰勫厛璁＄畻鎷熷悎鍙傛暟锛屽噺灏戝垵濮嬫嫋鍔ㄧ殑鍗￠】
        high_params, low_params = self.fit_two_regions(x)
        self.cached_high_params = high_params
        self.cached_low_params = low_params
        self.last_fit_position = x
    
    def connect(self):
        """杩炴帴鎵€鏈変簨浠?""
        # 鏀逛负杩炴帴鍒版暣涓敾甯冭€屼笉鏄偣瀵硅薄
        self.cidpress = self.ax.figure.canvas.mpl_connect('button_press_event', self.on_press)
        self.cidrelease = self.ax.figure.canvas.mpl_connect('button_release_event', self.on_release)
        self.cidmotion = self.ax.figure.canvas.mpl_connect('motion_notify_event', self.on_motion)
    
    def on_press(self, event):
        """褰撻紶鏍囨寜涓嬫椂"""
        if event.inaxes != self.ax:
            return
        
        # 妫€鏌ラ紶鏍囨槸鍚﹀湪铏氱嚎闄勮繎(卤5涓儚绱?
        # 灏嗕簨浠跺潗鏍囪浆鎹负鏁版嵁鍧愭爣
        mouse_x = event.xdata
        mouse_y = event.ydata
        
        # 妫€鏌ユ槸鍚︾偣鍑讳簡鎷熷悎绾跨鐐?        for i, point in enumerate(self.fit_line_points):
            if point and abs(mouse_x - point[0]) < 5 and abs(mouse_y - point[1]) < 0.05 * (max(self.densities) - min(self.densities)):
                self.active_point = i
                self.press = (point[0], point[1], event.xdata, event.ydata)
                
                # 榧犳爣鎸変笅鏃跺澶х偣鐨勫ぇ灏忎綔涓哄弽棣?                for artist in self.ax.lines[:]:
                    if hasattr(artist, 'is_control_point') and artist.is_control_point:
                        idx = self.ax.lines.index(artist)
                        if idx == i:
                            artist.set_markersize(10)  # 澧炲ぇ褰撳墠鎺у埗鐐?                            self.ax.figure.canvas.draw_idle()
                return
        
        # 濡傛灉榧犳爣鍦═g绾块檮杩戯紝鍒欐崟鑾锋嫋鍔ㄤ簨浠?        if abs(mouse_x - self.x) < 5:  # 5鏄儚绱犵骇璇樊锛屽彲浠ヨ皟鏁?            self.press = (self.x, event.xdata, event.ydata)
            self.active_point = None
            
            # 鏇存敼Tg绾挎牱寮?            self.tg_line.set_linewidth(2.0)
            self.tg_line.set_alpha(1.0)
            self.ax.figure.canvas.draw_idle()
    
    def on_motion(self, event):
        """褰撻紶鏍囩Щ鍔ㄦ椂"""
        if self.press is None:
            return
        if event.inaxes != self.ax:
            return
        
        # 濡傛灉姝ｅ湪鎷栧姩鎷熷悎绾跨鐐?        if self.active_point is not None:
            x0, y0, xpress, ypress = self.press
            
            # 璁＄畻绉诲姩澧為噺
            dx = event.xdata - xpress
            dy = event.ydata - ypress
            
            # 鏍规嵁鎷栧姩鐨勭偣鏇存柊鎷熷悎绾垮弬鏁?            if self.high_line and self.low_line:
                min_temp, max_temp = min(self.temperatures), max(self.temperatures)
                
                if self.active_point == 0:  # 楂樻俯绾垮乏绔偣
                    # 鏇存柊楂樻俯绾垮乏绔偣浣嶇疆
                    new_x = min(max(x0 + dx, min_temp), self.x)  # 闄愬埗鍦ㄦ暟鎹寖鍥村唴涓斾笉瓒呰繃Tg鐐?                    new_y = y0 + dy
                    self.fit_line_points[0] = (new_x, new_y)
                    self.update_fit_params_from_points()
                
                elif self.active_point == 1:  # 楂樻俯绾垮彸绔偣
                    # 鏇存柊楂樻俯绾垮彸绔偣浣嶇疆
                    new_x = min(max(x0 + dx, self.x), max_temp)  # 闄愬埗鍦ㄦ暟鎹寖鍥村唴涓斾笉灏忎簬Tg鐐?                    new_y = y0 + dy
                    self.fit_line_points[1] = (new_x, new_y)
                    self.update_fit_params_from_points()
                
                elif self.active_point == 2:  # 浣庢俯绾垮乏绔偣
                    # 鏇存柊浣庢俯绾垮乏绔偣浣嶇疆
                    new_x = min(max(x0 + dx, min_temp), self.x)  # 闄愬埗鍦ㄦ暟鎹寖鍥村唴涓斾笉瓒呰繃Tg鐐?                    new_y = y0 + dy
                    self.fit_line_points[2] = (new_x, new_y)
                    self.update_fit_params_from_points()
                
                elif self.active_point == 3:  # 浣庢俯绾垮彸绔偣
                    # 鏇存柊浣庢俯绾垮彸绔偣浣嶇疆
                    new_x = min(max(x0 + dx, self.x), max_temp)  # 闄愬埗鍦ㄦ暟鎹寖鍥村唴涓斾笉灏忎簬Tg鐐?                    new_y = y0 + dy
                    self.fit_line_points[3] = (new_x, new_y)
                    self.update_fit_params_from_points()
                
                # 鏇存柊鎷熷悎绾?                self.update_fit_lines_from_points()
                
                # 閲嶇粯鍥捐〃
                self.ax.figure.canvas.draw_idle()
                
                # 鍥炶皟鍑芥暟锛岄€氱煡澶栭儴鍙傛暟宸叉洿鏂?                if self.on_update_callback:
                    self.on_update_callback(self.x, self.cached_high_params, self.cached_low_params)
            
            return
        
        # 濡傛灉姝ｅ湪鎷栧姩Tg绾?        # 鍙厑璁告按骞崇Щ鍔紙X杞存柟鍚戯級
        x0, xpress, ypress = self.press
        dx = event.xdata - xpress
        
        # 鏇存柊x鍧愭爣锛堥檺鍒跺湪娓╁害鑼冨洿鍐咃級
        min_temp = min(self.temperatures)
        max_temp = max(self.temperatures)
        drag_x = min(max(x0 + dx, min_temp), max_temp)
        
        try:
            # 浼樺寲锛氬彧鏈夊綋绉诲姩瓒呰繃闃堝€兼椂鎵嶉噸鏂拌绠楁嫙鍚堝弬鏁?            if (self.last_fit_position is None or 
                abs(drag_x - self.last_fit_position) > self.drag_threshold):
                
                # 鏇存柊鎷熷悎鍙傛暟
                high_params, low_params = self.fit_two_regions(drag_x)
                self.cached_high_params = high_params
                self.cached_low_params = low_params
                self.last_fit_position = drag_x
            else:
                # 浣跨敤缂撳瓨鐨勬嫙鍚堝弬鏁?                high_params = self.cached_high_params
                low_params = self.cached_low_params
            
            # 璁＄畻涓ゆ潯鎷熷悎绾跨殑鐪熸浜ょ偣
            m1, b1 = high_params
            m2, b2 = low_params
            
            # 鎷栧姩鏃剁洿鎺ョЩ鍔ㄥ埌榧犳爣浣嶇疆锛屾彁楂樺搷搴旀€?            # 鍙湪榧犳爣閲婃斁鏃惰绠楃湡姝ｄ氦鐐?            new_x = drag_x
            
            # 璁＄畻榧犳爣浣嶇疆澶勭殑y鍊?            high_y = np.polyval(high_params, new_x)
            low_y = np.polyval(low_params, new_x)
            new_y = (high_y + low_y) / 2  # 鍙栦袱鏉＄嚎鍦ㄦ澶勭殑骞冲潎鍊?            
            # 鏇存柊浣嶇疆
            self.x = new_x
            self.y = new_y
            
            # 鏇存柊铏氱嚎浣嶇疆
            self.tg_line.set_xdata([new_x, new_x])
            
            # 鏇存柊鏂囨湰浣嶇疆鍜屽唴瀹?            self.text.set_x(new_x + 5)
            self.text.set_text(f'Tg = {new_x:.0f}K')
            
            # 鏇存柊鎷熷悎绾?            self.update_fit_lines(high_params, low_params)
            
            # 閲嶇粯鍥捐〃
            self.ax.figure.canvas.draw_idle()
            
            # 鍥炶皟鍑芥暟锛岄€氱煡澶栭儴Tg宸叉洿鏂?            if self.on_update_callback:
                self.on_update_callback(new_x, high_params, low_params)
        
        except Exception as e:
            # 鍑虹幇寮傚父鏃讹紝绠€鍗曞湴浣跨敤鎷栧姩浣嶇疆锛屼笉鏇存敼鎷熷悎绾?            print(f"澶勭悊鎷栧姩鏃跺嚭閿? {e}")
            self.x = drag_x
            self.tg_line.set_xdata([drag_x, drag_x])
            self.text.set_x(drag_x + 5)
            self.text.set_text(f'Tg = {drag_x:.0f}K')
            self.ax.figure.canvas.draw_idle()
    
    def on_release(self, event):
        """褰撻紶鏍囬噴鏀炬椂锛屼繚鎸乀g鍦ㄧ敤鎴烽噴鏀剧殑浣嶇疆"""
        if self.press is None:
            return
        
        # 濡傛灉姝ｅ湪鎷栧姩鎷熷悎绾跨鐐癸紝鍒欓噴鏀?        if self.active_point is not None:
            # 鎭㈠鐐圭殑姝ｅ父澶у皬
            for artist in self.ax.lines[:]:
                if hasattr(artist, 'is_control_point') and artist.is_control_point:
                    artist.set_markersize(8)  # 鎭㈠榛樿澶у皬
            
            self.active_point = None
            self.press = None
            self.ax.figure.canvas.draw()
            return
        
        # 鎭㈠Tg绾挎牱寮?        self.tg_line.set_linewidth(1.0)
        self.tg_line.set_alpha(0.7)
        
        # 淇濇寔Tg鍦ㄧ敤鎴锋嫋鍔ㄧ殑浣嶇疆锛堝嵆褰撳墠self.x浣嶇疆锛夛紝涓嶅啀閲嶆柊璁＄畻浜ょ偣
        # 浣嗛渶瑕侀噸鏂拌绠楁嫙鍚堝弬鏁?        high_params, low_params = self.fit_two_regions(self.x)
        self.cached_high_params = high_params
        self.cached_low_params = low_params
        self.last_fit_position = self.x
        
        # 鏇存柊鎷熷悎绾?        self.update_fit_lines(high_params, low_params)
        
        # 閲嶇疆鎸夊帇鐘舵€?        self.press = None
        self.ax.figure.canvas.draw()
        
        # 鍥炶皟鍑芥暟锛岄€氱煡澶栭儴Tg宸叉洿鏂?        if self.on_update_callback:
            self.on_update_callback(self.x, high_params, low_params)
    
    def disconnect(self):
        """鏂紑鎵€鏈変簨浠惰繛鎺?""
        # 鏇存柊涓轰娇鐢╝x.figure鑰屼笉鏄痯oint.figure
        self.ax.figure.canvas.mpl_disconnect(self.cidpress)
        self.ax.figure.canvas.mpl_disconnect(self.cidrelease)
        self.ax.figure.canvas.mpl_disconnect(self.cidmotion)
    
    def set_fit_lines(self, high_line, low_line):
        """璁剧疆鎷熷悎绾跨殑寮曠敤"""
        self.high_line = high_line
        self.low_line = low_line
        
        # 鍒濆鍖栨嫙鍚堢嚎绔偣鍙婂彲鎷栧姩鐐?        self.create_draggable_fit_points()
    
    def fit_two_regions(self, tg):
        """鏍规嵁褰撳墠Tg鍊兼嫙鍚堜袱涓尯鍩?""
        # 鍒嗗壊鏁版嵁
        high_temps = [t for t in self.temperatures if t > tg]
        high_dens = [d for t, d in zip(self.temperatures, self.densities) if t > tg]
        
        low_temps = [t for t in self.temperatures if t <= tg]
        low_dens = [d for t, d in zip(self.temperatures, self.densities) if t <= tg]
        
        # 鎷熷悎楂樻俯鍖哄煙 - 纭繚鑷冲皯鏈?涓偣
        if len(high_temps) > 1:
            try:
                high_temp_params = np.polyfit(high_temps, high_dens, 1)
            except:
                # 鎷熷悎澶辫触鏃惰繑鍥炰竴涓粯璁ゆ枩鐜囩殑绾?                high_temp_params = [-0.0001, np.mean(high_dens)]
        else:
            # 濡傛灉鐐瑰お灏戯紝浣跨敤榛樿鍊?            high_temp_params = [-0.0001, np.mean(self.densities) + 0.01]
        
        # 鎷熷悎浣庢俯鍖哄煙 - 纭繚鑷冲皯鏈?涓偣
        if len(low_temps) > 1:
            try:
                low_temp_params = np.polyfit(low_temps, low_dens, 1)
            except:
                # 鎷熷悎澶辫触鏃惰繑鍥炰竴涓粯璁ゆ枩鐜囩殑绾?                low_temp_params = [-0.00005, np.mean(low_dens)]
        else:
            # 濡傛灉鐐瑰お灏戯紝浣跨敤榛樿鍊?            low_temp_params = [-0.00005, np.mean(self.densities) - 0.01]
        
        return high_temp_params, low_temp_params
    
    def update_fit_lines(self, high_params, low_params):
        """鏇存柊鎷熷悎绾?""
        if self.high_line is None or self.low_line is None:
            return
            
        # 娓╁害鑼冨洿
        t_min, t_max = min(self.temperatures), max(self.temperatures)
        
        # 瀹氫箟閲嶅彔鑼冨洿锛岀‘淇濅袱鏉＄嚎鍦═g鐐圭浉浜?        overlap_range = (t_max - t_min) * 0.05  # 5%鐨勯噸鍙犺寖鍥?        
        # 鏇存柊楂樻俯鍖烘嫙鍚堢嚎 - 寤朵几鍒扮暐浣庝簬Tg鐨勭偣
        high_temp_range = np.linspace(self.x - overlap_range, t_max, 100)
        high_fit = np.polyval(high_params, high_temp_range)
        self.high_line.set_xdata(high_temp_range)
        self.high_line.set_ydata(high_fit)
        
        # 鏇存柊浣庢俯鍖烘嫙鍚堢嚎 - 寤朵几鍒扮暐楂樹簬Tg鐨勭偣
        low_temp_range = np.linspace(t_min, self.x + overlap_range, 100)
        low_fit = np.polyval(low_params, low_temp_range)
        self.low_line.set_xdata(low_temp_range)
        self.low_line.set_ydata(low_fit)
        
        # 淇濆瓨娓╁害鑼冨洿锛岀敤浜庣鐐规嫋鍔ㄥ姛鑳?        self.high_temp_range = high_temp_range
        self.low_temp_range = low_temp_range
        
        # 鏇存柊鎷熷悎绾跨鐐逛綅缃?        self.update_fit_points(high_params, low_params)
    
    def create_draggable_fit_points(self):
        """鍒涘缓鍙嫋鍔ㄧ殑鎷熷悎绾跨鐐?""
        if not self.high_line or not self.low_line:
            return
        
        # 鍒濆鍖栧洓涓偣鐨勪綅缃?        t_min, t_max = min(self.temperatures), max(self.temperatures)
        
        # 楂樻俯绾垮乏绔偣锛堥潬杩慣g澶勶級
        if len(self.high_line.get_xdata()) > 0 and len(self.high_line.get_ydata()) > 0:
            high_x_left = self.high_line.get_xdata()[0]
            high_y_left = self.high_line.get_ydata()[0]
        else:
            high_x_left = self.x
            high_y_left = np.mean(self.densities)
        
        # 楂樻俯绾垮彸绔偣
        if len(self.high_line.get_xdata()) > 0 and len(self.high_line.get_ydata()) > 0:
            high_x_right = self.high_line.get_xdata()[-1]
            high_y_right = self.high_line.get_ydata()[-1]
        else:
            high_x_right = t_max
            high_y_right = np.mean(self.densities) - 0.02
        
        # 浣庢俯绾垮乏绔偣
        if len(self.low_line.get_xdata()) > 0 and len(self.low_line.get_ydata()) > 0:
            low_x_left = self.low_line.get_xdata()[0]
            low_y_left = self.low_line.get_ydata()[0]
        else:
            low_x_left = t_min
            low_y_left = np.mean(self.densities) + 0.02
        
        # 浣庢俯绾垮彸绔偣锛堥潬杩慣g澶勶級
        if len(self.low_line.get_xdata()) > 0 and len(self.low_line.get_ydata()) > 0:
            low_x_right = self.low_line.get_xdata()[-1]
            low_y_right = self.low_line.get_ydata()[-1]
        else:
            low_x_right = self.x
            low_y_right = np.mean(self.densities)
        
        # 瀛樺偍鐐逛綅缃?(x, y)
        self.fit_line_points = [
            (high_x_left, high_y_left),    # 楂樻俯绾垮乏绔偣
            (high_x_right, high_y_right),  # 楂樻俯绾垮彸绔偣
            (low_x_left, low_y_left),      # 浣庢俯绾垮乏绔偣
            (low_x_right, low_y_right)     # 浣庢俯绾垮彸绔偣
        ]
        
        # 鍦ㄥ浘涓婄粯鍒剁鐐规爣璁?        self.draw_fit_points()
    
    def draw_fit_points(self):
        """鍦ㄥ浘涓婄粯鍒舵嫙鍚堢嚎绔偣鏍囪"""
        # 娓呴櫎鐜版湁鐨勬帶鍒剁偣鏍囪
        for artist in self.ax.lines[:]:
            if hasattr(artist, 'is_control_point') and artist.is_control_point:
                artist.remove()
        
        # 濡傛灉鎺у埗鐐逛笉鍙锛岀洿鎺ヨ繑鍥?        if not self.show_control_points:
            return
        
        # 缁樺埗楂樻俯绾跨鐐?        if len(self.fit_line_points) >= 2:
            # 楂樻俯绾垮乏绔偣
            high_left = self.ax.plot(self.fit_line_points[0][0], self.fit_line_points[0][1], 'o', 
                                    color='red', markersize=8, alpha=0.8, zorder=10)[0]
            high_left.is_control_point = True
            
            # 楂樻俯绾垮彸绔偣
            high_right = self.ax.plot(self.fit_line_points[1][0], self.fit_line_points[1][1], 'o', 
                                    color='red', markersize=8, alpha=0.8, zorder=10)[0]
            high_right.is_control_point = True
        
        # 缁樺埗浣庢俯绾跨鐐?        if len(self.fit_line_points) >= 4:
            # 浣庢俯绾垮乏绔偣
            low_left = self.ax.plot(self.fit_line_points[2][0], self.fit_line_points[2][1], 'o', 
                                    color='green', markersize=8, alpha=0.8, zorder=10)[0]
            low_left.is_control_point = True
            
            # 浣庢俯绾垮彸绔偣
            low_right = self.ax.plot(self.fit_line_points[3][0], self.fit_line_points[3][1], 'o', 
                                    color='green', markersize=8, alpha=0.8, zorder=10)[0]
            low_right.is_control_point = True
    
    def update_fit_points(self, high_params, low_params):
        """鏍规嵁鏂扮殑鎷熷悎鍙傛暟鏇存柊鎷熷悎绾跨鐐逛綅缃?""
        t_min, t_max = min(self.temperatures), max(self.temperatures)
        
        # 璁＄畻鏂扮殑绔偣浣嶇疆
        if self.high_temp_range is not None and len(self.high_temp_range) > 0:
            high_x_left = self.high_temp_range[0]
            high_x_right = self.high_temp_range[-1]
            high_y_left = np.polyval(high_params, high_x_left)
            high_y_right = np.polyval(high_params, high_x_right)
        else:
            high_x_left = self.x
            high_x_right = t_max
            high_y_left = np.polyval(high_params, high_x_left)
            high_y_right = np.polyval(high_params, high_x_right)
        
        if self.low_temp_range is not None and len(self.low_temp_range) > 0:
            low_x_left = self.low_temp_range[0]
            low_x_right = self.low_temp_range[-1]
            low_y_left = np.polyval(low_params, low_x_left)
            low_y_right = np.polyval(low_params, low_x_right)
        else:
            low_x_left = t_min
            low_x_right = self.x
            low_y_left = np.polyval(low_params, low_x_left)
            low_y_right = np.polyval(low_params, low_x_right)
        
        # 鏇存柊绔偣浣嶇疆
        self.fit_line_points = [
            (high_x_left, high_y_left),
            (high_x_right, high_y_right),
            (low_x_left, low_y_left),
            (low_x_right, low_y_right)
        ]
        
        # 閲嶆柊缁樺埗鎺у埗鐐?        self.draw_fit_points()
    
    def update_fit_params_from_points(self):
        """鏍规嵁鎷栧姩鐨勭鐐逛綅缃洿鏂版嫙鍚堝弬鏁?""
        # 纭繚鏈夎冻澶熺殑鐐?        if len(self.fit_line_points) < 4:
            return
        
        # 浠庣鐐硅绠楅珮娓╃嚎鍙傛暟 (slope, intercept)
        high_p1 = self.fit_line_points[0]  # 宸︾鐐?        high_p2 = self.fit_line_points[1]  # 鍙崇鐐?        
        # 妫€鏌ユ槸鍚︽湁鏁?        if high_p1[0] != high_p2[0]:  # 閬垮厤闄や互闆?            high_slope = (high_p2[1] - high_p1[1]) / (high_p2[0] - high_p1[0])
            high_intercept = high_p1[1] - high_slope * high_p1[0]
            high_params = [high_slope, high_intercept]
        else:
            # 榛樿鍙傛暟
            high_params = self.cached_high_params if self.cached_high_params else [-0.0001, np.mean(self.densities) + 0.01]
        
        # 浠庣鐐硅绠椾綆娓╃嚎鍙傛暟
        low_p1 = self.fit_line_points[2]  # 宸︾鐐?        low_p2 = self.fit_line_points[3]  # 鍙崇鐐?        
        # 妫€鏌ユ槸鍚︽湁鏁?        if low_p1[0] != low_p2[0]:  # 閬垮厤闄や互闆?            low_slope = (low_p2[1] - low_p1[1]) / (low_p2[0] - low_p1[0])
            low_intercept = low_p1[1] - low_slope * low_p1[0]
            low_params = [low_slope, low_intercept]
        else:
            # 榛樿鍙傛暟
            low_params = self.cached_low_params if self.cached_low_params else [-0.00005, np.mean(self.densities) - 0.01]
        
        # 鏇存柊缂撳瓨鐨勫弬鏁?        self.cached_high_params = high_params
        self.cached_low_params = low_params
        
        # 璁＄畻涓ゆ潯绾跨殑浜ょ偣锛堜粎鏄剧ず鍦ㄦ枃鏈爣绛句腑锛屼笉绉诲姩Tg绾匡級
        try:
            # 闃叉闄や互闆堕敊璇垨鏂滅巼鎺ヨ繎鐩哥瓑
            if abs(high_params[0] - low_params[0]) > 1e-6:
                # 璁＄畻浜ょ偣
                intercept_x = (low_params[1] - high_params[1]) / (high_params[0] - low_params[0])
                
                # 纭繚浜ょ偣鍦ㄥ悎鐞嗚寖鍥村唴
                min_temp = min(self.temperatures)
                max_temp = max(self.temperatures)
                
                if intercept_x >= min_temp and intercept_x <= max_temp:
                    # 浜ょ偣淇℃伅浠呬綔鍙傝€冿紝涓嶇Щ鍔═g绾?                    intercept_y = np.polyval(high_params, intercept_x)
                    
                    # 淇濆瓨璁＄畻鍑虹殑浜ょ偣浣嶇疆
                    self.calc_intercept = intercept_x
                    
                    # 鍙洿鏂颁氦鐐逛俊鎭枃鏈唴瀹癸紝涓嶆敼鍙楾g绾夸綅缃?                    self.text.set_text(f'Tg = {self.x:.0f}K (浜ょ偣: {intercept_x:.0f}K)')
        except Exception as e:
            print(f"璁＄畻浜ょ偣鍑洪敊: {e}")
            self.calc_intercept = None
    
    def update_fit_lines_from_points(self):
        """鏍规嵁鎷栧姩鐨勭鐐逛綅缃洿鏂版嫙鍚堢嚎鏄剧ず"""
        if self.high_line is None or self.low_line is None:
            return
        
        # 纭繚鏈夎冻澶熺殑鐐?        if len(self.fit_line_points) < 4:
            return
        
        # 楂樻俯绾夸袱涓鐐?        high_p1 = self.fit_line_points[0]
        high_p2 = self.fit_line_points[1]
        
        # 浣庢俯绾夸袱涓鐐?        low_p1 = self.fit_line_points[2]
        low_p2 = self.fit_line_points[3]
        
        # 鍒涘缓x鐐归泦
        high_x = np.linspace(high_p1[0], high_p2[0], 100)
        low_x = np.linspace(low_p1[0], low_p2[0], 100)
        
        # 浣跨敤褰撳墠鍙傛暟璁＄畻y鍊?        if self.cached_high_params and self.cached_low_params:
            high_y = np.polyval(self.cached_high_params, high_x)
            low_y = np.polyval(self.cached_low_params, low_x)
            
            # 鏇存柊绾跨殑鏁版嵁
            self.high_line.set_xdata(high_x)
            self.high_line.set_ydata(high_y)
            
            self.low_line.set_xdata(low_x)
            self.low_line.set_ydata(low_y)
            
            # 瀛樺偍娓╁害鑼冨洿
            self.high_temp_range = high_x
            self.low_temp_range = low_x
    
    def toggle_control_points(self):
        """鍒囨崲鎺у埗鐐圭殑鏄剧ず鐘舵€?""
        self.show_control_points = not self.show_control_points
        self.draw_fit_points()
        self.ax.figure.canvas.draw_idle()
        return self.show_control_points
    
    def move_to_calculated_intercept(self):
        """灏員g绾跨Щ鍔ㄥ埌璁＄畻鍑虹殑浜ょ偣浣嶇疆"""
        if self.calc_intercept is None:
            return False
        
        # 纭繚浜ょ偣鍦ㄥ悎鐞嗚寖鍥村唴
        min_temp = min(self.temperatures)
        max_temp = max(self.temperatures)
        
        if self.calc_intercept >= min_temp and self.calc_intercept <= max_temp:
            # 鏇存柊Tg浣嶇疆
            self.x = self.calc_intercept
            self.y = np.polyval(self.cached_high_params, self.calc_intercept)
            
            # 鏇存柊Tg绾夸綅缃?            self.tg_line.set_xdata([self.calc_intercept, self.calc_intercept])
            
            # 鏇存柊鏂囨湰浣嶇疆鍜屽唴瀹?            self.text.set_x(self.calc_intercept + 5)
            self.text.set_text(f'Tg = {self.calc_intercept:.0f}K')
            
            # 鏇存柊鎷熷悎绾?            self.update_fit_lines(self.cached_high_params, self.cached_low_params)
            
            # 閲嶇粯鍥捐〃
            self.ax.figure.canvas.draw_idle()
            
            # 閫氱煡澶栭儴Tg宸叉洿鏂?            if self.on_update_callback:
                self.on_update_callback(self.calc_intercept, self.cached_high_params, self.cached_low_params)
            
            return True
        
        return False

def read_data_from_file(file_path):
    """
    浠庢枃浠朵腑璇诲彇娓╁害鍜屽瘑搴︽暟鎹?    鏀寔CSV鍜孍xcel鏍煎紡
    
    鏀寔鐨勫垪鍚嶆牸寮忥細
    娓╁害鍒? 'Temperature', 'Temperature(K)', 'Temperature (K)' 绛?    瀵嗗害鍒? 'Density', 'Density(g/cm鲁)', 'Density g/cm3' 绛?    
    杩斿洖锛歵emperatures鍒楄〃, densities鍒楄〃锛堝崟鍒楀瘑搴︽椂锛夋垨densities_columns鍒楄〃锛堝鍒楀瘑搴︽椂锛夛紝is_multi_density鏍囧織
    """
    file_ext = os.path.splitext(file_path)[1].lower()
    
    if file_ext == '.csv':
        df = pd.read_csv(file_path)
    elif file_ext in ['.xls', '.xlsx']:
        df = pd.read_excel(file_path)
    else:
        raise ValueError(f"涓嶆敮鎸佺殑鏂囦欢鏍煎紡: {file_ext}锛岃浣跨敤CSV鎴朎xcel鏂囦欢")
    
    # 灏濊瘯鍖归厤娓╁害鍒楀拰瀵嗗害鍒?    temp_column = None
    density_columns = []
    
    # 甯歌鐨勬俯搴﹀垪鍚?    temp_patterns = ['Temperature', 'Temp', 'T', '娓╁害']
    # 甯歌鐨勫瘑搴﹀垪鍚?    density_patterns = ['Density', '蟻', '瀵嗗害']
    
    # 鏌ユ壘娓╁害鍒?    for col in df.columns:
        # 妫€鏌ヨ鍒楁槸鍚﹀尮閰嶄换涓€娓╁害妯″紡
        if any(pattern.lower() in col.lower() for pattern in temp_patterns):
            temp_column = col
            break
    
    # 鏌ユ壘鎵€鏈夊瘑搴﹀垪
    for col in df.columns:
        # 妫€鏌ヨ鍒楁槸鍚﹀尮閰嶄换涓€瀵嗗害妯″紡
        if any(pattern.lower() in col.lower() for pattern in density_patterns):
            density_columns.append(col)
    
    # 濡傛灉鏈壘鍒板垪锛屽皾璇曠洿鎺ヤ娇鐢ㄧ涓€鍒椾綔涓烘俯搴︼紝鍏朵綑鍒椾綔涓哄瘑搴?    if temp_column is None and len(density_columns) == 0 and len(df.columns) >= 2:
        print("璀﹀憡锛氭湭鑳借瘑鍒俯搴﹀拰瀵嗗害鍒楋紝浣跨敤绗竴鍒椾綔涓烘俯搴︼紝鍏朵綑鍒椾綔涓哄瘑搴︺€?)
        temp_column = df.columns[0]
        density_columns = df.columns[1:]
    else:
        # 濡傛灉浠嶆湭鎵惧埌娓╁害鍒楋紝鎶ラ敊
        if temp_column is None:
            raise ValueError(f"鏂囦欢缂哄皯蹇呰鐨勬俯搴﹀垪銆傛敮鎸佺殑鍒楀悕鍖呮嫭: {', '.join(temp_patterns)}鎴栫被浼煎悕绉?)
        
        # 濡傛灉鏈壘鍒板瘑搴﹀垪浣嗘壘鍒颁簡娓╁害鍒楋紝灏濊瘯浣跨敤闈炴俯搴﹀垪浣滀负瀵嗗害鍒?        if len(density_columns) == 0:
            for col in df.columns:
                if col != temp_column:
                    density_columns.append(col)
            
            if len(density_columns) == 0:
                raise ValueError(f"鏂囦欢缂哄皯蹇呰鐨勫瘑搴﹀垪銆傛敮鎸佺殑鍒楀悕鍖呮嫭: {', '.join(density_patterns)}鎴栫被浼煎悕绉?)
    
    print(f"浣跨敤鍒? '{temp_column}' 浣滀负娓╁害")
    print(f"浣跨敤鍒椾綔涓哄瘑搴? {', '.join(density_columns)}")
    
    # 鎻愬彇娓╁害鏁版嵁
    temperatures = df[temp_column].tolist()
    
    # 妫€鏌ユ槸鍗曞垪瀵嗗害杩樻槸澶氬垪瀵嗗害
    is_multi_density = len(density_columns) > 1
    
    if is_multi_density:
        # 澶氬垪瀵嗗害鎯呭喌
        densities_columns = []
        for col in density_columns:
            densities_columns.append(df[col].tolist())
        return temperatures, densities_columns, is_multi_density
    else:
        # 鍗曞垪瀵嗗害鎯呭喌
        densities = df[density_columns[0]].tolist()
        return temperatures, densities, is_multi_density

def find_glass_transition_temperature(temperatures, densities, output_path=None, sample_name=None, interactive=False, y_margin=0.05, y_min=None, y_max=None, language="cn"):
    """
    鍒嗘瀽娓╁害鍜屽瘑搴︽暟鎹紝瀵绘壘鐜荤拑鍖栬浆鍙樻俯搴?Tg)
    
    鍙傛暟:
    temperatures - 娓╁害鏁版嵁鍒楄〃
    densities - 瀵嗗害鏁版嵁鍒楄〃
    output_path - 杈撳嚭鍥惧儚淇濆瓨璺緞锛岄粯璁や负褰撳墠鐩綍
    sample_name - 鏍峰搧鍚嶇О锛岀敤浜庡浘琛ㄦ爣棰?    interactive - 鏄惁鍚敤浜や簰寮忔ā寮?鍙嫋鍔═g鐐?
    y_margin - Y杞磋竟璺濇瘮渚嬶紝鐢ㄤ簬绐佸嚭鏄剧ず娉㈠姩
    y_min - Y杞存渶灏忓€硷紙鎵嬪姩璁剧疆锛?    y_max - Y杞存渶澶у€硷紙鎵嬪姩璁剧疆锛?    language - 鏍囩璇█閫夋嫨锛?cn"涓轰腑鏂囷紝"en"涓鸿嫳鏂?    
    杩斿洖:
    Tg - 鐜荤拑鍖栬浆鍙樻俯搴﹀€?    """
    # 纭繚鏁版嵁鏄疦umPy鏁扮粍
    temperatures = np.array(temperatures)
    densities = np.array(densities)
    
    # 璇█閫夋嫨
    if language == "en":
        liquid_region_label = "Liquid Region Fitting"
        glass_region_label = "Glass Region Fitting"
        temperature_label = "Temperature (K)"
        density_label = f"Density ({DENSITY_UNIT})"
        plot_title_format = "Density vs Temperature Plot for {}"
    else:  # 榛樿涓枃
        liquid_region_label = "娑蹭綋鍖烘嫙鍚?
        glass_region_label = "鐜荤拑鍖烘嫙鍚?
        temperature_label = "娓╁害 (K)"
        density_label = f"瀵嗗害 ({DENSITY_UNIT})"
        plot_title_format = "{}鐨勫瘑搴﹂殢娓╁害鐨勫彉鍖?
    
    # 鍒濆鍖栧彉閲?    best_error = float('inf')
    best_split = None
    best_params = None
    best_r2_high = 0
    best_r2_low = 0
    
    # 灏濊瘯涓嶅悓鐨勫垎鍓茬偣锛屾壘鍑烘渶浣虫嫙鍚?    # 涓轰簡閬垮厤杩囨嫙鍚堬紝鎴戜滑璺宠繃鍓嶅嚑涓拰鍚庡嚑涓偣
    
    for split_idx in range(3, len(temperatures)-3):
        # 楂樻俯鍖哄煙鐨勭嚎鎬ф嫙鍚?        try:
            # 鎷熷悎楂樻俯鍖哄煙
            high_temps = temperatures[:split_idx]
            high_dens = densities[:split_idx]
            high_temp_params, high_residuals, _, _, _ = np.polyfit(high_temps, high_dens, 1, full=True)
            
            # 璁＄畻楂樻俯鍖哄煙R^2鍊?            high_fit = np.polyval(high_temp_params, high_temps)
            ss_tot_high = np.sum((high_dens - np.mean(high_dens))**2)
            ss_res_high = np.sum((high_dens - high_fit)**2)
            r2_high = 1 - (ss_res_high / ss_tot_high) if ss_tot_high != 0 else 0
            
            # 鎷熷悎浣庢俯鍖哄煙
            low_temps = temperatures[split_idx:]
            low_dens = densities[split_idx:]
            low_temp_params, low_residuals, _, _, _ = np.polyfit(low_temps, low_dens, 1, full=True)
            
            # 璁＄畻浣庢俯鍖哄煙R^2鍊?            low_fit = np.polyval(low_temp_params, low_temps)
            ss_tot_low = np.sum((low_dens - np.mean(low_dens))**2)
            ss_res_low = np.sum((low_dens - low_fit)**2)
            r2_low = 1 - (ss_res_low / ss_tot_low) if ss_tot_low != 0 else 0
            
            # 璁＄畻鎷熷悎璇樊
            high_error = np.sum((high_fit - high_dens)**2)
            low_error = np.sum((low_fit - low_dens)**2)
            total_error = high_error + low_error
            
            if total_error < best_error:
                best_error = total_error
                best_split = split_idx
                best_params = (high_temp_params, low_temp_params)
                best_r2_high = r2_high
                best_r2_low = r2_low
        except:
            continue
    
    if best_params:
        # 鎵惧嚭涓ゆ潯绾跨殑浜ょ偣
        high_params, low_params = best_params
        try:
            # m1*x + b1 = m2*x + b2
            # (m1-m2)*x = b2-b1
            # x = (b2-b1)/(m1-m2)
            m1, b1 = high_params
            m2, b2 = low_params
            
            # 闃叉闄や互闆堕敊璇垨鏂滅巼鎺ヨ繎鐩哥瓑
            if abs(m1 - m2) > 1e-6:
                tg_method2 = (b2-b1)/(m1-m2)
                
                # 妫€鏌ヤ氦鐐规槸鍚﹀湪鍚堢悊鑼冨洿鍐?                min_temp, max_temp = min(temperatures), max(temperatures)
                if tg_method2 < min_temp * 0.9 or tg_method2 > max_temp * 1.1:
                    # 鑻ヤ氦鐐逛笉鍦ㄥ悎鐞嗚寖鍥达紝浣跨敤涓棿鍊?                    tg_method2 = np.mean(temperatures)
            else:
                # 鑻ヤ袱绾垮钩琛岋紝浣跨敤涓棿鍊?                tg_method2 = np.mean(temperatures)
                
            final_tg = tg_method2
        except:
            # 璁＄畻浜ょ偣鍑洪敊锛屼娇鐢ㄥ閫夋柟娉?            final_tg = temperatures[best_split]
    else:
        # 濡傛灉鏃犳硶鎵惧埌浜ょ偣锛屼娇鐢ㄥ叾浠栨柟娉?        # 璁＄畻鏂滅巼(涓€闃跺鏁?
        slopes = []
        mid_temps = []
        
        for i in range(len(temperatures)-1):
            temp_diff = temperatures[i] - temperatures[i+1]
            density_diff = densities[i+1] - densities[i]
            slope = density_diff / temp_diff
            slopes.append(slope)
            mid_temps.append((temperatures[i] + temperatures[i+1]) / 2)
        
        slopes = np.array(slopes)
        mid_temps = np.array(mid_temps)
        
        # 浣跨敤Savitzky-Golay婊ゆ尝鍣ㄥ钩婊戞枩鐜囦互鍑忓皯鍣０
        if len(slopes) > 5:  # 璇ユ护娉㈠櫒鑷冲皯闇€瑕?涓偣
            window_length = min(5, len(slopes) - 2 if len(slopes) % 2 == 0 else len(slopes) - 1)
            smooth_slopes = savgol_filter(slopes, window_length, 2)
        else:
            smooth_slopes = slopes
        
        # 璁＄畻浜岄樁瀵兼暟(鏂滅巼鐨勫彉鍖栫巼)
        second_deriv = []
        second_mid_temps = []
        
        for i in range(len(smooth_slopes)-1):
            temp_diff = mid_temps[i] - mid_temps[i+1]
            slope_diff = smooth_slopes[i+1] - smooth_slopes[i]
            second_d = slope_diff / temp_diff
            second_deriv.append(second_d)
            second_mid_temps.append((mid_temps[i] + mid_temps[i+1]) / 2)
        
        second_deriv = np.array(second_deriv)
        second_mid_temps = np.array(second_mid_temps)
        
        # 鏂规硶1锛氭壘鍑轰簩闃跺鏁扮粷瀵瑰€兼渶澶х殑娓╁害鐐?        tg_method1 = second_mid_temps[np.argmax(np.abs(second_deriv))]
        final_tg = tg_method1
    
    # 鍒涘缓鐜荤拑鍖栬浆鍙樻俯搴﹀垎鏋愬浘
    fig = plt.figure(figsize=(8, 6), dpi=100)
    ax = fig.add_subplot(111)
    
    # 璁剧疆鏍峰搧鍚嶇О
    if sample_name is None:
        sample_name = "鏍峰搧" if language == "cn" else "Sample"
    
    # 涓诲浘 - 娓╁害vs瀵嗗害
    ax.scatter(temperatures, densities, color='#3182bd', s=40, alpha=0.8)
    
    # 濡傛灉鎻愪緵浜嗘墜鍔╕杞磋寖鍥达紝搴旂敤瀹?    if y_min is not None and y_max is not None:
        ax.set_ylim(y_min, y_max)
    else:
        # 璁剧疆y杞磋竟璺濇瘮渚嬶紝浣挎尝鍔ㄨ寖鍥存洿鍔犳槑鏄?        ax.margins(y=y_margin)
    
    # 浣跨敤绱у噾甯冨眬锛岃缁樺浘鍖哄煙鏈€澶у寲
    fig.tight_layout()
    
    # 濡傛灉鏈夋渶浣虫嫙鍚堝弬鏁帮紝缁樺埗鎷熷悎绾?    high_line = None
    low_line = None
    draggable_tg = None
    
    if best_params:
        high_params, low_params = best_params
        
        # 娓╁害鑼冨洿
        t_min, t_max = min(temperatures), max(temperatures)
        
        # 瀹氫箟閲嶅彔鑼冨洿锛岀‘淇濅袱鏉＄嚎鍦═g鐐圭浉浜?        overlap_range = (t_max - t_min) * 0.05  # 5%鐨勯噸鍙犺寖鍥?        
        # 鍒涘缓鍧囧寑鍒嗗竷鐨勬俯搴︾偣杩涜缁樺浘
        plot_temp_range = np.linspace(t_min, t_max, 1000)
        
        # 楂樻俯鍖哄煙鎷熷悎绾?- 寤朵几鍒扮暐浣庝簬Tg鐨勭偣
        high_mask = plot_temp_range >= (final_tg - overlap_range)
        if np.any(high_mask):
            high_temp_points = plot_temp_range[high_mask]
            high_fit = np.polyval(high_params, high_temp_points)
            high_line, = ax.plot(high_temp_points, high_fit, '-', color='#e41a1c', 
                    linewidth=2, label=f'{liquid_region_label} (R^2={best_r2_high:.4f})')
        
        # 浣庢俯鍖哄煙鎷熷悎绾?- 寤朵几鍒扮暐楂樹簬Tg鐨勭偣
        low_mask = plot_temp_range <= (final_tg + overlap_range)
        if np.any(low_mask):
            low_temp_points = plot_temp_range[low_mask]
            low_fit = np.polyval(low_params, low_temp_points)
            low_line, = ax.plot(low_temp_points, low_fit, '-', color='#4daf4a', 
                    linewidth=2, label=f'{glass_region_label} (R^2={best_r2_low:.4f})')
        
        # 鏍囪鐜荤拑鍖栬浆鍙樻俯搴?        if not interactive:
            # 璁＄畻浜ょ偣鐨剏鍊?            tg_y = np.polyval(high_params, final_tg)
            
            # 涓嶅啀缁樺埗鍨傜洿绾?            
            ax.text(final_tg+5, min(densities) + 0.9*(max(densities)-min(densities)), 
                   f'Tg = {final_tg:.0f}K', fontsize=14, color='darkred', 
                   bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5'))
        else:
            # 鍦ㄤ氦浜掓ā寮忎笅锛屽垱寤哄彲鎷栧姩鐨凾g鐐?            def on_tg_update(new_tg, high_params, low_params):
                nonlocal final_tg
                final_tg = new_tg
                # 鍦ㄤ氦浜掓ā寮忎笅鏇存柊y杞磋寖鍥翠互绐佸嚭鏄剧ず娉㈠姩
                adjust_y_axis(ax, temperatures, densities, high_params, low_params, new_tg, y_margin)
            
            # 璁＄畻Tg鐐圭殑y鍊硷紙涓ゆ潯绾跨殑浜ょ偣锛?            tg_y = np.polyval(high_params, final_tg)
            
            draggable_tg = DraggableTg(ax, final_tg, tg_y, temperatures, densities, 
                                      on_update_callback=on_tg_update, 
                                      y_min=y_min, y_max=y_max)
            draggable_tg.set_fit_lines(high_line, low_line)
    
    # 鏍煎紡璁剧疆
    ax.set_xlabel(temperature_label, fontsize=12)
    ax.set_ylabel(density_label, fontsize=12)
    ax.set_title(plot_title_format.format(sample_name), fontsize=14)
    ax.legend(loc='best')
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 璋冩暣甯冨眬
    plt.tight_layout()
    
    if not interactive:
        # 纭畾淇濆瓨璺緞
        if output_path is None:
            base_dir = os.path.dirname(os.path.abspath(__file__))
            output_file = os.path.join(base_dir, f'glass_transition_{sample_name}.png')
        else:
            output_file = output_path
        
        # 淇濆瓨楂樺垎杈ㄧ巼鍥惧儚
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"鍙鍖栫粨鏋滃凡淇濆瓨鑷?{output_file}")
        
        # 鏄剧ず鍥捐〃
        plt.show()
        
        # 鐢熸垚璇︾粏鍒嗘瀽鍥?(鍙€?
        if best_params:
            create_detailed_analysis(temperatures, densities, best_params, final_tg, sample_name, y_margin, y_min, y_max)
        
        # 杈撳嚭缁撴灉
        print(f"鐜荤拑鍖栬浆鍙樻俯搴?(Tg): {final_tg:.1f}K")
        
        # 杩斿洖鏈€缁堢粨鏋?        return final_tg
    else:
        # 鍦ㄤ氦浜掓ā寮忎笅锛岃繑鍥炲浘琛ㄥ拰鍙嫋鍔ㄥ璞?        return final_tg, fig, draggable_tg

def adjust_y_axis(ax, temperatures, densities, high_params, low_params, tg, margin=0.05, y_min=None, y_max=None):
    """鏍规嵁鎷熷悎绾垮拰鏁版嵁璋冩暣y杞磋寖鍥达紝绐佸嚭鏄剧ず娉㈠姩"""
    # 濡傛灉鎻愪緵浜嗘墜鍔ㄨ缃殑鑼冨洿锛岀洿鎺ュ簲鐢?    if y_min is not None and y_max is not None:
        ax.set_ylim(y_min, y_max)
        return
        
    # 璁＄畻鎷熷悎绾跨殑y鍊艰寖鍥?    t_min, t_max = min(temperatures), max(temperatures)
    
    # 鐢熸垚娓╁害閲囨牱鐐?    temp_range = np.linspace(t_min, t_max, 500)
    
    # 璁＄畻楂樻俯鍖哄煙鍜屼綆娓╁尯鍩熸嫙鍚堢嚎鐨勫€?    high_fit = np.polyval(high_params, temp_range[temp_range > tg])
    low_fit = np.polyval(low_params, temp_range[temp_range <= tg]) 
    
    # 缁勫悎鎵€鏈夊彲鑳界殑y鍊?    all_y_values = np.concatenate([densities, high_fit, low_fit])
    
    # 璁＄畻鏈€灏忓€煎拰鏈€澶у€硷紝绋嶅井鎵╁ぇ鑼冨洿
    y_min = np.min(all_y_values)
    y_max = np.max(all_y_values)
    
    # 璁＄畻涓績鍊?    y_center = (y_min + y_max) / 2
    
    # 璁＄畻鑼冨洿鐨勪竴鍗?    y_half_range = (y_max - y_min) / 2
    
    # 鎵╁睍鑼冨洿
    expanded_y_min = y_center - y_half_range * (1 + margin)
    expanded_y_max = y_center + y_half_range * (1 + margin)
    
    # 璁剧疆杞磋寖鍥?    ax.set_ylim(expanded_y_min, expanded_y_max)

def create_detailed_analysis(temperatures, densities, best_params, tg, sample_name=None, y_margin=0.05, y_min=None, y_max=None, language="cn", from_interactive=True, densities_columns=None, density_column_names=None):
    """
    鍒涘缓鐜荤拑鍖栬浆鍙樻俯搴︾殑璇︾粏鍒嗘瀽鍥?    
    鍙傛暟:
    temperatures - 娓╁害鏁版嵁鍒楄〃
    densities - 瀵嗗害鏁版嵁鍒楄〃 (涓昏瀵嗗害鍒?
    best_params - 鏈€浣虫嫙鍚堝弬鏁?(high_params, low_params)
    tg - 鐜荤拑鍖栬浆鍙樻俯搴?    sample_name - 鏍峰搧鍚嶇О
    y_margin - Y杞磋竟璺?    y_min - Y杞存渶灏忓€硷紙鎵嬪姩璁剧疆锛?    y_max - Y杞存渶澶у€硷紙鎵嬪姩璁剧疆锛?    language - 鏍囩璇█閫夋嫨锛?cn"涓轰腑鏂囷紝"en"涓鸿嫳鏂?    from_interactive - 鏄惁鏉ヨ嚜浜や簰寮忓垎鏋愮粨鏋?    densities_columns - 澶氬垪瀵嗗害鏁版嵁 (鍙€?
    density_column_names - 瀵嗗害鍒楀悕绉?(鍙€?
    """
    # 璇█閫夋嫨
    if language == "en":
        title_prefix = "Detailed Analysis"
        main_title = "Glass Transition Temperature Analysis"
        temp_title = "Temperature (K)"
        density_title = f"Density ({DENSITY_UNIT})"
        temp_density_title = "Temperature-Density"
        deriv_title = "First Derivative"
        secderiv_title = "Second Derivative"
        residual_title = "Residual Analysis"
        dataset_label = "Data Points"
        hightemp_label = "Liquid Region Fitting"
        lowtemp_label = "Glass Region Fitting"
        tg_label = f"Tg = {tg:.1f}K"
        residual_label = "Residuals"
        density_diff_title = "Density Difference"
        deviation_from_fit = "Deviation from fitted line"
        error_distribution = "Error Distribution"
        count = "Count"
        method1_text = f"Method 1 (Dual-line): Tg = {tg:.1f}K"
        plot_title_format = "for {}"
        from_interactive_text = "(from Interactive Analysis)" if from_interactive else ""
        multi_density_text = "Multi-column Density Data"
    else:  # 榛樿涓枃
        title_prefix = "璇︾粏鍒嗘瀽"
        main_title = "鐜荤拑鍖栬浆鍙樻俯搴﹀垎鏋?
        temp_title = "娓╁害 (K)"
        density_title = f"瀵嗗害 ({DENSITY_UNIT})"
        temp_density_title = "娓╁害-瀵嗗害鍏崇郴"
        deriv_title = "涓€闃跺鏁?
        secderiv_title = "浜岄樁瀵兼暟"
        residual_title = "娈嬪樊鍒嗘瀽"
        dataset_label = "鏁版嵁鐐?
        hightemp_label = "娑蹭綋鍖烘嫙鍚?
        lowtemp_label = "鐜荤拑鍖烘嫙鍚?
        tg_label = f"Tg = {tg:.1f}K"
        residual_label = "娈嬪樊"
        density_diff_title = "瀵嗗害宸紓"
        deviation_from_fit = "涓庢嫙鍚堢嚎鐨勫亸宸?
        error_distribution = "璇樊鍒嗗竷"
        count = "璁℃暟"
        method1_text = f"鏂规硶1锛堝弻绾挎嫙鍚堟硶锛? Tg = {tg:.1f}K"
        plot_title_format = "锛坽}锛?
        from_interactive_text = "(鏉ヨ嚜浜や簰寮忓垎鏋?" if from_interactive else ""
        multi_density_text = "澶氬垪瀵嗗害鏁版嵁"
    
    # 璁剧疆鏍峰搧鍚嶇О
    if sample_name is None:
        sample_name = "鏍峰搧" if language == "cn" else "Sample"
    
    # 鍒涘缓鎷熷悎绾?    high_params, low_params = best_params
    
    # 妫€鏌ユ槸鍚﹀瓨鍦ㄥ鍒楀瘑搴︽暟鎹?    has_multi_density = densities_columns is not None and len(densities_columns) > 0
    
    # 濡傛灉鏈夊鍒楀瘑搴︽暟鎹紝璋冩暣鍥捐〃甯冨眬
    if has_multi_density:
        plt.figure(figsize=(15, 12), dpi=100)  # 澧炲姞鍥捐〃楂樺害
        plt.suptitle(f"{title_prefix}: {main_title} {from_interactive_text} {plot_title_format.format(sample_name)}", fontsize=16)
        
        # 浣跨敤3x2甯冨眬锛屽鍔犲鍒楀瘑搴﹀浘琛?        gs = gridspec.GridSpec(3, 2)
    else:
        # 鍘熷甯冨眬
        plt.figure(figsize=(15, 10), dpi=100)
        plt.suptitle(f"{title_prefix}: {main_title} {from_interactive_text} {plot_title_format.format(sample_name)}", fontsize=16)
        
        # 浣跨敤2x2甯冨眬
        gs = gridspec.GridSpec(2, 2)
    
    # 1. 娓╁害-瀵嗗害鍏崇郴鍥?    ax1 = plt.subplot(gs[0, 0])
    
    # 缁樺埗褰撳墠閫変腑鐨勫瘑搴﹀垪鏁版嵁
    ax1.scatter(temperatures, densities, color='#3182bd', s=40, alpha=0.7, label=dataset_label)
    
    # 鎷熷悎绾?    t_min, t_max = min(temperatures), max(temperatures)
    temp_range = np.linspace(t_min, t_max, 1000)
    
    # 楂樻俯鍖哄煙鎷熷悎绾?    high_fit = np.polyval(high_params, temp_range)
    ax1.plot(temp_range, high_fit, '-', color='#e41a1c', 
             linewidth=2, label=hightemp_label)
    
    # 浣庢俯鍖哄煙鎷熷悎绾?    low_fit = np.polyval(low_params, temp_range)
    ax1.plot(temp_range, low_fit, '-', color='#4daf4a', 
             linewidth=2, label=lowtemp_label)
    
    # 鏍囪鐜荤拑鍖栬浆鍙樻俯搴?    ax1.axvline(x=tg, color='black', linestyle='--', alpha=0.7)
    ax1.text(tg+5, min(densities) + 0.9*(max(densities)-min(densities)), 
            tg_label, fontsize=12, color='darkred', 
            bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5'))
    
    # 鏍煎紡璁剧疆
    ax1.set_xlabel(temp_title, fontsize=12)
    ax1.set_ylabel(density_title, fontsize=12)
    ax1.set_title(temp_density_title, fontsize=14)
    
    # 濡傛灉鎻愪緵浜嗘墜鍔╕杞磋寖鍥达紝搴旂敤瀹?    if y_min is not None and y_max is not None:
        ax1.set_ylim(y_min, y_max)
    else:
        # 璁剧疆y杞磋竟璺濇瘮渚嬶紝浣挎尝鍔ㄨ寖鍥存洿鍔犳槑鏄?        ax1.margins(y=y_margin)
    
    ax1.legend(loc='best')
    ax1.grid(True, linestyle='--', alpha=0.7)
    
    # 2. 瀵嗗害宸紓鍥?    ax2 = plt.subplot(gs[0, 1])
    
    # 璁＄畻鎷熷悎瀵嗗害
    fitted_densities = []
    for temp in temperatures:
        if temp > tg:
            fitted_densities.append(np.polyval(high_params, temp))
        else:
            fitted_densities.append(np.polyval(low_params, temp))
    
    # 璁＄畻宸紓
    density_diff = np.array(densities) - np.array(fitted_densities)
    
    # 缁樺埗宸紓鏁ｇ偣
    ax2.scatter(temperatures, density_diff, color='#ff7f00', s=40, alpha=0.7, label=residual_label)
    ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.5)
    ax2.axvline(x=tg, color='black', linestyle='--', alpha=0.7)
    
    # 鏍煎紡璁剧疆
    ax2.set_xlabel(temp_title, fontsize=12)
    ax2.set_ylabel(deviation_from_fit, fontsize=12)
    ax2.set_title(density_diff_title, fontsize=14)
    ax2.legend(loc='best')
    ax2.grid(True, linestyle='--', alpha=0.7)
    
    # 3. 涓€闃跺鏁板浘锛堝瘑搴﹀彉鍖栫巼锛?    ax3 = plt.subplot(gs[1, 0])
    
    # 璁＄畻瀵兼暟
    slopes = []
    mid_temps = []
    
    for i in range(len(temperatures)-1):
        temp_diff = temperatures[i] - temperatures[i+1]
        density_diff = densities[i+1] - densities[i]
        slope = density_diff / temp_diff
        slopes.append(slope)
        mid_temps.append((temperatures[i] + temperatures[i+1]) / 2)
    
    slopes = np.array(slopes)
    mid_temps = np.array(mid_temps)
    
    # 浣跨敤Savitzky-Golay婊ゆ尝鍣ㄥ钩婊戞枩鐜囦互鍑忓皯鍣０
    if len(slopes) > 5:
        window_length = min(5, len(slopes) - 2 if len(slopes) % 2 == 0 else len(slopes) - 1)
        smooth_slopes = savgol_filter(slopes, window_length, 2)
    else:
        smooth_slopes = slopes
    
    # 缁樺埗瀵兼暟
    ax3.scatter(mid_temps, slopes, color='#b2df8a', s=30, alpha=0.7, label=deviation_from_fit)
    ax3.plot(mid_temps, smooth_slopes, '-', color='#33a02c', linewidth=2, label=deviation_from_fit)
    ax3.axvline(x=tg, color='black', linestyle='--', alpha=0.7)
    
    # 鏍煎紡璁剧疆
    ax3.set_xlabel(temp_title, fontsize=12)
    ax3.set_ylabel(f"d{density_title}/d{temp_title}", fontsize=12)
    ax3.set_title(deriv_title, fontsize=14)
    ax3.grid(True, linestyle='--', alpha=0.7)
    
    # 4. 娈嬪樊鍒嗗竷鐩存柟鍥?    ax4 = plt.subplot(2, 2, 4)
    
    # 缁樺埗鐩存柟鍥?    ax4.hist(density_diff, bins=15, color='#e41a1c', alpha=0.7)
    
    # 鏍煎紡璁剧疆
    ax4.set_xlabel(deviation_from_fit, fontsize=12)
    ax4.set_ylabel(count, fontsize=12)
    ax4.set_title(error_distribution, fontsize=14)
    ax4.grid(True, linestyle='--', alpha=0.7)
    
    # 鏄剧ず鏂规硶
    plt.figtext(0.5, 0.01, method1_text, 
               ha='center', fontsize=12, 
               bbox=dict(facecolor='#e6f5c9', alpha=0.8, boxstyle='round,pad=0.5'))
    
    # 璋冩暣甯冨眬
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    
    return plt.gcf()

def save_results_to_file(temperatures, densities, tg, output_path=None, language="cn"):
    """
    灏嗗垎鏋愮粨鏋滀繚瀛樺埌鏂囦欢涓?    
    鍙傛暟:
    temperatures - 娓╁害鏁版嵁鍒楄〃
    densities - 瀵嗗害鏁版嵁鍒楄〃
    tg - 璁＄畻寰楀埌鐨勭幓鐠冨寲杞彉娓╁害
    output_path - 杈撳嚭鏂囦欢璺緞锛岄粯璁や负褰撳墠鐩綍涓嬬殑 tg_results.csv
    language - 鏍囩璇█閫夋嫨锛?cn"涓轰腑鏂囷紝"en"涓鸿嫳鏂?    """
    if output_path is None:
        base_dir = os.path.dirname(os.path.abspath(__file__))
        output_path = os.path.join(base_dir, 'tg_results.csv')
    
    # 鍒涘缓缁撴灉DataFrame
    results_df = pd.DataFrame({
        'Temperature(K)': temperatures,
        'Density(g/cm3)': densities
    })
    
    # 娣诲姞棰濆鐨勭粨鏋滀俊鎭?    high_temp_mask = temperatures > tg
    low_temp_mask = temperatures <= tg
    
    # 鏍囪鍖哄煙
    results_df['Region'] = ['娑蹭綋' if temp > tg else '鐜荤拑' for temp in temperatures]
    
    # 淇濆瓨缁撴灉
    results_df.to_csv(output_path, index=False, encoding='utf-8-sig')
    print(f"鍒嗘瀽缁撴灉宸蹭繚瀛樿嚦 {output_path}")

class TgAnalysisApp:
    """
    鐜荤拑鍖栬浆鍙樻俯搴﹀垎鏋怗UI搴旂敤
    """
    def __init__(self, root):
        self.root = root
        self.root.title("鐜荤拑鍖栬浆鍙樻俯搴﹀垎鏋愬伐鍏?)
        self.root.geometry("1000x750")  # 鏇村ぇ鐨勯粯璁ょ獥鍙ｅ昂瀵?        self.root.minsize(900, 700)     # 澧炲姞鏈€灏忕獥鍙ｅ昂瀵?        
        # 璁剧疆搴旂敤椋庢牸
        style = ttk.Style()
        style.theme_use('clam')  # 浣跨敤杈冪幇浠ｇ殑涓婚
        
        # 璇█璁剧疆
        self.language_var = tk.StringVar(value="cn")  # 榛樿涓枃
        
        # 鍒涘缓涓绘鏋?- 浣跨敤Canvas鍜孲crollbar鏀寔婊氬姩
        self.main_canvas = tk.Canvas(self.root, borderwidth=0, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=self.main_canvas.yview)
        self.scrollable_frame = ttk.Frame(self.main_canvas, width=900)  # 璁剧疆鏈€灏忓搴?        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.main_canvas.configure(
                scrollregion=self.main_canvas.bbox("all")
            )
        )
        
        # 璁剧疆Canvas澶у皬闅忕潃绐楀彛鍙樺寲
        self.root.bind("<Configure>", self._on_window_resize)
        
        # 浣挎粴鍔ㄦ鏋跺～婊anvas鐨勬暣涓搴?        self.main_canvas.bind("<Configure>", 
                             lambda e: self.main_canvas.itemconfig(self.frame_id, width=e.width))
        
        # 鍒涘缓绐楀彛骞朵繚瀛業D浠ヤ究鍚庣画璋冩暣澶у皬
        self.frame_id = self.main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.main_canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # 浣跨敤grid甯冨眬绠＄悊鍣ㄤ唬鏇縫ack锛屼娇鐣岄潰濉弧鏁翠釜绐楀彛
        self.main_canvas.grid(row=0, column=0, sticky="nsew")
        self.scrollbar.grid(row=0, column=1, sticky="ns")
        
        # 璁剧疆缃戞牸鐨勬潈閲嶏紝浣夸富鐢诲竷鍙互鎵╁睍
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # 鍒涘缓椤堕儴妗嗘灦
        top_header_frame = ttk.Frame(self.scrollable_frame)
        top_header_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        # 鍒涘缓鏍囬
        title_label = ttk.Label(top_header_frame, text="鐜荤拑鍖栬浆鍙樻俯搴?Tg)鍒嗘瀽绋嬪簭", font=("SimHei", 16, "bold"))
        title_label.pack(side=tk.LEFT, pady=10)
        
        # 娣诲姞璇█鍒囨崲鎸夐挳
        self.lang_button = ttk.Button(top_header_frame, text="English", 
                                    command=self.toggle_language, width=10)
        self.lang_button.pack(side=tk.RIGHT, padx=10, pady=10)
        
        # 鍒涘缓涓婂崐閮ㄥ垎妗嗘灦 - 鏁版嵁杈撳叆鍜屾帶鍒?        self.top_frame = ttk.Frame(self.scrollable_frame)
        self.top_frame.pack(fill=tk.X, expand=False, pady=5)
        
        # 鏂囦欢閫夋嫨鍖哄煙
        self.file_frame = ttk.LabelFrame(self.top_frame, text="鏁版嵁鏂囦欢閫夋嫨", padding=5)
        self.file_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(self.file_frame, textvariable=self.file_path_var, width=50)
        self.file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        self.browse_button = ttk.Button(self.file_frame, text="娴忚...", command=self.browse_file)
        self.browse_button.pack(side=tk.LEFT, padx=5)
        
        self.load_button = ttk.Button(self.file_frame, text="鍔犺浇鏁版嵁", command=self.load_data)
        self.load_button.pack(side=tk.LEFT, padx=5)
        
        # 鏍峰搧淇℃伅鍖哄煙
        self.sample_frame = ttk.LabelFrame(self.top_frame, text="鏍峰搧淇℃伅", padding=5)
        self.sample_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        ttk.Label(self.sample_frame, text="鏍峰搧鍚嶇О:").pack(side=tk.LEFT, padx=5)
        self.sample_name_var = tk.StringVar(value="鏍峰搧")
        self.sample_entry = ttk.Entry(self.sample_frame, textvariable=self.sample_name_var, width=20)
        self.sample_entry.pack(side=tk.LEFT, padx=5)
        
        # 娣诲姞瀵嗗害鍒楅€夋嫨鍖哄煙
        self.density_column_frame = ttk.Frame(self.sample_frame)
        self.density_column_frame.pack(side=tk.LEFT, padx=5)
        ttk.Label(self.density_column_frame, text="瀵嗗害鍒?").pack(side=tk.LEFT, padx=5)
        self.density_column_var = tk.StringVar()
        self.density_column_combobox = ttk.Combobox(self.density_column_frame, textvariable=self.density_column_var, width=20, state="readonly")
        self.density_column_combobox.pack(side=tk.LEFT, padx=5)
        
        # 鏁版嵁棰勮鍖哄煙 - 鍑忓皬楂樺害
        self.data_frame = ttk.LabelFrame(self.scrollable_frame, text="鏁版嵁棰勮", padding=5)
        self.data_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        # 鍒涘缓琛ㄦ牸
        self.tree_frame = ttk.Frame(self.data_frame)
        self.tree_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        self.tree_scroll = ttk.Scrollbar(self.tree_frame)
        self.tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 璁剧疆杈冨皬楂樺害鐨凾reeview
        self.tree = ttk.Treeview(self.tree_frame, yscrollcommand=self.tree_scroll.set, height=5)
        self.tree.pack(fill=tk.X, expand=False)
        
        self.tree_scroll.config(command=self.tree.yview)
        
        # 璁剧疆鍒?        self.tree["columns"] = ("index", "temperature", "density")
        self.tree.column("#0", width=0, stretch=tk.NO)
        self.tree.column("index", anchor=tk.CENTER, width=50, stretch=tk.NO)
        self.tree.column("temperature", anchor=tk.CENTER, width=120, stretch=tk.YES)
        self.tree.column("density", anchor=tk.CENTER, width=120, stretch=tk.YES)
        
        self.tree.heading("#0", text="", anchor=tk.CENTER)
        self.tree.heading("index", text="搴忓彿", anchor=tk.CENTER)
        self.tree.heading("temperature", text="娓╁害 (K)", anchor=tk.CENTER)
        self.tree.heading("density", text=f"瀵嗗害 ({DENSITY_UNIT})", anchor=tk.CENTER)
        
        # 鍒涘缓鏁版嵁鍙鍖栭瑙?- 鍑忓皬楂樺害
        self.preview_frame = ttk.LabelFrame(self.scrollable_frame, text="鏁版嵁鍙鍖栭瑙?, padding=5)
        self.preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5, ipady=100)  # 淇敼涓築OTH鍜孴rue
        
        # 娣诲姞澶氬垪瀵嗗害琛ㄦ牸鏄剧ず鍖哄煙
        self.multi_density_frame = ttk.LabelFrame(self.scrollable_frame, text="澶氬垪瀵嗗害鏁版嵁", padding=5)
        # 鍒濆涓嶆樉绀烘妗嗘灦锛屼粎褰撴娴嬪埌澶氬垪瀵嗗害鏃舵墠鏄剧ず
        
        # 鍒嗘瀽鎺у埗鍖哄煙
        self.control_frame = ttk.LabelFrame(self.scrollable_frame, text="鎿嶄綔鎺у埗", padding=10)
        self.control_frame.pack(fill=tk.X, expand=False, padx=5, pady=10)
        
        # 浣跨敤鏇村ぇ鐨勬寜閽紝渚夸簬鐪嬪埌
        # 娣诲姞浜や簰寮忓垎鏋愭寜閽紝鏀惧湪鏈€宸﹁竟锛屼綔涓轰富瑕佹搷浣滄寜閽?        self.interactive_button = ttk.Button(self.control_frame, text="浜や簰寮忓垎鏋?, 
                                            command=self.interactive_analyze,
                                            style='Big.TButton')
        self.interactive_button.pack(side=tk.LEFT, padx=10, pady=10)
        
        self.analyze_button = ttk.Button(self.control_frame, text="纭鍒嗘瀽", command=self.analyze_data, 
                                         style='Big.TButton')
        self.analyze_button.pack(side=tk.LEFT, padx=10, pady=10)
        
        self.save_button = ttk.Button(self.control_frame, text="淇濆瓨缁撴灉", command=self.save_results,
                                     style='Big.TButton')
        self.save_button.pack(side=tk.LEFT, padx=10, pady=10)
        
        self.save_data_button = ttk.Button(self.control_frame, text="淇濆瓨鏁版嵁", command=self.save_data,
                                     style='Big.TButton')
        self.save_data_button.pack(side=tk.LEFT, padx=10, pady=10)
        
        self.detailed_var = tk.BooleanVar(value=False)
        self.detailed_check = ttk.Checkbutton(self.control_frame, text="鐢熸垚璇︾粏鍒嗘瀽", 
                                              variable=self.detailed_var)
        self.detailed_check.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 娣诲姞澶у瓧浣撶殑鐘舵€佹樉绀?        self.status_frame = ttk.Frame(self.scrollable_frame)
        self.status_frame.pack(fill=tk.X, expand=False, padx=5, pady=10)
        
        self.status_var = tk.StringVar()
        self.status_var.set("鍑嗗灏辩华 - 璇烽€夋嫨鏁版嵁鏂囦欢")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var, 
                                     font=("SimHei", 12, "bold"))
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 娣诲姞浜や簰寮忓浘琛ㄦ鏋?        self.interactive_frame = ttk.LabelFrame(self.scrollable_frame, text="浜や簰寮廡g鍒嗘瀽", padding=5)
        # interactive_frame浼氬湪interactive_analyze鏂规硶涓皟鐢ㄦ椂鎵嶆坊鍔犲埌鐣岄潰
        
        # 鍒涘缓澶ф寜閽牱寮?        style.configure('Big.TButton', font=('SimHei', 12))
        
        # 鍒濆鍖栨暟鎹?        self.temperatures = []
        self.densities = []
        self.densities_columns = []  # 澶氬垪瀵嗗害鏁版嵁
        self.is_multi_density = False  # 鏄惁涓哄鍒楀瘑搴︽暟鎹爣蹇?        self.density_column_names = []  # 瀵嗗害鍒楀悕绉?        self.active_density_column = 0  # 褰撳墠閫夋嫨鐨勫瘑搴﹀垪绱㈠紩
        self.tg_result = None
        self.data_loaded = False
        self.preview_canvas = None
        self.interactive_canvas = None
        self.interactive_fig = None
        self.interactive_draggable = None
        self.toolbar = None
        
        # 缁戝畾榧犳爣婊氳疆浜嬩欢
        if sys.platform.startswith('win'):
            self.root.bind("<MouseWheel>", self._on_mousewheel)
        elif sys.platform == 'darwin':
            self.root.bind("<MouseWheel>", self._on_mousewheel)
        else:
            self.root.bind("<Button-4>", self._on_mousewheel)
            self.root.bind("<Button-5>", self._on_mousewheel)
        
        # 缁戝畾瀵嗗害鍒楅€夋嫨浜嬩欢
        self.density_column_combobox.bind("<<ComboboxSelected>>", self.on_density_column_changed)
    
    def on_density_column_changed(self, event):
        """澶勭悊瀵嗗害鍒楅€夋嫨鍙樻洿浜嬩欢"""
        if not self.is_multi_density or not self.data_loaded:
            return
            
        selected = self.density_column_var.get()
        if selected:
            # 鏌ユ壘閫変腑鐨勫瘑搴﹀垪绱㈠紩
            for i, name in enumerate(self.density_column_names):
                if name == selected:
                    self.active_density_column = i
                    break
            
            # 鏇存柊褰撳墠浣跨敤鐨勫瘑搴︽暟鎹?            self.densities = self.densities_columns[self.active_density_column]
            
            # 鏇存柊琛ㄦ牸鏄剧ず
            self.update_data_preview()
            
            # 鏇存柊鍥捐〃棰勮
            self.create_preview_plot()
            
            # 娓呴櫎涔嬪墠鐨勪氦浜掑紡鍒嗘瀽缁撴灉
            if hasattr(self, 'interactive_fig') and self.interactive_fig is not None:
                plt.close(self.interactive_fig)
                self.interactive_fig = None
                self.interactive_draggable = None
    
    def load_data(self):
        """鍔犺浇鏁版嵁鏂囦欢"""
        filepath = self.file_path_var.get().strip()
        if not filepath:
            messagebox.showerror("閿欒", "璇峰厛閫夋嫨鏁版嵁鏂囦欢")
            return
            
        try:
            self.status_var.set("姝ｅ湪鍔犺浇鏁版嵁...")
            self.root.update()
            
            # 璇诲彇鏁版嵁锛岀幇鍦ㄦ敮鎸佸鍒楀瘑搴?            result = read_data_from_file(filepath)
            if len(result) == 3:  # 鏂扮殑杩斿洖鏍煎紡鍖呭惈澶氬垪瀵嗗害鏍囧織
                self.temperatures, data, self.is_multi_density = result
                
                if self.is_multi_density:
                    self.densities_columns = data  # 澶氬垪瀵嗗害鏁版嵁
                    self.density_column_names = []
                    
                    # 鎻愬彇鏂囦欢涓殑瀵嗗害鍒楀悕绉?                    file_ext = os.path.splitext(filepath)[1].lower()
                    if file_ext == '.csv':
                        df = pd.read_csv(filepath)
                    elif file_ext in ['.xls', '.xlsx']:
                        df = pd.read_excel(filepath)
                    
                    # 甯歌鐨勬俯搴﹀垪鍚?                    temp_patterns = ['Temperature', 'Temp', 'T', '娓╁害']
                    # 鏌ユ壘闈炴俯搴﹀垪浣滀负瀵嗗害鍒?                    for col in df.columns:
                        if not any(pattern.lower() in col.lower() for pattern in temp_patterns):
                            self.density_column_names.append(col)
                    
                    # 濡傛灉娌℃湁鎵惧埌瀵嗗害鍒楀悕绉帮紝鐢熸垚榛樿鍒楀悕
                    if len(self.density_column_names) == 0:
                        self.density_column_names = [f"瀵嗗害{i+1}" for i in range(len(self.densities_columns))]
                    
                    # 璁剧疆榛樿浣跨敤绗竴鍒楀瘑搴︽暟鎹?                    self.active_density_column = 0
                    self.densities = self.densities_columns[self.active_density_column]
                    
                    # 鏇存柊瀵嗗害鍒楅€夋嫨涓嬫媺妗?                    self.density_column_combobox['values'] = self.density_column_names
                    self.density_column_var.set(self.density_column_names[0])
                    
                    # 鏄剧ず澶氬垪瀵嗗害琛ㄦ牸
                    self.display_multi_density_table()
                else:
                    self.densities = data  # 鍗曞垪瀵嗗害鏁版嵁
            else:
                # 鍏煎鏃х殑杩斿洖鏍煎紡
                self.temperatures, self.densities = result
                self.is_multi_density = False
            
            self.data_loaded = True
            
            # 鏇存柊鏁版嵁棰勮琛ㄦ牸
            self.update_data_preview()
            
            # 鏇存柊鐘舵€?            self.status_var.set("宸插姞杞?{} 涓暟鎹偣 - 鐐瑰嚮'鍒嗘瀽鏁版嵁'缁х画".format(len(self.temperatures)))
            
            # 鐢熸垚棰勮鍥?            self.create_preview_plot()
            
        except Exception as e:
            messagebox.showerror("閿欒", f"鍔犺浇鏁版嵁澶辫触: {str(e)}")
            self.status_var.set("鏁版嵁鍔犺浇澶辫触")
    
    def update_data_preview(self):
        """鏇存柊鏁版嵁棰勮琛ㄦ牸"""
        # 娓呯┖鐜版湁琛ㄦ牸
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 濉厖琛ㄦ牸
        for i, (temp, dens) in enumerate(zip(self.temperatures, self.densities)):
            self.tree.insert("", tk.END, values=(i+1, f"{temp:.1f}", f"{dens:.4f}"))
    
    def display_multi_density_table(self):
        """鏄剧ず澶氬垪瀵嗗害鏁版嵁琛ㄦ牸"""
        # 娓呯┖澶氬垪瀵嗗害妗嗘灦鍐呭
        for widget in self.multi_density_frame.winfo_children():
            widget.destroy()
        
        # 鍒涘缓琛ㄦ牸
        columns = ["娓╁害(K)"] + self.density_column_names
        
        # 浣跨敤ttk.Treeview鍒涘缓澶氬垪琛ㄦ牸
        multi_tree_frame = ttk.Frame(self.multi_density_frame)
        multi_tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 娣诲姞鍨傜洿婊氬姩鏉?        vsb = ttk.Scrollbar(multi_tree_frame, orient="vertical")
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 娣诲姞姘村钩婊氬姩鏉?        hsb = ttk.Scrollbar(multi_tree_frame, orient="horizontal")
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 鍒涘缓鏍戠姸琛ㄦ牸
        tree_columns = tuple(f"#{i}" for i in range(len(columns)))
        multi_tree = ttk.Treeview(multi_tree_frame, columns=tree_columns, 
                                 yscrollcommand=vsb.set, xscrollcommand=hsb.set,
                                 height=10)
        
        # 璁剧疆婊氬姩鏉?        vsb.configure(command=multi_tree.yview)
        hsb.configure(command=multi_tree.xview)
        
        # 璁剧疆鍒楀鍜屽垪鍚?        multi_tree.column("#0", width=0, stretch=tk.NO)
        for i, col in enumerate(columns):
            col_id = f"#{i}"
            multi_tree.column(col_id, anchor=tk.CENTER, width=120, stretch=tk.YES)
            multi_tree.heading(col_id, text=col, anchor=tk.CENTER)
        
        # 濉厖鏁版嵁
        for i, temp in enumerate(self.temperatures):
            values = [f"{temp:.1f}"]
            for col_densities in self.densities_columns:
                values.append(f"{col_densities[i]:.4f}")
            multi_tree.insert("", tk.END, values=values)
        
        multi_tree.pack(fill=tk.BOTH, expand=True)
        
        # 鐜板湪鏄剧ず澶氬垪瀵嗗害妗嗘灦
        self.multi_density_frame.pack(fill=tk.BOTH, expand=False, padx=5, pady=5)
    
    def create_preview_plot(self):
        """鍒涘缓娓╁害-瀵嗗害鏁版嵁鐨勯瑙堝浘"""
        if not self.data_loaded or len(self.temperatures) == 0:
            return
        
        # 娓呴櫎棰勮妗嗘灦涓殑鍐呭
        for widget in self.preview_frame.winfo_children():
            widget.destroy()
        
        # 鍒涘缓棰勮鍥撅紝浣跨敤鏇村ぇ鐨勫昂瀵?        fig, ax = plt.subplots(figsize=(10, 5), dpi=100)
        
        # 浣跨敤鍏呭垎鍒╃敤鍙敤瀹藉害鐨凢rame
        canvas_frame = ttk.Frame(self.preview_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # 浣跨敤褰撳墠璇█璁剧疆
        current_lang = self.language_var.get()
        if current_lang == "en":
            xlabel = "Temperature (K)"
            ylabel = f"Density ({DENSITY_UNIT})"
            title = "Temperature-Density Preview"
        else:
            xlabel = "娓╁害 (K)"
            ylabel = f"瀵嗗害 ({DENSITY_UNIT})"
            title = "娓╁害-瀵嗗害鏁版嵁棰勮"
        
        # 鏍规嵁鏄惁涓哄鍒楀瘑搴︽暟鎹喅瀹氱粯鍥炬柟寮?        if self.is_multi_density and hasattr(self, 'densities_columns') and len(self.densities_columns) > 0:
            # 浣跨敤涓嶅悓棰滆壊缁樺埗澶氬垪瀵嗗害鏁版嵁
            colors = plt.cm.tab10.colors  # 浣跨敤tab10鑹插僵鏄犲皠鑾峰彇涓嶅悓棰滆壊
            
            # 缁樺埗鎵€鏈夊瘑搴﹀垪鏁版嵁
            for i, densities in enumerate(self.densities_columns):
                color_idx = i % len(colors)  # 闃叉绱㈠紩瓒呭嚭鑼冨洿
                label = self.density_column_names[i] if i < len(self.density_column_names) else f"瀵嗗害{i+1}"
                ax.scatter(self.temperatures, densities, 
                          color=colors[color_idx], alpha=0.7, 
                          label=label, s=40, marker='o')
            
            # 楂樹寒鏄剧ず褰撳墠閫変腑鐨勫瘑搴﹀垪
            ax.scatter(self.temperatures, self.densities_columns[self.active_density_column], 
                       color=colors[self.active_density_column % len(colors)], 
                       s=60, marker='o', edgecolor='black', linewidth=1.5,
                       label=f"{self.density_column_names[self.active_density_column]} (褰撳墠閫変腑)")
                       
            # 娣诲姞鍥句緥
            ax.legend(loc='best', fontsize=9)
        else:
            # 鍗曞垪瀵嗗害鏁版嵁浣跨敤榛樿钃濊壊
            ax.scatter(self.temperatures, self.densities, color='#3182bd', alpha=0.8)
        
        ax.set_xlabel(xlabel)
        ax.set_ylabel(ylabel)
        ax.set_title(title)
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 璋冩暣鍥惧舰浠ュ～婊＄獥鍙?        fig.tight_layout()
        
        # 灏嗗浘琛ㄥ祵鍏ュ埌Tkinter鐣岄潰锛屼娇鐢╢ill=BOTH鍜宔xpand=True
        canvas = FigureCanvasTkAgg(fig, master=canvas_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 娣诲姞宸ュ叿鏍忎互鍏佽鐢ㄦ埛涓庡浘琛ㄤ氦浜?        toolbar = NavigationToolbar2Tk(canvas, canvas_frame)
        toolbar.update()
        
        # 淇濆瓨鐢诲竷瀵硅薄渚涘悗缁娇鐢?        self.preview_canvas = canvas
    
    def analyze_data(self):
        """鍒嗘瀽鏁版嵁骞剁敓鎴愮幓鐠冨寲杞彉娓╁害缁撴灉"""
        if not self.data_loaded or len(self.temperatures) == 0:
            messagebox.showerror("閿欒" if self.language_var.get() == "cn" else "Error", 
                              "璇峰厛鍔犺浇鏈夋晥鏁版嵁" if self.language_var.get() == "cn" else "Please load valid data first")
            return
            
        try:
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
            
            # 鏇存柊鐘舵€?            self.status_var.set("姝ｅ湪鍒嗘瀽鏁版嵁..." if self.language_var.get() == "cn" else "Analyzing data...")
            self.root.update()
            
            # 鍒涘缓鍒嗘瀽鍥捐〃
            plt.figure(figsize=(8, 6), dpi=100)
            
            # 鑾峰彇Y杞磋缃紙濡傛灉鏈夛級
            y_margin = self.y_margin_var.get() if hasattr(self, 'y_margin_var') else 0.05
            y_min = None
            y_max = None
            
            if hasattr(self, 'enable_manual_range') and self.enable_manual_range.get():
                try:
                    y_min = float(self.y_min_var.get())
                    y_max = float(self.y_max_var.get())
                except:
                    pass
            
            # 妫€鏌ユ槸鍚﹀凡鏈変氦浜掑紡鍒嗘瀽缁撴灉
            if hasattr(self, 'interactive_draggable') and self.interactive_draggable:
                # 鐩存帴浣跨敤浜や簰寮忓垎鏋愮殑Tg鍊?                self.tg_result = self.interactive_draggable.x
                
                # 鍒涘缓涓庝氦浜掑紡鍒嗘瀽鐩稿悓鍙傛暟鐨勫垎鏋愬浘琛紙浣嗕笉鏄氦浜掑紡鐨勶級
                plt.figure(figsize=(8, 6), dpi=100)
                find_glass_transition_temperature(
                    self.temperatures, self.densities, 
                    sample_name=sample_name,
                    interactive=False,
                    y_margin=y_margin,
                    y_min=y_min,
                    y_max=y_max,
                    language=self.language_var.get()
                )
                
                # 鐢熸垚璇︾粏鍒嗘瀽锛堝鏋滈€変腑锛?                if self.detailed_var.get():
                    # 浠庝氦浜掑紡鍒嗘瀽涓幏鍙栨嫙鍚堝弬鏁?                    high_params = self.interactive_draggable.cached_high_params
                    low_params = self.interactive_draggable.cached_low_params
                    
                    if high_params and low_params:
                        best_params = (high_params, low_params)
                        # 鍒涘缓璇︾粏鍒嗘瀽锛屼紶閫掑鍒楀瘑搴︽暟鎹紙濡傛灉鏈夛級
                        if self.is_multi_density and hasattr(self, 'densities_columns') and len(self.densities_columns) > 0:
                            create_detailed_analysis(
                                self.temperatures, self.densities,
                                best_params, self.tg_result,
                                sample_name=sample_name,
                                y_margin=y_margin,
                                y_min=y_min,
                                y_max=y_max,
                                language=self.language_var.get(),
                                from_interactive=True,
                                densities_columns=self.densities_columns,
                                density_column_names=self.density_column_names
                            )
                        else:
                            create_detailed_analysis(
                                self.temperatures, self.densities,
                                best_params, self.tg_result,
                                sample_name=sample_name,
                                y_margin=y_margin,
                                y_min=y_min,
                                y_max=y_max,
                                language=self.language_var.get(),
                                from_interactive=True
                            )
                        plt.figure()  # 鍒涘缓鏂板浘褰互鏄剧ず璇︾粏鍒嗘瀽
                
                # 鏄剧ず鍥捐〃
                plt.show()
            else:
                # 娌℃湁浜や簰寮忓垎鏋愮粨鏋滐紝鎵ц姝ｅ父鍒嗘瀽
                self.tg_result = find_glass_transition_temperature(
                    self.temperatures, self.densities, 
                    sample_name=sample_name,
                    language=self.language_var.get()
                )
            
            # 鏇存柊鐘舵€?            self.status_var.set(f"鍒嗘瀽瀹屾垚! Tg = {self.tg_result:.1f}K" if self.language_var.get() == "cn" 
                             else f"Analysis complete! Tg = {self.tg_result:.1f}K")
            
            # 鏄剧ず缁撴灉
            messagebox.showinfo(
                "鍒嗘瀽缁撴灉" if self.language_var.get() == "cn" else "Analysis Result", 
                f"鐜荤拑鍖栬浆鍙樻俯搴?Tg): {self.tg_result:.1f}K" if self.language_var.get() == "cn" 
                else f"Glass Transition Temperature (Tg): {self.tg_result:.1f}K"
            )
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                f"鍒嗘瀽杩囩▼涓嚭閿? {str(e)}" if self.language_var.get() == "cn" 
                else f"Error during analysis: {str(e)}"
            )

    def interactive_analyze(self):
        """鍚姩浜や簰寮忓垎鏋愭ā寮?""
        if not self.data_loaded or len(self.temperatures) == 0:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                "璇峰厛鍔犺浇鏈夋晥鏁版嵁" if self.language_var.get() == "cn" else "Please load valid data first"
            )
            return
            
        try:
            self.status_var.set(
                "姝ｅ湪鍒涘缓浜や簰寮忓垎鏋?.." if self.language_var.get() == "cn" 
                else "Creating interactive analysis..."
            )
            self.root.update()
            
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
            
            # 纭繚浜や簰寮忔鏋跺凡鏄剧ず
            if not hasattr(self, 'interactive_frame_visible') or not self.interactive_frame_visible:
                self.interactive_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
                self.interactive_frame_visible = True
            
            # 娓呯┖浜や簰寮忔鏋?            for widget in self.interactive_frame.winfo_children():
                widget.destroy()
            
            # 鍒涘缓Y杞存帶鍒跺尯鍩?            y_control_frame = ttk.LabelFrame(
                self.interactive_frame, 
                text="Y杞存樉绀烘帶鍒? if self.language_var.get() == "cn" else "Y-axis Display Control", 
                padding=5
            )
            y_control_frame.pack(fill=tk.X, pady=5)
            
            # 鑷姩缂╂斁鎺у埗鍖哄煙
            auto_scale_frame = ttk.Frame(y_control_frame)
            auto_scale_frame.pack(fill=tk.X, pady=5)
            
            ttk.Label(
                auto_scale_frame, 
                text="鑷姩缂╂斁杈硅窛:" if self.language_var.get() == "cn" else "Auto Scale Margin:"
            ).pack(side=tk.LEFT, padx=5)
            
            # Y杞磋竟璺濇帶鍒舵粦鍧?            self.y_margin_var = tk.DoubleVar(value=0.05)
            y_margin_scale = ttk.Scale(auto_scale_frame, from_=0.01, to=0.5, 
                                       variable=self.y_margin_var, length=200, 
                                       orient=tk.HORIZONTAL)
            y_margin_scale.pack(side=tk.LEFT, padx=5)
            
            # 褰撳墠鍊兼樉绀?            y_margin_label = ttk.Label(auto_scale_frame, text="0.05")
            y_margin_label.pack(side=tk.LEFT, padx=5)
            
            # 鏇存柊婊戝潡鏍囩鐨勫嚱鏁?            def update_margin_label(*args):
                y_margin_label.config(text=f"{self.y_margin_var.get():.2f}")
            
            # 缁戝畾婊戝潡鍊煎彉鍖栦簨浠?            self.y_margin_var.trace_add("write", update_margin_label)
            
            # 鎵嬪姩鑼冨洿鎺у埗鍖哄煙
            manual_range_frame = ttk.Frame(y_control_frame)
            manual_range_frame.pack(fill=tk.X, pady=5)
            
            # 鍚敤鎵嬪姩鑼冨洿閫夐」
            self.enable_manual_range = tk.BooleanVar(value=False)
            manual_check = ttk.Checkbutton(
                manual_range_frame, 
                text="鍚敤鎵嬪姩Y杞磋寖鍥? if self.language_var.get() == "cn" else "Enable Manual Y-axis Range", 
                variable=self.enable_manual_range
            )
            manual_check.pack(side=tk.LEFT, padx=5)
            
            # Y杞存渶灏忓€?            ttk.Label(
                manual_range_frame, 
                text="Y杞存渶灏忓€?" if self.language_var.get() == "cn" else "Y-axis Min:"
            ).pack(side=tk.LEFT, padx=5)
            self.y_min_var = tk.StringVar()
            y_min_entry = ttk.Entry(manual_range_frame, textvariable=self.y_min_var, width=8)
            y_min_entry.pack(side=tk.LEFT, padx=5)
            
            # Y杞存渶澶у€?            ttk.Label(
                manual_range_frame, 
                text="Y杞存渶澶у€?" if self.language_var.get() == "cn" else "Y-axis Max:"
            ).pack(side=tk.LEFT, padx=5)
            self.y_max_var = tk.StringVar()
            y_max_entry = ttk.Entry(manual_range_frame, textvariable=self.y_max_var, width=8)
            y_max_entry.pack(side=tk.LEFT, padx=5)
            
            # 鑾峰彇榛樿鐨刌杞磋寖鍥?(澶х害鏈€灏忓€煎拰鏈€澶у€?
            y_min_default = min(self.densities) - 0.1
            y_max_default = max(self.densities) + 0.1
            self.y_min_var.set(f"{y_min_default:.3f}")
            self.y_max_var.set(f"{y_max_default:.3f}")
            
            # 鍒锋柊鎸夐挳
            refresh_button = ttk.Button(
                y_control_frame, 
                text="搴旂敤璁剧疆" if self.language_var.get() == "cn" else "Apply Settings", 
                command=self.refresh_interactive_plot
            )
            refresh_button.pack(side=tk.RIGHT, padx=10)
            
            # 鍒涘缓浜や簰寮廡g鍒嗘瀽
            self.tg_result, self.interactive_fig, self.interactive_draggable = find_glass_transition_temperature(
                self.temperatures, self.densities, 
                sample_name=sample_name,
                interactive=True,
                y_margin=self.y_margin_var.get(),
                language=self.language_var.get()
            )
            
            # 鍒涘缓鍥捐〃瀹瑰櫒妗嗘灦锛岀‘淇濆～婊℃暣涓搴?            chart_frame = ttk.Frame(self.interactive_frame)
            chart_frame.pack(fill=tk.BOTH, expand=True, pady=5)
            
            # 浼樺寲鍥捐〃甯冨眬
            self.interactive_fig.tight_layout()
            
            # 鍒涘缓鏂扮敾甯?            self.interactive_canvas = FigureCanvasTkAgg(self.interactive_fig, master=chart_frame)
            self.interactive_canvas.draw()
            
            # 纭繚鍥捐〃濉弧鏁翠釜鍖哄煙
            self.interactive_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 娣诲姞宸ュ叿鏍?            self.toolbar = NavigationToolbar2Tk(self.interactive_canvas, chart_frame)
            self.toolbar.update()
            self.toolbar.pack(fill=tk.X)
            
            # 娣诲姞甯姪鏂囨湰
            help_text = ("鎿嶄綔鎸囧崡锛歕n1. 鎷栧姩榛戣壊铏氱嚎鍙皟鏁碩g鍊间綅缃甛n2. 鎷栧姩绾㈣壊鍜岀豢鑹茬鐐瑰彲鎵嬪姩璋冩暣鎷熷悎绾垮€炬枩搴n"
                      "3. 璋冩暣Y杞磋缃彲鏀惧ぇ鏄剧ず娉㈠姩\n4. 鍕鹃€夋墜鍔╕杞磋寖鍥村彲绮剧‘鎺у埗鏄剧ず鑼冨洿") if self.language_var.get() == "cn" else (
                      "User Guide:\n1. Drag the black dashed line to adjust Tg position\n2. Drag red and green endpoints to adjust fitting lines\n"
                      "3. Adjust Y-axis settings to zoom in on variations\n4. Check manual Y-axis range for precise control"
                      )
            help_label = ttk.Label(self.interactive_frame, text=help_text, font=("SimHei", 10))
            help_label.pack(pady=5)
            
            # 娣诲姞褰撳墠Tg鍊兼樉绀?            self.current_tg_var = tk.StringVar()
            self.current_tg_var.set(
                f"褰撳墠Tg鍊? {self.tg_result:.1f}K" if self.language_var.get() == "cn" 
                else f"Current Tg: {self.tg_result:.1f}K"
            )
            
            tg_display_frame = ttk.Frame(self.interactive_frame)
            tg_display_frame.pack(fill=tk.X, expand=False, pady=5)
            
            tg_label = ttk.Label(tg_display_frame, textvariable=self.current_tg_var, 
                                font=("SimHei", 12, "bold"))
            tg_label.pack(side=tk.LEFT, padx=10)
            
            # 娣诲姞鎺у埗鎸夐挳妗嗘灦
            control_btns_frame = ttk.Frame(tg_display_frame)
            control_btns_frame.pack(side=tk.RIGHT, padx=10)
            
            # 娣诲姞鍒囨崲鎺у埗鐐规樉绀虹殑鎸夐挳
            self.show_points_var = tk.BooleanVar(value=True)
            def toggle_control_points():
                if self.interactive_draggable:
                    is_visible = self.interactive_draggable.toggle_control_points()
                    self.show_points_var.set(is_visible)
                    self.toggle_points_btn.config(
                        text="闅愯棌鎺у埗鐐? if is_visible and self.language_var.get() == "cn"
                        else "鏄剧ず鎺у埗鐐? if self.language_var.get() == "cn"
                        else "Hide Control Points" if is_visible
                        else "Show Control Points"
                    )
            
            self.toggle_points_btn = ttk.Button(
                control_btns_frame, 
                text="闅愯棌鎺у埗鐐? if self.language_var.get() == "cn" else "Hide Control Points", 
                command=toggle_control_points
            )
            self.toggle_points_btn.pack(side=tk.LEFT, padx=5)
            
            # 娣诲姞璺宠浆鍒拌绠椾氦鐐圭殑鎸夐挳
            def move_to_intercept():
                if self.interactive_draggable and hasattr(self.interactive_draggable, 'calc_intercept'):
                    if self.interactive_draggable.calc_intercept is not None:
                        success = self.interactive_draggable.move_to_calculated_intercept()
                        if success:
                            messagebox.showinfo(
                                "鎴愬姛" if self.language_var.get() == "cn" else "Success", 
                                f"宸插皢Tg绾跨Щ鍔ㄥ埌璁＄畻鍑虹殑浜ょ偣: {self.interactive_draggable.x:.1f}K" if self.language_var.get() == "cn"
                                else f"Moved Tg line to calculated intercept: {self.interactive_draggable.x:.1f}K"
                            )
                            self.current_tg_var.set(
                                f"褰撳墠Tg鍊? {self.interactive_draggable.x:.1f}K" if self.language_var.get() == "cn"
                                else f"Current Tg: {self.interactive_draggable.x:.1f}K"
                            )
                        else:
                            messagebox.showwarning(
                                "璀﹀憡" if self.language_var.get() == "cn" else "Warning", 
                                "鏃犳硶绉诲姩鍒颁氦鐐癸紝鍙兘涓嶅湪鏈夋晥鑼冨洿鍐? if self.language_var.get() == "cn"
                                else "Cannot move to intercept, it might be outside valid range"
                            )
                    else:
                        messagebox.showinfo(
                            "鎻愮ず" if self.language_var.get() == "cn" else "Information", 
                            "灏氭湭璁＄畻鍑轰氦鐐癸紝璇峰厛鎷栧姩鎷熷悎绾? if self.language_var.get() == "cn"
                            else "No intercept calculated yet, please drag fitting lines first"
                        )
            
            self.intercept_btn = ttk.Button(
                control_btns_frame, 
                text="璺宠浆鍒颁氦鐐? if self.language_var.get() == "cn" else "Jump to Intercept", 
                command=move_to_intercept
            )
            self.intercept_btn.pack(side=tk.LEFT, padx=5)
            
            # 娣诲姞淇濆瓨褰撳墠Tg鍊肩殑鎸夐挳
            save_tg_button = ttk.Button(
                control_btns_frame, 
                text="浣跨敤姝g鍊? if self.language_var.get() == "cn" else "Use This Tg", 
                command=self.use_interactive_tg
            )
            save_tg_button.pack(side=tk.LEFT, padx=5)
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "浜や簰寮忓垎鏋愬凡鍚姩锛屾嫋鍔═g绾垮拰鎷熷悎绾跨鐐瑰彲璋冩暣鍙傛暟" if self.language_var.get() == "cn"
                else "Interactive analysis started, drag Tg line and fitting line endpoints to adjust parameters"
            )
            
            # 璁剧疆鍥炶皟鍑芥暟锛屽湪Tg琚嫋鍔ㄦ椂鏇存柊鏄剧ず
            def update_tg_display(new_tg, *args):
                self.current_tg_var.set(
                    f"褰撳墠Tg鍊? {new_tg:.1f}K" if self.language_var.get() == "cn"
                    else f"Current Tg: {new_tg:.1f}K"
                )
            
            self.interactive_draggable.on_update_callback = update_tg_display
            
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                f"鍒涘缓浜や簰寮忓垎鏋愬け璐? {str(e)}" if self.language_var.get() == "cn"
                else f"Failed to create interactive analysis: {str(e)}"
            )
            self.status_var.set(
                "浜や簰寮忓垎鏋愬惎鍔ㄥけ璐? if self.language_var.get() == "cn"
                else "Interactive analysis failed to start"
            )
    
    def refresh_interactive_plot(self):
        """鍒锋柊浜や簰寮忓垎鏋愬浘琛紝搴旂敤鏂扮殑Y杞磋缃?""
        if not hasattr(self, 'interactive_fig') or self.interactive_fig is None:
            return
            
        try:
            # 鑾峰彇褰撳墠Tg鍊?            current_tg = self.interactive_draggable.x if self.interactive_draggable else self.tg_result
            
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
                
            # 澶勭悊鎵嬪姩Y杞磋寖鍥?            y_min = None
            y_max = None
            
            if self.enable_manual_range.get():
                try:
                    y_min = float(self.y_min_var.get())
                    y_max = float(self.y_max_var.get())
                    
                    # 楠岃瘉杈撳叆鍊兼槸鍚︽湁鏁?                    if y_min >= y_max:
                        messagebox.showerror(
                            "閿欒" if self.language_var.get() == "cn" else "Error", 
                            "Y杞存渶灏忓€煎繀椤诲皬浜庢渶澶у€? if self.language_var.get() == "cn" 
                            else "Y-axis minimum must be less than maximum"
                        )
                        return
                except ValueError:
                    messagebox.showerror(
                        "閿欒" if self.language_var.get() == "cn" else "Error", 
                        "璇疯緭鍏ユ湁鏁堢殑Y杞磋寖鍥村€? if self.language_var.get() == "cn"
                        else "Please enter valid Y-axis range values"
                    )
                    return
            
            # 浣跨敤鏂拌缃垱寤轰氦浜掑紡鍥?            self.status_var.set(
                "姝ｅ湪鍒锋柊浜や簰寮忓垎鏋?.." if self.language_var.get() == "cn"
                else "Refreshing interactive analysis..."
            )
            self.root.update()
            
            # 娓呯┖鐢诲竷妗嗘灦
            chart_frame = None
            for widget in self.interactive_frame.winfo_children():
                if isinstance(widget, ttk.Frame) and not isinstance(widget, ttk.LabelFrame):
                    chart_frame = widget
                    for child in chart_frame.winfo_children():
                        child.destroy()
                    break
            
            if not chart_frame:
                return
            
            # 閲嶆柊鍒涘缓鍒嗘瀽
            self.tg_result, self.interactive_fig, self.interactive_draggable = find_glass_transition_temperature(
                self.temperatures, self.densities, 
                sample_name=sample_name,
                interactive=True,
                y_margin=self.y_margin_var.get(),
                y_min=y_min,
                y_max=y_max,
                language=self.language_var.get()
            )
            
            # 浼樺寲鍥捐〃甯冨眬
            self.interactive_fig.tight_layout()
            
            # 鍒涘缓鏂扮敾甯?            self.interactive_canvas = FigureCanvasTkAgg(self.interactive_fig, master=chart_frame)
            self.interactive_canvas.draw()
            
            # 纭繚鍥捐〃濉弧鏁翠釜鍖哄煙
            self.interactive_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 娣诲姞宸ュ叿鏍?            self.toolbar = NavigationToolbar2Tk(self.interactive_canvas, chart_frame)
            self.toolbar.update()
            self.toolbar.pack(fill=tk.X)
            
            # 鏇存柊Tg鏄剧ず
            self.current_tg_var.set(
                f"褰撳墠Tg鍊? {self.tg_result:.1f}K" if self.language_var.get() == "cn"
                else f"Current Tg: {self.tg_result:.1f}K"
            )
            
            # 璁剧疆鍥炶皟鍑芥暟
            def update_tg_display(new_tg, *args):
                self.current_tg_var.set(
                    f"褰撳墠Tg鍊? {new_tg:.1f}K" if self.language_var.get() == "cn"
                    else f"Current Tg: {new_tg:.1f}K"
                )
            
            self.interactive_draggable.on_update_callback = update_tg_display
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "浜や簰寮忓垎鏋愬凡鍒锋柊" if self.language_var.get() == "cn"
                else "Interactive analysis refreshed"
            )
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error",
                f"鍒锋柊浜や簰寮忓垎鏋愬け璐? {str(e)}" if self.language_var.get() == "cn"
                else f"Failed to refresh interactive analysis: {str(e)}"
            )
    
    def use_interactive_tg(self):
        """浣跨敤浜や簰寮忓垎鏋愮殑Tg鍊间綔涓烘渶缁堢粨鏋?""
        if hasattr(self, 'interactive_draggable') and self.interactive_draggable:
            self.tg_result = self.interactive_draggable.x
            messagebox.showinfo(
                "鎴愬姛" if self.language_var.get() == "cn" else "Success",
                f"宸蹭繚瀛楾g鍊? {self.tg_result:.1f}K" if self.language_var.get() == "cn"
                else f"Tg value saved: {self.tg_result:.1f}K"
            )
            self.status_var.set(
                f"鍒嗘瀽瀹屾垚! Tg = {self.tg_result:.1f}K" if self.language_var.get() == "cn"
                else f"Analysis complete! Tg = {self.tg_result:.1f}K"
            )
        else:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error",
                "鏈壘鍒版湁鏁堢殑浜や簰寮忓垎鏋愮粨鏋? if self.language_var.get() == "cn"
                else "No valid interactive analysis result found"
            )
    
    def save_results(self):
        """淇濆瓨鍒嗘瀽缁撴灉鍜屽彲瑙嗗寲鍥捐〃"""
        if not hasattr(self, 'tg_result') or self.tg_result is None:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                "璇峰厛瀹屾垚Tg鍒嗘瀽" if self.language_var.get() == "cn" else "Please complete Tg analysis first"
            )
            return
            
        try:
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
            
            # 鏄剧ず淇濆瓨瀵硅瘽妗?            save_path = filedialog.asksaveasfilename(
                title=("閫夋嫨淇濆瓨璺緞" if self.language_var.get() == "cn" else "Select Save Path"),
                defaultextension=".png",
                filetypes=[
                    ("PNG鍥惧儚", "*.png") if self.language_var.get() == "cn" else ("PNG Images", "*.png"),
                    ("鎵€鏈夋枃浠?, "*.*") if self.language_var.get() == "cn" else ("All Files", "*.*"),
                ],
                initialfile=f"glass_transition_{sample_name}"
            )
            
            if not save_path:
                return
                
            # 鑾峰彇淇濆瓨鐩綍
            save_dir = os.path.dirname(save_path)
            base_name = os.path.basename(save_path)
            name_without_ext = os.path.splitext(base_name)[0]
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "姝ｅ湪淇濆瓨缁撴灉..." if self.language_var.get() == "cn" else "Saving results..."
            )
            self.root.update()
            
            # 鐢熸垚璇︾粏鍒嗘瀽鍥捐〃
            if self.detailed_var.get():
                # 鑾峰彇褰撳墠Y杞磋缃?                y_margin = self.y_margin_var.get() if hasattr(self, 'y_margin_var') else 0.05
                y_min = None
                y_max = None
                
                if hasattr(self, 'enable_manual_range') and self.enable_manual_range.get():
                    try:
                        y_min = float(self.y_min_var.get())
                        y_max = float(self.y_max_var.get())
                    except:
                        pass
                
                # 鐢熸垚璇︾粏鍒嗘瀽鍥捐〃
                if hasattr(self, 'interactive_draggable') and self.interactive_draggable:
                    # 浠庝氦浜掑紡鍒嗘瀽涓幏鍙栨嫙鍚堝弬鏁?                    high_params = self.interactive_draggable.cached_high_params
                    low_params = self.interactive_draggable.cached_low_params
                    
                    if high_params and low_params:
                        best_params = (high_params, low_params)
                        # 浣跨敤浜や簰寮忓垎鏋愮殑Tg鍊煎拰鎷熷悎鍙傛暟
                        create_detailed_analysis(
                            self.temperatures, self.densities, 
                            best_params, self.tg_result, 
                            sample_name=sample_name,
                            y_margin=y_margin,
                            y_min=y_min,
                            y_max=y_max,
                            language=self.language_var.get(),
                            from_interactive=True)
                        
                        # 淇濆瓨璇︾粏鍒嗘瀽缁撴灉
                        detailed_path = os.path.join(save_dir, f"{name_without_ext}_detailed.png")
                        plt.savefig(detailed_path, dpi=300, bbox_inches='tight')
                        plt.close()
                else:
                    # 濡傛灉娌℃湁浜や簰寮忓垎鏋愮粨鏋滐紝鍒欒繘琛屾爣鍑嗗垎鏋愮敓鎴愯缁嗗浘琛?                    # 杩欓噷鎴戜滑闇€瑕佸厛杩涜鎷熷悎浠ヨ幏鍙栨嫙鍚堝弬鏁?                    # 鍒涘缓涓存椂鍥捐〃杩涜鎷熷悎
                    temp_fig = plt.figure(figsize=(8, 6))
                    temp_ax = temp_fig.add_subplot(111)
                    
                    # 杩涜鎷熷悎
                    tmp_tg, tmp_fig, tmp_draggable = find_glass_transition_temperature(
                        self.temperatures, self.densities,
                        interactive=True,
                        y_margin=y_margin,
                        y_min=y_min,
                        y_max=y_max,
                        language=self.language_var.get()
                    )
                    
                    # 鑾峰彇鎷熷悎鍙傛暟
                    high_params = tmp_draggable.cached_high_params
                    low_params = tmp_draggable.cached_low_params
                    
                    if high_params and low_params:
                        best_params = (high_params, low_params)
                        # 鍒涘缓璇︾粏鍒嗘瀽
                        create_detailed_analysis(
                            self.temperatures, self.densities,
                            best_params, self.tg_result,
                            sample_name=sample_name,
                            y_margin=y_margin,
                            y_min=y_min,
                            y_max=y_max,
                            language=self.language_var.get(),
                            from_interactive=True)
                        
                        # 淇濆瓨璇︾粏鍒嗘瀽缁撴灉
                        detailed_path = os.path.join(save_dir, f"{name_without_ext}_detailed.png")
                        plt.savefig(detailed_path, dpi=300, bbox_inches='tight')
                        
                        # 鍏抽棴涓存椂鍥捐〃
                        plt.close(temp_fig)
                        plt.close()
            
            # 淇濆瓨涓诲垎鏋愮粨鏋?            # 鍒涘缓鏍囧噯鍒嗘瀽鍥捐〃
            standard_fig = plt.figure(figsize=(8, 6), dpi=100)
            
            # 浣跨敤find_glass_transition_temperature閲嶆柊鐢熸垚鏍囧噯鍥捐〃
            find_glass_transition_temperature(
                self.temperatures, self.densities, 
                output_path=save_path,
                sample_name=sample_name,
                interactive=False,
                y_margin=0.05,
                language=self.language_var.get()
            )
            
            # 淇濆瓨鏁版嵁缁撴灉
            csv_path = os.path.join(save_dir, f"{name_without_ext}_data.csv")
            save_results_to_file(self.temperatures, self.densities, self.tg_result, csv_path, language=self.language_var.get())
            
            # 鏄剧ず鎴愬姛娑堟伅
            messagebox.showinfo(
                "淇濆瓨鎴愬姛" if self.language_var.get() == "cn" else "Save Successful", 
                (f"缁撴灉宸蹭繚瀛樿嚦:\n{save_path}\n\n" +
                 (f"璇︾粏鍒嗘瀽宸蹭繚瀛樿嚦:\n{detailed_path}\n\n" if self.detailed_var.get() else "") +
                 f"鏁版嵁宸蹭繚瀛樿嚦:\n{csv_path}") if self.language_var.get() == "cn" else
                (f"Results saved to:\n{save_path}\n\n" +
                 (f"Detailed analysis saved to:\n{detailed_path}\n\n" if self.detailed_var.get() else "") +
                 f"Data saved to:\n{csv_path}")
            )
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "缁撴灉宸蹭繚瀛? if self.language_var.get() == "cn" else "Results saved"
            )
            
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                f"淇濆瓨缁撴灉澶辫触: {str(e)}" if self.language_var.get() == "cn" else f"Failed to save results: {str(e)}"
            )
    
    def toggle_language(self):
        """鍒囨崲鐣岄潰璇█鍜屽浘琛ㄦ爣绛捐瑷€"""
        current_lang = self.language_var.get()
        
        if current_lang == "cn":
            # 鍒囨崲鍒拌嫳鏂?            self.language_var.set("en")
            self.lang_button.config(text="涓枃")
            self.root.title("Glass Transition Temperature Analysis Tool")
            
            # 鏇存柊UI鏂囨湰
            self.file_frame.config(text="Data File Selection")
            self.sample_frame.config(text="Sample Information")
            self.data_frame.config(text="Data Preview")
            self.preview_frame.config(text="Data Visualization Preview")
            self.control_frame.config(text="Operation Controls")
            
            # 鏇存柊鏍戠姸鍥炬爣棰?            self.tree.heading("index", text="Index", anchor=tk.CENTER)
            self.tree.heading("temperature", text="Temperature (K)", anchor=tk.CENTER)
            self.tree.heading("density", text=f"Density ({DENSITY_UNIT})", anchor=tk.CENTER)
            
            # 鏇存柊鎸夐挳鏂囨湰
            self.browse_button.config(text="Browse...")
            self.load_button.config(text="Load Data")
            self.analyze_button.config(text="Confirm Analysis")
            self.interactive_button.config(text="Interactive Analysis")
            self.save_button.config(text="Save Results")
            self.save_data_button.config(text="Save Data")
            self.detailed_check.config(text="Generate Detailed Analysis")
            
            # 鏇存柊鐘舵€?            current_status = self.status_var.get()
            if current_status == "鍑嗗灏辩华 - 璇烽€夋嫨鏁版嵁鏂囦欢":
                self.status_var.set("Ready - Please select a data file")
            
            # 濡傛灉瀛樺湪浜や簰寮忔鏋讹紝鏇存柊鍏舵枃鏈?            if hasattr(self, 'interactive_frame_visible') and self.interactive_frame_visible:
                self.interactive_frame.config(text="Interactive Tg Analysis")
                
                # 灏濊瘯鏇存柊浜や簰寮忔鏋朵腑鐨勬帶浠?                for widget in self.interactive_frame.winfo_children():
                    if isinstance(widget, ttk.LabelFrame) and widget.cget("text") == "Y-axis Display Control":
                        widget.config(text="Y杞存樉绀烘帶鍒?)
                
                # 濡傛灉鏈夋帶鍒剁偣鍒囨崲鎸夐挳锛屾洿鏂板叾鏂囨湰
                if hasattr(self, 'toggle_points_btn'):
                    if self.show_points_var.get():
                        self.toggle_points_btn.config(text="Hide Control Points")
                    else:
                        self.toggle_points_btn.config(text="Show Control Points")
                
                # 濡傛灉鏈変氦鐐硅烦杞寜閽紝鏇存柊鍏舵枃鏈?                if hasattr(self, 'intercept_btn'):
                    self.intercept_btn.config(text="Jump to Intercept")
        else:
            # 鍒囨崲鍒颁腑鏂?            self.language_var.set("cn")
            self.lang_button.config(text="English")
            self.root.title("鐜荤拑鍖栬浆鍙樻俯搴﹀垎鏋愬伐鍏?)
            
            # 鏇存柊UI鏂囨湰
            self.file_frame.config(text="鏁版嵁鏂囦欢閫夋嫨")
            self.sample_frame.config(text="鏍峰搧淇℃伅")
            self.data_frame.config(text="鏁版嵁棰勮")
            self.preview_frame.config(text="鏁版嵁鍙鍖栭瑙?)
            self.control_frame.config(text="鎿嶄綔鎺у埗")
            
            # 鏇存柊鏍戠姸鍥炬爣棰?            self.tree.heading("index", text="搴忓彿", anchor=tk.CENTER)
            self.tree.heading("temperature", text="娓╁害 (K)", anchor=tk.CENTER)
            self.tree.heading("density", text=f"瀵嗗害 ({DENSITY_UNIT})", anchor=tk.CENTER)
            
            # 鏇存柊鎸夐挳鏂囨湰
            self.browse_button.config(text="娴忚...")
            self.load_button.config(text="鍔犺浇鏁版嵁")
            self.analyze_button.config(text="纭鍒嗘瀽")
            self.interactive_button.config(text="浜や簰寮忓垎鏋?)
            self.save_button.config(text="淇濆瓨缁撴灉")
            self.save_data_button.config(text="淇濆瓨鏁版嵁")
            self.detailed_check.config(text="鐢熸垚璇︾粏鍒嗘瀽")
            
            # 鏇存柊鐘舵€?            current_status = self.status_var.get()
            if current_status == "Ready - Please select a data file":
                self.status_var.set("鍑嗗灏辩华 - 璇烽€夋嫨鏁版嵁鏂囦欢")
            
            # 濡傛灉瀛樺湪浜や簰寮忔鏋讹紝鏇存柊鍏舵枃鏈?            if hasattr(self, 'interactive_frame_visible') and self.interactive_frame_visible:
                self.interactive_frame.config(text="浜や簰寮廡g鍒嗘瀽")
                
                # 灏濊瘯鏇存柊浜や簰寮忔鏋朵腑鐨勬帶浠?                for widget in self.interactive_frame.winfo_children():
                    if isinstance(widget, ttk.LabelFrame) and widget.cget("text") == "Y-axis Display Control":
                        widget.config(text="Y杞存樉绀烘帶鍒?)
                
                # 濡傛灉鏈夋帶鍒剁偣鍒囨崲鎸夐挳锛屾洿鏂板叾鏂囨湰
                if hasattr(self, 'toggle_points_btn'):
                    if self.show_points_var.get():
                        self.toggle_points_btn.config(text="闅愯棌鎺у埗鐐?)
                    else:
                        self.toggle_points_btn.config(text="鏄剧ず鎺у埗鐐?)
                
                # 濡傛灉鏈変氦鐐硅烦杞寜閽紝鏇存柊鍏舵枃鏈?                if hasattr(self, 'intercept_btn'):
                    self.intercept_btn.config(text="璺宠浆鍒颁氦鐐?)
        
        # 濡傛灉鏈夐瑙堝浘锛屾洿鏂伴瑙堝浘
        self.refresh_preview()
        
        # 濡傛灉鏈変氦浜掑紡鍥撅紝鍒锋柊浜や簰寮忓浘
        if hasattr(self, 'interactive_fig') and self.interactive_fig is not None:
            self.refresh_interactive_plot()
            
    def refresh_preview(self):
        """鍒锋柊棰勮鍥撅紝搴旂敤褰撳墠璇█璁剧疆"""
        if hasattr(self, 'preview_canvas') and self.preview_canvas is not None and self.data_loaded:
            # 娓呴櫎棰勮妗嗘灦鍐呭
            for widget in self.preview_frame.winfo_children():
                widget.destroy()
            
            # 浣跨敤褰撳墠璇█鍒涘缓棰勮鍥?            self.create_preview_plot()

    def save_data(self):
        """淇濆瓨鍒嗘瀽鏁版嵁鍒癈SV鍜孹LSX鏍煎紡"""
        if not hasattr(self, 'tg_result') or self.tg_result is None:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                "璇峰厛瀹屾垚Tg鍒嗘瀽" if self.language_var.get() == "cn" else "Please complete Tg analysis first"
            )
            return
            
        if not hasattr(self, 'interactive_draggable') or self.interactive_draggable is None:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error",
                "闇€瑕佸厛杩涜浜や簰寮忓垎鏋? if self.language_var.get() == "cn" else "Interactive analysis is required first"
            )
            return
            
        try:
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
            
            # 鏄剧ず淇濆瓨瀵硅瘽妗?            filetypes = [
                ("CSV鏂囦欢", "*.csv") if self.language_var.get() == "cn" else ("CSV Files", "*.csv"),
                ("Excel鏂囦欢", "*.xlsx") if self.language_var.get() == "cn" else ("Excel Files", "*.xlsx")
            ]
            
            save_path = filedialog.asksaveasfilename(
                title=("閫夋嫨淇濆瓨璺緞" if self.language_var.get() == "cn" else "Select Save Path"),
                defaultextension=".csv",
                filetypes=filetypes,
                initialfile=f"glass_transition_data_{sample_name}"
            )
            
            if not save_path:
                return
                
            # 鑾峰彇娓╁害鍜屽瘑搴︽暟鎹?            temperatures = self.temperatures
            densities = self.densities
            tg = self.tg_result
            
            # 鑾峰彇鎷熷悎鍙傛暟
            high_params = self.interactive_draggable.cached_high_params
            low_params = self.interactive_draggable.cached_low_params
            
            if not high_params or not low_params:
                messagebox.showerror(
                    "閿欒" if self.language_var.get() == "cn" else "Error",
                    "鏃犳硶鑾峰彇鎷熷悎鍙傛暟" if self.language_var.get() == "cn" else "Cannot get fitting parameters"
                )
                return
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "姝ｅ湪淇濆瓨鏁版嵁..." if self.language_var.get() == "cn" else "Saving data..."
            )
            self.root.update()
            
            # 鍒涘缓DataFrame
            data = {
                ('娓╁害 (K)' if self.language_var.get() == "cn" else 'Temperature (K)'): temperatures,
                (f'瀵嗗害 ({DENSITY_UNIT})' if self.language_var.get() == "cn" else f'Density ({DENSITY_UNIT})'): densities
            }
            
            # 娣诲姞鍖哄煙鏍囩
            regions = []
            for temp in temperatures:
                if temp > tg:
                    regions.append('娑蹭綋' if self.language_var.get() == "cn" else 'Liquid')
                else:
                    regions.append('鐜荤拑' if self.language_var.get() == "cn" else 'Glass')
            
            data['鍖哄煙' if self.language_var.get() == "cn" else 'Region'] = regions
            
            # 娣诲姞鎷熷悎鏁版嵁
            high_temps = [t for t in temperatures if t > tg]
            low_temps = [t for t in temperatures if t <= tg]
            
            high_fits = []
            low_fits = []
            
            # 璁＄畻楂樻俯鍖烘嫙鍚堝€?            for t in temperatures:
                if t > tg:
                    high_fits.append(high_params[0] * t + high_params[1])
                else:
                    high_fits.append(None)
            
            # 璁＄畻浣庢俯鍖烘嫙鍚堝€?            for t in temperatures:
                if t <= tg:
                    low_fits.append(low_params[0] * t + low_params[1])
                else:
                    low_fits.append(None)
            
            data['楂樻俯鎷熷悎' if self.language_var.get() == "cn" else 'High Temp Fit'] = high_fits
            data['浣庢俯鎷熷悎' if self.language_var.get() == "cn" else 'Low Temp Fit'] = low_fits
            
            # 鍒涘缓DataFrame
            df = pd.DataFrame(data)
            
            # 娣诲姞鎷熷悎鍙傛暟鍜孴g鍊煎埌绗簩涓〃
            fit_info = {
                '鍙傛暟' if self.language_var.get() == "cn" else 'Parameter': [
                    '楂樻俯鏂滅巼' if self.language_var.get() == "cn" else 'High Temp Slope',
                    '楂樻俯鎴窛' if self.language_var.get() == "cn" else 'High Temp Intercept',
                    '浣庢俯鏂滅巼' if self.language_var.get() == "cn" else 'Low Temp Slope',
                    '浣庢俯鎴窛' if self.language_var.get() == "cn" else 'Low Temp Intercept',
                    'Tg (K)'
                ],
                '鍊? if self.language_var.get() == "cn" else 'Value': [
                    high_params[0],
                    high_params[1],
                    low_params[0],
                    low_params[1],
                    tg
                ]
            }
            
            fit_df = pd.DataFrame(fit_info)
            
            # 淇濆瓨涓篊SV鎴朮LSX
            file_ext = os.path.splitext(save_path)[1].lower()
            
            if file_ext == '.csv':
                df.to_csv(save_path, index=False, encoding='utf-8-sig')
                
                # 淇濆瓨鎷熷悎鍙傛暟鍒板彟涓€涓狢SV鏂囦欢
                params_path = os.path.splitext(save_path)[0] + '_params.csv'
                fit_df.to_csv(params_path, index=False, encoding='utf-8-sig')
                
                messagebox.showinfo(
                    "淇濆瓨鎴愬姛" if self.language_var.get() == "cn" else "Save Successful",
                    (f"鏁版嵁宸蹭繚瀛樿嚦:\n{save_path}\n\n鍙傛暟宸蹭繚瀛樿嚦:\n{params_path}") 
                    if self.language_var.get() == "cn" else 
                    (f"Data saved to:\n{save_path}\n\nParameters saved to:\n{params_path}")
                )
            elif file_ext == '.xlsx':
                # 鍒涘缓Excel鏂囦欢骞舵坊鍔犱袱涓〃
                with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='鏁版嵁' if self.language_var.get() == "cn" else 'Data', index=False)
                    fit_df.to_excel(writer, sheet_name='鎷熷悎鍙傛暟' if self.language_var.get() == "cn" else 'Fitting Parameters', index=False)
                
                messagebox.showinfo(
                    "淇濆瓨鎴愬姛" if self.language_var.get() == "cn" else "Save Successful",
                    (f"鏁版嵁鍜屽弬鏁板凡淇濆瓨鑷?\n{save_path}") 
                    if self.language_var.get() == "cn" else 
                    (f"Data and parameters saved to:\n{save_path}")
                )
            else:
                # 榛樿淇濆瓨涓篊SV
                csv_path = os.path.splitext(save_path)[0] + '.csv'
                df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                
                params_path = os.path.splitext(csv_path)[0] + '_params.csv'
                fit_df.to_csv(params_path, index=False, encoding='utf-8-sig')
                
                messagebox.showinfo(
                    "淇濆瓨鎴愬姛" if self.language_var.get() == "cn" else "Save Successful",
                    (f"鏁版嵁宸蹭繚瀛樿嚦:\n{csv_path}\n\n鍙傛暟宸蹭繚瀛樿嚦:\n{params_path}") 
                    if self.language_var.get() == "cn" else 
                    (f"Data saved to:\n{csv_path}\n\nParameters saved to:\n{params_path}")
                )
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "鏁版嵁宸蹭繚瀛? if self.language_var.get() == "cn" else "Data saved"
            )
            
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                f"淇濆瓨鏁版嵁澶辫触: {str(e)}" if self.language_var.get() == "cn" else f"Failed to save data: {str(e)}"
            )

    def _on_window_resize(self, event):
        """绐楀彛澶у皬鍙樺寲鏃舵洿鏂癈anvas澶у皬"""
        # 纭繚event涓嶆槸鐢卞瓙鎺т欢瑙﹀彂鐨?        if event.widget == self.root:
            # 鏇存柊Canvas瀹藉害涓庣獥鍙ｅぇ灏忓尮閰?            width = event.width - self.scrollbar.winfo_width() - 5  # 鍑忓幓婊氬姩鏉″搴﹀拰涓€鐐瑰～鍏?            if width > 0:
                self.main_canvas.config(width=width)
                # 璋冩暣scrollable_frame瀹藉害
                self.main_canvas.itemconfig(self.frame_id, width=width)

    def _on_mousewheel(self, event):
        """澶勭悊榧犳爣婊氳疆浜嬩欢锛屽吋瀹逛笉鍚屾搷浣滅郴缁?""
        if sys.platform.startswith('win'):
            # Windows骞冲彴
            self.main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        elif sys.platform == 'darwin':
            # macOS骞冲彴
            self.main_canvas.yview_scroll(int(-1*event.delta), "units")
        else:
            # Linux骞冲彴
            if event.num == 4:
                self.main_canvas.yview_scroll(-1, "units")
            elif event.num == 5:
                self.main_canvas.yview_scroll(1, "units")
    
    def browse_file(self):
        """娴忚骞堕€夋嫨鏁版嵁鏂囦欢"""
        filetypes = [
            ('CSV鏂囦欢', '*.csv'),
            ('Excel鏂囦欢', '*.xlsx *.xls'),
            ('鎵€鏈夋枃浠?, '*.*')
        ]
        filepath = filedialog.askopenfilename(
            title="閫夋嫨鏁版嵁鏂囦欢",
            filetypes=filetypes
        )
        if filepath:
            self.file_path_var.set(filepath)
            # 浠庢枃浠跺悕鎻愬彇鏍峰搧鍚嶇О
            base_name = os.path.basename(filepath)
            sample_name = os.path.splitext(base_name)[0]
            self.sample_name_var.set(sample_name)
    
    def on_density_column_changed(self, event):
        """澶勭悊瀵嗗害鍒楅€夋嫨鍙樻洿浜嬩欢"""
        if not self.is_multi_density or not self.data_loaded:
            return
            
        selected = self.density_column_var.get()
        if selected:
            # 鏌ユ壘閫変腑鐨勫瘑搴﹀垪绱㈠紩
            for i, name in enumerate(self.density_column_names):
                if name == selected:
                    self.active_density_column = i
                    break
            
            # 鏇存柊褰撳墠浣跨敤鐨勫瘑搴︽暟鎹?            self.densities = self.densities_columns[self.active_density_column]
            
            # 鏇存柊琛ㄦ牸鏄剧ず
            self.update_data_preview()
            
            # 鏇存柊鍥捐〃棰勮
            self.create_preview_plot()
            
            # 娓呴櫎涔嬪墠鐨勪氦浜掑紡鍒嗘瀽缁撴灉
            if hasattr(self, 'interactive_fig') and self.interactive_fig is not None:
                plt.close(self.interactive_fig)
                self.interactive_fig = None
                self.interactive_draggable = None
    
    def load_data(self):
        """鍔犺浇鏁版嵁鏂囦欢"""
        filepath = self.file_path_var.get().strip()
        if not filepath:
            messagebox.showerror("閿欒", "璇峰厛閫夋嫨鏁版嵁鏂囦欢")
            return
            
        try:
            self.status_var.set("姝ｅ湪鍔犺浇鏁版嵁...")
            self.root.update()
            
            # 璇诲彇鏁版嵁锛岀幇鍦ㄦ敮鎸佸鍒楀瘑搴?            result = read_data_from_file(filepath)
            if len(result) == 3:  # 鏂扮殑杩斿洖鏍煎紡鍖呭惈澶氬垪瀵嗗害鏍囧織
                self.temperatures, data, self.is_multi_density = result
                
                if self.is_multi_density:
                    self.densities_columns = data  # 澶氬垪瀵嗗害鏁版嵁
                    self.density_column_names = []
                    
                    # 鎻愬彇鏂囦欢涓殑瀵嗗害鍒楀悕绉?                    file_ext = os.path.splitext(filepath)[1].lower()
                    if file_ext == '.csv':
                        df = pd.read_csv(filepath)
                    elif file_ext in ['.xls', '.xlsx']:
                        df = pd.read_excel(filepath)
                    
                    # 甯歌鐨勬俯搴﹀垪鍚?                    temp_patterns = ['Temperature', 'Temp', 'T', '娓╁害']
                    # 鏌ユ壘闈炴俯搴﹀垪浣滀负瀵嗗害鍒?                    for col in df.columns:
                        if not any(pattern.lower() in col.lower() for pattern in temp_patterns):
                            self.density_column_names.append(col)
                    
                    # 濡傛灉娌℃湁鎵惧埌瀵嗗害鍒楀悕绉帮紝鐢熸垚榛樿鍒楀悕
                    if len(self.density_column_names) == 0:
                        self.density_column_names = [f"瀵嗗害{i+1}" for i in range(len(self.densities_columns))]
                    
                    # 璁剧疆榛樿浣跨敤绗竴鍒楀瘑搴︽暟鎹?                    self.active_density_column = 0
                    self.densities = self.densities_columns[self.active_density_column]
                    
                    # 鏇存柊瀵嗗害鍒楅€夋嫨涓嬫媺妗?                    self.density_column_combobox['values'] = self.density_column_names
                    self.density_column_var.set(self.density_column_names[0])
                    
                    # 鏄剧ず澶氬垪瀵嗗害琛ㄦ牸
                    self.display_multi_density_table()
                else:
                    self.densities = data  # 鍗曞垪瀵嗗害鏁版嵁
            else:
                # 鍏煎鏃х殑杩斿洖鏍煎紡
                self.temperatures, self.densities = result
                self.is_multi_density = False
            
            self.data_loaded = True
            
            # 鏇存柊鏁版嵁棰勮琛ㄦ牸
            self.update_data_preview()
            
            # 鏇存柊鐘舵€?            self.status_var.set("宸插姞杞?{} 涓暟鎹偣 - 鐐瑰嚮'鍒嗘瀽鏁版嵁'缁х画".format(len(self.temperatures)))
            
            # 鐢熸垚棰勮鍥?            self.create_preview_plot()
            
        except Exception as e:
            messagebox.showerror("閿欒", f"鍔犺浇鏁版嵁澶辫触: {str(e)}")
            self.status_var.set("鏁版嵁鍔犺浇澶辫触")
    
    def update_data_preview(self):
        """鏇存柊鏁版嵁棰勮琛ㄦ牸"""
        # 娓呯┖鐜版湁琛ㄦ牸
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 濉厖琛ㄦ牸
        for i, (temp, dens) in enumerate(zip(self.temperatures, self.densities)):
            self.tree.insert("", tk.END, values=(i+1, f"{temp:.1f}", f"{dens:.4f}"))
    
    def display_multi_density_table(self):
        """鏄剧ず澶氬垪瀵嗗害鏁版嵁琛ㄦ牸"""
        # 娓呯┖澶氬垪瀵嗗害妗嗘灦鍐呭
        for widget in self.multi_density_frame.winfo_children():
            widget.destroy()
        
        # 鍒涘缓琛ㄦ牸
        columns = ["娓╁害(K)"] + self.density_column_names
        
        # 浣跨敤ttk.Treeview鍒涘缓澶氬垪琛ㄦ牸
        multi_tree_frame = ttk.Frame(self.multi_density_frame)
        multi_tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 娣诲姞鍨傜洿婊氬姩鏉?        vsb = ttk.Scrollbar(multi_tree_frame, orient="vertical")
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 娣诲姞姘村钩婊氬姩鏉?        hsb = ttk.Scrollbar(multi_tree_frame, orient="horizontal")
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 鍒涘缓鏍戠姸琛ㄦ牸
        tree_columns = tuple(f"#{i}" for i in range(len(columns)))
        multi_tree = ttk.Treeview(multi_tree_frame, columns=tree_columns, 
                                 yscrollcommand=vsb.set, xscrollcommand=hsb.set,
                                 height=10)
        
        # 璁剧疆婊氬姩鏉?        vsb.configure(command=multi_tree.yview)
        hsb.configure(command=multi_tree.xview)
        
        # 璁剧疆鍒楀鍜屽垪鍚?        multi_tree.column("#0", width=0, stretch=tk.NO)
        for i, col in enumerate(columns):
            col_id = f"#{i}"
            multi_tree.column(col_id, anchor=tk.CENTER, width=120, stretch=tk.YES)
            multi_tree.heading(col_id, text=col, anchor=tk.CENTER)
        
        # 濉厖鏁版嵁
        for i, temp in enumerate(self.temperatures):
            values = [f"{temp:.1f}"]
            for col_densities in self.densities_columns:
                values.append(f"{col_densities[i]:.4f}")
            multi_tree.insert("", tk.END, values=values)
        
        multi_tree.pack(fill=tk.BOTH, expand=True)
        
        # 鐜板湪鏄剧ず澶氬垪瀵嗗害妗嗘灦
        self.multi_density_frame.pack(fill=tk.BOTH, expand=False, padx=5, pady=5)
    
    def create_preview_plot(self):
        """鍒涘缓娓╁害-瀵嗗害鏁版嵁鐨勯瑙堝浘"""
        if not self.data_loaded or len(self.temperatures) == 0:
            return
        
        # 娓呴櫎棰勮妗嗘灦涓殑鍐呭
        for widget in self.preview_frame.winfo_children():
            widget.destroy()
        
        # 鍒涘缓棰勮鍥撅紝浣跨敤鏇村ぇ鐨勫昂瀵?        fig, ax = plt.subplots(figsize=(10, 5), dpi=100)
        
        # 浣跨敤鍏呭垎鍒╃敤鍙敤瀹藉害鐨凢rame
        canvas_frame = ttk.Frame(self.preview_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # 浣跨敤褰撳墠璇█璁剧疆
        current_lang = self.language_var.get()
        if current_lang == "en":
            xlabel = "Temperature (K)"
            ylabel = f"Density ({DENSITY_UNIT})"
            title = "Temperature-Density Preview"
        else:
            xlabel = "娓╁害 (K)"
            ylabel = f"瀵嗗害 ({DENSITY_UNIT})"
            title = "娓╁害-瀵嗗害鏁版嵁棰勮"
        
        # 鏍规嵁鏄惁涓哄鍒楀瘑搴︽暟鎹喅瀹氱粯鍥炬柟寮?        if self.is_multi_density and hasattr(self, 'densities_columns') and len(self.densities_columns) > 0:
            # 浣跨敤涓嶅悓棰滆壊缁樺埗澶氬垪瀵嗗害鏁版嵁
            colors = plt.cm.tab10.colors  # 浣跨敤tab10鑹插僵鏄犲皠鑾峰彇涓嶅悓棰滆壊
            
            # 缁樺埗鎵€鏈夊瘑搴﹀垪鏁版嵁
            for i, densities in enumerate(self.densities_columns):
                color_idx = i % len(colors)  # 闃叉绱㈠紩瓒呭嚭鑼冨洿
                label = self.density_column_names[i] if i < len(self.density_column_names) else f"瀵嗗害{i+1}"
                ax.scatter(self.temperatures, densities, 
                          color=colors[color_idx], alpha=0.7, 
                          label=label, s=40, marker='o')
            
            # 楂樹寒鏄剧ず褰撳墠閫変腑鐨勫瘑搴﹀垪
            ax.scatter(self.temperatures, self.densities_columns[self.active_density_column], 
                       color=colors[self.active_density_column % len(colors)], 
                       s=60, marker='o', edgecolor='black', linewidth=1.5,
                       label=f"{self.density_column_names[self.active_density_column]} (褰撳墠閫変腑)")
                       
            # 娣诲姞鍥句緥
            ax.legend(loc='best', fontsize=9)
        else:
            # 鍗曞垪瀵嗗害鏁版嵁浣跨敤榛樿钃濊壊
            ax.scatter(self.temperatures, self.densities, color='#3182bd', alpha=0.8)
        
        ax.set_xlabel(xlabel)
        ax.set_ylabel(ylabel)
        ax.set_title(title)
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 璋冩暣鍥惧舰浠ュ～婊＄獥鍙?        fig.tight_layout()
        
        # 灏嗗浘琛ㄥ祵鍏ュ埌Tkinter鐣岄潰锛屼娇鐢╢ill=BOTH鍜宔xpand=True
        canvas = FigureCanvasTkAgg(fig, master=canvas_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 娣诲姞宸ュ叿鏍忎互鍏佽鐢ㄦ埛涓庡浘琛ㄤ氦浜?        toolbar = NavigationToolbar2Tk(canvas, canvas_frame)
        toolbar.update()
        
        # 淇濆瓨鐢诲竷瀵硅薄渚涘悗缁娇鐢?        self.preview_canvas = canvas
    
    def analyze_data(self):
        """鍒嗘瀽鏁版嵁骞剁敓鎴愮幓鐠冨寲杞彉娓╁害缁撴灉"""
        if not self.data_loaded or len(self.temperatures) == 0:
            messagebox.showerror("閿欒" if self.language_var.get() == "cn" else "Error", 
                              "璇峰厛鍔犺浇鏈夋晥鏁版嵁" if self.language_var.get() == "cn" else "Please load valid data first")
            return
            
        try:
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
            
            # 鏇存柊鐘舵€?            self.status_var.set("姝ｅ湪鍒嗘瀽鏁版嵁..." if self.language_var.get() == "cn" else "Analyzing data...")
            self.root.update()
            
            # 鍒涘缓鍒嗘瀽鍥捐〃
            plt.figure(figsize=(8, 6), dpi=100)
            
            # 鑾峰彇Y杞磋缃紙濡傛灉鏈夛級
            y_margin = self.y_margin_var.get() if hasattr(self, 'y_margin_var') else 0.05
            y_min = None
            y_max = None
            
            if hasattr(self, 'enable_manual_range') and self.enable_manual_range.get():
                try:
                    y_min = float(self.y_min_var.get())
                    y_max = float(self.y_max_var.get())
                except:
                    pass
            
            # 妫€鏌ユ槸鍚﹀凡鏈変氦浜掑紡鍒嗘瀽缁撴灉
            if hasattr(self, 'interactive_draggable') and self.interactive_draggable:
                # 鐩存帴浣跨敤浜や簰寮忓垎鏋愮殑Tg鍊?                self.tg_result = self.interactive_draggable.x
                
                # 鍒涘缓涓庝氦浜掑紡鍒嗘瀽鐩稿悓鍙傛暟鐨勫垎鏋愬浘琛紙浣嗕笉鏄氦浜掑紡鐨勶級
                plt.figure(figsize=(8, 6), dpi=100)
                find_glass_transition_temperature(
                    self.temperatures, self.densities, 
                    sample_name=sample_name,
                    interactive=False,
                    y_margin=y_margin,
                    y_min=y_min,
                    y_max=y_max,
                    language=self.language_var.get()
                )
                
                # 鐢熸垚璇︾粏鍒嗘瀽锛堝鏋滈€変腑锛?                if self.detailed_var.get():
                    # 浠庝氦浜掑紡鍒嗘瀽涓幏鍙栨嫙鍚堝弬鏁?                    high_params = self.interactive_draggable.cached_high_params
                    low_params = self.interactive_draggable.cached_low_params
                    
                    if high_params and low_params:
                        best_params = (high_params, low_params)
                        # 鍒涘缓璇︾粏鍒嗘瀽锛屼紶閫掑鍒楀瘑搴︽暟鎹紙濡傛灉鏈夛級
                        if self.is_multi_density and hasattr(self, 'densities_columns') and len(self.densities_columns) > 0:
                            create_detailed_analysis(
                                self.temperatures, self.densities,
                                best_params, self.tg_result,
                                sample_name=sample_name,
                                y_margin=y_margin,
                                y_min=y_min,
                                y_max=y_max,
                                language=self.language_var.get(),
                                from_interactive=True,
                                densities_columns=self.densities_columns,
                                density_column_names=self.density_column_names
                            )
                        else:
                            create_detailed_analysis(
                                self.temperatures, self.densities,
                                best_params, self.tg_result,
                                sample_name=sample_name,
                                y_margin=y_margin,
                                y_min=y_min,
                                y_max=y_max,
                                language=self.language_var.get(),
                                from_interactive=True
                            )
                        plt.figure()  # 鍒涘缓鏂板浘褰互鏄剧ず璇︾粏鍒嗘瀽
                
                # 鏄剧ず鍥捐〃
                plt.show()
            else:
                # 娌℃湁浜や簰寮忓垎鏋愮粨鏋滐紝鎵ц姝ｅ父鍒嗘瀽
                self.tg_result = find_glass_transition_temperature(
                    self.temperatures, self.densities, 
                    sample_name=sample_name,
                    language=self.language_var.get()
                )
            
            # 鏇存柊鐘舵€?            self.status_var.set(f"鍒嗘瀽瀹屾垚! Tg = {self.tg_result:.1f}K" if self.language_var.get() == "cn" 
                             else f"Analysis complete! Tg = {self.tg_result:.1f}K")
            
            # 鏄剧ず缁撴灉
            messagebox.showinfo(
                "鍒嗘瀽缁撴灉" if self.language_var.get() == "cn" else "Analysis Result", 
                f"鐜荤拑鍖栬浆鍙樻俯搴?Tg): {self.tg_result:.1f}K" if self.language_var.get() == "cn" 
                else f"Glass Transition Temperature (Tg): {self.tg_result:.1f}K"
            )
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                f"鍒嗘瀽杩囩▼涓嚭閿? {str(e)}" if self.language_var.get() == "cn" 
                else f"Error during analysis: {str(e)}"
            )

    def interactive_analyze(self):
        """鍚姩浜や簰寮忓垎鏋愭ā寮?""
        if not self.data_loaded or len(self.temperatures) == 0:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                "璇峰厛鍔犺浇鏈夋晥鏁版嵁" if self.language_var.get() == "cn" else "Please load valid data first"
            )
            return
            
        try:
            self.status_var.set(
                "姝ｅ湪鍒涘缓浜や簰寮忓垎鏋?.." if self.language_var.get() == "cn" 
                else "Creating interactive analysis..."
            )
            self.root.update()
            
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
            
            # 纭繚浜や簰寮忔鏋跺凡鏄剧ず
            if not hasattr(self, 'interactive_frame_visible') or not self.interactive_frame_visible:
                self.interactive_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
                self.interactive_frame_visible = True
            
            # 娓呯┖浜や簰寮忔鏋?            for widget in self.interactive_frame.winfo_children():
                widget.destroy()
            
            # 鍒涘缓Y杞存帶鍒跺尯鍩?            y_control_frame = ttk.LabelFrame(
                self.interactive_frame, 
                text="Y杞存樉绀烘帶鍒? if self.language_var.get() == "cn" else "Y-axis Display Control", 
                padding=5
            )
            y_control_frame.pack(fill=tk.X, pady=5)
            
            # 鑷姩缂╂斁鎺у埗鍖哄煙
            auto_scale_frame = ttk.Frame(y_control_frame)
            auto_scale_frame.pack(fill=tk.X, pady=5)
            
            ttk.Label(
                auto_scale_frame, 
                text="鑷姩缂╂斁杈硅窛:" if self.language_var.get() == "cn" else "Auto Scale Margin:"
            ).pack(side=tk.LEFT, padx=5)
            
            # Y杞磋竟璺濇帶鍒舵粦鍧?            self.y_margin_var = tk.DoubleVar(value=0.05)
            y_margin_scale = ttk.Scale(auto_scale_frame, from_=0.01, to=0.5, 
                                       variable=self.y_margin_var, length=200, 
                                       orient=tk.HORIZONTAL)
            y_margin_scale.pack(side=tk.LEFT, padx=5)
            
            # 褰撳墠鍊兼樉绀?            y_margin_label = ttk.Label(auto_scale_frame, text="0.05")
            y_margin_label.pack(side=tk.LEFT, padx=5)
            
            # 鏇存柊婊戝潡鏍囩鐨勫嚱鏁?            def update_margin_label(*args):
                y_margin_label.config(text=f"{self.y_margin_var.get():.2f}")
            
            # 缁戝畾婊戝潡鍊煎彉鍖栦簨浠?            self.y_margin_var.trace_add("write", update_margin_label)
            
            # 鎵嬪姩鑼冨洿鎺у埗鍖哄煙
            manual_range_frame = ttk.Frame(y_control_frame)
            manual_range_frame.pack(fill=tk.X, pady=5)
            
            # 鍚敤鎵嬪姩鑼冨洿閫夐」
            self.enable_manual_range = tk.BooleanVar(value=False)
            manual_check = ttk.Checkbutton(
                manual_range_frame, 
                text="鍚敤鎵嬪姩Y杞磋寖鍥? if self.language_var.get() == "cn" else "Enable Manual Y-axis Range", 
                variable=self.enable_manual_range
            )
            manual_check.pack(side=tk.LEFT, padx=5)
            
            # Y杞存渶灏忓€?            ttk.Label(
                manual_range_frame, 
                text="Y杞存渶灏忓€?" if self.language_var.get() == "cn" else "Y-axis Min:"
            ).pack(side=tk.LEFT, padx=5)
            self.y_min_var = tk.StringVar()
            y_min_entry = ttk.Entry(manual_range_frame, textvariable=self.y_min_var, width=8)
            y_min_entry.pack(side=tk.LEFT, padx=5)
            
            # Y杞存渶澶у€?            ttk.Label(
                manual_range_frame, 
                text="Y杞存渶澶у€?" if self.language_var.get() == "cn" else "Y-axis Max:"
            ).pack(side=tk.LEFT, padx=5)
            self.y_max_var = tk.StringVar()
            y_max_entry = ttk.Entry(manual_range_frame, textvariable=self.y_max_var, width=8)
            y_max_entry.pack(side=tk.LEFT, padx=5)
            
            # 鑾峰彇榛樿鐨刌杞磋寖鍥?(澶х害鏈€灏忓€煎拰鏈€澶у€?
            y_min_default = min(self.densities) - 0.1
            y_max_default = max(self.densities) + 0.1
            self.y_min_var.set(f"{y_min_default:.3f}")
            self.y_max_var.set(f"{y_max_default:.3f}")
            
            # 鍒锋柊鎸夐挳
            refresh_button = ttk.Button(
                y_control_frame, 
                text="搴旂敤璁剧疆" if self.language_var.get() == "cn" else "Apply Settings", 
                command=self.refresh_interactive_plot
            )
            refresh_button.pack(side=tk.RIGHT, padx=10)
            
            # 鍒涘缓浜や簰寮廡g鍒嗘瀽
            self.tg_result, self.interactive_fig, self.interactive_draggable = find_glass_transition_temperature(
                self.temperatures, self.densities, 
                sample_name=sample_name,
                interactive=True,
                y_margin=self.y_margin_var.get(),
                language=self.language_var.get()
            )
            
            # 鍒涘缓鍥捐〃瀹瑰櫒妗嗘灦锛岀‘淇濆～婊℃暣涓搴?            chart_frame = ttk.Frame(self.interactive_frame)
            chart_frame.pack(fill=tk.BOTH, expand=True, pady=5)
            
            # 浼樺寲鍥捐〃甯冨眬
            self.interactive_fig.tight_layout()
            
            # 鍒涘缓鏂扮敾甯?            self.interactive_canvas = FigureCanvasTkAgg(self.interactive_fig, master=chart_frame)
            self.interactive_canvas.draw()
            
            # 纭繚鍥捐〃濉弧鏁翠釜鍖哄煙
            self.interactive_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 娣诲姞宸ュ叿鏍?            self.toolbar = NavigationToolbar2Tk(self.interactive_canvas, chart_frame)
            self.toolbar.update()
            self.toolbar.pack(fill=tk.X)
            
            # 娣诲姞甯姪鏂囨湰
            help_text = ("鎿嶄綔鎸囧崡锛歕n1. 鎷栧姩榛戣壊铏氱嚎鍙皟鏁碩g鍊间綅缃甛n2. 鎷栧姩绾㈣壊鍜岀豢鑹茬鐐瑰彲鎵嬪姩璋冩暣鎷熷悎绾垮€炬枩搴n"
                      "3. 璋冩暣Y杞磋缃彲鏀惧ぇ鏄剧ず娉㈠姩\n4. 鍕鹃€夋墜鍔╕杞磋寖鍥村彲绮剧‘鎺у埗鏄剧ず鑼冨洿") if self.language_var.get() == "cn" else (
                      "User Guide:\n1. Drag the black dashed line to adjust Tg position\n2. Drag red and green endpoints to adjust fitting lines\n"
                      "3. Adjust Y-axis settings to zoom in on variations\n4. Check manual Y-axis range for precise control"
                      )
            help_label = ttk.Label(self.interactive_frame, text=help_text, font=("SimHei", 10))
            help_label.pack(pady=5)
            
            # 娣诲姞褰撳墠Tg鍊兼樉绀?            self.current_tg_var = tk.StringVar()
            self.current_tg_var.set(
                f"褰撳墠Tg鍊? {self.tg_result:.1f}K" if self.language_var.get() == "cn" 
                else f"Current Tg: {self.tg_result:.1f}K"
            )
            
            tg_display_frame = ttk.Frame(self.interactive_frame)
            tg_display_frame.pack(fill=tk.X, expand=False, pady=5)
            
            tg_label = ttk.Label(tg_display_frame, textvariable=self.current_tg_var, 
                                font=("SimHei", 12, "bold"))
            tg_label.pack(side=tk.LEFT, padx=10)
            
            # 娣诲姞鎺у埗鎸夐挳妗嗘灦
            control_btns_frame = ttk.Frame(tg_display_frame)
            control_btns_frame.pack(side=tk.RIGHT, padx=10)
            
            # 娣诲姞鍒囨崲鎺у埗鐐规樉绀虹殑鎸夐挳
            self.show_points_var = tk.BooleanVar(value=True)
            def toggle_control_points():
                if self.interactive_draggable:
                    is_visible = self.interactive_draggable.toggle_control_points()
                    self.show_points_var.set(is_visible)
                    self.toggle_points_btn.config(
                        text="闅愯棌鎺у埗鐐? if is_visible and self.language_var.get() == "cn"
                        else "鏄剧ず鎺у埗鐐? if self.language_var.get() == "cn"
                        else "Hide Control Points" if is_visible
                        else "Show Control Points"
                    )
            
            self.toggle_points_btn = ttk.Button(
                control_btns_frame, 
                text="闅愯棌鎺у埗鐐? if self.language_var.get() == "cn" else "Hide Control Points", 
                command=toggle_control_points
            )
            self.toggle_points_btn.pack(side=tk.LEFT, padx=5)
            
            # 娣诲姞璺宠浆鍒拌绠椾氦鐐圭殑鎸夐挳
            def move_to_intercept():
                if self.interactive_draggable and hasattr(self.interactive_draggable, 'calc_intercept'):
                    if self.interactive_draggable.calc_intercept is not None:
                        success = self.interactive_draggable.move_to_calculated_intercept()
                        if success:
                            messagebox.showinfo(
                                "鎴愬姛" if self.language_var.get() == "cn" else "Success", 
                                f"宸插皢Tg绾跨Щ鍔ㄥ埌璁＄畻鍑虹殑浜ょ偣: {self.interactive_draggable.x:.1f}K" if self.language_var.get() == "cn"
                                else f"Moved Tg line to calculated intercept: {self.interactive_draggable.x:.1f}K"
                            )
                            self.current_tg_var.set(
                                f"褰撳墠Tg鍊? {self.interactive_draggable.x:.1f}K" if self.language_var.get() == "cn"
                                else f"Current Tg: {self.interactive_draggable.x:.1f}K"
                            )
                        else:
                            messagebox.showwarning(
                                "璀﹀憡" if self.language_var.get() == "cn" else "Warning", 
                                "鏃犳硶绉诲姩鍒颁氦鐐癸紝鍙兘涓嶅湪鏈夋晥鑼冨洿鍐? if self.language_var.get() == "cn"
                                else "Cannot move to intercept, it might be outside valid range"
                            )
                    else:
                        messagebox.showinfo(
                            "鎻愮ず" if self.language_var.get() == "cn" else "Information", 
                            "灏氭湭璁＄畻鍑轰氦鐐癸紝璇峰厛鎷栧姩鎷熷悎绾? if self.language_var.get() == "cn"
                            else "No intercept calculated yet, please drag fitting lines first"
                        )
            
            self.intercept_btn = ttk.Button(
                control_btns_frame, 
                text="璺宠浆鍒颁氦鐐? if self.language_var.get() == "cn" else "Jump to Intercept", 
                command=move_to_intercept
            )
            self.intercept_btn.pack(side=tk.LEFT, padx=5)
            
            # 娣诲姞淇濆瓨褰撳墠Tg鍊肩殑鎸夐挳
            save_tg_button = ttk.Button(
                control_btns_frame, 
                text="浣跨敤姝g鍊? if self.language_var.get() == "cn" else "Use This Tg", 
                command=self.use_interactive_tg
            )
            save_tg_button.pack(side=tk.LEFT, padx=5)
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "浜や簰寮忓垎鏋愬凡鍚姩锛屾嫋鍔═g绾垮拰鎷熷悎绾跨鐐瑰彲璋冩暣鍙傛暟" if self.language_var.get() == "cn"
                else "Interactive analysis started, drag Tg line and fitting line endpoints to adjust parameters"
            )
            
            # 璁剧疆鍥炶皟鍑芥暟锛屽湪Tg琚嫋鍔ㄦ椂鏇存柊鏄剧ず
            def update_tg_display(new_tg, *args):
                self.current_tg_var.set(
                    f"褰撳墠Tg鍊? {new_tg:.1f}K" if self.language_var.get() == "cn"
                    else f"Current Tg: {new_tg:.1f}K"
                )
            
            self.interactive_draggable.on_update_callback = update_tg_display
            
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                f"鍒涘缓浜や簰寮忓垎鏋愬け璐? {str(e)}" if self.language_var.get() == "cn"
                else f"Failed to create interactive analysis: {str(e)}"
            )
            self.status_var.set(
                "浜や簰寮忓垎鏋愬惎鍔ㄥけ璐? if self.language_var.get() == "cn"
                else "Interactive analysis failed to start"
            )
    
    def refresh_interactive_plot(self):
        """鍒锋柊浜や簰寮忓垎鏋愬浘琛紝搴旂敤鏂扮殑Y杞磋缃?""
        if not hasattr(self, 'interactive_fig') or self.interactive_fig is None:
            return
            
        try:
            # 鑾峰彇褰撳墠Tg鍊?            current_tg = self.interactive_draggable.x if self.interactive_draggable else self.tg_result
            
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
                
            # 澶勭悊鎵嬪姩Y杞磋寖鍥?            y_min = None
            y_max = None
            
            if self.enable_manual_range.get():
                try:
                    y_min = float(self.y_min_var.get())
                    y_max = float(self.y_max_var.get())
                    
                    # 楠岃瘉杈撳叆鍊兼槸鍚︽湁鏁?                    if y_min >= y_max:
                        messagebox.showerror(
                            "閿欒" if self.language_var.get() == "cn" else "Error", 
                            "Y杞存渶灏忓€煎繀椤诲皬浜庢渶澶у€? if self.language_var.get() == "cn" 
                            else "Y-axis minimum must be less than maximum"
                        )
                        return
                except ValueError:
                    messagebox.showerror(
                        "閿欒" if self.language_var.get() == "cn" else "Error", 
                        "璇疯緭鍏ユ湁鏁堢殑Y杞磋寖鍥村€? if self.language_var.get() == "cn"
                        else "Please enter valid Y-axis range values"
                    )
                    return
            
            # 浣跨敤鏂拌缃垱寤轰氦浜掑紡鍥?            self.status_var.set(
                "姝ｅ湪鍒锋柊浜や簰寮忓垎鏋?.." if self.language_var.get() == "cn"
                else "Refreshing interactive analysis..."
            )
            self.root.update()
            
            # 娓呯┖鐢诲竷妗嗘灦
            chart_frame = None
            for widget in self.interactive_frame.winfo_children():
                if isinstance(widget, ttk.Frame) and not isinstance(widget, ttk.LabelFrame):
                    chart_frame = widget
                    for child in chart_frame.winfo_children():
                        child.destroy()
                    break
            
            if not chart_frame:
                return
            
            # 閲嶆柊鍒涘缓鍒嗘瀽
            self.tg_result, self.interactive_fig, self.interactive_draggable = find_glass_transition_temperature(
                self.temperatures, self.densities, 
                sample_name=sample_name,
                interactive=True,
                y_margin=self.y_margin_var.get(),
                y_min=y_min,
                y_max=y_max,
                language=self.language_var.get()
            )
            
            # 浼樺寲鍥捐〃甯冨眬
            self.interactive_fig.tight_layout()
            
            # 鍒涘缓鏂扮敾甯?            self.interactive_canvas = FigureCanvasTkAgg(self.interactive_fig, master=chart_frame)
            self.interactive_canvas.draw()
            
            # 纭繚鍥捐〃濉弧鏁翠釜鍖哄煙
            self.interactive_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 娣诲姞宸ュ叿鏍?            self.toolbar = NavigationToolbar2Tk(self.interactive_canvas, chart_frame)
            self.toolbar.update()
            self.toolbar.pack(fill=tk.X)
            
            # 鏇存柊Tg鏄剧ず
            self.current_tg_var.set(
                f"褰撳墠Tg鍊? {self.tg_result:.1f}K" if self.language_var.get() == "cn"
                else f"Current Tg: {self.tg_result:.1f}K"
            )
            
            # 璁剧疆鍥炶皟鍑芥暟
            def update_tg_display(new_tg, *args):
                self.current_tg_var.set(
                    f"褰撳墠Tg鍊? {new_tg:.1f}K" if self.language_var.get() == "cn"
                    else f"Current Tg: {new_tg:.1f}K"
                )
            
            self.interactive_draggable.on_update_callback = update_tg_display
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "浜や簰寮忓垎鏋愬凡鍒锋柊" if self.language_var.get() == "cn"
                else "Interactive analysis refreshed"
            )
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error",
                f"鍒锋柊浜や簰寮忓垎鏋愬け璐? {str(e)}" if self.language_var.get() == "cn"
                else f"Failed to refresh interactive analysis: {str(e)}"
            )
    
    def use_interactive_tg(self):
        """浣跨敤浜や簰寮忓垎鏋愮殑Tg鍊间綔涓烘渶缁堢粨鏋?""
        if hasattr(self, 'interactive_draggable') and self.interactive_draggable:
            self.tg_result = self.interactive_draggable.x
            messagebox.showinfo(
                "鎴愬姛" if self.language_var.get() == "cn" else "Success",
                f"宸蹭繚瀛楾g鍊? {self.tg_result:.1f}K" if self.language_var.get() == "cn"
                else f"Tg value saved: {self.tg_result:.1f}K"
            )
            self.status_var.set(
                f"鍒嗘瀽瀹屾垚! Tg = {self.tg_result:.1f}K" if self.language_var.get() == "cn"
                else f"Analysis complete! Tg = {self.tg_result:.1f}K"
            )
        else:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error",
                "鏈壘鍒版湁鏁堢殑浜や簰寮忓垎鏋愮粨鏋? if self.language_var.get() == "cn"
                else "No valid interactive analysis result found"
            )
    
    def save_results(self):
        """淇濆瓨鍒嗘瀽缁撴灉鍜屽彲瑙嗗寲鍥捐〃"""
        if not hasattr(self, 'tg_result') or self.tg_result is None:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                "璇峰厛瀹屾垚Tg鍒嗘瀽" if self.language_var.get() == "cn" else "Please complete Tg analysis first"
            )
            return
            
        try:
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
            
            # 鏄剧ず淇濆瓨瀵硅瘽妗?            save_path = filedialog.asksaveasfilename(
                title=("閫夋嫨淇濆瓨璺緞" if self.language_var.get() == "cn" else "Select Save Path"),
                defaultextension=".png",
                filetypes=[
                    ("PNG鍥惧儚", "*.png") if self.language_var.get() == "cn" else ("PNG Images", "*.png"),
                    ("鎵€鏈夋枃浠?, "*.*") if self.language_var.get() == "cn" else ("All Files", "*.*"),
                ],
                initialfile=f"glass_transition_{sample_name}"
            )
            
            if not save_path:
                return
                
            # 鑾峰彇淇濆瓨鐩綍
            save_dir = os.path.dirname(save_path)
            base_name = os.path.basename(save_path)
            name_without_ext = os.path.splitext(base_name)[0]
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "姝ｅ湪淇濆瓨缁撴灉..." if self.language_var.get() == "cn" else "Saving results..."
            )
            self.root.update()
            
            # 鐢熸垚璇︾粏鍒嗘瀽鍥捐〃
            if self.detailed_var.get():
                # 鑾峰彇褰撳墠Y杞磋缃?                y_margin = self.y_margin_var.get() if hasattr(self, 'y_margin_var') else 0.05
                y_min = None
                y_max = None
                
                if hasattr(self, 'enable_manual_range') and self.enable_manual_range.get():
                    try:
                        y_min = float(self.y_min_var.get())
                        y_max = float(self.y_max_var.get())
                    except:
                        pass
                
                # 鐢熸垚璇︾粏鍒嗘瀽鍥捐〃
                if hasattr(self, 'interactive_draggable') and self.interactive_draggable:
                    # 浠庝氦浜掑紡鍒嗘瀽涓幏鍙栨嫙鍚堝弬鏁?                    high_params = self.interactive_draggable.cached_high_params
                    low_params = self.interactive_draggable.cached_low_params
                    
                    if high_params and low_params:
                        best_params = (high_params, low_params)
                        # 浣跨敤浜や簰寮忓垎鏋愮殑Tg鍊煎拰鎷熷悎鍙傛暟
                        create_detailed_analysis(
                            self.temperatures, self.densities, 
                            best_params, self.tg_result, 
                            sample_name=sample_name,
                            y_margin=y_margin,
                            y_min=y_min,
                            y_max=y_max,
                            language=self.language_var.get(),
                            from_interactive=True)
                        
                        # 淇濆瓨璇︾粏鍒嗘瀽缁撴灉
                        detailed_path = os.path.join(save_dir, f"{name_without_ext}_detailed.png")
                        plt.savefig(detailed_path, dpi=300, bbox_inches='tight')
                        plt.close()
                else:
                    # 濡傛灉娌℃湁浜や簰寮忓垎鏋愮粨鏋滐紝鍒欒繘琛屾爣鍑嗗垎鏋愮敓鎴愯缁嗗浘琛?                    # 杩欓噷鎴戜滑闇€瑕佸厛杩涜鎷熷悎浠ヨ幏鍙栨嫙鍚堝弬鏁?                    # 鍒涘缓涓存椂鍥捐〃杩涜鎷熷悎
                    temp_fig = plt.figure(figsize=(8, 6))
                    temp_ax = temp_fig.add_subplot(111)
                    
                    # 杩涜鎷熷悎
                    tmp_tg, tmp_fig, tmp_draggable = find_glass_transition_temperature(
                        self.temperatures, self.densities,
                        interactive=True,
                        y_margin=y_margin,
                        y_min=y_min,
                        y_max=y_max,
                        language=self.language_var.get()
                    )
                    
                    # 鑾峰彇鎷熷悎鍙傛暟
                    high_params = tmp_draggable.cached_high_params
                    low_params = tmp_draggable.cached_low_params
                    
                    if high_params and low_params:
                        best_params = (high_params, low_params)
                        # 鍒涘缓璇︾粏鍒嗘瀽
                        create_detailed_analysis(
                            self.temperatures, self.densities,
                            best_params, self.tg_result,
                            sample_name=sample_name,
                            y_margin=y_margin,
                            y_min=y_min,
                            y_max=y_max,
                            language=self.language_var.get(),
                            from_interactive=True)
                        
                        # 淇濆瓨璇︾粏鍒嗘瀽缁撴灉
                        detailed_path = os.path.join(save_dir, f"{name_without_ext}_detailed.png")
                        plt.savefig(detailed_path, dpi=300, bbox_inches='tight')
                        
                        # 鍏抽棴涓存椂鍥捐〃
                        plt.close(temp_fig)
                        plt.close()
            
            # 淇濆瓨涓诲垎鏋愮粨鏋?            # 鍒涘缓鏍囧噯鍒嗘瀽鍥捐〃
            standard_fig = plt.figure(figsize=(8, 6), dpi=100)
            
            # 浣跨敤find_glass_transition_temperature閲嶆柊鐢熸垚鏍囧噯鍥捐〃
            find_glass_transition_temperature(
                self.temperatures, self.densities, 
                output_path=save_path,
                sample_name=sample_name,
                interactive=False,
                y_margin=0.05,
                language=self.language_var.get()
            )
            
            # 淇濆瓨鏁版嵁缁撴灉
            csv_path = os.path.join(save_dir, f"{name_without_ext}_data.csv")
            save_results_to_file(self.temperatures, self.densities, self.tg_result, csv_path, language=self.language_var.get())
            
            # 鏄剧ず鎴愬姛娑堟伅
            messagebox.showinfo(
                "淇濆瓨鎴愬姛" if self.language_var.get() == "cn" else "Save Successful", 
                (f"缁撴灉宸蹭繚瀛樿嚦:\n{save_path}\n\n" +
                 (f"璇︾粏鍒嗘瀽宸蹭繚瀛樿嚦:\n{detailed_path}\n\n" if self.detailed_var.get() else "") +
                 f"鏁版嵁宸蹭繚瀛樿嚦:\n{csv_path}") if self.language_var.get() == "cn" else
                (f"Results saved to:\n{save_path}\n\n" +
                 (f"Detailed analysis saved to:\n{detailed_path}\n\n" if self.detailed_var.get() else "") +
                 f"Data saved to:\n{csv_path}")
            )
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "缁撴灉宸蹭繚瀛? if self.language_var.get() == "cn" else "Results saved"
            )
            
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                f"淇濆瓨缁撴灉澶辫触: {str(e)}" if self.language_var.get() == "cn" else f"Failed to save results: {str(e)}"
            )
    
    def toggle_language(self):
        """鍒囨崲鐣岄潰璇█鍜屽浘琛ㄦ爣绛捐瑷€"""
        current_lang = self.language_var.get()
        
        if current_lang == "cn":
            # 鍒囨崲鍒拌嫳鏂?            self.language_var.set("en")
            self.lang_button.config(text="涓枃")
            self.root.title("Glass Transition Temperature Analysis Tool")
            
            # 鏇存柊UI鏂囨湰
            self.file_frame.config(text="Data File Selection")
            self.sample_frame.config(text="Sample Information")
            self.data_frame.config(text="Data Preview")
            self.preview_frame.config(text="Data Visualization Preview")
            self.control_frame.config(text="Operation Controls")
            
            # 鏇存柊鏍戠姸鍥炬爣棰?            self.tree.heading("index", text="Index", anchor=tk.CENTER)
            self.tree.heading("temperature", text="Temperature (K)", anchor=tk.CENTER)
            self.tree.heading("density", text=f"Density ({DENSITY_UNIT})", anchor=tk.CENTER)
            
            # 鏇存柊鎸夐挳鏂囨湰
            self.browse_button.config(text="Browse...")
            self.load_button.config(text="Load Data")
            self.analyze_button.config(text="Confirm Analysis")
            self.interactive_button.config(text="Interactive Analysis")
            self.save_button.config(text="Save Results")
            self.save_data_button.config(text="Save Data")
            self.detailed_check.config(text="Generate Detailed Analysis")
            
            # 鏇存柊鐘舵€?            current_status = self.status_var.get()
            if current_status == "鍑嗗灏辩华 - 璇烽€夋嫨鏁版嵁鏂囦欢":
                self.status_var.set("Ready - Please select a data file")
            
            # 濡傛灉瀛樺湪浜や簰寮忔鏋讹紝鏇存柊鍏舵枃鏈?            if hasattr(self, 'interactive_frame_visible') and self.interactive_frame_visible:
                self.interactive_frame.config(text="Interactive Tg Analysis")
                
                # 灏濊瘯鏇存柊浜や簰寮忔鏋朵腑鐨勬帶浠?                for widget in self.interactive_frame.winfo_children():
                    if isinstance(widget, ttk.LabelFrame) and widget.cget("text") == "Y-axis Display Control":
                        widget.config(text="Y杞存樉绀烘帶鍒?)
                
                # 濡傛灉鏈夋帶鍒剁偣鍒囨崲鎸夐挳锛屾洿鏂板叾鏂囨湰
                if hasattr(self, 'toggle_points_btn'):
                    if self.show_points_var.get():
                        self.toggle_points_btn.config(text="Hide Control Points")
                    else:
                        self.toggle_points_btn.config(text="Show Control Points")
                
                # 濡傛灉鏈変氦鐐硅烦杞寜閽紝鏇存柊鍏舵枃鏈?                if hasattr(self, 'intercept_btn'):
                    self.intercept_btn.config(text="Jump to Intercept")
        else:
            # 鍒囨崲鍒颁腑鏂?            self.language_var.set("cn")
            self.lang_button.config(text="English")
            self.root.title("鐜荤拑鍖栬浆鍙樻俯搴﹀垎鏋愬伐鍏?)
            
            # 鏇存柊UI鏂囨湰
            self.file_frame.config(text="鏁版嵁鏂囦欢閫夋嫨")
            self.sample_frame.config(text="鏍峰搧淇℃伅")
            self.data_frame.config(text="鏁版嵁棰勮")
            self.preview_frame.config(text="鏁版嵁鍙鍖栭瑙?)
            self.control_frame.config(text="鎿嶄綔鎺у埗")
            
            # 鏇存柊鏍戠姸鍥炬爣棰?            self.tree.heading("index", text="搴忓彿", anchor=tk.CENTER)
            self.tree.heading("temperature", text="娓╁害 (K)", anchor=tk.CENTER)
            self.tree.heading("density", text=f"瀵嗗害 ({DENSITY_UNIT})", anchor=tk.CENTER)
            
            # 鏇存柊鎸夐挳鏂囨湰
            self.browse_button.config(text="娴忚...")
            self.load_button.config(text="鍔犺浇鏁版嵁")
            self.analyze_button.config(text="纭鍒嗘瀽")
            self.interactive_button.config(text="浜や簰寮忓垎鏋?)
            self.save_button.config(text="淇濆瓨缁撴灉")
            self.save_data_button.config(text="淇濆瓨鏁版嵁")
            self.detailed_check.config(text="鐢熸垚璇︾粏鍒嗘瀽")
            
            # 鏇存柊鐘舵€?            current_status = self.status_var.get()
            if current_status == "Ready - Please select a data file":
                self.status_var.set("鍑嗗灏辩华 - 璇烽€夋嫨鏁版嵁鏂囦欢")
            
            # 濡傛灉瀛樺湪浜や簰寮忔鏋讹紝鏇存柊鍏舵枃鏈?            if hasattr(self, 'interactive_frame_visible') and self.interactive_frame_visible:
                self.interactive_frame.config(text="浜や簰寮廡g鍒嗘瀽")
                
                # 灏濊瘯鏇存柊浜や簰寮忔鏋朵腑鐨勬帶浠?                for widget in self.interactive_frame.winfo_children():
                    if isinstance(widget, ttk.LabelFrame) and widget.cget("text") == "Y-axis Display Control":
                        widget.config(text="Y杞存樉绀烘帶鍒?)
                
                # 濡傛灉鏈夋帶鍒剁偣鍒囨崲鎸夐挳锛屾洿鏂板叾鏂囨湰
                if hasattr(self, 'toggle_points_btn'):
                    if self.show_points_var.get():
                        self.toggle_points_btn.config(text="闅愯棌鎺у埗鐐?)
                    else:
                        self.toggle_points_btn.config(text="鏄剧ず鎺у埗鐐?)
                
                # 濡傛灉鏈変氦鐐硅烦杞寜閽紝鏇存柊鍏舵枃鏈?                if hasattr(self, 'intercept_btn'):
                    self.intercept_btn.config(text="璺宠浆鍒颁氦鐐?)
        
        # 濡傛灉鏈夐瑙堝浘锛屾洿鏂伴瑙堝浘
        self.refresh_preview()
        
        # 濡傛灉鏈変氦浜掑紡鍥撅紝鍒锋柊浜や簰寮忓浘
        if hasattr(self, 'interactive_fig') and self.interactive_fig is not None:
            self.refresh_interactive_plot()
            
    def refresh_preview(self):
        """鍒锋柊棰勮鍥撅紝搴旂敤褰撳墠璇█璁剧疆"""
        if hasattr(self, 'preview_canvas') and self.preview_canvas is not None and self.data_loaded:
            # 娓呴櫎棰勮妗嗘灦鍐呭
            for widget in self.preview_frame.winfo_children():
                widget.destroy()
            
            # 浣跨敤褰撳墠璇█鍒涘缓棰勮鍥?            self.create_preview_plot()

    def save_data(self):
        """淇濆瓨鍒嗘瀽鏁版嵁鍒癈SV鍜孹LSX鏍煎紡"""
        if not hasattr(self, 'tg_result') or self.tg_result is None:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                "璇峰厛瀹屾垚Tg鍒嗘瀽" if self.language_var.get() == "cn" else "Please complete Tg analysis first"
            )
            return
            
        if not hasattr(self, 'interactive_draggable') or self.interactive_draggable is None:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error",
                "闇€瑕佸厛杩涜浜や簰寮忓垎鏋? if self.language_var.get() == "cn" else "Interactive analysis is required first"
            )
            return
            
        try:
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
            
            # 鏄剧ず淇濆瓨瀵硅瘽妗?            filetypes = [
                ("CSV鏂囦欢", "*.csv") if self.language_var.get() == "cn" else ("CSV Files", "*.csv"),
                ("Excel鏂囦欢", "*.xlsx") if self.language_var.get() == "cn" else ("Excel Files", "*.xlsx")
            ]
            
            save_path = filedialog.asksaveasfilename(
                title=("閫夋嫨淇濆瓨璺緞" if self.language_var.get() == "cn" else "Select Save Path"),
                defaultextension=".csv",
                filetypes=filetypes,
                initialfile=f"glass_transition_data_{sample_name}"
            )
            
            if not save_path:
                return
                
            # 鑾峰彇娓╁害鍜屽瘑搴︽暟鎹?            temperatures = self.temperatures
            densities = self.densities
            tg = self.tg_result
            
            # 鑾峰彇鎷熷悎鍙傛暟
            high_params = self.interactive_draggable.cached_high_params
            low_params = self.interactive_draggable.cached_low_params
            
            if not high_params or not low_params:
                messagebox.showerror(
                    "閿欒" if self.language_var.get() == "cn" else "Error",
                    "鏃犳硶鑾峰彇鎷熷悎鍙傛暟" if self.language_var.get() == "cn" else "Cannot get fitting parameters"
                )
                return
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "姝ｅ湪淇濆瓨鏁版嵁..." if self.language_var.get() == "cn" else "Saving data..."
            )
            self.root.update()
            
            # 鍒涘缓DataFrame
            data = {
                ('娓╁害 (K)' if self.language_var.get() == "cn" else 'Temperature (K)'): temperatures,
                (f'瀵嗗害 ({DENSITY_UNIT})' if self.language_var.get() == "cn" else f'Density ({DENSITY_UNIT})'): densities
            }
            
            # 娣诲姞鍖哄煙鏍囩
            regions = []
            for temp in temperatures:
                if temp > tg:
                    regions.append('娑蹭綋' if self.language_var.get() == "cn" else 'Liquid')
                else:
                    regions.append('鐜荤拑' if self.language_var.get() == "cn" else 'Glass')
            
            data['鍖哄煙' if self.language_var.get() == "cn" else 'Region'] = regions
            
            # 娣诲姞鎷熷悎鏁版嵁
            high_temps = [t for t in temperatures if t > tg]
            low_temps = [t for t in temperatures if t <= tg]
            
            high_fits = []
            low_fits = []
            
            # 璁＄畻楂樻俯鍖烘嫙鍚堝€?            for t in temperatures:
                if t > tg:
                    high_fits.append(high_params[0] * t + high_params[1])
                else:
                    high_fits.append(None)
            
            # 璁＄畻浣庢俯鍖烘嫙鍚堝€?            for t in temperatures:
                if t <= tg:
                    low_fits.append(low_params[0] * t + low_params[1])
                else:
                    low_fits.append(None)
            
            data['楂樻俯鎷熷悎' if self.language_var.get() == "cn" else 'High Temp Fit'] = high_fits
            data['浣庢俯鎷熷悎' if self.language_var.get() == "cn" else 'Low Temp Fit'] = low_fits
            
            # 鍒涘缓DataFrame
            df = pd.DataFrame(data)
            
            # 娣诲姞鎷熷悎鍙傛暟鍜孴g鍊煎埌绗簩涓〃
            fit_info = {
                '鍙傛暟' if self.language_var.get() == "cn" else 'Parameter': [
                    '楂樻俯鏂滅巼' if self.language_var.get() == "cn" else 'High Temp Slope',
                    '楂樻俯鎴窛' if self.language_var.get() == "cn" else 'High Temp Intercept',
                    '浣庢俯鏂滅巼' if self.language_var.get() == "cn" else 'Low Temp Slope',
                    '浣庢俯鎴窛' if self.language_var.get() == "cn" else 'Low Temp Intercept',
                    'Tg (K)'
                ],
                '鍊? if self.language_var.get() == "cn" else 'Value': [
                    high_params[0],
                    high_params[1],
                    low_params[0],
                    low_params[1],
                    tg
                ]
            }
            
            fit_df = pd.DataFrame(fit_info)
            
            # 淇濆瓨涓篊SV鎴朮LSX
            file_ext = os.path.splitext(save_path)[1].lower()
            
            if file_ext == '.csv':
                df.to_csv(save_path, index=False, encoding='utf-8-sig')
                
                # 淇濆瓨鎷熷悎鍙傛暟鍒板彟涓€涓狢SV鏂囦欢
                params_path = os.path.splitext(save_path)[0] + '_params.csv'
                fit_df.to_csv(params_path, index=False, encoding='utf-8-sig')
                
                messagebox.showinfo(
                    "淇濆瓨鎴愬姛" if self.language_var.get() == "cn" else "Save Successful",
                    (f"鏁版嵁宸蹭繚瀛樿嚦:\n{save_path}\n\n鍙傛暟宸蹭繚瀛樿嚦:\n{params_path}") 
                    if self.language_var.get() == "cn" else 
                    (f"Data saved to:\n{save_path}\n\nParameters saved to:\n{params_path}")
                )
            elif file_ext == '.xlsx':
                # 鍒涘缓Excel鏂囦欢骞舵坊鍔犱袱涓〃
                with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='鏁版嵁' if self.language_var.get() == "cn" else 'Data', index=False)
                    fit_df.to_excel(writer, sheet_name='鎷熷悎鍙傛暟' if self.language_var.get() == "cn" else 'Fitting Parameters', index=False)
                
                messagebox.showinfo(
                    "淇濆瓨鎴愬姛" if self.language_var.get() == "cn" else "Save Successful",
                    (f"鏁版嵁鍜屽弬鏁板凡淇濆瓨鑷?\n{save_path}") 
                    if self.language_var.get() == "cn" else 
                    (f"Data and parameters saved to:\n{save_path}")
                )
            else:
                # 榛樿淇濆瓨涓篊SV
                csv_path = os.path.splitext(save_path)[0] + '.csv'
                df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                
                params_path = os.path.splitext(csv_path)[0] + '_params.csv'
                fit_df.to_csv(params_path, index=False, encoding='utf-8-sig')
                
                messagebox.showinfo(
                    "淇濆瓨鎴愬姛" if self.language_var.get() == "cn" else "Save Successful",
                    (f"鏁版嵁宸蹭繚瀛樿嚦:\n{csv_path}\n\n鍙傛暟宸蹭繚瀛樿嚦:\n{params_path}") 
                    if self.language_var.get() == "cn" else 
                    (f"Data saved to:\n{csv_path}\n\nParameters saved to:\n{params_path}")
                )
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "鏁版嵁宸蹭繚瀛? if self.language_var.get() == "cn" else "Data saved"
            )
            
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                f"淇濆瓨鏁版嵁澶辫触: {str(e)}" if self.language_var.get() == "cn" else f"Failed to save data: {str(e)}"
            )

    def _on_window_resize(self, event):
        """绐楀彛澶у皬鍙樺寲鏃舵洿鏂癈anvas澶у皬"""
        # 纭繚event涓嶆槸鐢卞瓙鎺т欢瑙﹀彂鐨?        if event.widget == self.root:
            # 鏇存柊Canvas瀹藉害涓庣獥鍙ｅぇ灏忓尮閰?            width = event.width - self.scrollbar.winfo_width() - 5  # 鍑忓幓婊氬姩鏉″搴﹀拰涓€鐐瑰～鍏?            if width > 0:
                self.main_canvas.config(width=width)
                # 璋冩暣scrollable_frame瀹藉害
                self.main_canvas.itemconfig(self.frame_id, width=width)

    def _on_mousewheel(self, event):
        """澶勭悊榧犳爣婊氳疆浜嬩欢锛屽吋瀹逛笉鍚屾搷浣滅郴缁?""
        if sys.platform.startswith('win'):
            # Windows骞冲彴
            self.main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        elif sys.platform == 'darwin':
            # macOS骞冲彴
            self.main_canvas.yview_scroll(int(-1*event.delta), "units")
        else:
            # Linux骞冲彴
            if event.num == 4:
                self.main_canvas.yview_scroll(-1, "units")
            elif event.num == 5:
                self.main_canvas.yview_scroll(1, "units")
    
    def browse_file(self):
        """娴忚骞堕€夋嫨鏁版嵁鏂囦欢"""
        filetypes = [
            ('CSV鏂囦欢', '*.csv'),
            ('Excel鏂囦欢', '*.xlsx *.xls'),
            ('鎵€鏈夋枃浠?, '*.*')
        ]
        filepath = filedialog.askopenfilename(
            title="閫夋嫨鏁版嵁鏂囦欢",
            filetypes=filetypes
        )
        if filepath:
            self.file_path_var.set(filepath)
            # 浠庢枃浠跺悕鎻愬彇鏍峰搧鍚嶇О
            base_name = os.path.basename(filepath)
            sample_name = os.path.splitext(base_name)[0]
            self.sample_name_var.set(sample_name)
    
    def on_density_column_changed(self, event):
        """澶勭悊瀵嗗害鍒楅€夋嫨鍙樻洿浜嬩欢"""
        if not self.is_multi_density or not self.data_loaded:
            return
            
        selected = self.density_column_var.get()
        if selected:
            # 鏌ユ壘閫変腑鐨勫瘑搴﹀垪绱㈠紩
            for i, name in enumerate(self.density_column_names):
                if name == selected:
                    self.active_density_column = i
                    break
            
            # 鏇存柊褰撳墠浣跨敤鐨勫瘑搴︽暟鎹?            self.densities = self.densities_columns[self.active_density_column]
            
            # 鏇存柊琛ㄦ牸鏄剧ず
            self.update_data_preview()
            
            # 鏇存柊鍥捐〃棰勮
            self.create_preview_plot()
            
            # 娓呴櫎涔嬪墠鐨勪氦浜掑紡鍒嗘瀽缁撴灉
            if hasattr(self, 'interactive_fig') and self.interactive_fig is not None:
                plt.close(self.interactive_fig)
                self.interactive_fig = None
                self.interactive_draggable = None
    
    def load_data(self):
        """鍔犺浇鏁版嵁鏂囦欢"""
        filepath = self.file_path_var.get().strip()
        if not filepath:
            messagebox.showerror("閿欒", "璇峰厛閫夋嫨鏁版嵁鏂囦欢")
            return
            
        try:
            self.status_var.set("姝ｅ湪鍔犺浇鏁版嵁...")
            self.root.update()
            
            # 璇诲彇鏁版嵁锛岀幇鍦ㄦ敮鎸佸鍒楀瘑搴?            result = read_data_from_file(filepath)
            if len(result) == 3:  # 鏂扮殑杩斿洖鏍煎紡鍖呭惈澶氬垪瀵嗗害鏍囧織
                self.temperatures, data, self.is_multi_density = result
                
                if self.is_multi_density:
                    self.densities_columns = data  # 澶氬垪瀵嗗害鏁版嵁
                    self.density_column_names = []
                    
                    # 鎻愬彇鏂囦欢涓殑瀵嗗害鍒楀悕绉?                    file_ext = os.path.splitext(filepath)[1].lower()
                    if file_ext == '.csv':
                        df = pd.read_csv(filepath)
                    elif file_ext in ['.xls', '.xlsx']:
                        df = pd.read_excel(filepath)
                    
                    # 甯歌鐨勬俯搴﹀垪鍚?                    temp_patterns = ['Temperature', 'Temp', 'T', '娓╁害']
                    # 鏌ユ壘闈炴俯搴﹀垪浣滀负瀵嗗害鍒?                    for col in df.columns:
                        if not any(pattern.lower() in col.lower() for pattern in temp_patterns):
                            self.density_column_names.append(col)
                    
                    # 濡傛灉娌℃湁鎵惧埌瀵嗗害鍒楀悕绉帮紝鐢熸垚榛樿鍒楀悕
                    if len(self.density_column_names) == 0:
                        self.density_column_names = [f"瀵嗗害{i+1}" for i in range(len(self.densities_columns))]
                    
                    # 璁剧疆榛樿浣跨敤绗竴鍒楀瘑搴︽暟鎹?                    self.active_density_column = 0
                    self.densities = self.densities_columns[self.active_density_column]
                    
                    # 鏇存柊瀵嗗害鍒楅€夋嫨涓嬫媺妗?                    self.density_column_combobox['values'] = self.density_column_names
                    self.density_column_var.set(self.density_column_names[0])
                    
                    # 鏄剧ず澶氬垪瀵嗗害琛ㄦ牸
                    self.display_multi_density_table()
                else:
                    self.densities = data  # 鍗曞垪瀵嗗害鏁版嵁
            else:
                # 鍏煎鏃х殑杩斿洖鏍煎紡
                self.temperatures, self.densities = result
                self.is_multi_density = False
            
            self.data_loaded = True
            
            # 鏇存柊鏁版嵁棰勮琛ㄦ牸
            self.update_data_preview()
            
            # 鏇存柊鐘舵€?            self.status_var.set("宸插姞杞?{} 涓暟鎹偣 - 鐐瑰嚮'鍒嗘瀽鏁版嵁'缁х画".format(len(self.temperatures)))
            
            # 鐢熸垚棰勮鍥?            self.create_preview_plot()
            
        except Exception as e:
            messagebox.showerror("閿欒", f"鍔犺浇鏁版嵁澶辫触: {str(e)}")
            self.status_var.set("鏁版嵁鍔犺浇澶辫触")
    
    def update_data_preview(self):
        """鏇存柊鏁版嵁棰勮琛ㄦ牸"""
        # 娓呯┖鐜版湁琛ㄦ牸
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 濉厖琛ㄦ牸
        for i, (temp, dens) in enumerate(zip(self.temperatures, self.densities)):
            self.tree.insert("", tk.END, values=(i+1, f"{temp:.1f}", f"{dens:.4f}"))
    
    def display_multi_density_table(self):
        """鏄剧ず澶氬垪瀵嗗害鏁版嵁琛ㄦ牸"""
        # 娓呯┖澶氬垪瀵嗗害妗嗘灦鍐呭
        for widget in self.multi_density_frame.winfo_children():
            widget.destroy()
        
        # 鍒涘缓琛ㄦ牸
        columns = ["娓╁害(K)"] + self.density_column_names
        
        # 浣跨敤ttk.Treeview鍒涘缓澶氬垪琛ㄦ牸
        multi_tree_frame = ttk.Frame(self.multi_density_frame)
        multi_tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 娣诲姞鍨傜洿婊氬姩鏉?        vsb = ttk.Scrollbar(multi_tree_frame, orient="vertical")
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 娣诲姞姘村钩婊氬姩鏉?        hsb = ttk.Scrollbar(multi_tree_frame, orient="horizontal")
        hsb.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 鍒涘缓鏍戠姸琛ㄦ牸
        tree_columns = tuple(f"#{i}" for i in range(len(columns)))
        multi_tree = ttk.Treeview(multi_tree_frame, columns=tree_columns, 
                                 yscrollcommand=vsb.set, xscrollcommand=hsb.set,
                                 height=10)
        
        # 璁剧疆婊氬姩鏉?        vsb.configure(command=multi_tree.yview)
        hsb.configure(command=multi_tree.xview)
        
        # 璁剧疆鍒楀鍜屽垪鍚?        multi_tree.column("#0", width=0, stretch=tk.NO)
        for i, col in enumerate(columns):
            col_id = f"#{i}"
            multi_tree.column(col_id, anchor=tk.CENTER, width=120, stretch=tk.YES)
            multi_tree.heading(col_id, text=col, anchor=tk.CENTER)
        
        # 濉厖鏁版嵁
        for i, temp in enumerate(self.temperatures):
            values = [f"{temp:.1f}"]
            for col_densities in self.densities_columns:
                values.append(f"{col_densities[i]:.4f}")
            multi_tree.insert("", tk.END, values=values)
        
        multi_tree.pack(fill=tk.BOTH, expand=True)
        
        # 鐜板湪鏄剧ず澶氬垪瀵嗗害妗嗘灦
        self.multi_density_frame.pack(fill=tk.BOTH, expand=False, padx=5, pady=5)
    
    def create_preview_plot(self):
        """鍒涘缓娓╁害-瀵嗗害鏁版嵁鐨勯瑙堝浘"""
        if not self.data_loaded or len(self.temperatures) == 0:
            return
        
        # 娓呴櫎棰勮妗嗘灦涓殑鍐呭
        for widget in self.preview_frame.winfo_children():
            widget.destroy()
        
        # 鍒涘缓棰勮鍥撅紝浣跨敤鏇村ぇ鐨勫昂瀵?        fig, ax = plt.subplots(figsize=(10, 5), dpi=100)
        
        # 浣跨敤鍏呭垎鍒╃敤鍙敤瀹藉害鐨凢rame
        canvas_frame = ttk.Frame(self.preview_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # 浣跨敤褰撳墠璇█璁剧疆
        current_lang = self.language_var.get()
        if current_lang == "en":
            xlabel = "Temperature (K)"
            ylabel = f"Density ({DENSITY_UNIT})"
            title = "Temperature-Density Preview"
        else:
            xlabel = "娓╁害 (K)"
            ylabel = f"瀵嗗害 ({DENSITY_UNIT})"
            title = "娓╁害-瀵嗗害鏁版嵁棰勮"
        
        # 鏍规嵁鏄惁涓哄鍒楀瘑搴︽暟鎹喅瀹氱粯鍥炬柟寮?        if self.is_multi_density and hasattr(self, 'densities_columns') and len(self.densities_columns) > 0:
            # 浣跨敤涓嶅悓棰滆壊缁樺埗澶氬垪瀵嗗害鏁版嵁
            colors = plt.cm.tab10.colors  # 浣跨敤tab10鑹插僵鏄犲皠鑾峰彇涓嶅悓棰滆壊
            
            # 缁樺埗鎵€鏈夊瘑搴﹀垪鏁版嵁
            for i, densities in enumerate(self.densities_columns):
                color_idx = i % len(colors)  # 闃叉绱㈠紩瓒呭嚭鑼冨洿
                label = self.density_column_names[i] if i < len(self.density_column_names) else f"瀵嗗害{i+1}"
                ax.scatter(self.temperatures, densities, 
                          color=colors[color_idx], alpha=0.7, 
                          label=label, s=40, marker='o')
            
            # 楂樹寒鏄剧ず褰撳墠閫変腑鐨勫瘑搴﹀垪
            ax.scatter(self.temperatures, self.densities_columns[self.active_density_column], 
                       color=colors[self.active_density_column % len(colors)], 
                       s=60, marker='o', edgecolor='black', linewidth=1.5,
                       label=f"{self.density_column_names[self.active_density_column]} (褰撳墠閫変腑)")
                       
            # 娣诲姞鍥句緥
            ax.legend(loc='best', fontsize=9)
        else:
            # 鍗曞垪瀵嗗害鏁版嵁浣跨敤榛樿钃濊壊
            ax.scatter(self.temperatures, self.densities, color='#3182bd', alpha=0.8)
        
        ax.set_xlabel(xlabel)
        ax.set_ylabel(ylabel)
        ax.set_title(title)
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 璋冩暣鍥惧舰浠ュ～婊＄獥鍙?        fig.tight_layout()
        
        # 灏嗗浘琛ㄥ祵鍏ュ埌Tkinter鐣岄潰锛屼娇鐢╢ill=BOTH鍜宔xpand=True
        canvas = FigureCanvasTkAgg(fig, master=canvas_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 娣诲姞宸ュ叿鏍忎互鍏佽鐢ㄦ埛涓庡浘琛ㄤ氦浜?        toolbar = NavigationToolbar2Tk(canvas, canvas_frame)
        toolbar.update()
        
        # 淇濆瓨鐢诲竷瀵硅薄渚涘悗缁娇鐢?        self.preview_canvas = canvas
    
    def analyze_data(self):
        """鍒嗘瀽鏁版嵁骞剁敓鎴愮幓鐠冨寲杞彉娓╁害缁撴灉"""
        if not self.data_loaded or len(self.temperatures) == 0:
            messagebox.showerror("閿欒" if self.language_var.get() == "cn" else "Error", 
                              "璇峰厛鍔犺浇鏈夋晥鏁版嵁" if self.language_var.get() == "cn" else "Please load valid data first")
            return
            
        try:
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
            
            # 鏇存柊鐘舵€?            self.status_var.set("姝ｅ湪鍒嗘瀽鏁版嵁..." if self.language_var.get() == "cn" else "Analyzing data...")
            self.root.update()
            
            # 鍒涘缓鍒嗘瀽鍥捐〃
            plt.figure(figsize=(8, 6), dpi=100)
            
            # 鑾峰彇Y杞磋缃紙濡傛灉鏈夛級
            y_margin = self.y_margin_var.get() if hasattr(self, 'y_margin_var') else 0.05
            y_min = None
            y_max = None
            
            if hasattr(self, 'enable_manual_range') and self.enable_manual_range.get():
                try:
                    y_min = float(self.y_min_var.get())
                    y_max = float(self.y_max_var.get())
                except:
                    pass
            
            # 妫€鏌ユ槸鍚﹀凡鏈変氦浜掑紡鍒嗘瀽缁撴灉
            if hasattr(self, 'interactive_draggable') and self.interactive_draggable:
                # 鐩存帴浣跨敤浜や簰寮忓垎鏋愮殑Tg鍊?                self.tg_result = self.interactive_draggable.x
                
                # 鍒涘缓涓庝氦浜掑紡鍒嗘瀽鐩稿悓鍙傛暟鐨勫垎鏋愬浘琛紙浣嗕笉鏄氦浜掑紡鐨勶級
                plt.figure(figsize=(8, 6), dpi=100)
                find_glass_transition_temperature(
                    self.temperatures, self.densities, 
                    sample_name=sample_name,
                    interactive=False,
                    y_margin=y_margin,
                    y_min=y_min,
                    y_max=y_max,
                    language=self.language_var.get()
                )
                
                # 鐢熸垚璇︾粏鍒嗘瀽锛堝鏋滈€変腑锛?                if self.detailed_var.get():
                    # 浠庝氦浜掑紡鍒嗘瀽涓幏鍙栨嫙鍚堝弬鏁?                    high_params = self.interactive_draggable.cached_high_params
                    low_params = self.interactive_draggable.cached_low_params
                    
                    if high_params and low_params:
                        best_params = (high_params, low_params)
                        # 鍒涘缓璇︾粏鍒嗘瀽锛屼紶閫掑鍒楀瘑搴︽暟鎹紙濡傛灉鏈夛級
                        if self.is_multi_density and hasattr(self, 'densities_columns') and len(self.densities_columns) > 0:
                            create_detailed_analysis(
                                self.temperatures, self.densities,
                                best_params, self.tg_result,
                                sample_name=sample_name,
                                y_margin=y_margin,
                                y_min=y_min,
                                y_max=y_max,
                                language=self.language_var.get(),
                                from_interactive=True,
                                densities_columns=self.densities_columns,
                                density_column_names=self.density_column_names
                            )
                        else:
                            create_detailed_analysis(
                                self.temperatures, self.densities,
                                best_params, self.tg_result,
                                sample_name=sample_name,
                                y_margin=y_margin,
                                y_min=y_min,
                                y_max=y_max,
                                language=self.language_var.get(),
                                from_interactive=True
                            )
                        plt.figure()  # 鍒涘缓鏂板浘褰互鏄剧ず璇︾粏鍒嗘瀽
                
                # 鏄剧ず鍥捐〃
                plt.show()
            else:
                # 娌℃湁浜や簰寮忓垎鏋愮粨鏋滐紝鎵ц姝ｅ父鍒嗘瀽
                self.tg_result = find_glass_transition_temperature(
                    self.temperatures, self.densities, 
                    sample_name=sample_name,
                    language=self.language_var.get()
                )
            
            # 鏇存柊鐘舵€?            self.status_var.set(f"鍒嗘瀽瀹屾垚! Tg = {self.tg_result:.1f}K" if self.language_var.get() == "cn" 
                             else f"Analysis complete! Tg = {self.tg_result:.1f}K")
            
            # 鏄剧ず缁撴灉
            messagebox.showinfo(
                "鍒嗘瀽缁撴灉" if self.language_var.get() == "cn" else "Analysis Result", 
                f"鐜荤拑鍖栬浆鍙樻俯搴?Tg): {self.tg_result:.1f}K" if self.language_var.get() == "cn" 
                else f"Glass Transition Temperature (Tg): {self.tg_result:.1f}K"
            )
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                f"鍒嗘瀽杩囩▼涓嚭閿? {str(e)}" if self.language_var.get() == "cn" 
                else f"Error during analysis: {str(e)}"
            )

    def interactive_analyze(self):
        """鍚姩浜や簰寮忓垎鏋愭ā寮?""
        if not self.data_loaded or len(self.temperatures) == 0:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                "璇峰厛鍔犺浇鏈夋晥鏁版嵁" if self.language_var.get() == "cn" else "Please load valid data first"
            )
            return
            
        try:
            self.status_var.set(
                "姝ｅ湪鍒涘缓浜や簰寮忓垎鏋?.." if self.language_var.get() == "cn" 
                else "Creating interactive analysis..."
            )
            self.root.update()
            
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
            
            # 纭繚浜や簰寮忔鏋跺凡鏄剧ず
            if not hasattr(self, 'interactive_frame_visible') or not self.interactive_frame_visible:
                self.interactive_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
                self.interactive_frame_visible = True
            
            # 娓呯┖浜や簰寮忔鏋?            for widget in self.interactive_frame.winfo_children():
                widget.destroy()
            
            # 鍒涘缓Y杞存帶鍒跺尯鍩?            y_control_frame = ttk.LabelFrame(
                self.interactive_frame, 
                text="Y杞存樉绀烘帶鍒? if self.language_var.get() == "cn" else "Y-axis Display Control", 
                padding=5
            )
            y_control_frame.pack(fill=tk.X, pady=5)
            
            # 鑷姩缂╂斁鎺у埗鍖哄煙
            auto_scale_frame = ttk.Frame(y_control_frame)
            auto_scale_frame.pack(fill=tk.X, pady=5)
            
            ttk.Label(
                auto_scale_frame, 
                text="鑷姩缂╂斁杈硅窛:" if self.language_var.get() == "cn" else "Auto Scale Margin:"
            ).pack(side=tk.LEFT, padx=5)
            
            # Y杞磋竟璺濇帶鍒舵粦鍧?            self.y_margin_var = tk.DoubleVar(value=0.05)
            y_margin_scale = ttk.Scale(auto_scale_frame, from_=0.01, to=0.5, 
                                       variable=self.y_margin_var, length=200, 
                                       orient=tk.HORIZONTAL)
            y_margin_scale.pack(side=tk.LEFT, padx=5)
            
            # 褰撳墠鍊兼樉绀?            y_margin_label = ttk.Label(auto_scale_frame, text="0.05")
            y_margin_label.pack(side=tk.LEFT, padx=5)
            
            # 鏇存柊婊戝潡鏍囩鐨勫嚱鏁?            def update_margin_label(*args):
                y_margin_label.config(text=f"{self.y_margin_var.get():.2f}")
            
            # 缁戝畾婊戝潡鍊煎彉鍖栦簨浠?            self.y_margin_var.trace_add("write", update_margin_label)
            
            # 鎵嬪姩鑼冨洿鎺у埗鍖哄煙
            manual_range_frame = ttk.Frame(y_control_frame)
            manual_range_frame.pack(fill=tk.X, pady=5)
            
            # 鍚敤鎵嬪姩鑼冨洿閫夐」
            self.enable_manual_range = tk.BooleanVar(value=False)
            manual_check = ttk.Checkbutton(
                manual_range_frame, 
                text="鍚敤鎵嬪姩Y杞磋寖鍥? if self.language_var.get() == "cn" else "Enable Manual Y-axis Range", 
                variable=self.enable_manual_range
            )
            manual_check.pack(side=tk.LEFT, padx=5)
            
            # Y杞存渶灏忓€?            ttk.Label(
                manual_range_frame, 
                text="Y杞存渶灏忓€?" if self.language_var.get() == "cn" else "Y-axis Min:"
            ).pack(side=tk.LEFT, padx=5)
            self.y_min_var = tk.StringVar()
            y_min_entry = ttk.Entry(manual_range_frame, textvariable=self.y_min_var, width=8)
            y_min_entry.pack(side=tk.LEFT, padx=5)
            
            # Y杞存渶澶у€?            ttk.Label(
                manual_range_frame, 
                text="Y杞存渶澶у€?" if self.language_var.get() == "cn" else "Y-axis Max:"
            ).pack(side=tk.LEFT, padx=5)
            self.y_max_var = tk.StringVar()
            y_max_entry = ttk.Entry(manual_range_frame, textvariable=self.y_max_var, width=8)
            y_max_entry.pack(side=tk.LEFT, padx=5)
            
            # 鑾峰彇榛樿鐨刌杞磋寖鍥?(澶х害鏈€灏忓€煎拰鏈€澶у€?
            y_min_default = min(self.densities) - 0.1
            y_max_default = max(self.densities) + 0.1
            self.y_min_var.set(f"{y_min_default:.3f}")
            self.y_max_var.set(f"{y_max_default:.3f}")
            
            # 鍒锋柊鎸夐挳
            refresh_button = ttk.Button(
                y_control_frame, 
                text="搴旂敤璁剧疆" if self.language_var.get() == "cn" else "Apply Settings", 
                command=self.refresh_interactive_plot
            )
            refresh_button.pack(side=tk.RIGHT, padx=10)
            
            # 鍒涘缓浜や簰寮廡g鍒嗘瀽
            self.tg_result, self.interactive_fig, self.interactive_draggable = find_glass_transition_temperature(
                self.temperatures, self.densities, 
                sample_name=sample_name,
                interactive=True,
                y_margin=self.y_margin_var.get(),
                language=self.language_var.get()
            )
            
            # 鍒涘缓鍥捐〃瀹瑰櫒妗嗘灦锛岀‘淇濆～婊℃暣涓搴?            chart_frame = ttk.Frame(self.interactive_frame)
            chart_frame.pack(fill=tk.BOTH, expand=True, pady=5)
            
            # 浼樺寲鍥捐〃甯冨眬
            self.interactive_fig.tight_layout()
            
            # 鍒涘缓鏂扮敾甯?            self.interactive_canvas = FigureCanvasTkAgg(self.interactive_fig, master=chart_frame)
            self.interactive_canvas.draw()
            
            # 纭繚鍥捐〃濉弧鏁翠釜鍖哄煙
            self.interactive_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 娣诲姞宸ュ叿鏍?            self.toolbar = NavigationToolbar2Tk(self.interactive_canvas, chart_frame)
            self.toolbar.update()
            self.toolbar.pack(fill=tk.X)
            
            # 娣诲姞甯姪鏂囨湰
            help_text = ("鎿嶄綔鎸囧崡锛歕n1. 鎷栧姩榛戣壊铏氱嚎鍙皟鏁碩g鍊间綅缃甛n2. 鎷栧姩绾㈣壊鍜岀豢鑹茬鐐瑰彲鎵嬪姩璋冩暣鎷熷悎绾垮€炬枩搴n"
                      "3. 璋冩暣Y杞磋缃彲鏀惧ぇ鏄剧ず娉㈠姩\n4. 鍕鹃€夋墜鍔╕杞磋寖鍥村彲绮剧‘鎺у埗鏄剧ず鑼冨洿") if self.language_var.get() == "cn" else (
                      "User Guide:\n1. Drag the black dashed line to adjust Tg position\n2. Drag red and green endpoints to adjust fitting lines\n"
                      "3. Adjust Y-axis settings to zoom in on variations\n4. Check manual Y-axis range for precise control"
                      )
            help_label = ttk.Label(self.interactive_frame, text=help_text, font=("SimHei", 10))
            help_label.pack(pady=5)
            
            # 娣诲姞褰撳墠Tg鍊兼樉绀?            self.current_tg_var = tk.StringVar()
            self.current_tg_var.set(
                f"褰撳墠Tg鍊? {self.tg_result:.1f}K" if self.language_var.get() == "cn" 
                else f"Current Tg: {self.tg_result:.1f}K"
            )
            
            tg_display_frame = ttk.Frame(self.interactive_frame)
            tg_display_frame.pack(fill=tk.X, expand=False, pady=5)
            
            tg_label = ttk.Label(tg_display_frame, textvariable=self.current_tg_var, 
                                font=("SimHei", 12, "bold"))
            tg_label.pack(side=tk.LEFT, padx=10)
            
            # 娣诲姞鎺у埗鎸夐挳妗嗘灦
            control_btns_frame = ttk.Frame(tg_display_frame)
            control_btns_frame.pack(side=tk.RIGHT, padx=10)
            
            # 娣诲姞鍒囨崲鎺у埗鐐规樉绀虹殑鎸夐挳
            self.show_points_var = tk.BooleanVar(value=True)
            def toggle_control_points():
                if self.interactive_draggable:
                    is_visible = self.interactive_draggable.toggle_control_points()
                    self.show_points_var.set(is_visible)
                    self.toggle_points_btn.config(
                        text="闅愯棌鎺у埗鐐? if is_visible and self.language_var.get() == "cn"
                        else "鏄剧ず鎺у埗鐐? if self.language_var.get() == "cn"
                        else "Hide Control Points" if is_visible
                        else "Show Control Points"
                    )
            
            self.toggle_points_btn = ttk.Button(
                control_btns_frame, 
                text="闅愯棌鎺у埗鐐? if self.language_var.get() == "cn" else "Hide Control Points", 
                command=toggle_control_points
            )
            self.toggle_points_btn.pack(side=tk.LEFT, padx=5)
            
            # 娣诲姞璺宠浆鍒拌绠椾氦鐐圭殑鎸夐挳
            def move_to_intercept():
                if self.interactive_draggable and hasattr(self.interactive_draggable, 'calc_intercept'):
                    if self.interactive_draggable.calc_intercept is not None:
                        success = self.interactive_draggable.move_to_calculated_intercept()
                        if success:
                            messagebox.showinfo(
                                "鎴愬姛" if self.language_var.get() == "cn" else "Success", 
                                f"宸插皢Tg绾跨Щ鍔ㄥ埌璁＄畻鍑虹殑浜ょ偣: {self.interactive_draggable.x:.1f}K" if self.language_var.get() == "cn"
                                else f"Moved Tg line to calculated intercept: {self.interactive_draggable.x:.1f}K"
                            )
                            self.current_tg_var.set(
                                f"褰撳墠Tg鍊? {self.interactive_draggable.x:.1f}K" if self.language_var.get() == "cn"
                                else f"Current Tg: {self.interactive_draggable.x:.1f}K"
                            )
                        else:
                            messagebox.showwarning(
                                "璀﹀憡" if self.language_var.get() == "cn" else "Warning", 
                                "鏃犳硶绉诲姩鍒颁氦鐐癸紝鍙兘涓嶅湪鏈夋晥鑼冨洿鍐? if self.language_var.get() == "cn"
                                else "Cannot move to intercept, it might be outside valid range"
                            )
                    else:
                        messagebox.showinfo(
                            "鎻愮ず" if self.language_var.get() == "cn" else "Information", 
                            "灏氭湭璁＄畻鍑轰氦鐐癸紝璇峰厛鎷栧姩鎷熷悎绾? if self.language_var.get() == "cn"
                            else "No intercept calculated yet, please drag fitting lines first"
                        )
            
            self.intercept_btn = ttk.Button(
                control_btns_frame, 
                text="璺宠浆鍒颁氦鐐? if self.language_var.get() == "cn" else "Jump to Intercept", 
                command=move_to_intercept
            )
            self.intercept_btn.pack(side=tk.LEFT, padx=5)
            
            # 娣诲姞淇濆瓨褰撳墠Tg鍊肩殑鎸夐挳
            save_tg_button = ttk.Button(
                control_btns_frame, 
                text="浣跨敤姝g鍊? if self.language_var.get() == "cn" else "Use This Tg", 
                command=self.use_interactive_tg
            )
            save_tg_button.pack(side=tk.LEFT, padx=5)
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "浜や簰寮忓垎鏋愬凡鍚姩锛屾嫋鍔═g绾垮拰鎷熷悎绾跨鐐瑰彲璋冩暣鍙傛暟" if self.language_var.get() == "cn"
                else "Interactive analysis started, drag Tg line and fitting line endpoints to adjust parameters"
            )
            
            # 璁剧疆鍥炶皟鍑芥暟锛屽湪Tg琚嫋鍔ㄦ椂鏇存柊鏄剧ず
            def update_tg_display(new_tg, *args):
                self.current_tg_var.set(
                    f"褰撳墠Tg鍊? {new_tg:.1f}K" if self.language_var.get() == "cn"
                    else f"Current Tg: {new_tg:.1f}K"
                )
            
            self.interactive_draggable.on_update_callback = update_tg_display
            
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                f"鍒涘缓浜や簰寮忓垎鏋愬け璐? {str(e)}" if self.language_var.get() == "cn"
                else f"Failed to create interactive analysis: {str(e)}"
            )
            self.status_var.set(
                "浜や簰寮忓垎鏋愬惎鍔ㄥけ璐? if self.language_var.get() == "cn"
                else "Interactive analysis failed to start"
            )
    
    def refresh_interactive_plot(self):
        """鍒锋柊浜や簰寮忓垎鏋愬浘琛紝搴旂敤鏂扮殑Y杞磋缃?""
        if not hasattr(self, 'interactive_fig') or self.interactive_fig is None:
            return
            
        try:
            # 鑾峰彇褰撳墠Tg鍊?            current_tg = self.interactive_draggable.x if self.interactive_draggable else self.tg_result
            
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
                
            # 澶勭悊鎵嬪姩Y杞磋寖鍥?            y_min = None
            y_max = None
            
            if self.enable_manual_range.get():
                try:
                    y_min = float(self.y_min_var.get())
                    y_max = float(self.y_max_var.get())
                    
                    # 楠岃瘉杈撳叆鍊兼槸鍚︽湁鏁?                    if y_min >= y_max:
                        messagebox.showerror(
                            "閿欒" if self.language_var.get() == "cn" else "Error", 
                            "Y杞存渶灏忓€煎繀椤诲皬浜庢渶澶у€? if self.language_var.get() == "cn" 
                            else "Y-axis minimum must be less than maximum"
                        )
                        return
                except ValueError:
                    messagebox.showerror(
                        "閿欒" if self.language_var.get() == "cn" else "Error", 
                        "璇疯緭鍏ユ湁鏁堢殑Y杞磋寖鍥村€? if self.language_var.get() == "cn"
                        else "Please enter valid Y-axis range values"
                    )
                    return
            
            # 浣跨敤鏂拌缃垱寤轰氦浜掑紡鍥?            self.status_var.set(
                "姝ｅ湪鍒锋柊浜や簰寮忓垎鏋?.." if self.language_var.get() == "cn"
                else "Refreshing interactive analysis..."
            )
            self.root.update()
            
            # 娓呯┖鐢诲竷妗嗘灦
            chart_frame = None
            for widget in self.interactive_frame.winfo_children():
                if isinstance(widget, ttk.Frame) and not isinstance(widget, ttk.LabelFrame):
                    chart_frame = widget
                    for child in chart_frame.winfo_children():
                        child.destroy()
                    break
            
            if not chart_frame:
                return
            
            # 閲嶆柊鍒涘缓鍒嗘瀽
            self.tg_result, self.interactive_fig, self.interactive_draggable = find_glass_transition_temperature(
                self.temperatures, self.densities, 
                sample_name=sample_name,
                interactive=True,
                y_margin=self.y_margin_var.get(),
                y_min=y_min,
                y_max=y_max,
                language=self.language_var.get()
            )
            
            # 浼樺寲鍥捐〃甯冨眬
            self.interactive_fig.tight_layout()
            
            # 鍒涘缓鏂扮敾甯?            self.interactive_canvas = FigureCanvasTkAgg(self.interactive_fig, master=chart_frame)
            self.interactive_canvas.draw()
            
            # 纭繚鍥捐〃濉弧鏁翠釜鍖哄煙
            self.interactive_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 娣诲姞宸ュ叿鏍?            self.toolbar = NavigationToolbar2Tk(self.interactive_canvas, chart_frame)
            self.toolbar.update()
            self.toolbar.pack(fill=tk.X)
            
            # 鏇存柊Tg鏄剧ず
            self.current_tg_var.set(
                f"褰撳墠Tg鍊? {self.tg_result:.1f}K" if self.language_var.get() == "cn"
                else f"Current Tg: {self.tg_result:.1f}K"
            )
            
            # 璁剧疆鍥炶皟鍑芥暟
            def update_tg_display(new_tg, *args):
                self.current_tg_var.set(
                    f"褰撳墠Tg鍊? {new_tg:.1f}K" if self.language_var.get() == "cn"
                    else f"Current Tg: {new_tg:.1f}K"
                )
            
            self.interactive_draggable.on_update_callback = update_tg_display
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "浜や簰寮忓垎鏋愬凡鍒锋柊" if self.language_var.get() == "cn"
                else "Interactive analysis refreshed"
            )
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error",
                f"鍒锋柊浜や簰寮忓垎鏋愬け璐? {str(e)}" if self.language_var.get() == "cn"
                else f"Failed to refresh interactive analysis: {str(e)}"
            )
    
    def use_interactive_tg(self):
        """浣跨敤浜や簰寮忓垎鏋愮殑Tg鍊间綔涓烘渶缁堢粨鏋?""
        if hasattr(self, 'interactive_draggable') and self.interactive_draggable:
            self.tg_result = self.interactive_draggable.x
            messagebox.showinfo(
                "鎴愬姛" if self.language_var.get() == "cn" else "Success",
                f"宸蹭繚瀛楾g鍊? {self.tg_result:.1f}K" if self.language_var.get() == "cn"
                else f"Tg value saved: {self.tg_result:.1f}K"
            )
            self.status_var.set(
                f"鍒嗘瀽瀹屾垚! Tg = {self.tg_result:.1f}K" if self.language_var.get() == "cn"
                else f"Analysis complete! Tg = {self.tg_result:.1f}K"
            )
        else:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error",
                "鏈壘鍒版湁鏁堢殑浜や簰寮忓垎鏋愮粨鏋? if self.language_var.get() == "cn"
                else "No valid interactive analysis result found"
            )
    
    def save_results(self):
        """淇濆瓨鍒嗘瀽缁撴灉鍜屽彲瑙嗗寲鍥捐〃"""
        if not hasattr(self, 'tg_result') or self.tg_result is None:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                "璇峰厛瀹屾垚Tg鍒嗘瀽" if self.language_var.get() == "cn" else "Please complete Tg analysis first"
            )
            return
            
        try:
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
            
            # 鏄剧ず淇濆瓨瀵硅瘽妗?            save_path = filedialog.asksaveasfilename(
                title=("閫夋嫨淇濆瓨璺緞" if self.language_var.get() == "cn" else "Select Save Path"),
                defaultextension=".png",
                filetypes=[
                    ("PNG鍥惧儚", "*.png") if self.language_var.get() == "cn" else ("PNG Images", "*.png"),
                    ("鎵€鏈夋枃浠?, "*.*") if self.language_var.get() == "cn" else ("All Files", "*.*"),
                ],
                initialfile=f"glass_transition_{sample_name}"
            )
            
            if not save_path:
                return
                
            # 鑾峰彇淇濆瓨鐩綍
            save_dir = os.path.dirname(save_path)
            base_name = os.path.basename(save_path)
            name_without_ext = os.path.splitext(base_name)[0]
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "姝ｅ湪淇濆瓨缁撴灉..." if self.language_var.get() == "cn" else "Saving results..."
            )
            self.root.update()
            
            # 鐢熸垚璇︾粏鍒嗘瀽鍥捐〃
            if self.detailed_var.get():
                # 鑾峰彇褰撳墠Y杞磋缃?                y_margin = self.y_margin_var.get() if hasattr(self, 'y_margin_var') else 0.05
                y_min = None
                y_max = None
                
                if hasattr(self, 'enable_manual_range') and self.enable_manual_range.get():
                    try:
                        y_min = float(self.y_min_var.get())
                        y_max = float(self.y_max_var.get())
                    except:
                        pass
                
                # 鐢熸垚璇︾粏鍒嗘瀽鍥捐〃
                if hasattr(self, 'interactive_draggable') and self.interactive_draggable:
                    # 浠庝氦浜掑紡鍒嗘瀽涓幏鍙栨嫙鍚堝弬鏁?                    high_params = self.interactive_draggable.cached_high_params
                    low_params = self.interactive_draggable.cached_low_params
                    
                    if high_params and low_params:
                        best_params = (high_params, low_params)
                        # 浣跨敤浜や簰寮忓垎鏋愮殑Tg鍊煎拰鎷熷悎鍙傛暟
                        create_detailed_analysis(
                            self.temperatures, self.densities, 
                            best_params, self.tg_result, 
                            sample_name=sample_name,
                            y_margin=y_margin,
                            y_min=y_min,
                            y_max=y_max,
                            language=self.language_var.get(),
                            from_interactive=True)
                        
                        # 淇濆瓨璇︾粏鍒嗘瀽缁撴灉
                        detailed_path = os.path.join(save_dir, f"{name_without_ext}_detailed.png")
                        plt.savefig(detailed_path, dpi=300, bbox_inches='tight')
                        plt.close()
                else:
                    # 濡傛灉娌℃湁浜や簰寮忓垎鏋愮粨鏋滐紝鍒欒繘琛屾爣鍑嗗垎鏋愮敓鎴愯缁嗗浘琛?                    # 杩欓噷鎴戜滑闇€瑕佸厛杩涜鎷熷悎浠ヨ幏鍙栨嫙鍚堝弬鏁?                    # 鍒涘缓涓存椂鍥捐〃杩涜鎷熷悎
                    temp_fig = plt.figure(figsize=(8, 6))
                    temp_ax = temp_fig.add_subplot(111)
                    
                    # 杩涜鎷熷悎
                    tmp_tg, tmp_fig, tmp_draggable = find_glass_transition_temperature(
                        self.temperatures, self.densities,
                        interactive=True,
                        y_margin=y_margin,
                        y_min=y_min,
                        y_max=y_max,
                        language=self.language_var.get()
                    )
                    
                    # 鑾峰彇鎷熷悎鍙傛暟
                    high_params = tmp_draggable.cached_high_params
                    low_params = tmp_draggable.cached_low_params
                    
                    if high_params and low_params:
                        best_params = (high_params, low_params)
                        # 鍒涘缓璇︾粏鍒嗘瀽
                        create_detailed_analysis(
                            self.temperatures, self.densities,
                            best_params, self.tg_result,
                            sample_name=sample_name,
                            y_margin=y_margin,
                            y_min=y_min,
                            y_max=y_max,
                            language=self.language_var.get(),
                            from_interactive=True)
                        
                        # 淇濆瓨璇︾粏鍒嗘瀽缁撴灉
                        detailed_path = os.path.join(save_dir, f"{name_without_ext}_detailed.png")
                        plt.savefig(detailed_path, dpi=300, bbox_inches='tight')
                        
                        # 鍏抽棴涓存椂鍥捐〃
                        plt.close(temp_fig)
                        plt.close()
            
            # 淇濆瓨涓诲垎鏋愮粨鏋?            # 鍒涘缓鏍囧噯鍒嗘瀽鍥捐〃
            standard_fig = plt.figure(figsize=(8, 6), dpi=100)
            
            # 浣跨敤find_glass_transition_temperature閲嶆柊鐢熸垚鏍囧噯鍥捐〃
            find_glass_transition_temperature(
                self.temperatures, self.densities, 
                output_path=save_path,
                sample_name=sample_name,
                interactive=False,
                y_margin=0.05,
                language=self.language_var.get()
            )
            
            # 淇濆瓨鏁版嵁缁撴灉
            csv_path = os.path.join(save_dir, f"{name_without_ext}_data.csv")
            save_results_to_file(self.temperatures, self.densities, self.tg_result, csv_path, language=self.language_var.get())
            
            # 鏄剧ず鎴愬姛娑堟伅
            messagebox.showinfo(
                "淇濆瓨鎴愬姛" if self.language_var.get() == "cn" else "Save Successful", 
                (f"缁撴灉宸蹭繚瀛樿嚦:\n{save_path}\n\n" +
                 (f"璇︾粏鍒嗘瀽宸蹭繚瀛樿嚦:\n{detailed_path}\n\n" if self.detailed_var.get() else "") +
                 f"鏁版嵁宸蹭繚瀛樿嚦:\n{csv_path}") if self.language_var.get() == "cn" else
                (f"Results saved to:\n{save_path}\n\n" +
                 (f"Detailed analysis saved to:\n{detailed_path}\n\n" if self.detailed_var.get() else "") +
                 f"Data saved to:\n{csv_path}")
            )
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "缁撴灉宸蹭繚瀛? if self.language_var.get() == "cn" else "Results saved"
            )
            
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                f"淇濆瓨缁撴灉澶辫触: {str(e)}" if self.language_var.get() == "cn" else f"Failed to save results: {str(e)}"
            )
    
    def toggle_language(self):
        """鍒囨崲鐣岄潰璇█鍜屽浘琛ㄦ爣绛捐瑷€"""
        current_lang = self.language_var.get()
        
        if current_lang == "cn":
            # 鍒囨崲鍒拌嫳鏂?            self.language_var.set("en")
            self.lang_button.config(text="涓枃")
            self.root.title("Glass Transition Temperature Analysis Tool")
            
            # 鏇存柊UI鏂囨湰
            self.file_frame.config(text="Data File Selection")
            self.sample_frame.config(text="Sample Information")
            self.data_frame.config(text="Data Preview")
            self.preview_frame.config(text="Data Visualization Preview")
            self.control_frame.config(text="Operation Controls")
            
            # 鏇存柊鏍戠姸鍥炬爣棰?            self.tree.heading("index", text="Index", anchor=tk.CENTER)
            self.tree.heading("temperature", text="Temperature (K)", anchor=tk.CENTER)
            self.tree.heading("density", text=f"Density ({DENSITY_UNIT})", anchor=tk.CENTER)
            
            # 鏇存柊鎸夐挳鏂囨湰
            self.browse_button.config(text="Browse...")
            self.load_button.config(text="Load Data")
            self.analyze_button.config(text="Confirm Analysis")
            self.interactive_button.config(text="Interactive Analysis")
            self.save_button.config(text="Save Results")
            self.save_data_button.config(text="Save Data")
            self.detailed_check.config(text="Generate Detailed Analysis")
            
            # 鏇存柊鐘舵€?            current_status = self.status_var.get()
            if current_status == "鍑嗗灏辩华 - 璇烽€夋嫨鏁版嵁鏂囦欢":
                self.status_var.set("Ready - Please select a data file")
            
            # 濡傛灉瀛樺湪浜や簰寮忔鏋讹紝鏇存柊鍏舵枃鏈?            if hasattr(self, 'interactive_frame_visible') and self.interactive_frame_visible:
                self.interactive_frame.config(text="Interactive Tg Analysis")
                
                # 灏濊瘯鏇存柊浜や簰寮忔鏋朵腑鐨勬帶浠?                for widget in self.interactive_frame.winfo_children():
                    if isinstance(widget, ttk.LabelFrame) and widget.cget("text") == "Y-axis Display Control":
                        widget.config(text="Y杞存樉绀烘帶鍒?)
                
                # 濡傛灉鏈夋帶鍒剁偣鍒囨崲鎸夐挳锛屾洿鏂板叾鏂囨湰
                if hasattr(self, 'toggle_points_btn'):
                    if self.show_points_var.get():
                        self.toggle_points_btn.config(text="Hide Control Points")
                    else:
                        self.toggle_points_btn.config(text="Show Control Points")
                
                # 濡傛灉鏈変氦鐐硅烦杞寜閽紝鏇存柊鍏舵枃鏈?                if hasattr(self, 'intercept_btn'):
                    self.intercept_btn.config(text="Jump to Intercept")
        else:
            # 鍒囨崲鍒颁腑鏂?            self.language_var.set("cn")
            self.lang_button.config(text="English")
            self.root.title("鐜荤拑鍖栬浆鍙樻俯搴﹀垎鏋愬伐鍏?)
            
            # 鏇存柊UI鏂囨湰
            self.file_frame.config(text="鏁版嵁鏂囦欢閫夋嫨")
            self.sample_frame.config(text="鏍峰搧淇℃伅")
            self.data_frame.config(text="鏁版嵁棰勮")
            self.preview_frame.config(text="鏁版嵁鍙鍖栭瑙?)
            self.control_frame.config(text="鎿嶄綔鎺у埗")
            
            # 鏇存柊鏍戠姸鍥炬爣棰?            self.tree.heading("index", text="搴忓彿", anchor=tk.CENTER)
            self.tree.heading("temperature", text="娓╁害 (K)", anchor=tk.CENTER)
            self.tree.heading("density", text=f"瀵嗗害 ({DENSITY_UNIT})", anchor=tk.CENTER)
            
            # 鏇存柊鎸夐挳鏂囨湰
            self.browse_button.config(text="娴忚...")
            self.load_button.config(text="鍔犺浇鏁版嵁")
            self.analyze_button.config(text="纭鍒嗘瀽")
            self.interactive_button.config(text="浜や簰寮忓垎鏋?)
            self.save_button.config(text="淇濆瓨缁撴灉")
            self.save_data_button.config(text="淇濆瓨鏁版嵁")
            self.detailed_check.config(text="鐢熸垚璇︾粏鍒嗘瀽")
            
            # 鏇存柊鐘舵€?            current_status = self.status_var.get()
            if current_status == "Ready - Please select a data file":
                self.status_var.set("鍑嗗灏辩华 - 璇烽€夋嫨鏁版嵁鏂囦欢")
            
            # 濡傛灉瀛樺湪浜や簰寮忔鏋讹紝鏇存柊鍏舵枃鏈?            if hasattr(self, 'interactive_frame_visible') and self.interactive_frame_visible:
                self.interactive_frame.config(text="浜や簰寮廡g鍒嗘瀽")
                
                # 灏濊瘯鏇存柊浜や簰寮忔鏋朵腑鐨勬帶浠?                for widget in self.interactive_frame.winfo_children():
                    if isinstance(widget, ttk.LabelFrame) and widget.cget("text") == "Y-axis Display Control":
                        widget.config(text="Y杞存樉绀烘帶鍒?)
                
                # 濡傛灉鏈夋帶鍒剁偣鍒囨崲鎸夐挳锛屾洿鏂板叾鏂囨湰
                if hasattr(self, 'toggle_points_btn'):
                    if self.show_points_var.get():
                        self.toggle_points_btn.config(text="闅愯棌鎺у埗鐐?)
                    else:
                        self.toggle_points_btn.config(text="鏄剧ず鎺у埗鐐?)
                
                # 濡傛灉鏈変氦鐐硅烦杞寜閽紝鏇存柊鍏舵枃鏈?                if hasattr(self, 'intercept_btn'):
                    self.intercept_btn.config(text="璺宠浆鍒颁氦鐐?)
        
        # 濡傛灉鏈夐瑙堝浘锛屾洿鏂伴瑙堝浘
        self.refresh_preview()
        
        # 濡傛灉鏈変氦浜掑紡鍥撅紝鍒锋柊浜や簰寮忓浘
        if hasattr(self, 'interactive_fig') and self.interactive_fig is not None:
            self.refresh_interactive_plot()
            
    def refresh_preview(self):
        """鍒锋柊棰勮鍥撅紝搴旂敤褰撳墠璇█璁剧疆"""
        if hasattr(self, 'preview_canvas') and self.preview_canvas is not None and self.data_loaded:
            # 娓呴櫎棰勮妗嗘灦鍐呭
            for widget in self.preview_frame.winfo_children():
                widget.destroy()
            
            # 浣跨敤褰撳墠璇█鍒涘缓棰勮鍥?            self.create_preview_plot()

    def save_data(self):
        """淇濆瓨鍒嗘瀽鏁版嵁鍒癈SV鍜孹LSX鏍煎紡"""
        if not hasattr(self, 'tg_result') or self.tg_result is None:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                "璇峰厛瀹屾垚Tg鍒嗘瀽" if self.language_var.get() == "cn" else "Please complete Tg analysis first"
            )
            return
            
        if not hasattr(self, 'interactive_draggable') or self.interactive_draggable is None:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error",
                "闇€瑕佸厛杩涜浜や簰寮忓垎鏋? if self.language_var.get() == "cn" else "Interactive analysis is required first"
            )
            return
            
        try:
            # 鑾峰彇鏍峰搧鍚嶇О
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "鏍峰搧" if self.language_var.get() == "cn" else "Sample"
            
            # 鏄剧ず淇濆瓨瀵硅瘽妗?            filetypes = [
                ("CSV鏂囦欢", "*.csv") if self.language_var.get() == "cn" else ("CSV Files", "*.csv"),
                ("Excel鏂囦欢", "*.xlsx") if self.language_var.get() == "cn" else ("Excel Files", "*.xlsx")
            ]
            
            save_path = filedialog.asksaveasfilename(
                title=("閫夋嫨淇濆瓨璺緞" if self.language_var.get() == "cn" else "Select Save Path"),
                defaultextension=".csv",
                filetypes=filetypes,
                initialfile=f"glass_transition_data_{sample_name}"
            )
            
            if not save_path:
                return
                
            # 鑾峰彇娓╁害鍜屽瘑搴︽暟鎹?            temperatures = self.temperatures
            densities = self.densities
            tg = self.tg_result
            
            # 鑾峰彇鎷熷悎鍙傛暟
            high_params = self.interactive_draggable.cached_high_params
            low_params = self.interactive_draggable.cached_low_params
            
            if not high_params or not low_params:
                messagebox.showerror(
                    "閿欒" if self.language_var.get() == "cn" else "Error",
                    "鏃犳硶鑾峰彇鎷熷悎鍙傛暟" if self.language_var.get() == "cn" else "Cannot get fitting parameters"
                )
                return
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "姝ｅ湪淇濆瓨鏁版嵁..." if self.language_var.get() == "cn" else "Saving data..."
            )
            self.root.update()
            
            # 鍒涘缓DataFrame
            data = {
                ('娓╁害 (K)' if self.language_var.get() == "cn" else 'Temperature (K)'): temperatures,
                (f'瀵嗗害 ({DENSITY_UNIT})' if self.language_var.get() == "cn" else f'Density ({DENSITY_UNIT})'): densities
            }
            
            # 娣诲姞鍖哄煙鏍囩
            regions = []
            for temp in temperatures:
                if temp > tg:
                    regions.append('娑蹭綋' if self.language_var.get() == "cn" else 'Liquid')
                else:
                    regions.append('鐜荤拑' if self.language_var.get() == "cn" else 'Glass')
            
            data['鍖哄煙' if self.language_var.get() == "cn" else 'Region'] = regions
            
            # 娣诲姞鎷熷悎鏁版嵁
            high_temps = [t for t in temperatures if t > tg]
            low_temps = [t for t in temperatures if t <= tg]
            
            high_fits = []
            low_fits = []
            
            # 璁＄畻楂樻俯鍖烘嫙鍚堝€?            for t in temperatures:
                if t > tg:
                    high_fits.append(high_params[0] * t + high_params[1])
                else:
                    high_fits.append(None)
            
            # 璁＄畻浣庢俯鍖烘嫙鍚堝€?            for t in temperatures:
                if t <= tg:
                    low_fits.append(low_params[0] * t + low_params[1])
                else:
                    low_fits.append(None)
            
            data['楂樻俯鎷熷悎' if self.language_var.get() == "cn" else 'High Temp Fit'] = high_fits
            data['浣庢俯鎷熷悎' if self.language_var.get() == "cn" else 'Low Temp Fit'] = low_fits
            
            # 鍒涘缓DataFrame
            df = pd.DataFrame(data)
            
            # 娣诲姞鎷熷悎鍙傛暟鍜孴g鍊煎埌绗簩涓〃
            fit_info = {
                '鍙傛暟' if self.language_var.get() == "cn" else 'Parameter': [
                    '楂樻俯鏂滅巼' if self.language_var.get() == "cn" else 'High Temp Slope',
                    '楂樻俯鎴窛' if self.language_var.get() == "cn" else 'High Temp Intercept',
                    '浣庢俯鏂滅巼' if self.language_var.get() == "cn" else 'Low Temp Slope',
                    '浣庢俯鎴窛' if self.language_var.get() == "cn" else 'Low Temp Intercept',
                    'Tg (K)'
                ],
                '鍊? if self.language_var.get() == "cn" else 'Value': [
                    high_params[0],
                    high_params[1],
                    low_params[0],
                    low_params[1],
                    tg
                ]
            }
            
            fit_df = pd.DataFrame(fit_info)
            
            # 淇濆瓨涓篊SV鎴朮LSX
            file_ext = os.path.splitext(save_path)[1].lower()
            
            if file_ext == '.csv':
                df.to_csv(save_path, index=False, encoding='utf-8-sig')
                
                # 淇濆瓨鎷熷悎鍙傛暟鍒板彟涓€涓狢SV鏂囦欢
                params_path = os.path.splitext(save_path)[0] + '_params.csv'
                fit_df.to_csv(params_path, index=False, encoding='utf-8-sig')
                
                messagebox.showinfo(
                    "淇濆瓨鎴愬姛" if self.language_var.get() == "cn" else "Save Successful",
                    (f"鏁版嵁宸蹭繚瀛樿嚦:\n{save_path}\n\n鍙傛暟宸蹭繚瀛樿嚦:\n{params_path}") 
                    if self.language_var.get() == "cn" else 
                    (f"Data saved to:\n{save_path}\n\nParameters saved to:\n{params_path}")
                )
            elif file_ext == '.xlsx':
                # 鍒涘缓Excel鏂囦欢骞舵坊鍔犱袱涓〃
                with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='鏁版嵁' if self.language_var.get() == "cn" else 'Data', index=False)
                    fit_df.to_excel(writer, sheet_name='鎷熷悎鍙傛暟' if self.language_var.get() == "cn" else 'Fitting Parameters', index=False)
                
                messagebox.showinfo(
                    "淇濆瓨鎴愬姛" if self.language_var.get() == "cn" else "Save Successful",
                    (f"鏁版嵁鍜屽弬鏁板凡淇濆瓨鑷?\n{save_path}") 
                    if self.language_var.get() == "cn" else 
                    (f"Data and parameters saved to:\n{save_path}")
                )
            else:
                # 榛樿淇濆瓨涓篊SV
                csv_path = os.path.splitext(save_path)[0] + '.csv'
                df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                
                params_path = os.path.splitext(csv_path)[0] + '_params.csv'
                fit_df.to_csv(params_path, index=False, encoding='utf-8-sig')
                
                messagebox.showinfo(
                    "淇濆瓨鎴愬姛" if self.language_var.get() == "cn" else "Save Successful",
                    (f"鏁版嵁宸蹭繚瀛樿嚦:\n{csv_path}\n\n鍙傛暟宸蹭繚瀛樿嚦:\n{params_path}") 
                    if self.language_var.get() == "cn" else 
                    (f"Data saved to:\n{csv_path}\n\nParameters saved to:\n{params_path}")
                )
            
            # 鏇存柊鐘舵€?            self.status_var.set(
                "鏁版嵁宸蹭繚瀛? if self.language_var.get() == "cn" else "Data saved"
            )
            
        except Exception as e:
            messagebox.showerror(
                "閿欒" if self.language_var.get() == "cn" else "Error", 
                f"淇濆瓨鏁版嵁澶辫触: {str(e)}" if self.language_var.get() == "cn" else f"Failed to save data: {str(e)}"
            )

    def _on_window_resize(self, event):
        """绐楀彛澶у皬鍙樺寲鏃舵洿鏂癈anvas澶у皬"""
        # 纭繚event涓嶆槸鐢卞瓙鎺т欢瑙﹀彂鐨?        if event.widget == self.root:
            # 鏇存柊Canvas瀹藉害涓庣獥鍙ｅぇ灏忓尮閰?            width = event.width - self.scrollbar.winfo_width() - 5  # 鍑忓幓婊氬姩鏉″搴﹀拰涓€鐐瑰～鍏?            if width > 0:
                self.main_canvas.config(width=width)
                # 璋冩暣scrollable_frame瀹藉害
                self.main_canvas.itemconfig(self.frame_id, width=width)

    def _on_mousewheel(self, event):
        """澶勭悊榧犳爣婊氳疆浜嬩欢锛屽吋瀹逛笉鍚屾搷浣滅郴缁?""
        if sys.platform.startswith('win'):
            # Windows骞冲彴
            self.main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        elif sys.platform == 'darwin':
            # macOS骞冲彴
            self.main_canvas.yview_scroll(int(-1*event.delta), "units")
        else:
            # Linux骞冲彴
            if event.num == 4:
                self.main_canvas.yview_scroll(-1, "units")
            elif event.num == 5:
                self.main_canvas.yview_scroll(1, "units")
    
    def browse_file(self):
        """娴忚骞堕€夋嫨鏁版嵁鏂囦欢"""
        filetypes = [
            ('CSV鏂囦欢', '*.csv'),
            ('Excel鏂囦欢', '*.xlsx *.xls'),
            ('鎵€鏈夋枃浠?, '*.*')
        ]
        filepath = filedialog.askopenfilename(
            title="閫夋嫨鏁版嵁鏂囦欢",
            filetypes=filetypes
        )
        if filepath:
            self.file_path_var.set(filepath)
            # 浠庢枃浠跺悕鎻愬彇鏍峰搧鍚嶇О
            base_name = os.path.basename(filepath)
            sample_name = os.path.splitext(base_name)[0]
            self.sample_name_var.set(sample_name)
    
    def on_density_column_changed(self, event):
        """澶勭悊瀵嗗害鍒楅€夋嫨鍙樻洿浜嬩欢"""
        if not self.is_multi_density or not self.data_loaded:
            return
            
        selected = self.density_column_var.get()
        if selected:
            # 鏌ユ壘閫変腑鐨勫瘑搴﹀垪绱㈠紩
            for i, name in enumerate(self.density_column_names):
                if name == selected:
                    self.active_density_column = i
                    break
            
            # 鏇存柊褰撳墠浣跨敤鐨勫瘑搴︽暟鎹?            self.densities = self.densities_columns[self.active_density_column]
            
            # 鏇存柊琛ㄦ牸鏄剧ず
            self.update_data_preview()
# 程序入口点
if __name__ == "__main__":
    root = tk.Tk()
    app = TgAnalysisApp(root)
    root.mainloop()
