# 交联电场简化版脚本运行流程详解

## 1. 初始化与模块导入
- 导入必要的Perl模块：`strict`, `Getopt::Long`, `MaterialsScript`
- 定义常量：`TRUE`, `FALSE`, `DEBUG`, `PICO_TO_FEMTO`（皮秒到飞秒的转换系数，值为1000）

## 2. 用户参数设置
- 定义基础参数：
  - 文件名称：`$xsdDocName = "Original"`
  - 交联目标转化率：`$conversionTarget = 50`（百分比）
  - 反应半径范围：初始值`$MinRxnRadius = 4`，步长`$StepRxnRadius = 1`，最大值`$MaxRxnRadius = 9`
  - 每个半径的最大迭代次数：`$IterationsPerRadius = 2`
  - 电场动力学和交联循环的最大循环次数：`$maxCycles = 2`
- 设置反应原子和分子名称：
  - 单体分子名称：`$monomerName = "monomer"`
  - 单体反应性原子：`$monomerReactiveAtom = "R1"`
  - 交联剂分子名称：`$xlinkerName = "xlinker"`
  - 交联剂反应性原子：`$xlinkerReactiveAtom = "R2"`
- 设置模拟参数：
  - 力场：`$forcefield = "COMPASSIII"`
  - 时间步长：`$timeStep = 0.1`飞秒
  - 电荷计算方法：`$chargeMethod = "Atom based"`
  - 计算质量：`$Quality = "Medium"`
  - 温度控制：`$thermostat = "Andersen"`
  - 温度和压力：`$xlinkTemperature = 300K`, `$xlinkPressure = 0.0001`
  - 系综：`$ensemble = "NVE"`
- 电场设置：
  - 启用电场：`$useElectricField = TRUE`
  - 电场强度：`$electricFieldStrength = 1`（V/Å）
  - 电场方向：X=0, Y=0, Z=1（沿Z轴方向）
  - 反电场设置：`$counterElectricField = "Yes"`
- 动力学模拟时间设置：
  - 电场动力学时间：`$fieldDynamicsTime = 1`皮秒
  - 分析动力学：`$analyzeDuration = 10`皮秒

## 3. 参数获取与GUI交互
- 检查是否有活动文档（GUI环境下）：`$xsdDoc = Documents->ActiveDocument`
- 若无活动文档，使用默认参数
- 若有活动文档，通过`GetOptions`从GUI获取用户提供的参数
- 将用户选项写入`GUI_inputs.txt`文件

## 4. 文件准备
- 尝试导入服务器信息文件（`fromdsd.txt`和`mpd.hosts`）
- 创建进度报告文件：`Progress.txt`
- 创建时间记录文件：`Timings.txt`
- 复制初始结构文档：从`$xsdDoc`复制到新建的`"xlink.xsd"`
- 初始化Forcite模块设置：
  - 设置力场、质量、温度、压力
  - 设置轨迹保存频率为1000步
  - 设置轨迹追加方式
- 检查系统周期性条件：
  - 若无周期性，设置相应的非周期性电荷计算方法
  - 若有周期性，设置相应的周期性电荷计算方法
  - 对2D周期性发出警告（未经测试）
- 防止重启运行时的文件名冲突：若`$xsdDoc`名称以"xlink_"开头，则重命名为"initial"
- 创建统计表：`"xlink_statistics.std"`
- 创建结构表：`"xlink_structures.std"`，并设置列标题

## 5. 初始化交联数据
- 计算初始反应性原子数量：
  - 遍历所有原子，统计反应性单体原子`$reactiveMonomerAtoms`和反应性交联剂原子`$reactiveXLinkerAtoms`
  - 统计已反应原子数量：`$reactedMonomerAtoms`和`$reactedXLinkerAtoms`
  - 统计总单体原子数：`$totalMonomerAtoms`
- 计算交联剂可形成的交联数：`$xlinkPotential`
- 计算已存在的交联数：`$xlinkCounter`
- 计算当前转化率：`$conversion = calculateConversion($doc)`
- 检查交联剂数量是否足够达到目标转化率，不足则终止运行
- 初始化计数器：`$mdcounter`、`$geomoptcounter`和`$cycleCounter`

## 6. 电场动力学与交联循环
- 初始化循环计数器：`$cycleCounter = 0`
- 执行最多`$maxCycles`次电场动力学与交联循环（连续进行，每次循环使用上一次循环的输出结构作为输入）：
  - 递增循环计数器：`$cycleCounter++`
  - 重命名文档为：`"xlink_cycle_".$cycleCounter`
  - 执行电场动力学：
    * 计算所需步数：`$steps = ($fieldDynamicsTime * PICO_TO_FEMTO / $timeStep)`
    * 执行电场动力学模拟：`ForciteDynamics($doc, $steps, $ensemble)`（启用电场）
  - 对每个反应半径（从`$MinRxnRadius`到`$MaxRxnRadius`，步长为`$StepRxnRadius`）执行交联：
    * 设置文档命名格式：`"xlink_cycle_".$cycleCounter."_R%.2f"`
    * 对每个半径，执行最多`$IterationsPerRadius`次迭代：
      + 重命名文档为：`"$xsdNameDist"."_".$iteration"`
      + 在结构中创建新交联键：`$numBonds = createNewXlinks($doc, $RxnRadius)`
      + 更新转化率：`$conversion = calculateConversion($doc)`
      + 若无新键形成，跳出当前半径的循环
      + 执行几何优化：`ForciteGeomOpt($doc, 2000)`
      + 将当前结构保存到结构表中，并记录循环次数、半径、迭代次数和转化率
      + 运行分析动力学模拟：`ForciteDynamics($doc, $steps, $ensemble)`
      + 记录此次迭代的时间并保存所有文档
      + 如果达到目标转化率，设置`$targetReached = TRUE`并跳出迭代循环
    * 如果达到目标转化率，跳出半径循环
  - 每个循环结束时，当前的结构状态将自动作为下一个循环的输入结构，确保计算的连续性
  - 如果达到目标转化率或完成所有循环，跳出主循环

## 7. 结果处理与分析
- 重命名最终结构为：`"xlink_final"`
- 分析键长分布：`analyzeBonds($doc)`
- 创建交联原子集合：`XlinkSet($doc)`
- 输出最终统计信息：
  - 交联数量
  - 最终转化率
  - 完成的循环次数
  - 几何优化总步数
  - 分子动力学总步数
  - 计算总耗时
- 保存所有文档

## 8. 核心功能函数详解

### 8.1 calculateConversion
- 计算交联转化率：已反应单体原子占总单体原子的百分比
- 遍历所有原子，统计名称匹配`/^$monomerReactiveAtom-\d/`的原子数和名称匹配`/^$monomerReactiveAtom/`的总原子数
- 返回百分比值

### 8.2 createReactiveAtomSets
- 创建用于近距离接触计算的排除集合：`"notR1"`和`"notR2"`
- 遍历所有原子，分类为：
  - 反应性单体原子：加入`@notR2`，增加`$R1Counter`
  - 反应性交联剂原子：加入`@notR1`，增加`$R2Counter`
  - 其他原子：同时加入`@notR1`和`@notR2`
- 创建两个集合并设为不可见
- 返回更新的文档和计数器

### 8.3 isReactiveR1和isReactiveR2
- 检查原子是否为反应性单体原子或反应性交联剂原子
- 判断依据：原子名称是否精确匹配`$monomerReactiveAtom`或`$xlinkerReactiveAtom`

### 8.4 createNewXlinks
- 在指定距离内创建新交联键：
  - 更新反应性原子集合：`createReactiveAtomSets($doc1)`
  - 配置键计算设置：最大绝对距离为`$distance`
  - 计算近距离接触：`$doc1->CalculateCloseContacts`
  - 删除不符合条件的接触：保留反应性单体原子和反应性交联剂原子之间的接触
  - 锁定原子以避免一个原子在同一轮中多次反应
  - 将符合条件的近距离接触转换为化学键：`createNewBond($doc1, $atom1, $atom2)`
  - 调整氢原子：`$doc1->AdjustHydrogen`
  - 删除临时的近距离接触和集合
  - 对结构进行几何优化：`ForciteGeomOpt($doc1, 2000)`
  - 返回新创建的键数量

### 8.5 createNewBond
- 创建新交联键并设置属性：
  - 增加交联计数器：`$xlinkCounter++`
  - 创建单键：`$doc1->CreateBond($atom1, $atom2, "Single", ([Name => "xlink-".$xlinkCounter]))`
  - 设置原子显示样式为球棍式
  - 更新原子名称，附加交联索引：`$atom1->Name .= "-".$xlinkCounter`
  - 记录创建的键信息

### 8.6 ForciteGeomOpt
- 执行几何优化，设置最大迭代次数
- 使用eval捕获可能的错误
- 记录优化时间和步数

### 8.7 ForciteDynamics
- 执行分子动力学模拟：
  - 生成唯一文档名称以创建唯一轨迹文件：使用时间戳
  - 临时重命名文档
  - 配置动力学设置：步数、系综、轨迹频率等
  - 如果启用，添加电场设置
  - 执行动力学模拟：`$Forcite->Dynamics->Run($doc1, \@settings)`
  - 恢复原始文档名称
  - 处理可能的错误
  - 记录模拟时间和轨迹信息
  - 更新动力学步数计数器

### 8.8 analyzeBonds
- 分析键长分布：
  - 计算所有键的分布：`$Forcite->Analysis->LengthDistribution($doc1)`
  - 创建交联键距离集合：`"xlink distances"`
  - 专门分析交联键距离
  - 生成两个图表：`"AllBonds"`和`"XlinkBonds"`

### 8.9 XlinkSet
- 创建包含所有交联原子和键的集合：
  - 收集所有名称以`"xlink"`开头的键及其连接的原子
  - 创建不可见的集合：`"Crosslinks"`

这个脚本通过电场动力学和交联循环的双层嵌套循环，在每个电场动力学后执行交联过程，直到达到目标转化率或完成指定的循环次数。每次循环都是连续的，保持结构的延续性，即每次新循环都使用上一次循环后的结构作为输入。每个交联过程中逐步增加反应半径，在每个半径下迭代创建交联键。
