# 三组分交联模拟系统

## 功能介绍
本脚本基于Materials Studio软件的Forcite模块开发，用于模拟三种反应原子(R1、R2、R3)之间的交联反应，并能精确控制不同类型交联的比例。

### 主要特点
1. 支持三种反应原子类型：R1、R2、R3
2. 可控制三种交联类型的比例：
   - R1-R2交联（如C-N键）
   - R1-R3交联（如C-O键）
   - R2-R3交联（如N-O键）
3. 支持动态调整反应半径（从初始值逐步增加到最大值）
4. 根据原子的实际元素类型自动设置最优键长参数
5. 支持环氧开环反应（当R1原子参与交联时）
6. 提供详细的交联类型统计和分析报告
7. 支持分子自动平衡和结构优化

## 键长参数表
系统根据参与交联的原子元素类型自动设置最优键长：
- C-N键: 1.47 Å
- C-O键: 1.42 Å
- N-O键: 1.44 Å
- C-C键: 1.54 Å
- N-N键: 1.45 Å
- O-O键: 1.48 Å

## 使用方法

### 准备工作
1. 创建包含待交联分子的原子级XSD文件
2. 将参与反应的原子命名为R1、R2和R3
3. 确保三种反应类型的比例总和为100%

### 运行脚本
通过Materials Studio的Perl接口运行该脚本：
```
Perl -> Run Script -> 选择DMPB_Three-Component_Xlink.pl
```

### 主要参数
- `Conversion`: 目标转化率（默认80%）
- `MinRxnRadius`: 初始反应半径（默认4 Å）
- `MaxRxnRadius`: 最大反应半径（默认9 Å）
- `StepRxnRadius`: 反应半径增量（默认1 Å）
- `ThirdReactiveAtom`: 第三类反应原子名称（默认"R3"）
- `R1_R2_ratio`: R1与R2反应占总交联的比例（默认70%）
- `R1_R3_ratio`: R1与R3反应占总交联的比例（默认30%）
- `R2_R3_ratio`: R2与R3反应占总交联的比例（默认0%）

**注意**：三种反应类型的比例总和必须等于100%，否则程序会报错并终止。

### 高级选项
- `UseRestraintBonds`: 是否使用约束键平滑形成交联（默认开启）
- `OpenRings`: 是否在R1原子参与交联时打开环氧环（默认开启）
- `UseTempCycle`: 是否使用温度循环退火（默认关闭）
- `UseMaxBondEnergy`: 是否计算最大键能量（用于评估网络应变，默认开启）

## 执行步骤
脚本执行过程包括以下主要步骤：
1. 初始平衡动力学模拟
2. 更新反应半径
3. 创建新的交联键（按指定比例）
4. 通过优化和动力学模拟放松结构
5. 打开环氧环（当R1原子参与交联时）
6. 调整氢原子
7. 重新计算电荷组（可选）
8. 通过温度循环退火交联结构（可选）
9. 将交联数据写入研究表
10. 重复步骤2-9直到达到目标转化率或最大截止值

## 输出文件
1. `xlink_final.xsd` - 最终交联结构
2. `xlink_statistics.std` - 交联和热力学数据表
3. `xlink_structures.std` - 不同交联度下的中间结构表
4. `XlinkBonds.xcd` - 键长分布图（用于检查拉伸键）
5. `Progress.txt` - 实时更新的日志文件
6. `Timings.txt` - CPU时间日志文件
7. `fromdsd.txt` - MPI核心计数日志文件
8. `GUI_inputs.txt` - 用户参数日志（仅在从用户菜单执行时生成）

## 交联统计
脚本会生成详细的统计报告，包括：
1. 总交联度和各类型交联的分布
2. 根据实际元素类型显示的键类型统计（如C-N、C-O、N-O）
3. 目标比例与实际比例的对比
4. 网络结构和片段分析

## 注意事项
- 没有环穿透检查，但程序会监控最大键能量（应小于10 kcal/mol）
- 交联剂中至少需要两个原子
- 如果使用约束方法形成交联，请勿在结构中使用约束

## 版权信息
- 开发者：端木鹏博（PengBo Dumu）
- 最后修订：2025年5月28日
- 联系方式：<EMAIL>
- 版权所有 © 2025 端木鹏博。保留所有权利。

**免责声明**：本脚本按"原样"提供，不提供任何形式的担保。作者对脚本的准确性、适用性或可靠性不作任何明示或暗示的保证。在任何情况下，作者均不对因使用本脚本而导致的任何直接或间接损害负责。 