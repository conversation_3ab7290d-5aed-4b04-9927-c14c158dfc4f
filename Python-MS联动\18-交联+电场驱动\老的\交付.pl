#!perl

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);
use constant TRUE 	=> 1;
use constant FALSE 	=> 0;
use constant DEBUG	=> 2; # larger is more verbose
use constant PICO_TO_FEMTO => 1000;

# Purpose: 	Crosslink monomers and curing agents using atomistic models
# Modules:	Forcite
# Last revised:	Jan 4, 2017
# Author:	<PERSON>, <PERSON> & <PERSON>

# Disclaimer
# This script is distributed as-is on an unsupported basis. The authors do not make any
# guarantees it will work or that it is accurate. Post questions to the forum at
# http://community.3dsbiovia.com. Do not contact support unless it is clearly a fault in
# the supported scripting and Materials Studio functionality.

# Creates crosslinks in a system containing monomers and crosslinking molecules.
# Atoms on the monomers and crosslinkers are designated as reactive atoms by assigning 
# a special name to the atoms (usually R1 and R2). Close-contacts are calculated between 
# the reactive atoms and bonds are created. The close-contact distance is increased
# when no progress is made.

# Executes these steps:
#   1. One-time equilibration dynamics
#   2. Update reaction radius
#   3. Create new crosslinks (createNewXlinks)
#   4. Relax with optimization and dynamics
#   5. Open epoxy rings (optional)
#   6. Delete condensates (optional)
#   7. Adjust hydrogens
#   8. Recalculate charge groups (optional)
#   9. Anneal xlinked structure using temperature cycle (optional)
#  10. Write xlink data to study tables
#  11. Repeat steps 2-10 until target conversion or max cutoff is reached

# Input documents:
# (1) An atomistic xsd file with reactive atoms named (usually as R1 and R2)

# Output documents:

# (1) xlink_final.xsd - Final xlinked structure 
# (2) xlink_statistics.std - Table of xlinking and thermo data
# (3) xlink_structures.std - Table of intermediate structures at lower conversion levels
# (4) XlinkBonds.xcd - Distribution of bond lengths to check for stretched bonds
# (5) Progress.txt - Log file updated continuously during the run
# (6) Timings.txt - Log file of cpu time
# (7) fromdsd.txt - Log file with the number of MPI cores used for the run
# (8) GUI_inputs.txt - Only if executed from User menu. Log of user arguments.

# Limitations:
#
# No ring spearing check (but keep an eye on max bond energy, should be <10kcal/mol)
# Need to have at least two atoms in the xlinker (most if not all do)
# Don't use structures with restraints, if you are using the restraint method for xlink formation

# Change log
#
# JW 07/20/2011 Restraint method (optional) for smoother crosslinking
# JD 12/27/2012 Tcycle optional, error trapping, automatic charge groups, improve xlink stats
# JD 01/30/2013 Max bond energy, graceful fault on relaxNPT
# JD 02/11/2013 Redesigned user inputs for use from the User menu via GUI
# JD 02/28/2013 Support restarts on partially xlinked systems
# JD 03/04/2013 Fixed bug in T, P statistics
# JD 03/07/2013 Optional turn off maxbond, skip straightdynamicsNPT for first radius
# JD 03/20/2013 Geometry optimize after each restraint tightening - helps avoid crashes
# JD 10/03/2013 $remove_condensation option, eliminate molecule name requirements
# JD 12/04/2013 Fix bug that occasionally produces multiple xlinks on a single reactive atom
# JD 12/12/2013 Option for atoms to react multiple times
# JD 12/16/2013 Improve maxbond performance
# JD 12/18/2013 Remove clutter by using one dynamics subroutine
# JD 12/18/2013 Fragment analysis
# JD 03/09/2015 Fix bug where maxbond turned off atom typing for ewald and atom based charges
# JD 03/10/2015 Configured settings to run with or without GUI
# JD 03/13/2015 On-the-fly conversion limit, eliminating problem of open epoxy rings from deleted xlinks
# JD 09/02/2015 System does not need to have molecule objects defined
# JD 09/03/2015 Rename starting structure to avoid conflict
# JD 09/03/2015 Count opt & md steps, de-fork analysis dynamics, new GUI option md steps per radius
# JD 09/04/2015 Fix fragment analysis
# JD 10/14/2015 GUI parameters are optional (allows simpler GUIs to be defined)
# JD 02/01/2016 Allow run to continue even if molecules cannot be assigned (due to infinite network)
# JD 02/03/2016 Enable single or multi condensation reactions for R1/R2 atoms with multiple OH groups 
# JD 02/10/2016 Prevent intra-molecular xlinks (multiple between same fragments) (incl concurrent)
# JD 02/10/2016 Flag to run without using molecule objects (no stats output) eg if self-xlinking or interface
# JD 02/29/2016 Switch intra-prevention method to pathlength basis rather than relying on molecule objects
# JD 06/01/2016 Conversion formula with total R1 atom count on the fly in case R1 atoms are deleted during reactions
# JD 06/02/2016 Global replacement of the term 'oligomer' for the more common term 'monomer'
# JD 06/02/2016 Non-periodic systems (conditional pressure analysis), might work with 2D periodicity also
# JD 07/12/2016 Fixed bug in self-crosslinking systems causing xlinking in excess of proper coord nr
# JD 09/14/2016 Support for polyurethanes - convert C=N bond to single bond in isocyanate group
# JD 09/15/2016 Fix bug with mol-based intra exclusions - appending index to xlinker mol names
# JD 12/29/2016 Fix bug causing over-crosslinking in self silanol Si(OH)3 systems
# JD 12/30/2016 Prevent user from selecting Ewald for nonperiodic systems
# JD 01/04/2017 On-the-fly conversion calculation that works even when R1 atoms are variable
# JD 01/04/2017 Use can specify ensemble (normally NPT)

################################################################################################
#
# User settings - either from GUI or edit here
#
################################################################################################

# These will be used if there is no UI
my $xsdDocName			= "Original";	# Name of xsd file with xlinkers and monomers defined
my $conversionTarget 			= 50;		# Percent of reactive atoms on monomer that react
my $MinRxnRadius 			= 4;		# Initial close contacts cutoff
my $StepRxnRadius 			= 1;		# Close contact step size
my $MaxRxnRadius 			= 9;		# Final close contacts cutoff - hopefully conversion
							# is reached before this value is reached
my $IterationsPerRadius			= 2;		# Max number of xlink tries at each radius
						
# Special names used for the reactive atoms and molecules. The input structure must have atoms with these names defined.

my $monomerName 			= "monomer";	# Monomer molecules are all given this name to help track stats
my $monomerReactiveAtom 		= "R1";		# The name of the reactive atom(s) in the monomer molecule
my $xlinkerName 			= "xlinker";	# Xlinker molecules are all given this name to help track stats
my $xlinkerReactiveAtom 		= "R2";		# The name of the reactive atom(s) in the cross linking molecule

# Normally we define molecule objects for each fragment in order to track statistics
# In some cases like self-crosslinking fragments and interfacial crosslinking, use this flag to turn off molecule use
# The consequence is that statistics for the monomer and xlinker species will not be reported
my $noMol				= TRUE;

# Use this flag to open epoxide-type rings after crosslinking
my $openRing				= FALSE;
my $deleteAtom 				= "O";		# Delete bond between R1 and this atom - for ring opening

# Condensation: Remove OH group from reacted atoms?
my $remove_condensation			= FALSE;

# Polyurethane: Convert double to single bond for isocyanates, assuming R1 = carbon from O=C=N grp
my $polyurethane			= FALSE;

# Allow multiple reactions on a single atom?
my $react_multi_monomer			= FALSE;
my $react_multi_xlinker			= FALSE;

# Prevent multiple crosslinks between same two fragments
# Two methods are implemented depending on the value of $noMol. If we are using molecule
# objects, then we prevent two molecules from linking to each other twice.
# If no molecules, we prevent crosslinks that would result in a pathlength <= 3 between R1 and R2
my $prevent_intraxlinks			= TRUE;
my %xconnect; # connection table to prevent intraxlinks

# Simulation settings
my $forcefield		 	= "COMPASSIII";
my $timeStep			= 0.1;			# Dynamics time step in fs
my $chargeMethod 		= "Atom based";	# Atom based, Group based or Ewald
my $Quality			= "Medium";		# Coarse/Medium/Fine/Ultra Fine
my $thermostat			= "Andersen";		# Andersen, Nose, Velocity Scale, Berendsen or NHL
my $xlinkTemperature		= 300;			# Main temperature throughout
my $xlinkPressure		= 0.0001;		# Main pressure throughout
my $ensemble			= "NVE";		# Normally NPT, but use NVT if eg liquid slab with vacuum layer
							# if non-periodic NVT will be used in any case

# Electric field settings (applies to ALL dynamics simulations including one_time_equilibration)
my $useElectricField            = TRUE;         # Whether to apply an electric field (global switch for all dynamics runs)
my $electricFieldStrength       = 1;          # Electric field strength in V/Å
my $electricFieldX              = 0;             # X component of the field direction
my $electricFieldY              = 0;             # Y component of the field direction
my $electricFieldZ              = 1;             # Z component of the field direction
my $counterElectricField        = "Yes";         # Whether to apply an opposite force to a system with net charge

my $one_time_equilibration	= 20;			# ps of dynamics for initial equilibration

# Options for the perturbations -  Temperature cycle settings
my $UseTempCycle			= FALSE;
my $startTemp				= $xlinkTemperature;		# Starting temperature
my $lowTemp				= $xlinkTemperature;		# Ending temperature
my $highTemp				= $xlinkTemperature + 200;	# High temperature
my $tempStep				= 50;				# Temperature step
my $tempDuration			= 20;				# Time at each temperature (ps)

# Analysis dynamics settings
my $analyzeDuration			= 10;		# ps of dynamics for sampling thermo data

# dynamics before crosslinking at each new distance
my $straightDynamicsTime		= 10;		# Time in ps

# Parameters for temporary restraint bonds used to bring Xlinked bonds together smoothly
my $UseRestraintBonds			= TRUE;		# turn smoothing algorithm on/off
my $RestraintBondingTargetLength 	= 1.47;		# use C-N bond parameters from CVFF
my $RestraintForceConstant 		= 356.5988;
my $nRestraintBondingStages		= 3;		# increments for smoothly increasing restraint bonds
my $relax_length 			= 1;		# time (ps) to relax structures before bonding

# Option for calculating the largest bond energy to help assess network strain and possible ring spearing
# Could impact performance, but algorithm has been tuned to speed it up
my $UseMaxBondEnergy			= TRUE;

######################################################
# GUI? - attempt to open the active document
######################################################
my $xsdDoc;
eval{ $xsdDoc = Documents->ActiveDocument; };

if ($@) # no active doc - use parameters defined above
{
	$xsdDoc	= $Documents{"$xsdDocName.xsd"};
}
else
{
	# Get the user supplied options
	my %Arg;
	GetOptions(\%Arg,
		"Forcefield=s", 
		"Conversion=i", 
		"MinRxnRadius=f", 
		"MaxRxnRadius=f", 
		"StepRxnRadius=f",
		"Iterations_per_Radius=i", 
		"ChargeMethod=s", 
		"Temperature=f",
		"TemperatureCycle=s",
		"Temperature_Step=f",
		"High_Temperature=f",
		"ps_per_Temperature=f",
		"Initial_Equil_ps=f",
		"Equil_ps=f",
		"OpenRings=s",
		"Condensation=s",
		"Polyurethane=s",
		"React_Multi_Monomer=s",
		"React_Multi_Xlinker=s",
		"Use_Molecules=s",
		"Ensemble=s",
		"UseElectricField=s",
		"ElectricFieldStrength=f",
		"ElectricFieldX=f",
		"ElectricFieldY=f",
		"ElectricFieldZ=f",
		"CounterElectricField=s"
	);

	$conversionTarget 	= $Arg{Conversion}	if (exists $Arg{Conversion});
	$MinRxnRadius 		= $Arg{MinRxnRadius}	if (exists $Arg{MinRxnRadius});
	$StepRxnRadius 		= $Arg{StepRxnRadius}	if (exists $Arg{StepRxnRadius});
	$MaxRxnRadius 		= $Arg{MaxRxnRadius}	if (exists $Arg{MaxRxnRadius});
	$IterationsPerRadius	= $Arg{Iterations_per_Radius} if (exists $Arg{Iterations_per_Radius});
	$openRing		= TRUE  if (exists $Arg{OpenRings} and $Arg{OpenRings} eq "Yes");
	$openRing		= FALSE if (exists $Arg{OpenRings} and $Arg{OpenRings} eq "No");
	$remove_condensation	= TRUE  if (exists $Arg{Condensation} and $Arg{Condensation} eq "Yes");
	$remove_condensation	= FALSE if (exists $Arg{Condensation} and $Arg{Condensation} eq "No");
	$polyurethane		= TRUE  if (exists $Arg{Polyurethane} and $Arg{Polyurethane} eq "Yes");
	$polyurethane		= FALSE if (exists $Arg{Polyurethane} and $Arg{Polyurethane} eq "No");
	$react_multi_monomer	= TRUE  if ($Arg{React_Multi_Monomer} eq "Yes");
	$react_multi_monomer	= FALSE if ($Arg{React_Multi_Monomer} eq "No");
	$react_multi_xlinker	= TRUE  if ($Arg{React_Multi_Xlinker} eq "Yes");
	$react_multi_xlinker	= FALSE if ($Arg{React_Multi_Xlinker} eq "No");
	$forcefield	 	= $Arg{Forcefield}	if (exists $Arg{Forcefield});
	$chargeMethod 		= $Arg{ChargeMethod}	if (exists $Arg{ChargeMethod});
	$xlinkTemperature	= $Arg{Temperature}	if (exists $Arg{Temperature});
	$one_time_equilibration	= $Arg{Initial_Equil_ps} if (exists $Arg{Initial_Equil_ps});
	$straightDynamicsTime   = $Arg{Equil_ps}	if (exists $Arg{Equil_ps});
	$UseTempCycle		= TRUE  if (exists $Arg{TemperatureCycle} and $Arg{TemperatureCycle} eq "Yes");
	$UseTempCycle		= FALSE if (exists $Arg{TemperatureCycle} and $Arg{TemperatureCycle} eq "No");
	$highTemp		= $Arg{High_Temperature}	if (exists $Arg{High_Temperature});
	$tempStep		= $Arg{Temperature_Step}	if (exists $Arg{Temperature_Step});
	$tempDuration		= $Arg{ps_per_Temperature}	if (exists $Arg{ps_per_Temperature});
	$noMol			= TRUE  if (exists $Arg{Use_Molecules} and $Arg{Use_Molecules} eq "No");
	$noMol			= FALSE if (exists $Arg{Use_Molecules} and $Arg{Use_Molecules} eq "Yes");
	$ensemble		= $Arg{Ensemble}	if (exists $Arg{Ensemble} and $Arg{Ensemble} ne "");
	
	# Electric field parameters
	$useElectricField       = TRUE  if (exists $Arg{UseElectricField} and $Arg{UseElectricField} eq "Yes");
	$useElectricField       = FALSE if (exists $Arg{UseElectricField} and $Arg{UseElectricField} eq "No");
	$electricFieldStrength  = $Arg{ElectricFieldStrength} if (exists $Arg{ElectricFieldStrength});
	$electricFieldX         = $Arg{ElectricFieldX} if (exists $Arg{ElectricFieldX});
	$electricFieldY         = $Arg{ElectricFieldY} if (exists $Arg{ElectricFieldY});
	$electricFieldZ         = $Arg{ElectricFieldZ} if (exists $Arg{ElectricFieldZ});
	$counterElectricField   = "Yes" if (exists $Arg{CounterElectricField} and $Arg{CounterElectricField} eq "Yes");
	$counterElectricField   = "No"  if (exists $Arg{CounterElectricField} and $Arg{CounterElectricField} eq "No");

	# Write to text file options specified by user in the GUI
	my $textDoc = Documents->New("GUI_inputs.txt");
	$textDoc->Append("USER OPTIONS\n============================\n");
	while ( my ($key, $value) = each(%Arg) ) 
	{
		$textDoc->Append(sprintf "$key => $value\n");
	}
	$textDoc->Append("============================\n\n");
	$textDoc->Close;

}

###########################################################################################
#
# End user settings
#
###########################################################################################

# Import some server files with info on hostname, mpi, arguments
eval 
{
	Documents->Import("fromdsd.txt");
	Documents->Import("mpd.hosts");
};

# File for reporting run progress
my $textDoc = Documents->New("Progress.txt");

# File for reporting time taken by each cycle 
my $timeDoc = Documents->New("Timings.txt");
$timeDoc->Append("Distance Iteration Elapsted_Time(hr) Segment_Time(hr) Conversion(%)\n");
my $segtime = time; # Time in seconds for a segment of the run

# Make a copy of the first document to preserve the starting structure
my $rootName = "xlink";
my $doc = Documents->New("$rootName.xsd");
$doc->CopyFrom($xsdDoc);

# Initialize Forcite with settings to be used globally
my $Forcite = Modules->Forcite;
$Forcite->ChangeSettings([
	CurrentForcefield	=> $forcefield,
	Quality			=> $Quality,
	Temperature		=> $xlinkTemperature,
	Pressure		=> $xlinkPressure,
	Thermostat		=> $thermostat,
	Barostat		=> "Andersen",
	TimeStep		=> $timeStep,
	TrajectoryFrequency	=> 5000,
	AppendTrajectory	=> "No",
	WriteVelocities		=> "Yes",
	EnergyDeviation		=> 500000000,
	WriteLevel		=> "Silent"
]);

# Periodicity: 0 and 3 are supported. 2 might work
my $Periodicity;
if (($doc->SymmetrySystems->Count == 0) || ($doc->SymmetrySystem->SymmetryDefinition->Periodicity == 0))
{
	$Periodicity = 0;
	die "Cannot use Ewald for nonperiodic systems\n" if ($chargeMethod eq "Ewald");
	$Forcite->ChangeSettings([
		NonPeriodicElectrostaticSummationMethod		=> $chargeMethod,
		NonPeriodicvdWSummationMethod			=> "Atom based"
	]);
}
else
{
	$Periodicity = $doc->SymmetrySystem->SymmetryDefinition->Periodicity;
	$Forcite->ChangeSettings([
	    	'3DPeriodicElectrostaticSummationMethod'	=> $chargeMethod,
		'****************************' 			=> "Atom based",
		'2DPeriodicElectrostaticSummationMethod'	=> $chargeMethod,
		'2DPeriodicvdWSummationMethod'			=> "Atom based",
	]);
} 
warn "2D periodicity has not been tested\n" if ($Periodicity == 2);


# Prevent filename conflict for restart runs
$xsdDoc->Name = "initial" if ($xsdDoc->Name =~ /^xlink_/);
$xsdDoc->Close;

# Create a study table to hold the statistics
my $statTable = Documents->New($rootName."_statistics.std");
my $nstatsheets = 1;

# Create a study table to hold the intermediate structures at the end of each distance cycle
# This can be used for further analysis of the evolution of the system.
# (could be merged with the above, but would have to shift the columns to the right)
my $structureTable = Documents->New($rootName."_structures.std");
$structureTable->ColumnHeading(1) = "Distance (A)";
$structureTable->ColumnHeading(2) = "Iteration";
$structureTable->ColumnHeading(3) = "Percent Conversion";



###########################################################################################
# Initialize the crosslinking stuff

# Count the reacted and reactive atoms
my $reactiveMonomerAtoms = 0;
my $reactiveXLinkerAtoms = 0;
my $reactedMonomerAtoms  = 0;
my $reactedXLinkerAtoms  = 0;
my $totalMonomerAtoms    = 0;
foreach my $atom (@{$doc->UnitCell->Atoms})
{
	$reactiveMonomerAtoms++ if ( isReactiveR1($atom) );
	$reactiveXLinkerAtoms++  if ( isReactiveR2($atom) );
	$reactedMonomerAtoms++  if ($atom->Name =~ /^$monomerReactiveAtom-\d/);
	$reactedXLinkerAtoms++   if ($atom->Name =~ /^$xlinkerReactiveAtom-\d/);
	$totalMonomerAtoms++    if ($atom->Name =~ /^$monomerReactiveAtom/);
}

# xlinkPotential is the number of possible crosslinks that the xlinkers can form
my $xlinkPotential = $reactiveXLinkerAtoms;
if ($react_multi_xlinker)
{
	$xlinkPotential = 0;
	foreach my $atom (@{$doc->UnitCell->Atoms})
	{
		if ($atom->Name =~ /^$xlinkerReactiveAtom/)
		{
			$xlinkPotential += HCount($atom);
			if ($remove_condensation)
			{
				my ($nOH) = OHCount($atom);
				$xlinkPotential += $nOH;
			}
		}
	}
}

# Count xlinks formed during previous runs
my $xlinkCounter = 0;
foreach my $bond (@{$doc->UnitCell->Bonds})
{
	$xlinkCounter++ if ($bond->Name =~ /^xlink/);
}

my $conversion = calculateConversion($doc);
my $rowCounter		= 0;	# Current row in the study tables

# Check required number of monomer and xlinker atoms are consistent with conversion
my $targetMonomerAtoms = $totalMonomerAtoms * ($conversionTarget / 100) - $reactedMonomerAtoms;
$textDoc->Append( "Conversion = percent of reactive monomer atoms that have reacted\n");
$textDoc->Append( "Conversion target = $conversionTarget, Current conversion = $conversion\n");
$textDoc->Append( "Total number of monomer atoms to react: $targetMonomerAtoms \n");
$textDoc->Append( "Reactant atoms: monomer $reactiveMonomerAtoms, xlinker $reactiveXLinkerAtoms\n");
$textDoc->Save;
die "Need more xlinkers to achieve specified conversion\n" 
	if ($xlinkPotential < $targetMonomerAtoms);

# Counters for optimization and dynamics steps
my $mdcounter = 0;
my $geomoptcounter = 0;

# Prepare the molecule objects so xlink statistics can be tracked
Initialize_xlinkStatistics($doc) unless ($noMol);

###########################################################################################
# One time equilibration
# Note: Electric field will be applied here if $useElectricField is TRUE

if ($one_time_equilibration > 0)
{
	my $steps = ($one_time_equilibration * PICO_TO_FEMTO / $timeStep);
	$textDoc->Append("\nOne-time equilibration\n");
	my $results = ForciteDynamics($doc, $steps, "NVT");
	$results->Trajectory->Delete;
	my $results = ForciteDynamics($doc, $steps, $ensemble);
	$results->Trajectory->Delete;
}

###########################################################################################
# Main crosslinking loop
# Increment reaction radius
$textDoc->Append("Entering main crosslinking loop\n");
$textDoc->Save;

for (my $RxnRadius = $MinRxnRadius; $RxnRadius <= $MaxRxnRadius; $RxnRadius += $StepRxnRadius) 
{
	# Base name for the structure	
	my $xsdNameDist = sprintf("%s_R%.2f", $rootName, $RxnRadius);

  	# Equilibrate for each new radius (except first)
  	if ($RxnRadius > $MinRxnRadius)  
  	{
		$textDoc->Append( "Equilibrating at new radius\n");
		$doc->Name = $xsdNameDist . "_init";
		ForciteGeomOpt($doc, 2000);
		my $steps = ($straightDynamicsTime * PICO_TO_FEMTO / $timeStep);
		my $results = ForciteDynamics($doc, $steps, $ensemble);
		$results->Trajectory->Delete;
  	}

	# Iterate crosslinking at each radius until one of:
	# A) Maximum number of iterations is reached
	# B) No new xlinks formed
	# C) Conversion target is reached
	for (my $iteration = 1; $iteration <= $IterationsPerRadius; $iteration++)
	{
	
		$textDoc->Append( "\n\n##########################################################\n");
		$textDoc->Append( "###### Radius $RxnRadius, iteration $iteration\n");
		$textDoc->Append( "##########################################################\n\n");
		$textDoc->Save;
			
  		$doc->Name = $xsdNameDist."_".$iteration;

		# Create the new bonds in the structure				
		my $numBonds = createNewXlinks($doc, $RxnRadius);

		# Update conversion
		$conversion = calculateConversion($doc);
		$textDoc->Append(sprintf "Crosslinks= %d \nConversion= %.01F %%\n", $xlinkCounter, $conversion);				
		$textDoc->Save;
			
		# If there are no new bonds, exit loop and go to next radius
		if ($numBonds == 0)
		{
			$textDoc->Append("No new bonds created, increasing reaction distance\n");
			last;
		}
		
		# Perturbation to remove long bonds that may have been generated
		optimizeAndPerturb($doc);

		# Generate some cross link statistics
		xlinkStatistics($doc, $RxnRadius, $rowCounter) unless ($noMol);	
		maxBondEnergy($doc, $RxnRadius, $rowCounter) if ($UseMaxBondEnergy);
		AnalyzeFragments($doc, $RxnRadius, $rowCounter);

		# Save structure to study table
		$textDoc->Append("Saving intermediate structure to study table\n");				
		$doc->InsertInto($structureTable);
		$structureTable->Cell($rowCounter,1) = $RxnRadius;
		$structureTable->Cell($rowCounter,2) = $iteration;
		$structureTable->Cell($rowCounter,3) = $conversion;

		# short dynamics run to record some thermodynamic properties
		$textDoc->Append( "\n\nRunning additional dynamics for analysis\n");
		my $steps = ($analyzeDuration * PICO_TO_FEMTO / $timeStep);
		my $freq = int($steps/20);
		my $results = ForciteDynamics($doc, $steps, $ensemble, (TrajectoryFrequency => $freq) );								
		getTrajTempAndPressure($results, $rowCounter, $RxnRadius);
		getEnergies($results,$rowCounter);
		$results->Trajectory->Delete;
		$rowCounter++;
				
		# Report the time taken by this iteration
		$timeDoc->Append(sprintf "%-8.2f %-9d %-17.1f %-16.2f %-8.1f\n", 
			$RxnRadius, $iteration, (time-$^T)/3600, 
			(time-$segtime)/3600, $conversion);
		$segtime = time;
		
		# Save all docs in case you want to terminate the script and not lose data		
		Documents->SaveAll;
	
		last if ($conversion >= $conversionTarget);

	} # next iteration
	
	last if ($conversion >= $conversionTarget);

} # next radius
 	
# Rename the final xsd with a unique name
$doc->Name = $rootName."_final";

# Calculate the bond distribution
analyzeBonds($doc);

# Create set of xlinked atoms.
XlinkSet($doc);

$textDoc->Append( "\n##############################################################\n\n");
$textDoc->Append( "Calculation is complete\n");
$textDoc->Append( "There are $xlinkCounter crosslinks in the system\n");
$textDoc->Append( sprintf "Final conversion %.1f%\n", $conversion);
$textDoc->Append( "Total geometry optimization steps: $geomoptcounter\n");
$textDoc->Append( "Total molecular dynamics steps: $mdcounter\n");

# Report total time
my $time_hr = (time-$^T)/3600;
$textDoc->Append(sprintf ("\nTotal time %.2f hr\n", $time_hr));
$textDoc->Append( "\n##############################################################\n");
$textDoc->Save;
Documents->SaveAll;



##########################################################################################################
#
#		END OF MAIN
#
##########################################################################################################

# Conversion is defined as the percent of reactive monomer (R1) atoms that have reacted at least once
sub calculateConversion
{
	my $doc1 = shift;
	# Count the reacted atoms
	my $reactedMonomerAtoms  = 0;
	my $totalMonomerAtoms    = 0;
	foreach my $atom (@{$doc1->UnitCell->Atoms})
	{
		$reactedMonomerAtoms++  if ($atom->Name =~ /^$monomerReactiveAtom-\d/);
		$totalMonomerAtoms++    if ($atom->Name =~ /^$monomerReactiveAtom/);
	}

	my $conversion = 100.0 * $reactedMonomerAtoms / $totalMonomerAtoms;
	return $conversion;
}

#########################################################################################################

# Creates 'not-R1' and 'not-R2' sets 
# These are used in the close-contacts calculation, done in set-exclusion mode. 
# Finds contacts between:
# (A) R1 and R2 atoms in the unit cell
# (B) (unfortunately) R1 or R2 atoms in the unit cell and image atoms in adjacent cells.

sub createReactiveAtomSets 
{
	my $doc = shift;

	$textDoc->Append("  createReactiveAtomSets\n");

	# Initialise reactive atom counters	
	my $R1Counter = 0;
	my $R2Counter = 0;
	
	# Create two perl arrays for atoms	
	my @notR1;
	my @notR2;

	# Create two sets based on atom names
	# One contains all atoms except R1 and the other all except R2
	my $atoms = $doc->UnitCell->Atoms;
	foreach my $atom (@$atoms) 
	{
		# Check to see if the atom is a reactive atom	
		if (isReactiveR1($atom)) 
		{					
			push (@notR2, $atom);
			$R1Counter++;		
		} 
		elsif (isReactiveR2($atom)) 
		{					
			push (@notR1, $atom);
			$R2Counter++;		
		} 
		else 
		{		
			push (@notR1, $atom);
			push (@notR2, $atom);
		}	
	}

	# Create the sets based on the atom arrays and hide them
	my $notR1Set = $doc->CreateSet("notR1", \@notR1);
	my $notR2Set = $doc->CreateSet("notR2", \@notR2);
	$notR1Set->IsVisible = 0;
	$notR2Set->IsVisible = 0;

	$textDoc->Append("    $R1Counter reactive monomer atoms\n");
	$textDoc->Append("    $R2Counter reactive xlink atoms\n\n");
	$textDoc->Save;
		
	return ($doc, $R1Counter, $R2Counter);
}

#########################################################################################################

# Determine whether this is an monomer reactive atom
# It depends if react_multi is being used. If so, we check attached hydrogens.

sub isReactiveR1
{
	my $atom = shift;
	my $name = $atom->Name;
	if ($name =~ /^$monomerReactiveAtom/)
	{
		return TRUE if (not $react_multi_monomer and $name eq "$monomerReactiveAtom");
		if ($remove_condensation and $react_multi_monomer)
		{
			my ($nOH) = OHCount($atom);
			return TRUE if ($nOH > 0);
		}
		return TRUE if ($react_multi_monomer and HCount($atom) > 0);
	}
	return FALSE;
}

#########################################################################################################

# Determine whether this is an xlinker reactive atom
# It depends if react_multi is being used. If so, we check attached hydrogens.

sub isReactiveR2
{
	my $atom = shift;
	my $name = $atom->Name;
	if ($name =~ /^$xlinkerReactiveAtom/)
	{
		return TRUE if (not $react_multi_xlinker and $name eq "$xlinkerReactiveAtom");
		if ($remove_condensation and $react_multi_xlinker)
		{
			my ($nOH) = OHCount($atom);
			return TRUE if ($nOH > 0);
		}
		return TRUE if ($react_multi_xlinker and HCount($atom) > 0);
	}
	return FALSE;
}

#########################################################################################################

# This was added to handle restarts more gracefully
# Returns the number of atoms with names like R1-38 and R2-7

sub countReactedAtoms
{
	my ($doc1) = @_;
	my $nO = 0; my $nXL = 0;
	my $atoms = $doc1->UnitCell->Atoms;
	foreach my $atom (@$atoms) 
	{
		$nO++ if ($atom->Name =~ /^$monomerReactiveAtom-\d+$/);
		$nXL++ if ($atom->Name =~ /^$xlinkerReactiveAtom-\d+$/);
	}
	return ($nO, $nXL);
}

#########################################################################################################

# Crosslink R1-R2 close-contact atoms either with restraints or with real bonds
# Atoms are renamed to prevent further crosslinking (unless multi option is on)

sub createNewXlinks 
{
	my $doc1 = shift;
	my $distance = shift;
	
	my $t0 = time;
	$textDoc->Append("createNewXlinks\n");	

	# Update the reactive sets
	($doc1, my $R1Count, my $R2Count) = createReactiveAtomSets($doc1);
			
	# Recalculate close contacts using set-exclusion, based on current reaction 
	# distance cutoff and updated sets
	Tools->BondCalculation->ChangeSettings([
		DistanceCriterionMode	=> "Absolute",
		ExclusionMode		=> "Set", 
		MaxAbsoluteDistance	=> $distance
	]);
	my $closeContacts = $doc1->CalculateCloseContacts;		
		
	# Delete contacts with non-R atoms in adjacent cells
	$textDoc->Append(sprintf "Found %d close contacts\n", $closeContacts->Count) if (DEBUG);
	foreach my $closeContact (@$closeContacts) 
	{
		my $atom1 = $closeContact->Atom1;
		my $atom2 = $closeContact->Atom2;	
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}
		# Delete unless both atoms are reactive and of opposite types
		$closeContact->Delete unless ( isReactiveR1($atom1) and isReactiveR2($atom2) );
	}
	$textDoc->Append(sprintf "After non-R exclusions: %d\n", $closeContacts->Count) if (DEBUG > 1);


	# Do not allow 'intra' crosslinks, i.e. multiple bonds between same units
	$closeContacts = excludeIntra($doc1, $closeContacts) if ($closeContacts->Count);


	# Exclude crosslinks where an atoms reactivity is nullified by an earlier crosslink in the list
	$closeContacts = excludeConcurrentIsreactive($doc1, $closeContacts) if ($closeContacts->Count);
	

	# Only allow atoms to have one new crosslink per round (just to be conservative)
	$closeContacts = excludeSameAtom($doc1, $closeContacts) if ($closeContacts->Count);

	
	# Exclude any that would exceed conversion target
	$closeContacts = excludeConversionLimit($doc1, $closeContacts) if ($closeContacts->Count);


	# Convert the surviving close contacts to restraints or bonds
	my $newBondCounter = 0;
	
	foreach my $closeContact (@$closeContacts) 
	{
		my $atom1 = $closeContact->Atom1;
		my $atom2 = $closeContact->Atom2;
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}

		# Create the xlink (real or virtual)				
		if ($UseRestraintBonds)
		{
			createNewBondRestraint($doc1, $atom1, $atom2);
		} 
		else 
		{
			createNewBond($doc1, $atom1, $atom2);
		}
		$newBondCounter++;
		
	}
	
	$textDoc->Append("  $newBondCounter links formed\n");	
	
	EquilibrateRestraintXlinks($doc1) if ($UseRestraintBonds and $newBondCounter > 0);

	$textDoc->Append( "  $newBondCounter new xlink bonds\n\n");
	$textDoc->Save;
	
	# Adjust hydrogens
	$doc1->AdjustHydrogen;
	
	# Make sure all the R atoms have the right coordination
	testCoordination($doc1);
	
	# Regenerate charge groups
	if ($chargeMethod eq "Group based")
	{
		$doc1 = DivideAndConquer($doc1, $Forcite);
	}
	
	# Remove the close contacts and sets for the next cycle	
	$doc1->UnitCell->CloseContacts->Delete;
	$doc1->UnitCell->Sets->Delete;
	
	# Geometry optimize
	$textDoc->Append("  ");
	ForciteGeomOpt($doc1, 2000);
	
	# Report time
	$textDoc->Append(sprintf "\ncreateNewXlinks took %d seconds\n\n", time-$t0); 
	$textDoc->Save;

	return ($newBondCounter);
}


##############################################################################################################
# Test that all the R atoms have the right number of bonds

sub testCoordination
{
	my $doc2 = shift;
	my %Coord = (
		"C" => 4,
		"N" => 3,
		"O" => 2,
		"Si" => 4
		);
	my $nmatch = 0; my $nmismatch = 0;
	foreach (@{$doc2->UnitCell->Atoms})
	{
		next unless ($_->Name =~ /^$monomerReactiveAtom/ or $_->Name =~ /^$xlinkerReactiveAtom/);
		next unless (exists $Coord{$_->ElementSymbol});
		my $expectedCoordination = $Coord{$_->ElementSymbol};
		my $actualCoordination = calcCoord($_);
		if ($expectedCoordination == $actualCoordination)
		{
			$nmatch++;
		}
		else
		{
			$nmismatch++;
			$textDoc->Append(sprintf "Coordination mismatch: name %s, element %s, expected %.1f, actual %.1f\n", 
				$_->Name, $_->ElementSymbol, $expectedCoordination, $actualCoordination)
				if (DEBUG > 1);
		}
	}
	if ($nmismatch)
	{
		$textDoc->Append(sprintf "WARNING: %d of %d of the reactive atoms have a coordination issue\n", 
			$nmismatch, $nmatch+$nmismatch);
	}
	else
	{
		$textDoc->Append("Coordination test on $nmatch atoms OK\n") if (DEBUG);
	}
}

##############################################################################################################

sub calcCoord
{
	my $atom = shift;
	my $coord = 0;
	foreach (@{$atom->Bonds})
	{
		$coord++ if ($_->BondType eq "Single");
		$coord+2 if ($_->BondType eq "Double");
		$coord+3 if ($_->BondType eq "Triple");
		$coord+1.5 if ($_->BondType eq "Aromatic");
		$coord+1.5 if ($_->BondType eq "Partial double");
	}
	return $coord;
}

##############################################################################################################
# Delete intraxlinks
# An intraxlink is defined as a second crosslink between the same two atoms or unitary fragments

sub excludeIntra
{
	my $doc2 = shift;
	my $closeContacts = shift;
	
	if ($prevent_intraxlinks and $noMol) 
	{
		my @tmpbonds = ();
		# Here we just prevent xlinks between atoms
		# already linked together by 3 or fewer bond pathlength
		foreach my $closeContact (@$closeContacts) 
		{
			my $atom1 = $closeContact->Atom1;
			my $atom2 = $closeContact->Atom2;
			if ( alreadyLinked($atom1, $atom2) )
			{
				$closeContact->Delete;
			}
			else
			{
				# Create temporary bond to prevent concurrent intraxlinks
				my $bond = $doc2->CreateBond($atom1, $atom2, "Single", [Name=>"tmpbond",Color=>RGB(128,255,0)]);
				push @tmpbonds, $bond;
			}
		}
		foreach (@tmpbonds)
		{
			$_->Delete;
		}
	} # end of path-based intra
	
	elsif ($prevent_intraxlinks and not $noMol)
	{
		my $intra1Count=0;
		foreach my $closeContact (@$closeContacts) 
		{
			my $atom1 = $closeContact->Atom1;
			my $atom2 = $closeContact->Atom2;
			my $mol1; my $mol2;
			eval{ 
				$mol1 = $atom1->Ancestors->Molecule->Name; 
				$mol2 = $atom2->Ancestors->Molecule->Name;
				foreach my $mol (@{$xconnect{$mol1}})
				{
					if ($mol eq $mol2)
					{
						$closeContact->Delete;
						$intra1Count++;
						last;
					}
				}
			};
		}
		

		# exclude concurrent intraxlinks
		my $intra2Count=0;
		my %xconnect_tmp;
		foreach my $closeContact (@$closeContacts) 
		{
			# test if this close contact clashes with any previous
			my $isIntra = FALSE;
			my $atom1 = $closeContact->Atom1;
			my $atom2 = $closeContact->Atom2;
			my $mol1; my $mol2;
			eval{ 
				$mol1 = $atom1->Ancestors->Molecule->Name; 
				$mol2 = $atom2->Ancestors->Molecule->Name;
				foreach my $mol (@{$xconnect_tmp{$mol1}})
				{
					if ($mol eq $mol2)
					{
						$closeContact->Delete;
						$intra2Count++;
						$isIntra = TRUE;
						last;
					}
				}
				# add this close contact to temporary xconnect table
				if (not $isIntra)
				{
					push @{$xconnect_tmp{$mol1}}, $mol2;
					push @{$xconnect_tmp{$mol2}}, $mol1;
				}
			};
		}

		$textDoc->Append(sprintf "Deleted %d prior and %d concurrent intra-closecontacts\n", $intra1Count, $intra2Count) 
			if (DEBUG > 2);
		
	} # end of mol-based intra
	
	$textDoc->Append(sprintf "After intra exclusions: %d close contacts\n", $closeContacts->Count) if (DEBUG > 1);
	return $closeContacts;
}


##############################################################################################################
# Delete concurrent-isreactive xlinks
# Crosslinks can affect the isreactive state of their constituent atoms
# The following excludes cases where a 'previous' concurrent crosslink would turn the atom's reactivity off

sub excludeConcurrentIsreactive
{
	my $doc2 = shift;
	my $closeContacts = shift;
	
	
	# Create a temporary document to test the crosslinking scheme
	my $tmpdoc = $doc2->SaveAs("CrosslinkTest.xsd");
	my $tmpCC = $tmpdoc->UnitCell->CloseContacts;
	$textDoc->Append("WARNING: Number of close contacts do not match\n") if ($tmpCC->Count != $closeContacts->Count);
	
	# Synchronize the closecontacts in each document
	my $nAsync=0;
	my @cclist = ();
	for (my $i=0; $i < $closeContacts->Count; $i++)
	{
		my $name = sprintf("cc%d", $i+1);
		push @cclist, $name;
		my $ccA = $closeContacts->[$i];
		my $ccB = $tmpCC->[$i];
		$ccA->Name = $name;
		$ccB->Name = $name;
		# create a unique ID for each close-contact
		my $ccAtest = sprintf("X1%f_X2%f", $ccA->Atom1->X, $ccA->Atom2->X);
		my $ccBtest = sprintf("X1%f_X2%f", $ccB->Atom1->X, $ccB->Atom2->X);
		$nAsync++ if ($ccAtest ne $ccBtest);
	}
	$textDoc->Append("WARNING: $nAsync close contacts do not match\n") if ($nAsync);
	
	# Loop through the close-contacts, giving priority to those earlier in the list
	my $nExcluded=0; my $nImplicitExcluded=0;
	foreach my $ccname (@cclist) 
	{
		$textDoc->Append("CC $ccname") if (DEBUG > 3);
		my $cc = $closeContacts->Item($ccname);
		
		# an atom in the close-contact might already have been deleted
		my $closeContact;
		eval { $closeContact = $tmpCC->Item($ccname); };
		if ($@) 
		{
			# if the atom was deleted (eg because of condensation removal of OH)
			# then this close contact was deleted implicitely. So we need to 
			# exclude it in the real structure.
			$cc->Delete;
			$nImplicitExcluded++;
			$textDoc->Append(" implicit exclusion\n") if (DEBUG > 3);
			next;
		}
		
		# fetch the atoms, with the monomer atom first
		my $atom1; my $atom2;
		$atom1 = $closeContact->Atom1;
		$atom2 = $closeContact->Atom2;
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}
		

		# are they still reactive?
		if (isReactiveR1($atom1) and isReactiveR2($atom2))
		{
			$textDoc->Append(" Creating test bonds.") if (DEBUG > 3);
			# Create temporary bond to return correct isReactive state on next iteration
			createNewBond($tmpdoc, $atom1, $atom2, TRUE);
			$tmpdoc->AdjustHydrogen;
		}
		else
		{
			$textDoc->Append(" not reactive, deleting $ccname.") if (DEBUG > 3);
			$cc->Delete;
			$closeContact->Delete;
			$nExcluded++;
		}
		$textDoc->Append(sprintf "CC %d testCC %d\n", $closeContacts->Count, $tmpCC->Count) if (DEBUG > 3);
	}
	$textDoc->Append(sprintf " Deleted %d (%d explicit & %d implicit) contacts due to concurrent-isreact rule\n", 
		$nExcluded+$nImplicitExcluded, $nExcluded, $nImplicitExcluded) if (DEBUG > 2);
	$textDoc->Append(sprintf "After concurrent-isreactive exclusions: %d close contacts\n", $closeContacts->Count) 
		if (DEBUG > 1);
	$tmpdoc->Delete;
	return $closeContacts;
}


##############################################################################################################
# Only allow atoms to have one new crosslink per round (just to be conservative)

sub excludeSameAtom
{
	my $doc2 = shift;
	my $closeContacts = shift;
	
	my $lockedAtoms = $doc2->UnitCell->Beads->Atoms; # empty collection
	foreach my $closeContact (@$closeContacts) 
	{
		my $atom1 = $closeContact->Atom1;
		my $atom2 = $closeContact->Atom2;	
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}
		if ($atom1->Name =~ /_LOCKED$/ or $atom2->Name =~ /_LOCKED$/)
		{
			$closeContact->Delete;
			next;
		}
		# Lock these atoms so they don't react again
		$atom1->Name .= "_LOCKED";
		$atom2->Name .= "_LOCKED";
		$lockedAtoms->Add($atom1);
		$lockedAtoms->Add($atom2);
	}
	# Unlock the atoms
	foreach my $atom (@$lockedAtoms)
	{
		$atom->Name =~ s/_LOCKED$//;
	}
	$textDoc->Append(sprintf "After same-atom exclusions: %d close contacts\n", $closeContacts->Count) 
		if (DEBUG > 1);
	return $closeContacts;
}


##############################################################################################################
# Delete any crosslinks that would exceed conversion target
# We simulate the bonding in a separate document since eg condensation may mean that conversion must
# be recalculated after each crosslink (for instance R1 atoms might be deleted)

sub excludeConversionLimit
{
	my $doc2 = shift;
	my $closeContacts = shift;
	
	# Create a temporary document to test the crosslinking scheme
	my $tmpdoc = $doc2->SaveAs("CrosslinkTest2.xsd");
	my $tmpCC = $tmpdoc->UnitCell->CloseContacts;
	$textDoc->Append("WARNING: Number of close contacts do not match\n") if ($tmpCC->Count != $closeContacts->Count);
	
	# Synchronize the closecontacts in each document
	my $nAsync=0;
	my @cclist = ();
	for (my $i=0; $i < $closeContacts->Count; $i++)
	{
		my $name = sprintf("cc%d", $i+1);
		push @cclist, $name;
		my $ccA = $closeContacts->[$i];
		my $ccB = $tmpCC->[$i];
		$ccA->Name = $name;
		$ccB->Name = $name;
		# create a unique ID for each close-contact
		my $ccAtest = sprintf("X1%f_X2%f", $ccA->Atom1->X, $ccA->Atom2->X);
		my $ccBtest = sprintf("X1%f_X2%f", $ccB->Atom1->X, $ccB->Atom2->X);
		$nAsync++ if ($ccAtest ne $ccBtest);
	}
	$textDoc->Append("WARNING: $nAsync close contacts do not match\n") if ($nAsync);
	
	# Loop through the close-contacts, giving priority to those earlier in the list
	my $nLimit = -1;
	for (my $i=0; $i<@cclist; $i++)
	{
		my $ccname = $cclist[$i];
		my $cc = $closeContacts->Item($ccname);
		my $closeContact = $tmpCC->Item($ccname);
		
		# fetch the atoms, with the monomer atom first
		my $atom1; my $atom2;
		$atom1 = $closeContact->Atom1;
		$atom2 = $closeContact->Atom2;
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}
		
		# Update bonding in tmp doc
		createNewBond($tmpdoc, $atom1, $atom2, TRUE);
		$tmpdoc->AdjustHydrogen;
		
		my $tmpConversion = calculateConversion($tmpdoc);
		if ($tmpConversion > $conversionTarget)
		{
			$nLimit = $i;
			last;
		}
	}
	$tmpdoc->Delete;
	
	# now delete all of the contacts above the limit
	# here we allow one over the limit to trigger the end of the script in the main loop
	if ($nLimit >= 0)
	{
		for (my $i=$nLimit+1; $i<@cclist; $i++)
		{
			my $ccname = $cclist[$i];
			my $cc = $closeContacts->Item($ccname);
			$cc->Delete;
		}
	}
	$textDoc->Append(sprintf "After conversion limit: %d close contacts\n", $closeContacts->Count) 
		if (DEBUG > 1);
	return $closeContacts;
}


#########################################################################################################
# check if two atoms are separated by 3 or fewer bonds
sub alreadyLinked
{
	my ($atom1, $atom2) = @_;
	my $atom2name_orig = $atom2->Name;
	my $atom2name = "atom2_alreadyLinked";
	$atom2->Name = $atom2name;
	my $atom1name = "atom1_alreadyLinked";
	my $atom1name_orig = $atom1->Name;
	$atom1->Name = $atom1name;
	my $onebondconnection = FALSE;
	my $twobondconnection = FALSE;
	my $threebondconnection = FALSE;
	foreach my $atomA (@{$atom1->AttachedAtoms})
	{
		my $atomAname = "atomA_alreadyLinked";
		my $atomAname_orig = $atomA->Name;
		$atomA->Name = $atomAname;
		$onebondconnection = TRUE if ($atomA->Name eq $atom2name);
		foreach my $atomB (@{$atomA->AttachedAtoms})
		{
			next if ($atomB->Name eq $atom1name);
			$twobondconnection = TRUE if ($atomB->Name eq $atom2name);
			foreach my $atomC (@{$atomB->AttachedAtoms})
			{
				next if ($atomC->Name eq $atomAname);
				$threebondconnection = TRUE if ($atomC->Name eq $atom2name);
			}
		}
		$atomA->Name = $atomAname_orig;
	}
	$atom1->Name = $atom1name_orig;
	$atom2->Name = $atom2name_orig;
	return TRUE if ($onebondconnection or $twobondconnection or $threebondconnection);
	return FALSE;
}

#########################################################################################################

# Impose harmonic distance restraint between two atoms

sub createNewBondRestraint
{	
	my ($doc2, $atom1, $atom2) = @_;	
	
	# measure the distance between reacting atoms			
	my $distance = $doc2->CreateDistance([$atom1, $atom2]);
	
	# create restraint and set initial equilibrium bond length and force constants
	# (these are updated appropriately in the equilibration routine)					
			
	my $restraint = $distance->CreateRestraint("Harmonic");
	$restraint->HarmonicForceConstant = 0;
	$restraint->HarmonicMinimum = $distance->Distance;
}

#########################################################################################################

# Create the new xlink bond and change the display style and name of the link
# Also adjust chemistry: epoxy rings and condensates
# Expects $atom1 to be from monomer and $atom2 from xlinker

sub createNewBond
{	
	my $doc1 = shift;
	my $atom1 = shift;
	my $atom2 = shift;
	my $test = shift;

	# Open the epoxy ring	
	deleteEpoxyBond($atom1) if ($openRing);

	# Remove condensation OH group	
	if ($remove_condensation) 
	{
		my $oh1 = CondenseLink($atom1, $test);
		my $oh2 = CondenseLink($atom2, $test);
		if (DEBUG > 0)
		{
			$textDoc->Append("    WARNING: CondenseLink removed OH groups from both monomer and xlinker\n") 
				if ($oh1 and $oh2);
			$textDoc->Append("    WARNING: CondenseLink removed OH groups from neither monomer nor xlinker\n") 
				if (not $oh1 and not $oh2);
		}
	}

	# Convert double bond to single in polyurethane	
	Polyurethane($atom1) if ($polyurethane);

	# Create the new bond
        $xlinkCounter++ unless ($test);
	my $newBond = $doc1->CreateBond($atom1, $atom2, "Single", ([Name => "xlink-".$xlinkCounter]));        
        
        # Set the display style of the created bond to ball and stick       
        $atom1->Style = "Ball and stick";
        $atom2->Style = "Ball and stick";
        
        # Append xlink index to each atom name (will not affect future xlinking)
        $atom1->Name .= "-".$xlinkCounter;
        $atom2->Name .= "-".$xlinkCounter;

	# Update connectivity table
	if ($prevent_intraxlinks and not $test)
	{
		my $mol1; my $mol2;
		eval{ 
			$mol1 = $atom1->Ancestors->Molecule->Name;
			$mol2 = $atom2->Ancestors->Molecule->Name;
			push @{$xconnect{$mol1}}, $mol2;
			push @{$xconnect{$mol2}}, $mol1;
		};
	}
        
        $textDoc->Append( sprintf "    Created bond between %s and %s \n\n", $atom1->Name, $atom2->Name)
        	unless ($test);
}

#########################################################################

# Count attached hydrogens

sub HCount
{
	my $atom = shift;
	my $n = 0;
	foreach (@{$atom->AttachedAtoms})
	{
		$n++ if ($_->ElementSymbol eq "H");
	}
	return $n;
}

#########################################################################

# Count attached OH groups and return the first one
# Logical rules:
# (A) First attached atom must be oxygen and have two bonds
# (B) Second attached atom must be hydrogen
# These two rules should ensure that only a proper OH is found

sub OHCount
{
	my $atom = shift;
	my $n = 0;
	my $Oatom;
	my $Hatom;
	foreach my $atom1 (@{$atom->AttachedAtoms})
	{
		if ($atom1->ElementSymbol eq "O" and $atom1->Bonds->Count == 2)
		{
			foreach my $atom2 (@{$atom1->AttachedAtoms})
			{
				if ($atom2->ElementSymbol eq "H")
				{
					$n++;
					if ($n == 1)
					{
						# for the first OH return the atoms in case we want to delete them
						$Oatom = $atom1;
						$Hatom = $atom2;
					}
				}
			}
		}
	}
	return ($n, $Oatom, $Hatom);
}

#########################################################################
# Identify and remove OH group attached to given atom
# In cases where there are multiple OH groups attached, just delete the first 

sub CondenseLink
{
	my $xlinkAtom = shift;
	my $test = shift;
	my $status = 0;
	
	my ($nOH, $Oatom, $Hatom) = OHCount($xlinkAtom);
	if ($nOH > 0)
	{
		$Oatom->Delete;
		$Hatom->Delete;
		$status = 1;
		$textDoc->Append( sprintf "  CondenseLink: deleted OH group from %s\n", $xlinkAtom->Name)
			if (DEBUG > 0 and not $test);
	}
	
	return $status;
}

#######################################################################################################################

# Delete bond between specified atom and the $deleteAtom

sub deleteEpoxyBond 
{
	my $epoxyatom = shift;
	
	my $n = 0;
	foreach my $atom (@{$epoxyatom->AttachedAtoms}) 
	{
		if ($atom->ElementSymbol eq "$deleteAtom")
		{
			foreach my $bond (@{$atom->Bonds}) 
			{
				if ($bond->Atom1->Name eq $epoxyatom->Name or $bond->Atom2->Name eq $epoxyatom->Name)
				{
					$textDoc->Append(
						sprintf "    Deleting epoxide bond between %s and %s\n",
						$bond->Atom1->Name, $bond->Atom2->Name ) 
					if (DEBUG);
					$bond->Delete;
					$n++;
				} 
			}
		}
	}
	warn "deleteEpoxyBond: deleted $n bonds" unless ($n == 1);
}

#######################################################################################################################

# Polyurethane converstion: change double bond to single bond in isocyanate (O=C=N) group

sub Polyurethane 
{
	my $atom0 = shift;
	
	# first validate that it is an isocyanate
	my $isIsocyanate = FALSE;
	
	# ~C~ ??
	unless ($atom0->ElementSymbol eq 'C' and $atom0->Bonds->Count == 2)
	{
		warn "WARNING Invalid polyurethane group, code 1\n";
		return $isIsocyanate;
	}
	
	
	# N~x~O
	if ($atom0->AttachedAtoms->[0]->ElementSymbol eq 'O' and $atom0->AttachedAtoms->[1]->ElementSymbol eq 'N')
	{
	}
	elsif ($atom0->AttachedAtoms->[0]->ElementSymbol eq 'N' and $atom0->AttachedAtoms->[1]->ElementSymbol eq 'O')
	{
	}
	else
	{
		warn "WARNING Invalid polyurethane group, code 2\n";
		return $isIsocyanate;
	}
		
	# =x=
	my $bonds = $atom0->Bonds;
	my $bond1 = $bonds->[0];
	my $bond2 = $bonds->[1];
	if ($bond1->BondType ne 'Double' or $bond2->BondType ne 'Double')
	{
		warn "WARNING Invalid polyurethane group, code 3\n";
		return $isIsocyanate;
	}

	# okay, we know it is an isocyanate since we have atoms C, N and O and we have a pair of double bonds
	# now find the bond we need to change
	$isIsocyanate = TRUE;
	my $Nbond = $bond1;
	$Nbond = $bond2 if ($bond2->Atom1->ElementSymbol eq 'N' or $bond2->Atom2->ElementSymbol eq 'N');
	
	# change it to a single bond
	$Nbond->BondType = 'Single';
		
	$textDoc->Append(
		sprintf "    Converted urethane bond between %s and %s\n",
		$Nbond->Atom1->Name, $Nbond->Atom2->Name ) 
		if (DEBUG);

	return $isIsocyanate;
}


#######################################################################################################

# Return maximum bond energy
# Efficient algorithm that calculates the energy only for the shortest bond of each type
# Requires: $textDoc, $statTable, $nstatsheets

sub maxBondEnergy
{
	my ($doc1, $distance, $row) = @_;
	my $t0 = time;
	my $bonds = $doc1->UnitCell->Bonds;
	$textDoc->Append(sprintf "Analyzing %d bonds in the unit cell\n", $bonds->Count)
		if (DEBUG > 1);
	
	# First make a hash of bond lengths by type
	my %length;
	foreach my $bond (@$bonds)
	{
		my $sequence = bondsequence($bond);
		#printf "%d %s %s\n", $i, $bond->Name, $sequence if ($i%50 == 0);
		push @{ $length{$sequence} }, $bond->Length;
	}
	my @bondTypes = keys %length;
	#print "@bondTypes\n";
	$textDoc->Append(sprintf "Found %d distinct bond types\n", scalar @bondTypes)
		if (DEBUG > 1);

	# Create a temporary document for energy calcs
	my $tmpDoc = Documents->New("bondEnergy.xsd");
	my $a1 = $tmpDoc->CreateAtom("C", Point());
	my $a2 = $tmpDoc->CreateAtom("C", Point(X => 1.5));
	my $bond = $tmpDoc->CreateBond($a1, $a2, "Single");

	# One energy calculation per bond type
	my $maxBondEnergy = 0;
	my $bondLength; 
	my $bondType;
	foreach my $type (keys %length)
	{
		# Sort each list of lengths in reverse order
		my @lengths = reverse sort @{ $length{$type} };
		
		# Set atom types and bond length in energy doc
		my @fftype = split /,/, $type;
		$a1->ForcefieldType = $fftype[0];
		$a2->ForcefieldType = $fftype[1];
		$a2->X = $lengths[0];
		
		# Calculate energy
		my $results = $Forcite->Energy->Run($tmpDoc, [ 
			AssignForcefieldTypes => "No", 
			WriteLevel => "Silent"]);
		my $bondEnergy = $tmpDoc->BondEnergy;
		#printf "%s %f %f\n", $type, $bond->Length, $bondEnergy;
		
		# Update max value and associated properties of interest
		if ($maxBondEnergy < $bondEnergy)
		{
			$maxBondEnergy = $bondEnergy;
			$bondLength = $lengths[0];
			$bondType = $type;
		}
	}
	
	# Delete the energy document
	$tmpDoc->Discard;
	
	# Output to study table
	if ($row == 0)
	{
		my $sheet = $statTable->InsertSheet($nstatsheets,"Max Bond Energy");
		$nstatsheets++;
		$sheet->ColumnHeading(0) = "Reaction Radius (A)";
		$sheet->ColumnHeading(1) = "Percent Conversion";
		$sheet->ColumnHeading(2) = "Max Bond Energy (kcal/mol)";
	}
	my $sheet = $statTable->Sheets("Max Bond Energy");
	$sheet->InsertRow;
	$sheet->Cell($row,0) = $distance;
	$sheet->Cell($row,1) = $conversion;
	$sheet->Cell($row,2) = $maxBondEnergy;

	# Report on progress
	if (DEBUG > 0)
	{
		$textDoc->Append(sprintf "\nmaxBondEnergy, %d seconds, %s: %f kcal/mol\n", 
			time-$t0, $bondType, $maxBondEnergy); 
		$textDoc->Save;
	}

	return ($maxBondEnergy, $bondType, $bondLength);
}

#######################################################################################################
# Return canonical bond sequence - i.e. comma separated, alphabetized, fftype list

sub bondsequence
{
	my $bond = shift;
	return join ",", sort ($bond->Atom1->ForcefieldType, $bond->Atom2->ForcefieldType);
}
	
#######################################################################################################
#
# Prepare the system for tracking xlink statistics (eg, number of xlinker per monomer)
# For a non-restart run, all fragments will be turned into molecule objects using the AssignMolecules
# function and then the molecules will be assigned names indicating whether they are monomers or
# xlinkers.
# Requires globals: 
# $xlinkerName, $monomerName, $xlinkerReactiveAtom, $monomerReactiveAtom

sub Initialize_xlinkStatistics 
{
	my ($doc1) = @_;

	
	# Rename the xlinker and monomer molecules at the beginning
	# Assign molecule objects, unless this is a restart run
	if ($conversion == 0)
	{
		eval { $doc1->AssignMolecules; };
		warn "AssignMolecules failed. This may result in a loss of some molecule-based statistics.\n" if ($@);
	}
	else
	{
		die "No molecule objects are defined!\n" if ($doc1->UnitCell->Molecules->Count == 0);
	}
	
	# Use the reactive atoms to identify corresponding molecules
	foreach my $atom (@{$doc1->UnitCell->Atoms})
	{
		if ($atom->Name eq "$xlinkerReactiveAtom")
		{
			eval{ $atom->Ancestors->Molecule->Name = $xlinkerName; };
		}
		elsif ($atom->Name eq "$monomerReactiveAtom")
		{
			eval{ $atom->Ancestors->Molecule->Name = $monomerName; };
		}
	}
	
	# Append an index to each monomer molecule name
	my $Counter = 0;
	foreach my $mol (@{$doc1->UnitCell->Molecules}) 
	{	
		if ($mol->Name eq "$monomerName") 
		{
			++$Counter;
			$mol->Name .= "_$Counter";
		}
	}
	# Append an index to each xlinker molecule name
	my $Counter = 0;
	foreach my $mol (@{$doc1->UnitCell->Molecules}) 
	{	
		if ($mol->Name eq "$xlinkerName") 
		{
			++$Counter;
			$mol->Name .= "_$Counter";
		}
	}
}
	
#######################################################################################################

# Compute stats on reactive atoms from the xlinker and monomer molecules
# Requires globals: $statTable, $textDoc, $nstatsheets,
# $xlinkerName, $monomerName, $xlinkerReactiveAtom, $monomerReactiveAtom

sub xlinkStatistics 
{
	my ($doc1, $distance, $row) = @_;

	my $t0 = time;

	# Initialize some counters
	my $intraXLinks	= 0;
	my $nXlinker = 0;
	my $nReactedAtoms = 0;
	
	# Distribution of number of reacted R2 atoms in each xlinker
	my $max = 10; # hard coded (assumes there can never be more than this many bonds per xlinker)
	my @reactedXlinkers;
	for (my $i=0; $i<=$max; $i++) { $reactedXlinkers[$i]=0; }
	
	# For each xlink molecule, grab the names of the molecules attached to the reactive
	# atoms. Store these in an array, @xlMonomer
	
	foreach my $mol (@{$doc1->UnitCell->Molecules}) 
	{
		my @xlMonomer = ();	
		next unless ($mol->Name =~ /^$xlinkerName/);

		# Search reactive atoms in the xlinker molecule
		foreach my $atom (@{$mol->Descendants->Atoms}) 
		{
			next unless ($atom->Name =~ /^$xlinkerReactiveAtom/);
			# Array of monomer names attached to this xlinker
			foreach my $attachedAtom (@{$atom->AttachedAtoms}) 
			{
				next unless ($attachedAtom->Name =~ /^$monomerReactiveAtom/);
				eval{ push @xlMonomer, $attachedAtom->Ancestors->Molecule->Name; };
			} 
		}
			
		# update xlinker distbn
		my $numReactedAtoms = scalar(@xlMonomer);
		$reactedXlinkers[$numReactedAtoms]++;

		# count number of 'intra' xlinks - when same xlinker and monomer 
		# are linked multiple times
		for (my $i=0; $i<$numReactedAtoms-1; $i++)
		{
			for (my $j=$i+1; $j<$numReactedAtoms; $j++)
			{
				++$intraXLinks if ($xlMonomer[$i] eq  $xlMonomer[$j]); 
			}
		}
			
		$nReactedAtoms += $numReactedAtoms;
		$nXlinker++;
	} #next mol
	
	# Calculate number of reacted xlinker molecules from non-zero elements of distbn
	my $nReactedMols = 0;
	for (my $i=1; $i<=$max; $i++)
	{
		$nReactedMols += $reactedXlinkers[$i];
	}
	my $xlRatio = $nReactedAtoms / $nXlinker;

	# Initialize xlinker sheet on first time
	$statTable->ActiveSheetIndex = 0;
	if ($row == 0)
	{
		$statTable->Title = "Xlinkers";
		$statTable->ColumnHeading(0) = "Reaction Radius (A)";
		$statTable->ColumnHeading(1) = "xlinkers reacted (n=$nXlinker)";
		$statTable->ColumnHeading(2) = "Percent xlinkers reacted (n=$nXlinker)";
		$statTable->ColumnHeading(3) = "Intramolecular xlinks";
		$statTable->ColumnHeading(4) = "Reacted xlinker atoms per xlinker";
	}

	# Add xlinker stats to study table
	$statTable->Cell($row,0) = $distance;
	$statTable->Cell($row,1) = $nReactedMols;
	$statTable->Cell($row,2) = 100 * $nReactedMols / $nXlinker;
	$statTable->Cell($row,3) = $intraXLinks;
	$statTable->Cell($row,4) = $xlRatio;
	for (my $i=0; $i<=$max; $i++)
	{
		$statTable->ColumnHeading(5+$i) = "Percent xlinkers with $i reactions" if ($row == 0);
		$statTable->Cell($row,5+$i) = 100*$reactedXlinkers[$i]/$nXlinker;
	}

	$textDoc->Append( "\n##########################################################\n");
	$textDoc->Append( "################## S T A T I S T I C S ###################\n");
	$textDoc->Append( "##########################################################\n\n");
		
	$textDoc->Append( "\n#######   Statistics for the xlinker   ########\n\n");

	$textDoc->Append("$nReactedMols of $nXlinker crosslinker molecules have reacted\n");
	$textDoc->Append("     $intraXLinks formed intramolecular cross links\n");
	$textDoc->Append(sprintf "     %.2f reacted xlinker atoms per xlinker\n\n", $xlRatio);


	############################################################################
	# Now do nearly the same thing for the monomers
	
	my $nMonomer = 0;
	my $nReactedAtoms = 0;
	my $max = 10; 
	my @reactedMonomers;
	for (my $i=0; $i<=$max; $i++) { $reactedMonomers[$i]=0; }
	
	# For each monomer molecule, grab the names of the molecules attached to the reactive
	# atoms. Store these in an array, @xlXlinker
	
	foreach my $mol (@{$doc1->UnitCell->Molecules}) 
	{
		my @xlXlinker = ();	
		next unless ($mol->Name =~ /^$monomerName/);

		# Grab the reactive atoms in the monomer molecule
		foreach my $atom (@{$mol->Descendants->Atoms}) 
		{
			next unless ($atom->Name =~ /^$monomerReactiveAtom/);
			# Array of xlinker names attached to this monomer
			foreach my $attachedAtom (@{$atom->AttachedAtoms}) 
			{
				next unless ($attachedAtom->Name =~ /^$xlinkerReactiveAtom/);
				eval{ push @xlXlinker, $attachedAtom->Ancestors->Molecule->Name; };
			} 
		}
			
		# update distbn
		my $numReactedAtoms = scalar(@xlXlinker);
		$reactedMonomers[$numReactedAtoms]++;
		$nReactedAtoms += $numReactedAtoms;
		$nMonomer++;
	} #next mol


	# Calculate number of reacted monomer molecules from non-zero elements of distbn
	my $nReactedMols = 0;
	for (my $i=1; $i<=$max; $i++)
	{
		$nReactedMols += $reactedMonomers[$i];
	}
	my $xlRatio = $nReactedAtoms / $nMonomer;

	# Add new sheet for monomer data on first time through
	if ($row == 0)
	{
		my $sheet = $statTable->InsertSheet($nstatsheets,"Monomers");
		$nstatsheets++;
		$sheet->ColumnHeading(0) = "Reaction Radius (A)";
		$sheet->ColumnHeading(1) = "Monomers reacted (n=$nMonomer)";
		$sheet->ColumnHeading(2) = "Percent reacted (n=$nMonomer)";
		$sheet->ColumnHeading(3) = "Reacted atoms per monomer";
	}
	
	# Update study table with monomer data
	my $sheet = $statTable->Sheets("Monomers");
	$sheet->InsertRow;
	$sheet->Cell($row,0) = $distance;
	$sheet->Cell($row,1) = $nReactedMols;
	$sheet->Cell($row,2) = 100 * $nReactedMols / $nMonomer;
	$sheet->Cell($row,3) = $xlRatio;
	for (my $i=0; $i<=$max; $i++)
	{
		$sheet->ColumnHeading(4+$i) = "Percent monomers with $i reactions" if ($row == 0);
		$sheet->Cell($row,4+$i) = 100 * $reactedMonomers[$i] / $nMonomer;
	}

	$textDoc->Append( "\n#######   Statistics for the monomer   ########\n\n");
	$textDoc->Append("$nReactedMols of $nMonomer monomer molecules have reacted\n");
	$textDoc->Append(sprintf "     %.2f reacted monomer atoms per monomer\n\n", $xlRatio);

	$textDoc->Append(sprintf "\nxlinkStatistics %d seconds\n", time-$t0); 
	$textDoc->Save;
}

#########################################################################################################

# Optimization, short NVT dynamics and temperature cycle

sub optimizeAndPerturb 
{
	$textDoc->Append("\noptimizeAndPerturb\n");
	$textDoc->Save; 
	my $t0 = time; 
	my $mdStepCounter = 0; 
	my ($doc1) = @_;
	
	my $cycles 			= 1;
	
	# use the *Results variables to store the results of the eval statements	 
	my $short_dynamics_results 	= undef;
	my $heatCycleResults 		= undef;
	my $coolCycleResults 		= undef;

	my $while_control 		= 0;
	my $short_dynamics_passed 	= undef;

	$textDoc->Append("  ");
	ForciteGeomOpt($doc1, 200);
	
	$textDoc->Append("  ");
	my $results = ForciteDynamics($doc1, 1000, "NVT");
	$mdStepCounter+=1000;
	$results->Trajectory->Delete;

	return $doc1 if ($UseTempCycle == FALSE);


	# Optional annealing run	

	my $steps = ($tempDuration * PICO_TO_FEMTO / $timeStep);
	
	for (my $cycleCounter = 0; $cycleCounter < $cycles; ++$cycleCounter) 
	{
		# take the temperature up		
		for (my $temperature = $startTemp; $temperature <= $highTemp; $temperature += $tempStep) 
		{
			$textDoc->Append( "  Heating up, running at $temperature K\n  ");
			$textDoc->Save;
			TemperatureCycleStep($doc1, $steps, $temperature);
			$mdStepCounter += 2*$steps;
		}
		
		# take the temperature down
		for (my $temperature = $highTemp; $temperature >= $lowTemp; $temperature -= $tempStep) 
		{			
			$textDoc->Append( "  Cooling down, running at $temperature K\n  ");	
			$textDoc->Save;
			TemperatureCycleStep($doc1, $steps, $temperature);
			$mdStepCounter += 2*$steps;
		}
	}
	
	ForciteGeomOpt($doc1, 500);
	if (DEBUG) {
		$textDoc->Append( sprintf "optimizeAndPerturb %d total steps, %d seconds\n", 
			$mdStepCounter, time-$t0 ); 
	}
	$textDoc->Save; 
}


#######################################################################################################

# Single step in a temperature cycle: NVT and then NPT/NVT dynamics at specified temperature
# Requires globals: $tempDuration, $timeStep, $Forcite, $cycleSteps, $textDoc, $Quality

sub TemperatureCycleStep 
{
	my $doc1 = shift;
	my $steps = shift;
	my $temperature = shift;
	
	my $results = ForciteDynamics($doc1, $steps, "NVT", ( Temperature => $temperature ) );
	$results->Trajectory->Delete;
	
	my $results = ForciteDynamics($doc1, $steps, $ensemble, ( 
		Temperature		=> $temperature,
		InitialVelocities  	=> "Current"
	) );
	$results->Trajectory->Delete;
}

#########################################################################################################

# Analyze bond length distributions

sub analyzeBonds
{
	my $doc1 = shift;
	
	# Calculate the distribution of all the bonds	
	my $bondAnalysis = $Forcite->Analysis->LengthDistribution($doc1, [
		LengthDistributionUseBonds	=> "Yes"
	]);	
	$bondAnalysis->LengthDistributionChartAsStudyTable->Delete;	
	$bondAnalysis->LengthDistributionChart->Name = "AllBonds";
	
	# Create a set of distances for all the x-link bonds
	my @distances;
	foreach my $bond (@{$doc1->UnitCell->Bonds}) 
	{	
		if ($bond->Name =~ /^xlink/) 
		{
			my $distance = $doc1->CreateDistance([$bond->Atom1, $bond->Atom2]);		
			push @distances, $distance;			
		}	
	}
	return unless ( scalar(@distances) > 0 );
	$doc1->CreateSet("xlink distances", \@distances);
	
	# Now analyze the xlink distances only	
	my $bondAnalysis = $Forcite->Analysis->LengthDistribution($doc1, [
		LengthDistributionSetA	=> "xlink distances"
	]);
	$bondAnalysis->LengthDistributionChartAsStudyTable->Delete;
	$bondAnalysis->LengthDistributionChart->Name = "XlinkBonds";
	$doc1->UnitCell->Distances->Delete;
}

#########################################################################################################

# Gradually increase the restraint force and reduce the restraint distance
# Each iteration includes geometry optimization and dynamics to relax the system
# Finish by replacing the restraints with bonds

sub EquilibrateRestraintXlinks 
{
	my $t0 = time;
	if (DEBUG) { $textDoc->Append("\n  EquilibrateRestraintXlinks\n") };
	my ($doc) = @_;

	my $ForceConstantInc = $RestraintForceConstant/$nRestraintBondingStages;

	# loop over changes in restraint paramters	
	for (my $i = 1; $i <= $nRestraintBondingStages; $i++) 
	{
		my $k = $i * $ForceConstantInc;
		$textDoc->Append(sprintf "\n    Stage %d, restraint K=%.1f\n", $i, $k) if (DEBUG);
		# Tighten restraints (increase force constant & decrease equilibrium distance)
		my $restraints = $doc->UnitCell->Restraints;
		foreach my $restraint (@$restraints) 
		{
			my $b0inc = ($restraint->HarmonicMinimum - $RestraintBondingTargetLength)
					/ ($nRestraintBondingStages+1-$i);
			my $b0 = $restraint->HarmonicMinimum - $b0inc;
					
			$restraint->HarmonicForceConstant 	= $k;
			$restraint->HarmonicMinimum 		= $b0;
			
			$textDoc->Append(sprintf "    Setting restraint B=%f\n", $b0) if (DEBUG > 3);
		}
		
		$textDoc->Append("    ");
		ForciteGeomOpt($doc, 1000);
		$textDoc->Append("    ");
		my $steps = ($relax_length * PICO_TO_FEMTO / $timeStep);
		my $results = ForciteDynamics($doc, $steps, $ensemble);
		$results->Trajectory->Delete;
	}	
	$textDoc->Append("\n");

	# Now do the full bonding using the restraint objects to identify the atoms involved
	my $restraints = $doc->UnitCell->Restraints;
	foreach my $restraint (@$restraints) 
	{
		if (RestraintType($restraint) eq "distance") 
		{
			my $dist = $restraint->Ancestors->Distance;
			my $atom1 = $dist->DistanceNodeI;
			my $atom2 = $dist->DistanceNodeJ;		 			
			my $atom1_name = $atom1->Name;
			my $atom2_name = $atom2->Name;
			
        		$textDoc->Append(
        			sprintf "    Creating xlink %d between %s and %s and deleting restraint\n",
        				$xlinkCounter+1, $atom1_name, $atom2_name)
        		if (DEBUG);
        		
        		createNewBond($doc, $atom1, $atom2);
						
			$restraint->Delete;
			$dist->Delete;
		}
	}
		
	if (DEBUG) 
	{ 
		$textDoc->Append(sprintf "\n  EquilibrateRestraintXlinks took %d seconds\n\n", time-$t0); 
		$textDoc->Save; 
	}
}

#########################################################################################################

sub RestraintType
{
    my ($restraint) = @_;
    my $type = "unknown";
    eval {
        my $ancestors = $restraint->Ancestors;
        if ( $ancestors->Distance->Count ) {
            $type = "distance";
        }
        elsif ( $ancestors->Angle->Count ) {
            $type = "angle";
        }
        elsif ( $ancestors->Torsion->Count ) {
            $type = "torsion";
        }
    };
    return $type;
}


###################################################################################################################

sub getEnergies 
{

	my $doc = shift;
	my $row_counter = shift;
	my $trj = $doc->Trajectory;
	my @bond_energies = ();
	my @angle_energies = ();
	my @potential_energies =();
	for (my $frame_counter = 1; $frame_counter <= $trj->NumFrames; ++$frame_counter) 
	{
	
		#Sets the current frame and creates a new document
	
		$trj->CurrentFrame = $frame_counter;
		
		# calculate energies for this snapshot
		#my $frame_doc->CopyFrom($trj);
		Modules->Forcite->Energy->Run($trj);
		my $bond_energy = $trj->BondEnergy;
		my $angle_energy = $trj->AngleEnergy;
		my $potential_energy = $trj->PotentialEnergy;
		
		# push values into the appropriate array for subsequent analysis
		
		push (@bond_energies,$bond_energy);
		push (@angle_energies, $angle_energy);
		push (@potential_energies, $potential_energy);
				
	}
	
	# calcaulte average and std dev.
	
	my ($avg_bond, $sd_bond)   = calculateStatistics(@bond_energies);
	my ($avg_angle, $sd_angle) = calculateStatistics(@angle_energies);
	my ($avg_pe, $sd_pe)       = calculateStatistics(@potential_energies);
	
	$textDoc->Append( sprintf "Bond energy  %11.5g %11.5g\n",   $avg_bond,  $sd_bond);  	
	$textDoc->Append( sprintf "Angle energy %11.5g %11.5g\n",   $avg_angle, $sd_angle);  	
	$textDoc->Append( sprintf "Pot. energy  %11.5g %11.5g\n\n", $avg_pe,    $sd_pe);  
	
	# write these to study table
	my $sheet = $statTable->Sheets("Thermo");
	if ($row_counter == 0)
	{
		$sheet->ColumnHeading(5) = "Potential Energy (kcal/mol)";
		$sheet->ColumnHeading(6) = "Std Dev";
		$sheet->ColumnHeading(7) = "Bond Energy (kcal/mol)";
		$sheet->ColumnHeading(8) = "Std Dev";
		$sheet->ColumnHeading(9) = "Angle Energy (kcal/mol)";
		$sheet->ColumnHeading(10) = "Std Dev";
	}

	$sheet->Cell($row_counter,5) = $avg_pe;
	$sheet->Cell($row_counter,6) = $sd_pe;
	$sheet->Cell($row_counter,7) = $avg_bond;
	$sheet->Cell($row_counter,8) = $sd_bond;
	$sheet->Cell($row_counter,9) = $avg_angle;
	$sheet->Cell($row_counter,10) = $sd_angle;
}

#######################################################################################################################

# Compute temperature and pressure statistics from frames in trajectory
# Requires globals: $statTable, $nstatsheets

sub getTrajTempAndPressure
{
	my $dynamicsResults	= shift;
        my $row_counter		= shift;
        my $distance		= shift;
        
        # Forcite analysis
	my $T_analysis = Modules->Forcite->Analysis->Temperature($dynamicsResults->Trajectory, 
			[ComputeProfile => "Yes", ComputeBlockAverages => "No"]);
	my $hasPressure = TRUE;
	my $P_analysis;
	eval {
		$P_analysis = Modules->Forcite->Analysis->Pressure($dynamicsResults->Trajectory,
				[ComputeProfile => "Yes", ComputeBlockAverages => "No"]);
	};
	if ($@) { $hasPressure = FALSE; }
	my $T_std = $T_analysis->TemperatureChartAsStudyTable;
	my $P_std = $P_analysis->PressureChartAsStudyTable if ($hasPressure);

	# Push data from study tables into perl arrays
	my @T = ();
	my @P = ();
	for (my $row = 0; $row < $T_std->RowCount; ++$row) 
	{
		push (@T, $T_std->Cell($row, "Temperature"));
		push (@P, $P_std->Cell($row, "Pressure")) if ($hasPressure);
	}
	
	$T_std->Delete;
	$T_analysis->TemperatureChart->Delete;
	$P_std->Delete if ($hasPressure);
	$P_analysis->PressureChart->Delete if ($hasPressure);

	# Use calculateStatistics to calculate stats for the properties
	my ($Tavg, $Tsd) = calculateStatistics(@T);
	my ($Pavg, $Psd) = calculateStatistics(@P) if ($hasPressure);
	
	$textDoc->Append("\n                 Average      StdDev\n");
	$textDoc->Append(sprintf "Temperature  %11.5g %11.5g\n", $Tavg, $Tsd);
	$textDoc->Append(sprintf "Pressure     %11.5g %11.5g\n", $Pavg, $Psd) if ($hasPressure);

	# write these to study table
	if ($row_counter == 0)
	{
		my $sheet = $statTable->InsertSheet($nstatsheets,"Thermo");
		$nstatsheets++;
		$statTable->ColumnHeading(0) = "Reaction Radius (A)";
		$statTable->ColumnHeading(1) = "Average Temp (K)";
		$statTable->ColumnHeading(2) = "Std Dev";
		$statTable->ColumnHeading(3) = "Average Pressure (GPa)" if ($hasPressure);
		$statTable->ColumnHeading(4) = "Std Dev" if ($hasPressure);
	}
	my $sheet = $statTable->Sheets("Thermo");
	$sheet->InsertRow;
	$sheet->Cell($row_counter,0) = $distance;
	$sheet->Cell($row_counter,1) = $Tavg;
	$sheet->Cell($row_counter,2) = $Tsd;
	$sheet->Cell($row_counter,3) = $Pavg if ($hasPressure);
	$sheet->Cell($row_counter,4) = $Psd if ($hasPressure);
}

##########################################################################################################

# Calculate the mean and standard deviation

sub calculateStatistics 
{
	my @property = @_;
	
	my $numberValues = @property;
	
	# calculate Mean
	my $mean = 0;
	foreach my $value (@property) {
		$mean += $value;
	}
	$mean /= $numberValues;
	
	# Calculate standard deviation
	my $stdDev = 0;
	foreach my $value (@property) {
		my $diffsq = ($value - $mean)**2;
		$stdDev += $diffsq;	
	}
	$stdDev = sqrt($stdDev/($numberValues - 1));
	
	return $mean, $stdDev;
}

#########################################################################################################

# Recalculate charge groups

sub DivideAndConquer
{
	my ($doc, $Forcite) = @_;
	
	# Single point energy to generate new charge groups
	$Forcite->Energy->Run($doc, [
		AssignForcefieldTypes				=> "Yes",
		ChargeAssignment				=> "Forcefield assigned",
		AssignChargeGroups				=> "Yes",
		ChargeGroupAssignmentMethod             	=> "Divide-and-conquer", 
		"3DPeriodicElectrostaticSummationMethod"	=> "Group based",
		"2DPeriodicElectrostaticSummationMethod"	=> "Group based",
		"NonPeriodicElectrostaticSummationMethod"	=> "Group based",
		WriteLevel					=> "Silent"
	]);
	
	# Check max charge group size
	my $max1 = 0;
	my $max2 = 0;
	my $max3 = 0;
	foreach my $cg (@{$doc->UnitCell->ChargeGroups})
	{
		$max1 = abs($cg->NetCharge) 		if ($max1 < abs($cg->NetCharge));
		$max2 = $cg->ChargeGroupAtoms->Count 	if ($max2 < $cg->ChargeGroupAtoms->Count);
		$max3 = $cg->ChargeGroupSize 		if ($max3 < $cg->ChargeGroupSize);
	}
	$textDoc->Append("  DivideAndConquer charge groups reassigned\n");
	$textDoc->Append(sprintf "    max: %.1g charge, %d atoms, %.1f Angstrom\n", $max1, $max2, $max3);
	
	# Check net charge
	my $qnet = 0;
	foreach (@{$doc->UnitCell->Atoms})
	{
		$qnet += $_->Charge;
	}
	warn "    WARNING: System is not neutral. Net charge = $qnet\n" if (abs($qnet) > 0.2);
	
	return $doc;
}


#########################################################################################################

# Create a set containing crosslinked atoms and bonds

sub XlinkSet
{
	my $doc = shift;

	my @xlinked_atoms;
	foreach my $bond (@{$doc->UnitCell->Bonds}) 
	{
		if ($bond->Name =~ /^xlink/) 
		{
			push @xlinked_atoms, $bond->Atom1;
			push @xlinked_atoms, $bond->Atom2;
			push @xlinked_atoms, $bond;
		}
	}
	return if ( scalar(@xlinked_atoms) == 0 );

	$textDoc->Append( "\nCreating crosslink set\n");
	$doc->CreateSet("Crosslinks", \@xlinked_atoms, [ IsVisible => "No" , Style => "None" ] );
}

#########################################################################################################

# Geometry optimize with current Forcite settings and given number of steps, ignoring fatal errors
# Requires $Forcite, $textDoc, $geomoptcounter

sub ForciteGeomOpt
{
	my $t0 = time;
	my $doc1 = shift;
	my $steps = shift;
	
	my $results;	
	eval 
	{
		$results = $Forcite->GeometryOptimization->Run($doc1, [MaxIterations => $steps]);
	};

	if ($@) 
	{	 
	 	$textDoc->Append( "ForciteGeomOpt: Failed during geometry optimization\n");
	 	$textDoc->Append( $@);
	}

	if (DEBUG) { 
		$textDoc->Append(sprintf "ForciteGeomOpt %d steps, %d seconds\n", $steps, time-$t0); 
		$textDoc->Save; 
	}

	$geomoptcounter += $steps;
	return $results;
}

#########################################################################################################

# Forcite dynamics
# Required globals: $Forcite, $textDoc, $mdcounter
# Usage: $results = ForciteDynamics($doc, $steps, $ensemble, (optionalSetting1 => 100, ...))
# Notes: If $useElectricField is TRUE, electric field settings will be automatically applied

sub ForciteDynamics
{
	# Start the timer
	my $t0 = time;
	
	# Required arguments
	my $doc1 = shift;
	my $steps = shift;
	my $ensemble = shift;
	
	# Formulate the settings as a perl array
	my @settings = (
		NumberOfSteps	=> $steps,
		Ensemble3D	=> $ensemble,
	);
	
	# Add electric field settings if enabled - no longer restricted to NVT ensemble
	if ($useElectricField) {
		push @settings, (
			ElectricFieldStrength => $electricFieldStrength,
			ElectricFieldX => $electricFieldX,
			ElectricFieldY => $electricFieldY,
			ElectricFieldZ => $electricFieldZ,
			CounterElectricField => $counterElectricField
		);
		$textDoc->Append("Applying electric field: strength=$electricFieldStrength V/Å, direction=[$electricFieldX,$electricFieldY,$electricFieldZ]\n") if (DEBUG);
	}
	
	# The remainder are assumed to be custom settings
	push @settings, @_;
			
	# Run the dynamics inside an eval to prevent failed runs from stopping script
	my $results;
	eval 
	{
		$results = $Forcite->Dynamics->Run($doc1, \@settings);
	};
	if ($@) 
	{
		$textDoc->Append( "ERROR: ForciteDynamics failed\n");
		$textDoc->Append( $@);
		die "Failed in ForciteDynamics\n";
	}

	# Report time used
	if (DEBUG) 
	{ 
		$textDoc->Append(sprintf "ForciteDynamics %d steps, %s ensemble, %d seconds\n", 
			$steps, $ensemble, time-$t0);
		$textDoc->Save;
	}

	$mdcounter += $steps;
	return $results;	
}

#########################################################################################################

# Analyze the fragments in the network
# Required globals: $textDoc, $statTable, $nstatsheets

sub AnalyzeFragments
{
	my $doc = shift;
	my $distance = shift;
	my $row = shift;
	
	my $t0 = time;

	# Initialize some properties for the report
	my $networks = 0;
	my @fragmass;

	# Copy to a new temporary document
	my $tmpdoc = Documents->New("fragments.xsd");
	$tmpdoc->CopyFrom($doc);
	
	# Rename all the atoms to Unassigned. At the end they will be numbered by fragment.
	my $atoms = $tmpdoc->AsymmetricUnit->Atoms;
	foreach (@$atoms)
	{
		$_->Name = "Unassigned";
	}

	my $fragmentCounter = 0;
	my $nUnassigned = $atoms->Count;

	# Loop until all atoms have been assigned a fragment number to their name
	while ($nUnassigned > 0)
	{
		$fragmentCounter++;
		
		# Find all of the atoms connected to the first one in the list
		my $atom1 = $atoms->Atoms("Unassigned");
		my $fragmentAtoms;
		eval {
		
			$fragmentAtoms = $atom1->Fragment->Atoms;
		};
		
		
		if ($@) 
		{
			# The Fragment filter failed so it is an infinite network
			$networks++;
			$atom1->Name = "$fragmentCounter";
			
			# the "edge" is the set of atoms on the periphery of the fragment graph
			my @edge = ();
			push @edge, $atom1;
	
			# iterate until there are no more edge atoms
			while (scalar(@edge) > 0)
			{
				my @new_edge = ();
				foreach my $edge_atom (@edge)
				{
					foreach my $attached_atom (@{$edge_atom->AttachedAtoms})
					{
						if ($attached_atom->Name eq "Unassigned")
						{
							$attached_atom->Name = "$fragmentCounter";
							push @new_edge, $attached_atom;
						}
					}
				}
				@edge = @new_edge;
			}
	
			$fragmentAtoms = $doc->Habits; # an empty collection
			foreach (@$atoms)
			{
				$fragmentAtoms->Add($_) if ($_->Name eq "$fragmentCounter");
			}
			#printf "Network fragment %d atoms\n", $fragmentAtoms->Count;
		}
		else # no error means it is a normal fragment and can be easily processed
		{
			#printf "Normal fragment %d atoms\n", $fragmentAtoms->Count;
			foreach (@$fragmentAtoms)
			{
				$_->Name = "$fragmentCounter";
			}
		}
		
		# Fragment mass
		my $mass = 0;
		foreach (@$fragmentAtoms)
		{
			$mass += $_->Mass;
		}
		$fragmass[$fragmentCounter] = $mass;
	
		# perform a new census of the unassigned atoms
		$nUnassigned = 0;
		foreach (@$atoms)
		{
			$nUnassigned++ if ($_->Name eq "Unassigned");
		}
	}
	# sort fragment masses in descending numerical order
	@fragmass = sort {$b <=> $a} @fragmass;

	
	# Get rid of the temporary structure
	$tmpdoc->Discard;
	
	# Put properties in table
	if ($row == 0)
	{
		my $sheet = $statTable->InsertSheet($nstatsheets,"Fragments");
		$nstatsheets++;
		$sheet->ColumnHeading(0) = "Reaction Radius (A)";
		$sheet->ColumnHeading(1) = "Percent Conversion";
		$sheet->ColumnHeading(2) = "Fragments";
		$sheet->ColumnHeading(3) = "Network fragments";
		$sheet->ColumnHeading(4) = "Mass of largest fragment";
		$sheet->ColumnHeading(5) = "Mass of 2nd largest fragment";
		$sheet->ColumnHeading(6) = "Mass of 3rd largest fragment";
	}
	my $sheet = $statTable->Sheets("Fragments");
	$sheet->InsertRow;
	$sheet->Cell($row,0) = $distance;
	$sheet->Cell($row,1) = $conversion;
	$sheet->Cell($row,2) = $fragmentCounter;
	$sheet->Cell($row,3) = $networks;
	$sheet->Cell($row,4) = $fragmass[0] if (@fragmass > 0);
	$sheet->Cell($row,5) = $fragmass[1] if (@fragmass > 1);
	$sheet->Cell($row,6) = $fragmass[2] if (@fragmass > 2);

	# Report on progress
	if (DEBUG > 0)
	{
	  $textDoc->Append(sprintf 
	    "\nFragmentAnalysis, %d seconds, %d fragments, %d networks, %.1f amu largest\n",
	    time-$t0, $fragmentCounter, $networks, $fragmass[0]); 
	  $textDoc->Save;
	}

}

