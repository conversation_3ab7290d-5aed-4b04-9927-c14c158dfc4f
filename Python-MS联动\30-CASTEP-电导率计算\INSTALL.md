# CASTEP电导率计算器安装指南

## 系统要求

- **Python版本**: 3.7 或更高版本
- **操作系统**: Windows, macOS, Linux
- **内存**: 建议 4GB 以上
- **存储空间**: 约 100MB

## 快速安装

### 1. 检查Python环境

```powershell
# 检查Python版本
python --version

# 如果没有Python，请从 https://python.org 下载安装
```

### 2. 安装依赖包

```powershell
# 进入项目目录
cd "Python-MS联动\30-CASTEP-电导率计算"

# 安装必需依赖
pip install -r requirements.txt
```

### 3. 验证安装

```powershell
# 运行测试脚本
python test_calculator.py

# 运行示例程序
python example_usage.py
```

## 详细安装步骤

### 步骤1: Python环境准备

如果您还没有安装Python：

1. 访问 [Python官网](https://www.python.org/downloads/)
2. 下载适合您系统的Python 3.8+版本
3. 安装时勾选"Add Python to PATH"
4. 验证安装：`python --version`

### 步骤2: 依赖包安装

项目需要以下Python包：

```text
numpy>=1.20.0          # 数值计算
scipy>=1.7.0           # 科学计算
pandas>=1.3.0          # 数据处理
matplotlib>=3.4.0      # 图表绘制
openpyxl>=3.0.0        # Excel支持
streamlit>=1.0.0       # Web界面(可选)
```

安装命令：

```powershell
# 方法1: 使用requirements.txt (推荐)
pip install -r requirements.txt

# 方法2: 逐个安装
pip install numpy scipy pandas matplotlib openpyxl

# 方法3: 如果需要Web界面
pip install streamlit altair
```

### 步骤3: 验证安装

运行测试脚本确认所有功能正常：

```powershell
# 基础功能测试
python test_calculator.py

# 完整功能演示
python example_usage.py

# 命令行帮助
python castep_conductivity_calculator.py -h
```

## 使用方法

### 命令行版本 (推荐)

```powershell
# 基本使用
python castep_conductivity_calculator.py -i your_data.epsilon -o results.csv

# 完整功能
python castep_conductivity_calculator.py -i data.csv --fit_points 10 --method polynomial --export_all --save_plots
```

### Web界面版本

```powershell
# 启动Web界面
streamlit run conductivity_gui.py

# 浏览器会自动打开 http://localhost:8501
```

### Python API使用

```python
from src.io.file_reader import CastepFileReader
from src.core.conductivity_calculator import ConductivityCalculator

# 读取数据
reader = CastepFileReader()
energy, epsilon_1, epsilon_2 = reader.read_epsilon_file("data.epsilon")

# 计算电导率
calculator = ConductivityCalculator()
omega, sigma_omega = calculator.calculate_optical_conductivity(energy, epsilon_2)
fit_result = calculator.extrapolate_dc_conductivity(energy, sigma_omega)

print(f"直流电导率: {fit_result['sigma_dc']:.6e} S/m")
```

## 数据格式要求

### 支持的文件格式

- **CASTEP .epsilon文件**: 原生格式
- **CSV文件**: 逗号分隔
- **TXT文件**: 制表符或空格分隔
- **Excel文件**: .xlsx 或 .xls

### 数据列要求

文件应包含至少3列数据：

```
Energy(eV)    Epsilon_1    Epsilon_2
0.010000      12.345678    0.001234
0.020000      12.234567    0.002345
...
```

## 常见问题解决

### 问题1: 导入模块失败

```
ImportError: No module named 'numpy'
```

**解决方案**: 安装缺失的依赖包
```powershell
pip install numpy scipy pandas matplotlib openpyxl
```

### 问题2: 文件读取失败

```
FileNotFoundError: 文件不存在
```

**解决方案**: 
- 检查文件路径是否正确
- 确认文件格式是否支持
- 使用绝对路径

### 问题3: 计算结果异常

```
ValueError: 数据验证失败
```

**解决方案**:
- 检查数据是否包含负值或NaN
- 确认能量数据单调递增
- 验证ε₂数据不全为零

### 问题4: 图表显示问题

```
Font warning: 字体不支持某些字符
```

**解决方案**: 这是正常警告，不影响功能。如需改善：
```powershell
# 安装中文字体支持
pip install matplotlib --upgrade
```

### 问题5: Web界面无法启动

```
ModuleNotFoundError: No module named 'streamlit'
```

**解决方案**:
```powershell
pip install streamlit altair
```

## 性能优化建议

### 大数据集处理

- 数据点数 > 10000 时，考虑数据抽样
- 使用 `--no_plots` 选项跳过图表生成
- 分批处理多个文件

### 内存使用优化

```python
# 对于大文件，使用分块读取
import pandas as pd
data = pd.read_csv("large_file.csv", chunksize=1000)
```

## 技术支持

### 获取帮助

1. **查看文档**: README.md
2. **运行示例**: example_usage.py
3. **检查测试**: test_calculator.py

### 报告问题

如遇到问题，请提供：
- Python版本信息
- 错误信息完整输出
- 输入数据文件示例
- 运行环境描述

### 更新程序

```powershell
# 更新依赖包
pip install --upgrade numpy scipy pandas matplotlib

# 检查程序版本
python castep_conductivity_calculator.py --version
```

---

**安装完成后，建议运行 `python example_usage.py` 来熟悉程序功能！**
