import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from mpl_toolkits.mplot3d import Axes3D
import pandas as pd
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import os
import matplotlib
matplotlib.use("TkAgg")  # 确保使用TkAgg后端

class TrajectoryVisualizerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("水分子轨迹可视化工具")
        self.root.geometry("1200x850")
        self.root.config(padx=10, pady=10)
        
        # 设置更好的默认样式
        style = ttk.Style()
        if 'clam' in style.theme_names():
            style.theme_use('clam')
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧控制面板
        self.control_frame = ttk.LabelFrame(self.main_frame, text="控制面板")
        self.control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 文件选择按钮
        self.file_button = ttk.Button(self.control_frame, text="选择Excel文件", command=self.load_file)
        self.file_button.pack(pady=10, padx=10, fill=tk.X)
        
        # 显示选择的文件
        self.file_label = ttk.Label(self.control_frame, text="未选择文件", wraplength=200)
        self.file_label.pack(pady=5, padx=10, fill=tk.X)
        
        # 列选择区域
        self.column_frame = ttk.LabelFrame(self.control_frame, text="列映射")
        self.column_frame.pack(pady=10, padx=10, fill=tk.X)
        
        # 创建列选择下拉菜单
        self.time_var = tk.StringVar()
        self.x_var = tk.StringVar()
        self.y_var = tk.StringVar()
        self.z_var = tk.StringVar()
        
        ttk.Label(self.column_frame, text="时间列:").grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
        self.time_combo = ttk.Combobox(self.column_frame, textvariable=self.time_var, state="readonly")
        self.time_combo.grid(row=0, column=1, pady=5, padx=5, sticky=tk.W+tk.E)
        
        ttk.Label(self.column_frame, text="X坐标列:").grid(row=1, column=0, sticky=tk.W, pady=5, padx=5)
        self.x_combo = ttk.Combobox(self.column_frame, textvariable=self.x_var, state="readonly")
        self.x_combo.grid(row=1, column=1, pady=5, padx=5, sticky=tk.W+tk.E)
        
        ttk.Label(self.column_frame, text="Y坐标列:").grid(row=2, column=0, sticky=tk.W, pady=5, padx=5)
        self.y_combo = ttk.Combobox(self.column_frame, textvariable=self.y_var, state="readonly")
        self.y_combo.grid(row=2, column=1, pady=5, padx=5, sticky=tk.W+tk.E)
        
        ttk.Label(self.column_frame, text="Z坐标列:").grid(row=3, column=0, sticky=tk.W, pady=5, padx=5)
        self.z_combo = ttk.Combobox(self.column_frame, textvariable=self.z_var, state="readonly")
        self.z_combo.grid(row=3, column=1, pady=5, padx=5, sticky=tk.W+tk.E)
        
        # 可视化配置区域
        self.viz_frame = ttk.LabelFrame(self.control_frame, text="可视化设置")
        self.viz_frame.pack(pady=10, padx=10, fill=tk.X)
        
        # 标题输入
        ttk.Label(self.viz_frame, text="图表标题:").grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
        self.title_var = tk.StringVar(value="default-test")
        self.title_entry = ttk.Entry(self.viz_frame, textvariable=self.title_var, width=25)
        self.title_entry.grid(row=0, column=1, pady=5, padx=5, sticky=tk.W+tk.E)
        
        # 轨迹线设置
        ttk.Label(self.viz_frame, text="轨迹线颜色:").grid(row=1, column=0, sticky=tk.W, pady=5, padx=5)
        self.line_color_var = tk.StringVar(value="red")
        self.line_color_combo = ttk.Combobox(self.viz_frame, textvariable=self.line_color_var, 
                                           values=["red", "blue", "green", "black", "purple", "orange", "cyan", "magenta"], 
                                           state="readonly")
        self.line_color_combo.grid(row=1, column=1, pady=5, padx=5, sticky=tk.W+tk.E)
        
        ttk.Label(self.viz_frame, text="轨迹线宽度:").grid(row=2, column=0, sticky=tk.W, pady=5, padx=5)
        self.line_width_var = tk.DoubleVar(value=3.0)
        self.line_width_scale = ttk.Scale(self.viz_frame, from_=0.5, to=10.0, variable=self.line_width_var, orient=tk.HORIZONTAL)
        self.line_width_scale.grid(row=2, column=1, pady=5, padx=5, sticky=tk.W+tk.E)
        self.line_width_label = ttk.Label(self.viz_frame, text="3.0")
        self.line_width_label.grid(row=2, column=2, pady=5, padx=5)
        self.line_width_scale.config(command=lambda s: self.line_width_label.config(text=f"{float(s):.1f}"))
        
        # 点设置
        ttk.Label(self.viz_frame, text="点大小:").grid(row=3, column=0, sticky=tk.W, pady=5, padx=5)
        self.point_size_var = tk.IntVar(value=50)
        self.point_size_scale = ttk.Scale(self.viz_frame, from_=10, to=150, variable=self.point_size_var, orient=tk.HORIZONTAL)
        self.point_size_scale.grid(row=3, column=1, pady=5, padx=5, sticky=tk.W+tk.E)
        self.point_size_label = ttk.Label(self.viz_frame, text="50")
        self.point_size_label.grid(row=3, column=2, pady=5, padx=5)
        self.point_size_scale.config(command=lambda s: self.point_size_label.config(text=str(int(float(s)))))
        
        # 点颜色设置 - 不使用颜色条映射，改为单一颜色
        ttk.Label(self.viz_frame, text="点颜色:").grid(row=4, column=0, sticky=tk.W, pady=5, padx=5)
        self.point_color_var = tk.StringVar(value="green")
        self.point_color_combo = ttk.Combobox(self.viz_frame, textvariable=self.point_color_var,
                                            values=["red", "green", "blue", "black", "purple", "orange", "cyan", "magenta"],
                                            state="readonly")
        self.point_color_combo.grid(row=4, column=1, pady=5, padx=5, sticky=tk.W+tk.E)
        
        # 显示设置
        self.show_points_var = tk.BooleanVar(value=True)
        self.show_points_check = ttk.Checkbutton(self.viz_frame, text="显示数据点", variable=self.show_points_var)
        self.show_points_check.grid(row=5, column=0, sticky=tk.W, pady=5, padx=5)
        
        self.show_lines_var = tk.BooleanVar(value=True)
        self.show_lines_check = ttk.Checkbutton(self.viz_frame, text="显示轨迹线", variable=self.show_lines_var)
        self.show_lines_check.grid(row=5, column=1, sticky=tk.W, pady=5, padx=5)
        
        # 图形样式设置
        self.style_frame = ttk.LabelFrame(self.control_frame, text="图形样式")
        self.style_frame.pack(pady=10, padx=10, fill=tk.X)
        
        # 显示网格
        self.show_grid_var = tk.BooleanVar(value=True)
        self.show_grid_check = ttk.Checkbutton(self.style_frame, text="显示网格", variable=self.show_grid_var)
        self.show_grid_check.grid(row=0, column=0, sticky=tk.W, pady=5, padx=5)
        
        # 显示透明背景
        self.transparent_bg_var = tk.BooleanVar(value=True)
        self.transparent_bg_check = ttk.Checkbutton(self.style_frame, text="透明背景", variable=self.transparent_bg_var)
        self.transparent_bg_check.grid(row=0, column=1, sticky=tk.W, pady=5, padx=5)
        
        # 轴范围设置
        self.axis_frame = ttk.LabelFrame(self.control_frame, text="坐标轴范围设置")
        self.axis_frame.pack(pady=10, padx=10, fill=tk.X)
        
        self.auto_axis_var = tk.BooleanVar(value=True)
        self.auto_axis_check = ttk.Checkbutton(self.axis_frame, text="自动设置坐标轴范围", 
                                              variable=self.auto_axis_var,
                                              command=self.toggle_axis_entry)
        self.auto_axis_check.grid(row=0, column=0, columnspan=4, sticky=tk.W, pady=5, padx=5)
        
        # X轴范围
        ttk.Label(self.axis_frame, text="X轴最小值:").grid(row=1, column=0, sticky=tk.W, pady=5, padx=5)
        self.x_min_var = tk.StringVar()
        self.x_min_entry = ttk.Entry(self.axis_frame, textvariable=self.x_min_var, width=7, state=tk.DISABLED)
        self.x_min_entry.grid(row=1, column=1, pady=5, padx=5, sticky=tk.W)
        
        ttk.Label(self.axis_frame, text="X轴最大值:").grid(row=1, column=2, sticky=tk.W, pady=5, padx=5)
        self.x_max_var = tk.StringVar()
        self.x_max_entry = ttk.Entry(self.axis_frame, textvariable=self.x_max_var, width=7, state=tk.DISABLED)
        self.x_max_entry.grid(row=1, column=3, pady=5, padx=5, sticky=tk.W)
        
        # Y轴范围
        ttk.Label(self.axis_frame, text="Y轴最小值:").grid(row=2, column=0, sticky=tk.W, pady=5, padx=5)
        self.y_min_var = tk.StringVar()
        self.y_min_entry = ttk.Entry(self.axis_frame, textvariable=self.y_min_var, width=7, state=tk.DISABLED)
        self.y_min_entry.grid(row=2, column=1, pady=5, padx=5, sticky=tk.W)
        
        ttk.Label(self.axis_frame, text="Y轴最大值:").grid(row=2, column=2, sticky=tk.W, pady=5, padx=5)
        self.y_max_var = tk.StringVar()
        self.y_max_entry = ttk.Entry(self.axis_frame, textvariable=self.y_max_var, width=7, state=tk.DISABLED)
        self.y_max_entry.grid(row=2, column=3, pady=5, padx=5, sticky=tk.W)
        
        # Z轴范围
        ttk.Label(self.axis_frame, text="Z轴最小值:").grid(row=3, column=0, sticky=tk.W, pady=5, padx=5)
        self.z_min_var = tk.StringVar()
        self.z_min_entry = ttk.Entry(self.axis_frame, textvariable=self.z_min_var, width=7, state=tk.DISABLED)
        self.z_min_entry.grid(row=3, column=1, pady=5, padx=5, sticky=tk.W)
        
        ttk.Label(self.axis_frame, text="Z轴最大值:").grid(row=3, column=2, sticky=tk.W, pady=5, padx=5)
        self.z_max_var = tk.StringVar()
        self.z_max_entry = ttk.Entry(self.axis_frame, textvariable=self.z_max_var, width=7, state=tk.DISABLED)
        self.z_max_entry.grid(row=3, column=3, pady=5, padx=5, sticky=tk.W)
        
        # 抑制错误对话框选项
        self.suppress_errors_var = tk.BooleanVar(value=True)
        self.suppress_errors_check = ttk.Checkbutton(self.control_frame, text="抑制错误对话框", variable=self.suppress_errors_var)
        self.suppress_errors_check.pack(pady=5, padx=10, fill=tk.X)
        
        # 可视化按钮
        self.button_frame = ttk.Frame(self.control_frame)
        self.button_frame.pack(pady=10, padx=10, fill=tk.X)
        
        self.visualize_button = ttk.Button(self.button_frame, text="可视化", command=self.visualize, state=tk.DISABLED)
        self.visualize_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        self.save_button = ttk.Button(self.button_frame, text="保存图像", command=self.save_figure, state=tk.DISABLED)
        self.save_button.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0))
        
        # 右侧图表区域
        self.figure_frame = ttk.LabelFrame(self.main_frame, text="轨迹可视化")
        self.figure_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建新的图形实例
        self.create_figure()
        
        # 存储数据
        self.df = None
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 帮助按钮
        self.help_button = ttk.Button(self.control_frame, text="帮助", command=self.show_help)
        self.help_button.pack(pady=10, padx=10, fill=tk.X)
    
    def create_figure(self):
        """创建新的Matplotlib图形"""
        # 如果已存在图形，先清除它
        if hasattr(self, 'canvas') and self.canvas:
            for widget in self.figure_frame.winfo_children():
                widget.destroy()
        
        # 创建新的图形
        self.fig = plt.figure(figsize=(8, 6), dpi=100)
        self.ax = self.fig.add_subplot(111, projection='3d')
        
        # 设置默认标签
        self.ax.set_xlabel('X', fontsize=12, labelpad=10)
        self.ax.set_ylabel('Y', fontsize=12, labelpad=10)
        self.ax.set_zlabel('Z', fontsize=12, labelpad=10)
        
        # 创建Tkinter小部件
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.figure_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加工具栏
        self.toolbar_frame = ttk.Frame(self.figure_frame)
        self.toolbar_frame.pack(fill=tk.X)
        self.toolbar = NavigationToolbar2Tk(self.canvas, self.toolbar_frame)
        self.toolbar.update()
    
    def toggle_axis_entry(self):
        """切换坐标轴范围输入框的状态"""
        state = tk.DISABLED if self.auto_axis_var.get() else tk.NORMAL
        for entry in [self.x_min_entry, self.x_max_entry, 
                      self.y_min_entry, self.y_max_entry, 
                      self.z_min_entry, self.z_max_entry]:
            entry.config(state=state)
    
    def load_file(self):
        """加载Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        
        if not file_path:
            return
            
        try:
            self.status_var.set("正在加载文件...")
            self.root.update_idletasks()
            
            # 读取Excel文件
            self.df = pd.read_excel(file_path)
            
            # 更新文件标签
            self.file_label.config(text=os.path.basename(file_path))
            
            # 更新列选择下拉菜单
            columns = list(self.df.columns)
            
            self.time_combo['values'] = columns
            self.x_combo['values'] = columns
            self.y_combo['values'] = columns
            self.z_combo['values'] = columns
            
            # 尝试自动检测合适的列
            self._auto_detect_columns(columns)
            
            # 启用可视化按钮
            self.visualize_button.config(state=tk.NORMAL)
            
            # 显示前几行数据
            self.status_var.set(f"已加载文件。数据行数: {len(self.df)}")
            
        except Exception as e:
            if not self.suppress_errors_var.get():
                messagebox.showerror("错误", f"加载文件时出错: {str(e)}")
            self.status_var.set(f"加载文件失败: {str(e)}")
    
    def _auto_detect_columns(self, columns):
        """尝试自动检测合适的列"""
        # 时间列
        time_candidates = [col for col in columns if 'time' in col.lower() or 'frame' in col.lower()]
        if time_candidates:
            self.time_var.set(time_candidates[0])
        elif columns:
            self.time_var.set(columns[0])
            
        # X坐标列
        x_candidates = [col for col in columns if 'x' in col.lower() or 'x_' in col.lower()]
        if x_candidates:
            self.x_var.set(x_candidates[0])
        elif len(columns) > 1:
            self.x_var.set(columns[1])
            
        # Y坐标列
        y_candidates = [col for col in columns if 'y' in col.lower() or 'y_' in col.lower()]
        if y_candidates:
            self.y_var.set(y_candidates[0])
        elif len(columns) > 2:
            self.y_var.set(columns[2])
            
        # Z坐标列
        z_candidates = [col for col in columns if 'z' in col.lower() or 'z_' in col.lower()]
        if z_candidates:
            self.z_var.set(z_candidates[0])
        elif len(columns) > 3:
            self.z_var.set(columns[3])
    
    def visualize(self):
        """可视化轨迹数据"""
        if self.df is None:
            if not self.suppress_errors_var.get():
                messagebox.showwarning("警告", "请先加载数据")
            return
            
        # 获取所选列
        time_col = self.time_var.get()
        x_col = self.x_var.get()
        y_col = self.y_var.get()
        z_col = self.z_var.get()
        
        if not all([time_col, x_col, y_col, z_col]):
            if not self.suppress_errors_var.get():
                messagebox.showwarning("警告", "请选择所有必要的列")
            return
            
        try:
            self.status_var.set("正在生成可视化...")
            self.root.update_idletasks()
            
            # 完全重新创建图形和轴，避免任何潜在的问题
            self.create_figure()
            
            # 获取坐标范围
            if not self.auto_axis_var.get():
                try:
                    x_min = float(self.x_min_var.get()) if self.x_min_var.get() else None
                    x_max = float(self.x_max_var.get()) if self.x_max_var.get() else None
                    y_min = float(self.y_min_var.get()) if self.y_min_var.get() else None
                    y_max = float(self.y_max_var.get()) if self.y_max_var.get() else None
                    z_min = float(self.z_min_var.get()) if self.z_min_var.get() else None
                    z_max = float(self.z_max_var.get()) if self.z_max_var.get() else None
                    
                    if x_min is not None and x_max is not None:
                        self.ax.set_xlim(x_min, x_max)
                    if y_min is not None and y_max is not None:
                        self.ax.set_ylim(y_min, y_max)
                    if z_min is not None and z_max is not None:
                        self.ax.set_zlim(z_min, z_max)
                except ValueError:
                    if not self.suppress_errors_var.get():
                        messagebox.showwarning("警告", "坐标轴范围必须是数字")
            
            # 设置标题
            self.ax.set_title(self.title_var.get(), fontsize=14, pad=20)
            
            # 设置网格
            if self.show_grid_var.get():
                self.ax.grid(True, linestyle='--', alpha=0.7)
            else:
                self.ax.grid(False)
            
            # 设置背景透明度
            if self.transparent_bg_var.get():
                self.ax.xaxis.pane.fill = False
                self.ax.yaxis.pane.fill = False
                self.ax.zaxis.pane.fill = False
            
            # 绘制轨迹线
            if self.show_lines_var.get():
                self.ax.plot(
                    self.df[x_col], 
                    self.df[y_col], 
                    self.df[z_col], 
                    '-', 
                    color=self.line_color_var.get(),
                    linewidth=self.line_width_var.get(),
                    alpha=1.0,
                    zorder=10  # 确保线在散点之上
                )
            
            # 绘制散点图
            if self.show_points_var.get():
                # 使用单一颜色而不是颜色映射
                self.ax.scatter(
                    self.df[x_col], 
                    self.df[y_col], 
                    self.df[z_col],
                    color=self.point_color_var.get(),
                    s=self.point_size_var.get(),
                    alpha=0.8,
                    edgecolors='black',
                    linewidths=0.5,
                    zorder=5
                )
            
            # 设置视角
            self.ax.view_init(elev=30, azim=-45)
            
            # 更新画布
            self.fig.tight_layout()
            self.canvas.draw()
            
            # 启用保存按钮
            self.save_button.config(state=tk.NORMAL)
            
            # 如果是自动坐标范围，保存当前的坐标范围
            if self.auto_axis_var.get():
                x_lim = self.ax.get_xlim()
                y_lim = self.ax.get_ylim()
                z_lim = self.ax.get_zlim()
                
                self.x_min_var.set(f"{x_lim[0]:.2f}")
                self.x_max_var.set(f"{x_lim[1]:.2f}")
                self.y_min_var.set(f"{y_lim[0]:.2f}")
                self.y_max_var.set(f"{y_lim[1]:.2f}")
                self.z_min_var.set(f"{z_lim[0]:.2f}")
                self.z_max_var.set(f"{z_lim[1]:.2f}")
            
            self.status_var.set("可视化完成")
            
        except Exception as e:
            error_msg = f"可视化时出错: {str(e)}"
            self.status_var.set(error_msg)
            if not self.suppress_errors_var.get():
                messagebox.showerror("错误", error_msg)
            # 如果出错，打印详细错误信息以便调试
            import traceback
            print(traceback.format_exc())
    
    def save_figure(self):
        """保存图形"""
        file_path = filedialog.asksaveasfilename(
            title="保存图像",
            defaultextension=".png",
            filetypes=[
                ("PNG图像", "*.png"),
                ("JPEG图像", "*.jpg"),
                ("PDF文档", "*.pdf"),
                ("SVG矢量图", "*.svg"),
                ("高分辨率TIFF", "*.tiff"),
                ("所有文件", "*.*")
            ]
        )
        
        if not file_path:
            return
            
        try:
            self.status_var.set("正在保存图像...")
            self.root.update_idletasks()
            
            # 暂时移除工具栏，以免它出现在保存的图像中
            self.toolbar.pack_forget()
            
            # 调整图形大小以获得更好的输出质量
            orig_fig_size = self.fig.get_size_inches()
            self.fig.set_size_inches(10, 8)
            self.fig.set_dpi(300)  # 提高DPI以获得更好的输出质量
            
            # 保存图像
            self.fig.savefig(file_path, dpi=300, bbox_inches='tight')
            
            # 恢复原始图形大小
            self.fig.set_size_inches(*orig_fig_size)
            
            # 恢复工具栏
            self.toolbar.pack(fill=tk.X)
            
            self.status_var.set(f"图像已保存至 {os.path.basename(file_path)}")
            if not self.suppress_errors_var.get():
                messagebox.showinfo("成功", f"图像已保存至:\n{file_path}")
            
        except Exception as e:
            error_msg = f"保存图像时出错: {str(e)}"
            self.status_var.set(error_msg)
            if not self.suppress_errors_var.get():
                messagebox.showerror("错误", error_msg)
            
            # 确保工具栏被恢复
            self.toolbar.pack(fill=tk.X)
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
水分子轨迹可视化工具 - 使用指南

1. 数据加载:
   - 点击"选择Excel文件"按钮加载.xlsx或.xls格式的数据文件
   - 程序会尝试自动识别时间和坐标列

2. 列映射:
   - 选择对应的时间列和X、Y、Z坐标列
   - 列名中包含相关关键词的列会被自动识别

3. 可视化设置:
   - 图表标题: 自定义图表的标题文本
   - 轨迹线颜色和宽度: 设置连接数据点的线条样式
   - 点大小和颜色: 调整散点的显示效果
   - 显示选项: 可单独控制是否显示数据点和轨迹线

4. 图形样式:
   - 网格显示: 控制是否显示坐标网格
   - 透明背景: 设置坐标平面是否透明

5. 坐标轴范围:
   - 默认自动设置坐标范围
   - 取消勾选可手动设置X、Y、Z轴的最小值和最大值

6. 错误处理:
   - 勾选"抑制错误对话框"可以避免弹出错误信息窗口
   - 错误信息会显示在状态栏

7. 可视化与保存:
   - 点击"可视化"按钮生成3D图形
   - 使用工具栏可以旋转、缩放和平移视图
   - 点击"保存图像"导出高质量图像，支持多种格式

注意: 此版本针对不同版本的Matplotlib进行了兼容性处理，
应该能够在大多数环境中正常运行。
        """
        
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("600x500")
        help_window.resizable(True, True)
        
        help_frame = ttk.Frame(help_window, padding=10)
        help_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本区域
        text_widget = tk.Text(help_frame, wrap=tk.WORD, font=("SimSun", 11))
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(help_frame, command=text_widget.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        text_widget.config(yscrollcommand=scrollbar.set)
        
        # 插入帮助文本
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)  # 使文本只读
        
        # 关闭按钮
        close_button = ttk.Button(help_window, text="关闭", command=help_window.destroy)
        close_button.pack(pady=10)

def main():
    root = tk.Tk()
    app = TrajectoryVisualizerApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()