# 线性模型机器学习应用

这是一个基于Python的机器学习应用程序，提供了简单直观的GUI界面，使用户能够轻松应用线性模型算法进行数据分析和预测。

## 主要功能

- 支持多种线性模型：线性回归、岭回归、Lasso回归和弹性网络
- 简洁易用的GUI界面，无需编程知识即可使用
- 导入CSV和Excel数据文件
- 交互式参数设置
- 数据可视化：预测vs实际值散点图、特征重要性图、残差图
- 导出预测结果为CSV或Excel格式

## 依赖环境

- Python 3.6+
- numpy
- pandas
- scikit-learn
- matplotlib
- tkinter (通常随Python一起安装)

## 安装方法

1. 确保已安装Python 3.6或更高版本
2. 安装所需依赖包：

```bash
pip install -r requirements.txt
```

## 使用方法

1. 运行主程序：

```bash
python main.py
```

2. 在应用界面中：
   - 点击"加载数据文件"按钮，选择CSV或Excel数据文件
   - 选择目标变量（需要预测的变量）
   - 从特征列表中选择一个或多个特征
   - 设置测试集比例（默认为20%）
   - 选择是否标准化数据（对于大多数情况建议启用）
   - 选择算法类型（线性回归、岭回归、Lasso回归或弹性网络）
   - 根据选择的算法调整参数
   - 点击底部橙色的"运行模型"按钮
   - 查看右侧的性能指标和可视化结果
   - 可选择"导出结果"保存预测结果

## 支持的线性模型

1. **线性回归**：基本的线性回归模型，无额外参数。
2. **岭回归**：通过L2正则化减少过拟合的线性回归模型。
   - 参数：alpha（正则化强度，值越大，模型越简单）
3. **Lasso回归**：通过L1正则化进行特征选择的线性回归模型。
   - 参数：alpha（正则化强度）和最大迭代次数
4. **弹性网络**：结合L1和L2正则化的线性回归模型。
   - 参数：alpha（正则化强度）、l1_ratio（L1正则化比例）和最大迭代次数

## 示例数据集

应用程序包含两个示例数据集：

1. **house_price_data.csv**：房价预测数据集，包含面积、房间数、房龄和到市中心距离等特征。
2. **sales_data.csv**：销售预测数据集，包含广告支出、促销折扣、竞争对手价格和季节因素等特征。

您也可以使用generate_sample_data.py脚本生成更多示例数据：

```bash
python generate_sample_data.py
```

## 选择合适的模型

- **线性回归**：数据简单，特征少，无需正则化
- **岭回归**：特征间有相关性，需要减少过拟合
- **Lasso回归**：需要特征选择，希望部分特征系数为零
- **弹性网络**：既需要特征选择，又需要处理相关特征

## 解读结果

- **MSE (均方误差)**：误差的平方的平均值，越小越好
- **R² (决定系数)**：模型解释数据变异的比例，接近1表示拟合良好
- **预测vs实际值散点图**：点越接近对角线，预测越准确
- **特征重要性图**：显示各特征的系数，正值表示正相关，负值表示负相关
- **残差图**：理想情况下，残差应随机分布在零线附近 