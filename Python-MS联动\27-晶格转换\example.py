#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CIF晶格转换工具使用示例
"""

import os
import sys
from cif_transformer import CifTransformer

def main():
    """
    示例程序：演示如何使用CifTransformer类进行晶格转换
    """
    # 检查示例文件是否存在
    example_cif = "example.cif"
    if not os.path.exists(example_cif):
        print(f"示例CIF文件 '{example_cif}' 不存在。")
        print("请先准备一个CIF文件，或使用以下命令行方式运行工具：")
        print("python cif_transformer.py --input <你的CIF文件路径> --output <输出目录>")
        return
    
    # 创建输出目录
    output_dir = "transformed_cifs"
    os.makedirs(output_dir, exist_ok=True)
    
    print("=== CIF晶格转换工具示例 ===")
    print(f"输入文件: {example_cif}")
    print(f"输出目录: {output_dir}")
    print("转换晶面: 100, 010, 101")
    print("===========================")
    
    # 创建转换器实例，只处理选定的晶面
    transformer = CifTransformer(
        input_path=example_cif,
        output_dir=output_dir,
        planes=["100", "010", "101"],  # 只处理这三个晶面
        prefix="trans_",              # 输出文件名前缀
        suffix="_ortho"               # 输出文件名后缀
    )
    
    # 执行转换
    transformer.process_all_files()
    
    print("\n转换完成！输出文件命名格式为: trans_<原文件名>_<晶面>_ortho.cif")
    print(f"例如: trans_example_100_ortho.cif")
    
    # 展示如何使用全部晶面进行转换
    print("\n如果要转换为所有支持的晶面，可以这样做：")
    print("```python")
    print("transformer = CifTransformer(")
    print("    input_path='example.cif',")
    print("    output_dir='output_all_planes'")
    print(")")
    print("transformer.process_all_files()")
    print("```")
    
    # 展示使用命令行运行的例子
    print("\n也可以通过命令行直接运行：")
    print("```bash")
    print("# 处理单个文件，转换所有晶面")
    print("python cif_transformer.py --input example.cif --output transformed_cifs/")
    print("")
    print("# 处理目录内所有CIF文件，只转换特定晶面")
    print("python cif_transformer.py --input cif_files/ --output transformed_cifs/ --planes 100,010,101")
    print("```")


if __name__ == "__main__":
    main() 