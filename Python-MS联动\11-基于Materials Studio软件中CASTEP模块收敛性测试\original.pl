#################################################################################################################
# perl                                                                                                          #
#                                                                                                               #
# Author: IMATSOFT.DM                                                                                           #
# Version: 2.3                                                                                                  #
# Tested on: Materials Studio 2020                                                                              #
#                                                                                                               #
# Required modules: Materials Visualizer, CASTEP                                                                #
# This script performs convergence testing for energy cutoff and k-point grid.                                  #
# It first tests a range of energy cutoffs, then uses the optimal cutoff to test k-point grid densities.        #
#                                                                                                               #
# Date: 2023-03-27                                                                                              #
# Modified for User Menu Execution: 2023-11-15                                                                  #
# Fixed undefined energy cutoff issue: 2023-11-20                                                               #
# Improved convergence testing logic: 2023-11-25                                                                #
#                                                                                                               #
#################################################################################################################

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);

# 定义参数哈希
my %Args;

# 获取从用户菜单传递的参数
GetOptions(\%Args, 
    "Structure=s", 
    "Min_Energy_Cutoff=i",
    "Max_Energy_Cutoff=i",
    "Energy_Cutoff_Step=i",
    "Min_KPoint_Grid=s",
    "Max_KPoint_Grid=s",
    "Energy_Difference_Tolerance=f",
     "XCFunctional=s" ,
     "Pseudopotentials=s"
);

# 定义能量单位转换常数（kcal/mol 转 eV）
my $KCAL_TO_EV = 0.0433641000238951;

#################################################################################################################
#                                         BEGIN USER INPUT (可被参数覆盖)                                       #
#################################################################################################################
# 获取输入结构
my $structure;
if ($Args{Structure}) {
    $structure = $Documents{$Args{Structure}};
    unless ($structure) {
        die "Error: Specified structure '$Args{Structure}' not found in project.\n";
    }
} else {
    $structure = Documents->ActiveDocument;
    unless ($structure) {
        die "Error: No active document found. Please open or specify a structure.\n";
    }
}

# 生成能量截断值数组
my $minCutoff = $Args{Min_Energy_Cutoff} || 300;
my $maxCutoff = $Args{Max_Energy_Cutoff} || 500;
my $step = $Args{Energy_Cutoff_Step} || 10;
my @energyCutoffs;
for (my $i = $minCutoff; $i <= $maxCutoff; $i += $step) {
    push @energyCutoffs, $i;
}

# 检查能量截断范围是否合理
if ($minCutoff >= $maxCutoff) {
    die "Error: Min energy cutoff ($minCutoff) must be less than max energy cutoff ($maxCutoff).\n";
}

my $energyCutoffWorkbook = Documents->New("Energy_Cutoff_Results.std");

# 解析K点网格密度范围
my $minGridStr = $Args{Min_KPoint_Grid} || "4 4 4";
my $maxGridStr = $Args{Max_KPoint_Grid} || "6 6 6";
my $minGridDensity = [split /\s+/, $minGridStr];
my $maxGridDensity = [split /\s+/, $maxGridStr];

# 检查K点网格范围是否合理
for my $i (0..2) {
    if ($minGridDensity->[$i] > $maxGridDensity->[$i]) {
        die "Error: Min K-point grid value must be less than or equal to max value for each dimension.\n";
    }
}

# 直接使用最小K点网格作为初始网格，不再查找初始K点网格参数
my $gridDensity = [@$minGridDensity];

#################################################################################################################
#                                         END USER INPUT                                                        #
#################################################################################################################

# 设置结果表格
my $energyCutoffSheet = $energyCutoffWorkbook->ActiveSheet;
$energyCutoffSheet->ColumnHeading(0) = "No.";
$energyCutoffSheet->ColumnHeading(1) = "Energy Cutoff (eV)";
$energyCutoffSheet->ColumnHeading(2) = "Grid Density A";
$energyCutoffSheet->ColumnHeading(3) = "Grid Density B";
$energyCutoffSheet->ColumnHeading(4) = "Grid Density C";
$energyCutoffSheet->ColumnHeading(5) = "Total Energy (kcal/mol)";
$energyCutoffSheet->ColumnHeading(6) = "Total Energy (eV)";

my $rowNumber = 1;
my $energyMin;  # 将在第一次成功计算后初始化
my @energyResults;
my $tolerance = $Args{Energy_Difference_Tolerance} || 0.2;

printf("Starting energy cutoff convergence testing from %d to %d eV in steps of %d eV\n",
       $minCutoff, $maxCutoff, $step);
printf("Using initial grid density: [%d, %d, %d]\n", @$gridDensity);

# 能量截断收敛测试 - 计算所有指定的截断能
my $firstSuccess = 1;
foreach my $energyCutoff (@energyCutoffs) {
    my $results;
    eval {
        $results = run_castep_calculation($structure, $gridDensity, $energyCutoff);
    };
    if ($@) {
        printf("Warning: Calculation failed for energy cutoff %d eV: %s\n", $energyCutoff, $@);
        next;
    }

    my $energyData = {
        Cutoff => $energyCutoff,
        TotalEnergy => $results->TotalEnergy,
        EnergyeV => $results->TotalEnergy * $KCAL_TO_EV,
        GridDensity => [@$gridDensity]
    };
    push @energyResults, $energyData;

    $energyCutoffSheet->Cell($rowNumber, 0) = $rowNumber;
    $energyCutoffSheet->Cell($rowNumber, 1) = $energyCutoff;
    $energyCutoffSheet->Cell($rowNumber, 2) = $gridDensity->[0];
    $energyCutoffSheet->Cell($rowNumber, 3) = $gridDensity->[1];
    $energyCutoffSheet->Cell($rowNumber, 4) = $gridDensity->[2];
    $energyCutoffSheet->Cell($rowNumber, 5) = sprintf("%.6f", $results->TotalEnergy);
    $energyCutoffSheet->Cell($rowNumber, 6) = sprintf("%.6f", $energyData->{EnergyeV});

    # 初始化energyMin或更新最小能量
    if ($firstSuccess) {
        $energyMin = {
            TotalEnergy => $results->TotalEnergy,
            EnergyCutoff => $energyCutoff,
            GridDensity => [@$gridDensity]
        };
        $firstSuccess = 0;
    } elsif ($results->TotalEnergy < $energyMin->{TotalEnergy}) {
        $energyMin->{TotalEnergy} = $results->TotalEnergy;
        $energyMin->{EnergyCutoff} = $energyCutoff;
        $energyMin->{GridDensity} = [@$gridDensity];
    }

    $rowNumber++;
    
    # 输出进度信息
    my $progress = ($energyCutoff - $minCutoff) / ($maxCutoff - $minCutoff) * 100;
    printf("Energy cutoff test progress: %.1f%% complete\n", $progress);
}

$energyCutoffWorkbook->Save;

# 检查是否至少有一次成功的计算
if ($firstSuccess) {
    die "Error: All energy cutoff calculations failed. Please check your input parameters.\n";
}

# 分析能量截断结果
my $energyCutoffForKPoints;
my $foundConvergence = 0;

# 寻找满足能量差异条件的连续能量值
for (my $i = 1; $i < @energyResults; $i++) {
    my $diff = abs($energyResults[$i]{EnergyeV} - $energyResults[$i-1]{EnergyeV});
    if ($diff < $tolerance) {
        $energyCutoffForKPoints = $energyResults[$i]{Cutoff};
        $foundConvergence = 1;
        last;
    }
}

# 输出能量截断测试结果
printf("\nEnergy Cutoff Test Results:\n");
printf("Minimum energy found: %.6f (eV) at cutoff: %d eV\n", 
       $energyMin->{TotalEnergy} * $KCAL_TO_EV, $energyMin->{EnergyCutoff});

if ($foundConvergence) {
    printf("Found consecutive energies with difference < %.2f eV at cutoff: %d eV\n", 
           $tolerance, $energyCutoffForKPoints);
} else {
    printf("No consecutive energies meet the tolerance of %.2f eV.\n", $tolerance);
    printf("Using the minimum energy cutoff (%d eV) for K-point testing.\n", $energyMin->{EnergyCutoff});
    $energyCutoffForKPoints = $energyMin->{EnergyCutoff};
}

#################################################################################################################
#                                         K点收敛测试部分                                                       #
#################################################################################################################

my $resultsWorkbook = Documents->New("Energy_Kpoints_Results.std");

my $kPointsSheet = $resultsWorkbook->ActiveSheet;
$kPointsSheet->ColumnHeading(0) = "No.";
$kPointsSheet->ColumnHeading(1) = "Energy Cutoff (eV)";
$kPointsSheet->ColumnHeading(2) = "Grid Density A";
$kPointsSheet->ColumnHeading(3) = "Grid Density B";
$kPointsSheet->ColumnHeading(4) = "Grid Density C";
$kPointsSheet->ColumnHeading(5) = "Total Energy (kcal/mol)";
$kPointsSheet->ColumnHeading(6) = "Total Energy (eV)";
$kPointsSheet->ColumnHeading(7) = "Energy Diff (eV)";

$rowNumber = 1;
$firstSuccess = 1;  # 重置以用于K点测试
my @kPointResults;  # 存储K点结果以进行比较
my $gridDensityCopy = [@$minGridDensity];

printf("\nStarting K-point convergence testing with energy cutoff: %d eV\n", $energyCutoffForKPoints);
printf("Testing K-point grid range: [%d-%d, %d-%d, %d-%d]\n",
       $minGridDensity->[0], $maxGridDensity->[0],
       $minGridDensity->[1], $maxGridDensity->[1],
       $minGridDensity->[2], $maxGridDensity->[2]);

# 计算总测试数量用于显示进度
my $totalKTests = ($maxGridDensity->[0] - $minGridDensity->[0] + 1) *
                  ($maxGridDensity->[1] - $minGridDensity->[1] + 1) *
                  ($maxGridDensity->[2] - $minGridDensity->[2] + 1);
my $currentKTest = 0;

for (my $a = $minGridDensity->[0]; $a <= $maxGridDensity->[0]; ++$a) {
    $gridDensityCopy->[0] = $a;
    for (my $b = $minGridDensity->[1]; $b <= $maxGridDensity->[1]; ++$b) {
        $gridDensityCopy->[1] = $b;
        for (my $c = $minGridDensity->[2]; $c <= $maxGridDensity->[2]; ++$c) {
            $gridDensityCopy->[2] = $c;
            $currentKTest++;

            my $results;
            eval {
                $results = run_castep_calculation($structure, $gridDensityCopy, $energyCutoffForKPoints);
            };
            if ($@) {
                printf("Warning: Calculation failed for K-point grid [%d, %d, %d]: %s\n",
                      $a, $b, $c, $@);
                next;
            }

            my $energyeV = $results->TotalEnergy * $KCAL_TO_EV;
            
            # 计算与上一个结果的能量差异（如果有的话）
            my $energyDiff = "";
            if (@kPointResults > 0) {
                $energyDiff = abs($energyeV - $kPointResults[-1]->{EnergyeV});
            }
            
            my $kPointData = {
                GridDensity => [@$gridDensityCopy],
                TotalEnergy => $results->TotalEnergy,
                EnergyeV => $energyeV,
                EnergyDiff => $energyDiff
            };
            push @kPointResults, $kPointData;
            
            $kPointsSheet->Cell($rowNumber, 0) = $rowNumber;
            $kPointsSheet->Cell($rowNumber, 1) = $energyCutoffForKPoints;
            $kPointsSheet->Cell($rowNumber, 2) = $gridDensityCopy->[0];
            $kPointsSheet->Cell($rowNumber, 3) = $gridDensityCopy->[1];
            $kPointsSheet->Cell($rowNumber, 4) = $gridDensityCopy->[2];
            $kPointsSheet->Cell($rowNumber, 5) = sprintf("%.6f", $results->TotalEnergy);
            $kPointsSheet->Cell($rowNumber, 6) = sprintf("%.6f", $energyeV);
            if ($energyDiff ne "") {
                $kPointsSheet->Cell($rowNumber, 7) = sprintf("%.6f", $energyDiff);
            }

            # 初始化或更新最小能量值
            if ($firstSuccess) {
                $energyMin = {
                    TotalEnergy => $results->TotalEnergy,
                    GridDensity => [@$gridDensityCopy]
                };
                $firstSuccess = 0;
            } elsif ($results->TotalEnergy < $energyMin->{TotalEnergy}) {
                $energyMin->{TotalEnergy} = $results->TotalEnergy;
                $energyMin->{GridDensity} = [@$gridDensityCopy];
            }

            $rowNumber++;
            
            # 显示进度信息
            my $progress = $currentKTest / $totalKTests * 100;
            printf("K-point test progress: %.1f%% complete (Grid: [%d, %d, %d])\n", 
                   $progress, $a, $b, $c);
        }
    }
}

$resultsWorkbook->Save;

# 检查是否至少有一次成功的K点计算
if ($firstSuccess) {
    die "Error: All K-point calculations failed. Please check your input parameters.\n";
}

# 分析K点收敛结果
my $kPointConvergence = 0;
my $optimalKPoints;

# 寻找满足能量差异条件的连续K点网格
for (my $i = 1; $i < @kPointResults; $i++) {
    if (defined $kPointResults[$i]->{EnergyDiff} && $kPointResults[$i]->{EnergyDiff} < $tolerance) {
        $kPointConvergence = 1;
        $optimalKPoints = $kPointResults[$i]->{GridDensity};
        last;
    }
}

# 输出K点测试最终结果
printf("\nK-point Test Results:\n");
printf("Minimum energy found: %.6f (eV)\n", $energyMin->{TotalEnergy} * $KCAL_TO_EV);
printf("Optimal energy cutoff: %d eV\n", $energyCutoffForKPoints);
printf("Optimal grid density: [%d, %d, %d]\n", @{$energyMin->{GridDensity}});

if ($kPointConvergence) {
    printf("Found K-point convergence with energy difference < %.2f eV at grid: [%d, %d, %d]\n",
           $tolerance, @$optimalKPoints);
} else {
    printf("No K-point grid meets the convergence tolerance of %.2f eV.\n", $tolerance);
    printf("Consider testing larger K-point grids or adjusting the tolerance.\n");
}

#################################################################################################################
#                                         CASTEP计算子程序                                                      #
#################################################################################################################

sub run_castep_calculation {
    my ($structure, $gridDensity, $energyCutoff) = @_;

    # 验证输入参数
    unless (defined $energyCutoff && $energyCutoff > 0) {
        die "Invalid energy cutoff value: $energyCutoff";
    }
    unless (defined $gridDensity && ref($gridDensity) eq 'ARRAY' && scalar @$gridDensity == 3) {
        die "Grid density must be an array with 3 values";
    }
    foreach my $val (@$gridDensity) {
        unless (defined $val && $val > 0) {
            die "Grid density values must be positive integers";
        }
    }

    # 验证并设置正确的赝势类型
    my $pseudopotentialType = $Args{Pseudopotentials} || 'Ultrasoft';
    
    # 确保赝势类型是有效的选择之一
    my @validPseudopotentials = ('High throughput', 'Norm-conserving', 'OTFG norm-conserving', 
                                'OTFG ultrasoft', 'On the fly', 'Ultrasoft');
    
    my $validPseudo = 0;
    foreach my $valid (@validPseudopotentials) {
        if ($pseudopotentialType eq $valid) {
            $validPseudo = 1;
            last;
        }
    }
    
    # 如果不是有效的赝势类型，则使用默认的'Ultrasoft'
    if (!$validPseudo) {
        printf("Warning: Invalid pseudopotential type '%s'. Using default 'Ultrasoft' instead.\n", 
               $pseudopotentialType);
        $pseudopotentialType = 'Ultrasoft';
    }

    my $settings = Settings(
        UseCustomEnergyCutoff => 'Yes',
        EnergyCutoff => $energyCutoff,
        FFTQuality => "Fine",
        FFTFineGrid => 1.2,
        SCFConvergence => 5e-007,
        MaximumSCFCycles => 1000,
        ParameterA => $gridDensity->[0],
        ParameterB => $gridDensity->[1],
        ParameterC => $gridDensity->[2],
        UseInsulatorDerivation => 'Yes',
        Pseudopotentials => $pseudopotentialType,
        XCFunctional => $Args{XCFunctional} || 'PBE',  
        KPointDerivation => 'CustomGrid'
    );

    return Modules->CASTEP->Energy->Run($structure, $settings);
}
