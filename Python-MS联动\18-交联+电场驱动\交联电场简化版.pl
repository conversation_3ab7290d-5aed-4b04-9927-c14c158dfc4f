#!perl

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);
use constant TRUE 	=> 1;
use constant FALSE 	=> 0;
use constant DEBUG	=> 2; # larger is more verbose
use constant PICO_TO_FEMTO => 1000;

# 简化交联+电场驱动脚本
# 基于最终版-交付客户.pl修改
# 此版本根据新的代码运行步骤进行简化，保留核心功能

################################################################################################
#
# 用户设置
#
################################################################################################

# 基础参数设置
my $xsdDocName			= "Original";	# 输入xsd文件名
my $conversionTarget 		= 50;		# 交联反应目标转化率(%)
my $MinRxnRadius 		= 4;		# 初始近距离接触截断值
my $StepRxnRadius 		= 1;		# 近距离接触步长
my $MaxRxnRadius 		= 9;		# 最终近距离接触截断值
my $IterationsPerRadius		= 2;		# 每个半径的最大尝试次数
						
# 反应原子和分子的特殊名称
my $monomerName 		= "monomer";	# 单体分子名称
my $monomerReactiveAtom 	= "R1";	        # 单体上的反应性原子名称
my $xlinkerName 		= "xlinker";	# 交联剂分子名称
my $xlinkerReactiveAtom 	= "R2";	        # 交联剂上的反应性原子名称

# 禁用分子对象标记，简化
my $noMol			= TRUE;

# 模拟设置
my $forcefield		 	= "COMPASSIII";
my $timeStep			= 0.1;		# 动力学时间步长(fs)
my $chargeMethod 		= "Atom based";	# 电荷计算方法: Atom based, Group based or Ewald
my $Quality			= "Medium";	# 计算质量: Coarse/Medium/Fine/Ultra Fine
my $thermostat			= "Andersen";	# 温度控制: Andersen, Nose, Velocity Scale, Berendsen or NHL
my $xlinkTemperature		= 300;		# 主要温度设置
my $xlinkPressure		= 0.0001;	# 主要压力设置
my $ensemble			= "NVE";	# 系综

# 电场设置
my $useElectricField            = TRUE;         # 是否应用电场
my $electricFieldStrength       = 1;            # 电场强度(V/Å)
my $electricFieldX              = 0;            # 电场X方向分量
my $electricFieldY              = 0;            # 电场Y方向分量
my $electricFieldZ              = 1;            # 电场Z方向分量
my $counterElectricField        = "Yes";        # 是否对带电系统应用反电场

# 初始平衡化设置
my $one_time_equilibration	= 20;		# 初始平衡化动力学时间(ps)

# 交联前动力学设置
my $straightDynamicsTime	= 10;		# 每次新距离前的动力学时间(ps)

# 分析动力学设置
my $analyzeDuration		= 10;		# 用于采样热力学数据的动力学时间(ps)

##########################################################################################################
#
# 检查是否有GUI并获取参数
#
##########################################################################################################

my $xsdDoc;
eval{ $xsdDoc = Documents->ActiveDocument; };

if ($@) # 无活动文档 - 使用上述定义的参数
{
	$xsdDoc	= $Documents{"$xsdDocName.xsd"};
}
else
{
	# 获取用户提供的参数
	my %Arg;
	GetOptions(\%Arg,
		"Forcefield=s", 
		"Conversion=i", 
		"MinRxnRadius=f", 
		"MaxRxnRadius=f", 
		"StepRxnRadius=f",
		"Iterations_per_Radius=i", 
		"ChargeMethod=s", 
		"Temperature=f",
		"Initial_Equil_ps=f",
		"Equil_ps=f",
		"Ensemble=s",
		"UseElectricField=s",
		"ElectricFieldStrength=f",
		"ElectricFieldX=f",
		"ElectricFieldY=f",
		"ElectricFieldZ=f",
		"CounterElectricField=s"
	);

	# 如果参数存在，则使用GUI中提供的值
	$conversionTarget 	= $Arg{Conversion}	if (exists $Arg{Conversion});
	$MinRxnRadius 		= $Arg{MinRxnRadius}	if (exists $Arg{MinRxnRadius});
	$StepRxnRadius 		= $Arg{StepRxnRadius}	if (exists $Arg{StepRxnRadius});
	$MaxRxnRadius 		= $Arg{MaxRxnRadius}	if (exists $Arg{MaxRxnRadius});
	$IterationsPerRadius	= $Arg{Iterations_per_Radius} if (exists $Arg{Iterations_per_Radius});
	$forcefield	 	= $Arg{Forcefield}	if (exists $Arg{Forcefield});
	$chargeMethod 		= $Arg{ChargeMethod}	if (exists $Arg{ChargeMethod});
	$xlinkTemperature	= $Arg{Temperature}	if (exists $Arg{Temperature});
	$one_time_equilibration	= $Arg{Initial_Equil_ps} if (exists $Arg{Initial_Equil_ps});
	$straightDynamicsTime   = $Arg{Equil_ps}	if (exists $Arg{Equil_ps});
	$ensemble		= $Arg{Ensemble}	if (exists $Arg{Ensemble} and $Arg{Ensemble} ne "");
	
	# 电场参数
	$useElectricField       = TRUE  if (exists $Arg{UseElectricField} and $Arg{UseElectricField} eq "Yes");
	$useElectricField       = FALSE if (exists $Arg{UseElectricField} and $Arg{UseElectricField} eq "No");
	$electricFieldStrength  = $Arg{ElectricFieldStrength} if (exists $Arg{ElectricFieldStrength});
	$electricFieldX         = $Arg{ElectricFieldX} if (exists $Arg{ElectricFieldX});
	$electricFieldY         = $Arg{ElectricFieldY} if (exists $Arg{ElectricFieldY});
	$electricFieldZ         = $Arg{ElectricFieldZ} if (exists $Arg{ElectricFieldZ});
	$counterElectricField   = "Yes" if (exists $Arg{CounterElectricField} and $Arg{CounterElectricField} eq "Yes");
	$counterElectricField   = "No"  if (exists $Arg{CounterElectricField} and $Arg{CounterElectricField} eq "No");

	# 将用户在GUI中指定的选项写入文本文件
	my $textDoc = Documents->New("GUI_inputs.txt");
	$textDoc->Append("USER OPTIONS\n============================\n");
	while ( my ($key, $value) = each(%Arg) ) 
	{
		$textDoc->Append(sprintf "$key => $value\n");
	}
	$textDoc->Append("============================\n\n");
	$textDoc->Close;
}

###########################################################################################
#
# 文件准备
#
###########################################################################################

# 尝试导入服务器文件
eval 
{
	Documents->Import("fromdsd.txt");
	Documents->Import("mpd.hosts");
};

# 创建进度报告文件
my $textDoc = Documents->New("Progress.txt");

# 创建时间记录文件
my $timeDoc = Documents->New("Timings.txt");
$timeDoc->Append("Distance Iteration Elapsted_Time(hr) Segment_Time(hr) Conversion(%)\n");
my $segtime = time; # 运行段的时间(秒)

# 复制第一个文档以保存初始结构
my $rootName = "xlink";
my $doc = Documents->New("$rootName.xsd");
$doc->CopyFrom($xsdDoc);

# 使用全局设置初始化Forcite
my $Forcite = Modules->Forcite;
$Forcite->ChangeSettings([
	CurrentForcefield	=> $forcefield,
	Quality			=> $Quality,
	Temperature		=> $xlinkTemperature,
	Pressure		=> $xlinkPressure,
	Thermostat		=> $thermostat,
	Barostat		=> "Andersen",
	TimeStep		=> $timeStep,
	TrajectoryFrequency	=> 1000,            # 轨迹帧保存频率
	AppendTrajectory	=> "Yes",           # 追加轨迹而不是覆盖
	WriteVelocities		=> "Yes",
	EnergyDeviation		=> 500000000,
	WriteLevel		=> "Silent"
]);

# 检查周期性：支持0和3，2可能有效
my $Periodicity;
if (($doc->SymmetrySystems->Count == 0) || ($doc->SymmetrySystem->SymmetryDefinition->Periodicity == 0))
{
	$Periodicity = 0;
	die "无周期系统不能使用Ewald\n" if ($chargeMethod eq "Ewald");
	$Forcite->ChangeSettings([
		NonPeriodicElectrostaticSummationMethod		=> $chargeMethod,
		NonPeriodicvdWSummationMethod			=> "Atom based"
	]);
}
else
{
	$Periodicity = $doc->SymmetrySystem->SymmetryDefinition->Periodicity;
	$Forcite->ChangeSettings([
	    	'3DPeriodicElectrostaticSummationMethod'	=> $chargeMethod,
		'****************************' 			=> "Atom based",
		'2DPeriodicElectrostaticSummationMethod'	=> $chargeMethod,
		'2DPeriodicvdWSummationMethod'			=> "Atom based",
	]);
} 
warn "2D周期性尚未测试\n" if ($Periodicity == 2);

# 防止重启运行时的文件名冲突
$xsdDoc->Name = "initial" if ($xsdDoc->Name =~ /^xlink_/);
$xsdDoc->Close;

# 创建统计表
my $statTable = Documents->New($rootName."_statistics.std");
my $nstatsheets = 1;

# 创建存储中间结构的表格，用于后续分析系统演化
my $structureTable = Documents->New($rootName."_structures.std");
$structureTable->ColumnHeading(1) = "Distance (A)";
$structureTable->ColumnHeading(2) = "Iteration";
$structureTable->ColumnHeading(3) = "Percent Conversion"; 

###########################################################################################
# 初始化交联数据

# 计算反应和已反应原子数
my $reactiveMonomerAtoms = 0;
my $reactiveXLinkerAtoms = 0;
my $reactedMonomerAtoms  = 0;
my $reactedXLinkerAtoms  = 0;
my $totalMonomerAtoms    = 0;
foreach my $atom (@{$doc->UnitCell->Atoms})
{
	$reactiveMonomerAtoms++ if ( isReactiveR1($atom) );
	$reactiveXLinkerAtoms++  if ( isReactiveR2($atom) );
	$reactedMonomerAtoms++  if ($atom->Name =~ /^$monomerReactiveAtom-\d/);
	$reactedXLinkerAtoms++   if ($atom->Name =~ /^$xlinkerReactiveAtom-\d/);
	$totalMonomerAtoms++    if ($atom->Name =~ /^$monomerReactiveAtom/);
}

# 计算交联剂可形成的可能交联数
my $xlinkPotential = $reactiveXLinkerAtoms;

# 计算先前已形成的交联数
my $xlinkCounter = 0;
foreach my $bond (@{$doc->UnitCell->Bonds})
{
	$xlinkCounter++ if ($bond->Name =~ /^xlink/);
}

# 计算当前转化率
my $conversion = calculateConversion($doc);
my $rowCounter = 0;	# 当前Study表中的行

# 检查单体和交联剂原子数与转化率的一致性
my $targetMonomerAtoms = $totalMonomerAtoms * ($conversionTarget / 100) - $reactedMonomerAtoms;
$textDoc->Append("转化率 = 已反应单体反应性原子的百分比\n");
$textDoc->Append("转化率目标 = $conversionTarget, 当前转化率 = $conversion\n");
$textDoc->Append("需要反应的单体原子总数: $targetMonomerAtoms\n");
$textDoc->Append("反应原子: 单体 $reactiveMonomerAtoms, 交联剂 $reactiveXLinkerAtoms\n");
$textDoc->Save;
die "交联剂数量不足以达到指定转化率\n" 
	if ($xlinkPotential < $targetMonomerAtoms);

# 优化和动力学步骤计数器
my $mdcounter = 0;
my $geomoptcounter = 0;

###########################################################################################
# 初始平衡化
# 如果$useElectricField为TRUE则应用电场

if ($one_time_equilibration > 0)
{
	my $steps = ($one_time_equilibration * PICO_TO_FEMTO / $timeStep);
	$textDoc->Append("\n初始平衡化\n");
	my $results = ForciteDynamics($doc, $steps, "NVE");
	my $results = ForciteDynamics($doc, $steps, $ensemble);
}

###########################################################################################
# 主交联循环
$textDoc->Append("进入主交联循环\n");
$textDoc->Save;

for (my $RxnRadius = $MinRxnRadius; $RxnRadius <= $MaxRxnRadius; $RxnRadius += $StepRxnRadius) 
{
	# 结构基本名称
	my $xsdNameDist = sprintf("%s_R%.2f", $rootName, $RxnRadius);

  	# 对每个新半径进行平衡化(第一个除外)
  	if ($RxnRadius > $MinRxnRadius)  
  	{
		$textDoc->Append("在新半径下平衡化\n");
		$doc->Name = $xsdNameDist . "_init";
		ForciteGeomOpt($doc, 2000);
		my $steps = ($straightDynamicsTime * PICO_TO_FEMTO / $timeStep);
		my $results = ForciteDynamics($doc, $steps, $ensemble);
  	}

	# 在每个半径下迭代交联，直到以下情况之一:
	# A) 达到最大迭代次数
	# B) 无新交联形成
	# C) 达到转化率目标
	for (my $iteration = 1; $iteration <= $IterationsPerRadius; $iteration++)
	{
		$textDoc->Append("\n\n##########################################################\n");
		$textDoc->Append("###### 半径 $RxnRadius, 迭代 $iteration\n");
		$textDoc->Append("##########################################################\n\n");
		$textDoc->Save;
			
  		$doc->Name = $xsdNameDist."_".$iteration;

		# 在结构中创建新键			
		my $numBonds = createNewXlinks($doc, $RxnRadius);

		# 更新转化率
		$conversion = calculateConversion($doc);
		$textDoc->Append(sprintf "交联数= %d \n转化率= %.01F %%\n", $xlinkCounter, $conversion);				
		$textDoc->Save;
			
		# 如果无新键形成，退出循环进入下一个半径
		if ($numBonds == 0)
		{
			$textDoc->Append("未创建新键，增加反应距离\n");
			last;
		}
		
		# 几何优化
		$textDoc->Append("  ");
		ForciteGeomOpt($doc, 2000);
		
		# 保存结构到Study表
		$textDoc->Append("保存中间结构到Study表\n");				
		$doc->InsertInto($structureTable);
		$structureTable->Cell($rowCounter,1) = $RxnRadius;
		$structureTable->Cell($rowCounter,2) = $iteration;
		$structureTable->Cell($rowCounter,3) = $conversion;

		# 短动力学运行记录热力学性质
		$textDoc->Append("\n\n运行额外动力学进行分析\n");
		my $steps = ($analyzeDuration * PICO_TO_FEMTO / $timeStep);
		my $freq = int($steps/20);
		my $results = ForciteDynamics($doc, $steps, $ensemble, (TrajectoryFrequency => $freq));								
		$rowCounter++;
				
		# 报告此次迭代所用时间
		$timeDoc->Append(sprintf "%-8.2f %-9d %-17.1f %-16.2f %-8.1f\n", 
			$RxnRadius, $iteration, (time-$^T)/3600, 
			(time-$segtime)/3600, $conversion);
		$segtime = time;
		
		# 保存所有文档以便可以终止脚本而不丢失数据		
		Documents->SaveAll;
	
		last if ($conversion >= $conversionTarget);

	} # 下一次迭代
	
	last if ($conversion >= $conversionTarget);

} # 下一个半径
 	
# 使用唯一名称重命名最终xsd
$doc->Name = $rootName."_final";

# 计算键分布
analyzeBonds($doc);

# 创建交联原子集合
XlinkSet($doc);

$textDoc->Append("\n##############################################################\n\n");
$textDoc->Append("计算完成\n");
$textDoc->Append("系统中有 $xlinkCounter 个交联\n");
$textDoc->Append(sprintf "最终转化率 %.1f%\n", $conversion);
$textDoc->Append("几何优化总步数: $geomoptcounter\n");
$textDoc->Append("分子动力学总步数: $mdcounter\n");

# 报告总时间
my $time_hr = (time-$^T)/3600;
$textDoc->Append(sprintf "\n总时间 %.2f 小时\n", $time_hr);
$textDoc->Append("\n##############################################################\n");
$textDoc->Save;
Documents->SaveAll; 

##########################################################################################################
#
#		核心功能函数
#
##########################################################################################################

# 计算转化率：定义为至少反应一次的单体(R1)原子百分比
sub calculateConversion
{
	my $doc1 = shift;
	# 计算已反应原子
	my $reactedMonomerAtoms  = 0;
	my $totalMonomerAtoms    = 0;
	foreach my $atom (@{$doc1->UnitCell->Atoms})
	{
		$reactedMonomerAtoms++  if ($atom->Name =~ /^$monomerReactiveAtom-\d/);
		$totalMonomerAtoms++    if ($atom->Name =~ /^$monomerReactiveAtom/);
	}

	my $conversion = 100.0 * $reactedMonomerAtoms / $totalMonomerAtoms;
	return $conversion;
}

#########################################################################################################

# 创建'not-R1'和'not-R2'集合
# 这些用于近距离接触计算，以排除模式进行
# 寻找接触：
# (A) 单元格中R1和R2原子之间
# (B) (不幸的是)单元格中R1或R2原子与相邻单元格中的像原子之间

sub createReactiveAtomSets 
{
	my $doc = shift;

	$textDoc->Append("  createReactiveAtomSets\n");

	# 初始化反应性原子计数器	
	my $R1Counter = 0;
	my $R2Counter = 0;
	
	# 为原子创建两个perl数组	
	my @notR1;
	my @notR2;

	# 基于原子名称创建两个集合
	# 一个包含除R1以外的所有原子，另一个包含除R2以外的所有原子
	my $atoms = $doc->UnitCell->Atoms;
	foreach my $atom (@$atoms) 
	{
		# 检查原子是否为反应性原子	
		if (isReactiveR1($atom)) 
		{					
			push (@notR2, $atom);
			$R1Counter++;		
		} 
		elsif (isReactiveR2($atom)) 
		{					
			push (@notR1, $atom);
			$R2Counter++;		
		} 
		else 
		{		
			push (@notR1, $atom);
			push (@notR2, $atom);
		}	
	}

	# 基于原子数组创建集合并隐藏
	my $notR1Set = $doc->CreateSet("notR1", \@notR1);
	my $notR2Set = $doc->CreateSet("notR2", \@notR2);
	$notR1Set->IsVisible = 0;
	$notR2Set->IsVisible = 0;

	$textDoc->Append("    $R1Counter 个反应性单体原子\n");
	$textDoc->Append("    $R2Counter 个反应性交联剂原子\n\n");
	$textDoc->Save;
		
	return ($doc, $R1Counter, $R2Counter);
}

#########################################################################################################

# 确定这是否是单体反应性原子
sub isReactiveR1
{
	my $atom = shift;
	my $name = $atom->Name;
	if ($name =~ /^$monomerReactiveAtom/)
	{
		return TRUE if ($name eq "$monomerReactiveAtom");
	}
	return FALSE;
}

#########################################################################################################

# 确定这是否是交联剂反应性原子
sub isReactiveR2
{
	my $atom = shift;
	my $name = $atom->Name;
	if ($name =~ /^$xlinkerReactiveAtom/)
	{
		return TRUE if ($name eq "$xlinkerReactiveAtom");
	}
	return FALSE;
}

#########################################################################################################

# 创建R1-R2近接原子的交联
sub createNewXlinks 
{
	my $doc1 = shift;
	my $distance = shift;
	
	my $t0 = time;
	$textDoc->Append("createNewXlinks\n");	

	# 更新反应性集合
	($doc1, my $R1Count, my $R2Count) = createReactiveAtomSets($doc1);
			
	# 使用集合排除法，基于当前反应距离截断和更新集合重新计算近距离接触
	Tools->BondCalculation->ChangeSettings([
		DistanceCriterionMode	=> "Absolute",
		ExclusionMode		=> "Set", 
		MaxAbsoluteDistance	=> $distance
	]);
	my $closeContacts = $doc1->CalculateCloseContacts;		
		
	# 删除与相邻单元格中非R原子的接触
	$textDoc->Append(sprintf "找到 %d 个近距离接触\n", $closeContacts->Count) if (DEBUG);
	foreach my $closeContact (@$closeContacts) 
	{
		my $atom1 = $closeContact->Atom1;
		my $atom2 = $closeContact->Atom2;	
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}
		# 除非两个原子都是反应性的且类型相反，否则删除
		$closeContact->Delete unless ( isReactiveR1($atom1) and isReactiveR2($atom2) );
	}
	$textDoc->Append(sprintf "排除非R后: %d\n", $closeContacts->Count) if (DEBUG > 1);

	# 仅允许原子在每轮中有一个新交联(保守起见)
	my $lockedAtoms = $doc1->UnitCell->Beads->Atoms; # 空集合
	foreach my $closeContact (@$closeContacts) 
	{
		my $atom1 = $closeContact->Atom1;
		my $atom2 = $closeContact->Atom2;	
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}
		if ($atom1->Name =~ /_LOCKED$/ or $atom2->Name =~ /_LOCKED$/)
		{
			$closeContact->Delete;
			next;
		}
		# 锁定这些原子使它们不再反应
		$atom1->Name .= "_LOCKED";
		$atom2->Name .= "_LOCKED";
		$lockedAtoms->Add($atom1);
		$lockedAtoms->Add($atom2);
	}
	# 解锁原子
	foreach my $atom (@$lockedAtoms)
	{
		$atom->Name =~ s/_LOCKED$//;
	}
	
	# 转换幸存的近距离接触为键
	my $newBondCounter = 0;
	
	foreach my $closeContact (@$closeContacts) 
	{
		my $atom1 = $closeContact->Atom1;
		my $atom2 = $closeContact->Atom2;
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}

		# 创建交联键		
		createNewBond($doc1, $atom1, $atom2);
		$newBondCounter++;
	}
	
	$textDoc->Append("  形成了 $newBondCounter 个链接\n");	
	$textDoc->Append("  $newBondCounter 个新交联键\n\n");
	$textDoc->Save;
	
	# 调整氢原子
	$doc1->AdjustHydrogen;
	
	# 删除下一轮的近距离接触和集合	
	$doc1->UnitCell->CloseContacts->Delete;
	$doc1->UnitCell->Sets->Delete;
	
	# 几何优化
	$textDoc->Append("  ");
	ForciteGeomOpt($doc1, 2000);
	
	# 报告时间
	$textDoc->Append(sprintf "\ncreateNewXlinks 用时 %d 秒\n\n", time-$t0); 
	$textDoc->Save;

	return ($newBondCounter);
}

#########################################################################################################

# 创建新的交联键并更改链接的显示样式和名称
sub createNewBond
{	
	my $doc1 = shift;
	my $atom1 = shift;
	my $atom2 = shift;
	my $test = shift;

	# 创建新键
    $xlinkCounter++ unless ($test);
	my $newBond = $doc1->CreateBond($atom1, $atom2, "Single", ([Name => "xlink-".$xlinkCounter]));        
    
    # 设置创建的键的显示样式为球棍式       
    $atom1->Style = "Ball and stick";
    $atom2->Style = "Ball and stick";
    
    # 在每个原子名称后附加交联索引(不会影响未来的交联)
    $atom1->Name .= "-".$xlinkCounter;
    $atom2->Name .= "-".$xlinkCounter;
    
    $textDoc->Append( sprintf "    在 %s 和 %s 之间创建键\n\n", $atom1->Name, $atom2->Name)
    	unless ($test);
}

#######################################################################################################

# Forcite几何优化
sub ForciteGeomOpt
{
	my $t0 = time;
	my $doc1 = shift;
	my $steps = shift;
	
	my $results;	
	eval 
	{
		$results = $Forcite->GeometryOptimization->Run($doc1, [MaxIterations => $steps]);
	};

	if ($@) 
	{	 
	 	$textDoc->Append("ForciteGeomOpt: 几何优化过程中失败\n");
	 	$textDoc->Append($@);
	}

	if (DEBUG) { 
		$textDoc->Append(sprintf "ForciteGeomOpt %d 步, %d 秒\n", $steps, time-$t0); 
		$textDoc->Save; 
	}

	$geomoptcounter += $steps;
	return $results;
}

#########################################################################################################

# Forcite动力学
sub ForciteDynamics
{
	# 启动计时器
	my $t0 = time;
	
	# 必填参数
	my $doc1 = shift;
	my $steps = shift;
	my $ensemble = shift;
	
	# 通过重命名文档来创建唯一的轨迹文件
	my $originalName = $doc1->Name;
	my ($sec,$min,$hour) = localtime(time);
	my $timestamp = sprintf("%02d%02d%02d", $hour, $min, $sec);
	my $uniqueName = sprintf("%s_%s_%dsteps_%s", $originalName, $ensemble, $steps, $timestamp);
	
	# 保存当前文档的原始名称，然后重命名
	$doc1->Name = $uniqueName;
	$textDoc->Append(sprintf "临时重命名文档: %s -> %s\n", $originalName, $uniqueName);
	$textDoc->Save;
	
	# 将设置构建为perl数组
	my @settings = (
		NumberOfSteps	=> $steps,
		Ensemble3D	=> $ensemble,
		AppendTrajectory => "No",        # 不追加，为每次模拟创建新轨迹
		TrajectoryFrequency => 1000      # 轨迹保存频率
	);
	
	# 如果启用，添加电场设置
	if ($useElectricField) {
		push @settings, (
			ElectricFieldStrength => $electricFieldStrength,
			ElectricFieldX => $electricFieldX,
			ElectricFieldY => $electricFieldY,
			ElectricFieldZ => $electricFieldZ,
			CounterElectricField => $counterElectricField
		);
		$textDoc->Append("应用电场: 强度=$electricFieldStrength V/Å, 方向=[$electricFieldX,$electricFieldY,$electricFieldZ]\n") if (DEBUG);
	}
	
	# 假定其余为自定义设置
	push @settings, @_;
			
	# 在eval内运行动力学以防止失败的运行停止脚本
	my $results;
	eval 
	{
		$results = $Forcite->Dynamics->Run($doc1, \@settings);
	};
	
	# 无论成功与否，恢复原始文档名称
	$textDoc->Append(sprintf "恢复文档原始名称: %s -> %s\n", $uniqueName, $originalName);
	$doc1->Name = $originalName;
	$textDoc->Save;
	
	# 处理错误
	if ($@) 
	{
		$textDoc->Append("错误: ForciteDynamics 失败\n");
		$textDoc->Append($@);
		die "ForciteDynamics中失败\n";
	}

	# 报告所用时间
	if (DEBUG) 
	{ 
		$textDoc->Append(sprintf "ForciteDynamics %d 步, %s 系综, %d 秒\n", 
			$steps, $ensemble, time-$t0);
		$textDoc->Save;
	}
	
	# 输出轨迹文件信息
	if ($results->Trajectory) {
		$textDoc->Append(sprintf "已保存轨迹文件: %s (共%d帧)\n", 
			$results->Trajectory->Name, $results->Trajectory->NumFrames);
		$textDoc->Save;
	}

	$mdcounter += $steps;
	return $results;	
}

#########################################################################################################

# 分析键长分布
sub analyzeBonds
{
	my $doc1 = shift;
	
	# 计算所有键的分布	
	my $bondAnalysis = $Forcite->Analysis->LengthDistribution($doc1, [
		LengthDistributionUseBonds	=> "Yes"
	]);	
	$bondAnalysis->LengthDistributionChartAsStudyTable->Delete;	
	$bondAnalysis->LengthDistributionChart->Name = "AllBonds";
	
	# 为所有交联键创建距离集合
	my @distances;
	foreach my $bond (@{$doc1->UnitCell->Bonds}) 
	{	
		if ($bond->Name =~ /^xlink/) 
		{
			my $distance = $doc1->CreateDistance([$bond->Atom1, $bond->Atom2]);		
			push @distances, $distance;			
		}	
	}
	return unless (scalar(@distances) > 0);
	$doc1->CreateSet("xlink distances", \@distances);
	
	# 现在仅分析交联距离	
	my $bondAnalysis = $Forcite->Analysis->LengthDistribution($doc1, [
		LengthDistributionSetA	=> "xlink distances"
	]);
	$bondAnalysis->LengthDistributionChartAsStudyTable->Delete;
	$bondAnalysis->LengthDistributionChart->Name = "XlinkBonds";
	$doc1->UnitCell->Distances->Delete;
}

#########################################################################################################

# 创建包含交联原子和键的集合
sub XlinkSet
{
	my $doc = shift;

	my @xlinked_atoms;
	foreach my $bond (@{$doc->UnitCell->Bonds}) 
	{
		if ($bond->Name =~ /^xlink/) 
		{
			push @xlinked_atoms, $bond->Atom1;
			push @xlinked_atoms, $bond->Atom2;
			push @xlinked_atoms, $bond;
		}
	}
	return if (scalar(@xlinked_atoms) == 0);

	$textDoc->Append("\n创建交联集合\n");
	$doc->CreateSet("Crosslinks", \@xlinked_atoms, [ IsVisible => "No" , Style => "None" ]);
} 