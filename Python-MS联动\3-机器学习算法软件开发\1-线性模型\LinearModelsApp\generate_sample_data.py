import numpy as np
import pandas as pd
from sklearn.datasets import make_regression

# 生成回归数据集
def generate_regression_dataset(n_samples=1000, n_features=5, noise=0.5, random_state=42):
    X, y = make_regression(n_samples=n_samples, 
                          n_features=n_features, 
                          noise=noise, 
                          random_state=random_state)
    
    # 创建DataFrame
    feature_names = [f'特征_{i+1}' for i in range(n_features)]
    df = pd.DataFrame(X, columns=feature_names)
    df['目标变量'] = y
    
    return df

# 生成房价预测示例数据集
def generate_house_price_dataset(n_samples=1000, random_state=42):
    np.random.seed(random_state)
    
    # 创建特征
    area = np.random.normal(100, 30, n_samples)  # 面积
    rooms = np.random.randint(1, 6, n_samples)   # 房间数
    age = np.random.randint(1, 50, n_samples)    # 房龄
    distance = np.random.normal(5, 2, n_samples) # 到市中心距离
    
    # 生成房价(目标变量)，加入一些噪声
    base_price = 100000
    price = base_price + 1000 * area + 10000 * rooms - 1000 * age - 5000 * distance
    price = price + np.random.normal(0, 20000, n_samples)
    
    # 创建DataFrame
    df = pd.DataFrame({
        '面积': area,
        '房间数': rooms,
        '房龄': age,
        '到市中心距离': distance,
        '房价': price
    })
    
    return df

# 生成销售预测示例数据集
def generate_sales_dataset(n_samples=1000, random_state=42):
    np.random.seed(random_state)
    
    # 创建特征
    adv_spend = np.random.uniform(1000, 10000, n_samples)  # 广告支出
    promo_discount = np.random.uniform(0, 30, n_samples)   # 促销折扣
    competitor_price = np.random.normal(50, 10, n_samples) # 竞争对手价格
    season_factor = np.random.choice([0.8, 1.0, 1.2, 1.5], n_samples) # 季节因素
    
    # 生成销售量(目标变量)，加入一些噪声
    base_sales = 1000
    sales = base_sales + 0.1 * adv_spend + 20 * promo_discount - 10 * competitor_price
    sales = sales * season_factor
    sales = sales + np.random.normal(0, 200, n_samples)
    
    # 创建DataFrame
    df = pd.DataFrame({
        '广告支出': adv_spend,
        '促销折扣': promo_discount,
        '竞争对手价格': competitor_price,
        '季节因素': season_factor,
        '销售量': sales
    })
    
    return df

if __name__ == "__main__":
    # 生成三种示例数据集
    random_dataset = generate_regression_dataset()
    house_price_dataset = generate_house_price_dataset()
    sales_dataset = generate_sales_dataset()
    
    # 保存为CSV文件
    random_dataset.to_csv('random_data.csv', index=False)
    house_price_dataset.to_csv('house_price_data.csv', index=False)
    sales_dataset.to_csv('sales_data.csv', index=False)
    
    print("已生成三个示例数据集：")
    print("1. random_data.csv - 随机回归数据")
    print("2. house_price_data.csv - 房价预测数据")
    print("3. sales_data.csv - 销售预测数据") 