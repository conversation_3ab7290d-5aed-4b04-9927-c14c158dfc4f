# MS卸载助手 - 用户使用手册

## 产品介绍

MS卸载助手是一款专业的 Materials Studio 卸载工具，能够帮助您彻底卸载 Materials Studio 软件及其相关组件、清理系统残留，恢复系统环境。本工具具有以下特点：

- **一键式卸载**：全自动完成 Materials Studio 的卸载过程
- **智能对话框处理**：自动处理卸载过程中的各种确认对话框
- **深度清理**：清理注册表、文件系统中的残留项目
- **安全保障**：提供激活机制确保工具仅限授权使用
- **可视化界面**：直观的进度显示和操作提示

## 系统要求

- **操作系统**：Windows 7/8/10/11
- **权限要求**：管理员权限
- **运行环境**：无需额外安装（激活工具需要 Python 环境）
- **磁盘空间**：至少 50MB 可用空间
- **推荐配置**：64 位操作系统，4GB 以上内存

## 获取与安装

### 获取软件

1. 通过官方渠道下载 MS卸载助手安装包
2. 解压下载的压缩包到任意目录

### 文件清单

安装包中应包含以下文件：
- `MS卸载助手.exe` - 主程序
- `激活工具.py` - 激活码生成工具
- `重置码生成器.py` - 重置码生成工具
- `README.txt` - 简要说明文件

## 使用前准备

### 1. 软件位置要求

为确保卸载过程顺利，请将 MS卸载助手文件夹放置到 Materials Studio 的安装目录下。通常位于：
- `C:\Program Files\Accelrys\Materials Studio X.X\` 或
- `C:\Program Files\BIOVIA\Materials Studio X.X\`

### 2. 关闭相关程序

使用前请确保：
- 已关闭所有 Materials Studio 相关程序
- 关闭可能访问 Materials Studio 文件的应用
- 退出所有 BIOVIA 相关服务

### 3. 数据备份

卸载操作不可逆，请在卸载前备份重要数据：
- Materials Studio 项目文件
- 自定义设置和模板
- 许可证信息（如有需要）

## 使用流程

### 第一步：获取机器码

1. 以管理员身份运行 `MS卸载助手.exe`
2. 程序启动后显示机器码
3. 复制此机器码（点击"复制"按钮或手动复制）

### 第二步：获取激活码

#### 方式一：使用激活工具

1. 确保计算机上安装了 Python 环境
2. 运行 `激活工具.py`（双击或通过命令行 `python 激活工具.py`）
3. 在激活工具中粘贴之前获取的机器码
4. 点击"生成激活码"按钮
5. 复制生成的激活序列码

#### 方式二：联系管理员

如果您无法运行激活工具，请联系管理员并提供您的机器码，由管理员生成激活码。

### 第三步：输入激活码并开始卸载

1. 返回 MS卸载助手主界面
2. 在"激活序列码"输入框中粘贴激活码
3. 根据需要选择高级卸载选项：
   - 清理临时文件和缓存（推荐）
   - 清理注册表项（推荐）
   - 清理用户数据和配置文件（可选，会删除个人设置）
4. 点击"开始卸载"按钮
5. 等待卸载过程完成

### 第四步：完成卸载

卸载完成后，程序会显示卸载摘要信息：
- 卸载的组件清单
- 清理的文件和注册表项数量
- 总耗时
- 建议的后续操作

## 高级功能

### 管理员模式

MS卸载助手提供管理员模式以访问更多高级功能：

1. 在欢迎界面，点击右上角的"⚙"图标
2. 输入管理员密码
3. 验证通过后进入管理员模式

管理员模式下可用的额外功能：
- 查看详细的卸载日志
- 访问高级清理选项
- 覆盖激活验证

### 重置功能

如需在同一台计算机上再次使用 MS卸载助手，需要重置使用状态：

1. 运行 `重置码生成器.py`
2. 输入您的机器码
3. 生成重置码
4. 在 MS卸载助手中使用重置码重置使用状态
   - 直接启动 MS卸载助手，在弹出的"程序已使用"对话框中选择"是"
   - 或使用命令行参数 `MS卸载助手.exe --reset`

## 常见问题解答

### 1. 激活码无效怎么办？

**问题：** 输入激活码后提示"激活码无效"。

**解决方案：**
- 确认激活码输入无误，注意区分字母大小写和数字
- 确保激活码是针对当前机器生成的
- 尝试使用激活工具重新生成激活码
- 联系管理员获取有效的激活码

### 2. 程序无法启动怎么办？

**问题：** 双击程序图标后无反应或出现错误信息。

**解决方案：**
- 以管理员身份运行程序
- 检查是否缺少必要的系统组件（如 Visual C++ 运行库）
- 尝试将程序放置在非系统盘
- 禁用杀毒软件后再试

### 3. 卸载过程中断怎么办？

**问题：** 卸载过程中出错或被意外中断。

**解决方案：**
- 重新启动程序（可能需要重置）
- 检查日志了解失败原因
- 手动关闭可能干扰的应用程序
- 重新启动计算机后再试

### 4. 无法清理某些文件怎么办？

**问题：** 卸载完成，但提示某些文件无法删除。

**解决方案：**
- 重新启动计算机后再次运行程序
- 手动检查是否有程序仍在使用这些文件
- 使用系统工具（如任务管理器）结束占用文件的进程
- 使用第三方文件解锁工具

### 5. Materials Studio 相关程序仍在控制面板中怎么办？

**问题：** 卸载后在控制面板中仍能看到 Materials Studio 相关项目。

**解决方案：**
- 使用控制面板手动卸载剩余组件
- 重新运行 MS卸载助手并选择"清理注册表项"选项
- 系统重启后检查是否彻底移除

### 6. 丢失重要文件怎么办？

**问题：** 卸载后发现丢失了重要文件。

**解决方案：**
- 检查回收站是否包含这些文件
- 使用数据恢复软件尝试恢复（如果文件确实重要）
- 在未来使用前，务必备份重要数据

## 注意事项

1. **一次性使用限制**：MS卸载助手设计为在每台计算机上只能使用一次，再次使用需要重置码。

2. **不可逆操作**：卸载操作不可撤销，确保您确实需要完全移除 Materials Studio。

3. **磁盘空间**：卸载过程可能临时需要额外磁盘空间，请确保系统盘有足够空间。

4. **杀毒软件**：某些杀毒软件可能干扰卸载过程，必要时暂时禁用。

5. **网络要求**：卸载过程不需要网络连接，工具完全在本地工作。

## 技术支持

如果您在使用过程中遇到本文档未涵盖的问题，请通过以下方式获取支持：

1. 查看程序目录下的 `README.txt` 文件获取最新信息
2. 联系您的系统管理员或IT支持人员
3. 发送详细问题描述和日志到开发者邮箱

## 许可说明

MS卸载助手是专用软件，仅授权给特定用户使用。未经授权，不得分发、修改或反编译本软件。本软件仅限用于卸载 Materials Studio 软件，不得用于其他目的。 