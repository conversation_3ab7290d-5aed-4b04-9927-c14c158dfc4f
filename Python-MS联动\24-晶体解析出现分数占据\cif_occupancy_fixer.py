#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CIF文件原子占据率修改工具（可视化版）

此脚本用于修复晶体结构CIF文件中的分数占据问题，
根据"保大去小"原则，将所有非1.0的占据率替换为1.0。

使用方法：
    直接运行脚本，通过图形界面选择一个或多个CIF文件进行处理。

版权声明：
    本程序免费使用，不可售卖。
    编译者：端木鹏博 (<EMAIL>)
"""

import sys
import os
import re
import tkinter as tk
from tkinter import filedialog, scrolledtext, ttk, messagebox
from collections import Counter
import threading


def analyze_occupancy(cif_content):
    """分析CIF文件中的所有占据率值"""
    occupancy_values = []
    
    # 找到atom_site定义部分
    atom_site_section = False
    occupancy_index = -1
    
    lines = cif_content.split('\n')
    for i, line in enumerate(lines):
        if line.strip().startswith('loop_'):
            # 检查下面的行是否包含_atom_site_开头的条目
            j = i + 1
            atom_site_columns = []
            while j < len(lines) and lines[j].strip().startswith('_atom_site_'):
                atom_site_columns.append(lines[j].strip())
                j += 1
            
            if '_atom_site_occupancy' in atom_site_columns:
                atom_site_section = True
                occupancy_index = atom_site_columns.index('_atom_site_occupancy')
                continue
        
        # 如果正在处理atom_site部分，且该行不是注释或空行
        if atom_site_section and line.strip() and not line.strip().startswith('#') and not line.strip().startswith('_'):
            # 如果行开头有空格且包含数据，则认为是原子数据行
            parts = line.strip().split()
            if len(parts) > occupancy_index+1:  # 确保有足够的列
                try:
                    occupancy = float(parts[occupancy_index])
                    occupancy_values.append(occupancy)
                except (ValueError, IndexError):
                    pass
    
    # 统计不同占据率值的出现次数
    occupancy_counter = Counter(occupancy_values)
    return occupancy_counter


def replace_occupancy(cif_content, target_value=1.0):
    """将所有非target_value的占据率替换为target_value，保持原始格式不变"""
    # 找到atom_site定义部分
    modified_lines = []
    atom_site_section = False
    occupancy_index = -1
    
    lines = cif_content.split('\n')
    for i, line in enumerate(lines):
        if line.strip().startswith('loop_'):
            modified_lines.append(line)
            # 检查下面的行是否包含_atom_site_开头的条目
            j = i + 1
            atom_site_columns = []
            while j < len(lines) and lines[j].strip().startswith('_atom_site_'):
                atom_site_columns.append(lines[j].strip())
                j += 1
            
            if '_atom_site_occupancy' in atom_site_columns:
                atom_site_section = True
                occupancy_index = atom_site_columns.index('_atom_site_occupancy')
                continue
        
        # 如果正在处理atom_site部分，且该行不是注释或空行
        if atom_site_section and line.strip() and not line.strip().startswith('#') and not line.strip().startswith('_'):
            # 如果行包含数据，则认为是原子数据行
            parts = line.strip().split()
            if len(parts) > occupancy_index+1:  # 确保有足够的列
                try:
                    current_occupancy = float(parts[occupancy_index])
                    if current_occupancy != target_value:
                        # 找到原始行中占据率值的位置
                        original_line = line
                        # 将占据率值转为字符串表示形式（保留相同位数）
                        occupancy_str = str(current_occupancy)
                        # 计算新值的字符串（保持相同格式）
                        if '.' in occupancy_str:
                            # 确定小数点后的位数
                            decimal_places = len(occupancy_str.split('.')[1])
                            new_value_str = f"{target_value:.{decimal_places}f}"
                        else:
                            new_value_str = f"{target_value:.1f}"
                        
                        # 在原始行中查找并替换占据率值
                        # 首先构建可能的占据率字符串表示形式
                        possible_formats = [
                            f"{current_occupancy:.1f}",
                            f"{current_occupancy:.2f}",
                            f"{current_occupancy:.3f}",
                            f"{current_occupancy:.4f}",
                            f"{current_occupancy:.5f}",
                            f"{current_occupancy:.6f}",
                            f"{current_occupancy}",
                            f"{current_occupancy:.0f}"
                        ]
                        
                        # 查找并替换
                        modified_line = original_line
                        # 从最长的格式开始尝试，避免部分匹配问题
                        possible_formats.sort(key=len, reverse=True)
                        
                        for fmt in possible_formats:
                            if fmt in original_line:
                                # 确保替换的是正确的占据率值（作为独立的单词）
                                # 使用正则表达式匹配独立的数字
                                pattern = r'(\s|^)' + re.escape(fmt) + r'(\s|$)'
                                match = re.search(pattern, original_line)
                                if match:
                                    # 保持相同的格式（长度相同）
                                    if len(new_value_str) < len(fmt):
                                        # 如果新值更短，添加空格保持对齐
                                        padding = ' ' * (len(fmt) - len(new_value_str))
                                        replacement = match.group(1) + new_value_str + padding + match.group(2)
                                    else:
                                        replacement = match.group(1) + new_value_str + match.group(2)
                                    
                                    modified_line = original_line[:match.start()] + replacement + original_line[match.end():]
                                    break
                        
                        modified_lines.append(modified_line)
                        continue
                except (ValueError, IndexError):
                    pass
        
        modified_lines.append(line)
    
    return '\n'.join(modified_lines)


def fix_duplicate_loop_tags(cif_content):
    """修复CIF内容中可能出现的重复的loop_标签"""
    # 防止连续出现多个loop_标签
    lines = cif_content.split('\n')
    fixed_lines = []
    
    i = 0
    while i < len(lines):
        fixed_lines.append(lines[i])
        
        # 如果当前行是loop_，检查下一行是否也是loop_
        if lines[i].strip() == 'loop_' and i + 1 < len(lines) and lines[i + 1].strip() == 'loop_':
            # 跳过下一行（重复的loop_）
            i += 1
        
        i += 1
    
    return '\n'.join(fixed_lines)


class CifOccupancyFixerApp:
    """CIF占据率修改工具图形界面应用"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("CIF文件占据率修改工具V1.0-编译者_端木鹏博（<EMAIL>）")
        self.root.geometry("800x600")
        
        # 创建主框架
        self.main_frame = ttk.Frame(root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 文件选择部分
        self.file_frame = ttk.LabelFrame(self.main_frame, text="文件选择", padding="10")
        self.file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.file_list = tk.Listbox(self.file_frame, height=5, selectmode=tk.MULTIPLE)
        self.file_list.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.file_frame, orient="vertical", command=self.file_list.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.file_list.config(yscrollcommand=scrollbar.set)
        
        # 输出文件夹选择部分
        self.output_frame = ttk.LabelFrame(self.main_frame, text="输出文件夹", padding="10")
        self.output_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.output_path = tk.StringVar()
        self.output_entry = ttk.Entry(self.output_frame, textvariable=self.output_path)
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        self.select_output_button = ttk.Button(self.output_frame, text="选择文件夹", command=self.select_output_folder)
        self.select_output_button.pack(side=tk.RIGHT, padx=5)
        
        # 按钮框架
        self.button_frame = ttk.Frame(self.main_frame)
        self.button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.add_files_button = ttk.Button(self.button_frame, text="添加文件", command=self.add_files)
        self.add_files_button.pack(side=tk.LEFT, padx=5)
        
        self.clear_files_button = ttk.Button(self.button_frame, text="清空列表", command=self.clear_files)
        self.clear_files_button.pack(side=tk.LEFT, padx=5)
        
        self.about_button = ttk.Button(self.button_frame, text="关于", command=self.show_about)
        self.about_button.pack(side=tk.LEFT, padx=5)
        
        self.process_button = ttk.Button(self.button_frame, text="处理文件", command=self.process_files)
        self.process_button.pack(side=tk.RIGHT, padx=5)
        
        # 进度条
        self.progress_frame = ttk.Frame(self.main_frame)
        self.progress_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.progress_label = ttk.Label(self.progress_frame, text="进度:")
        self.progress_label.pack(side=tk.LEFT, padx=5)
        
        self.progress_bar = ttk.Progressbar(self.progress_frame, length=100, mode="determinate")
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        # 结果显示区域
        self.result_frame = ttk.LabelFrame(self.main_frame, text="处理结果", padding="10")
        self.result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.result_text = scrolledtext.ScrolledText(self.result_frame, wrap=tk.WORD)
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        # 状态栏
        self.status_bar = ttk.Label(root, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 存储文件列表
        self.files = []
        
        # 设置默认输出文件夹
        default_output = os.path.join(os.path.dirname(os.path.abspath(__file__)), "processed_files")
        self.output_path.set(default_output)
        
        # 启动时显示版权声明
        self.show_copyright_on_startup()
    
    def show_copyright_on_startup(self):
        """启动时显示版权声明"""
        copyright_text = """CIF文件占据率修改工具 V1.0
        
版权声明：
    本程序免费使用，不可售卖。
    编译者：端木鹏博 (<EMAIL>)

使用说明：
    1. 点击"添加文件"按钮选择需要处理的CIF文件
    2. 选择或确认输出文件夹
    3. 点击"处理文件"按钮开始处理
    4. 处理完成后将在输出文件夹中生成修改后的文件
        """
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, copyright_text)
    
    def show_about(self):
        """显示关于对话框"""
        about_text = """CIF文件占据率修改工具 V1.0

此工具用于修复晶体结构CIF文件中的分数占据问题，
根据"保大去小"原则，将所有非1.0的占据率替换为1.0。

版权声明：
    本程序免费使用，不可售卖。
    
编译者：端木鹏博 (<EMAIL>)
        """
        messagebox.showinfo("关于", about_text)
    
    def select_output_folder(self):
        """选择输出文件夹"""
        folder = filedialog.askdirectory(title="选择输出文件夹")
        if folder:
            self.output_path.set(folder)
            self.status_bar.config(text=f"已选择输出文件夹: {folder}")
    
    def add_files(self):
        """添加CIF文件到列表"""
        filetypes = [("CIF文件", "*.cif"), ("所有文件", "*.*")]
        new_files = filedialog.askopenfilenames(filetypes=filetypes, title="选择CIF文件")
        
        for file in new_files:
            if file not in self.files:
                self.files.append(file)
                self.file_list.insert(tk.END, os.path.basename(file))
        
        self.status_bar.config(text=f"已添加 {len(new_files)} 个文件，总计 {len(self.files)} 个文件")
    
    def clear_files(self):
        """清空文件列表"""
        self.files = []
        self.file_list.delete(0, tk.END)
        self.status_bar.config(text="文件列表已清空")
    
    def process_files(self):
        """处理所选文件"""
        if not self.files:
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "错误: 请先添加CIF文件!\n")
            return
        
        # 检查输出文件夹
        output_folder = self.output_path.get().strip()
        if not output_folder:
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "错误: 请选择输出文件夹!\n")
            return
        
        # 确保输出文件夹存在
        try:
            os.makedirs(output_folder, exist_ok=True)
        except Exception as e:
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, f"错误: 无法创建输出文件夹: {str(e)}\n")
            return
        
        # 禁用按钮，防止多次点击
        self.process_button.config(state=tk.DISABLED)
        self.add_files_button.config(state=tk.DISABLED)
        self.clear_files_button.config(state=tk.DISABLED)
        self.select_output_button.config(state=tk.DISABLED)
        self.about_button.config(state=tk.DISABLED)
        
        # 清空结果区域
        self.result_text.delete(1.0, tk.END)
        
        # 重置进度条
        self.progress_bar["value"] = 0
        self.progress_bar["maximum"] = len(self.files)
        
        # 启动处理线程
        threading.Thread(target=self.process_files_thread, daemon=True).start()
    
    def process_files_thread(self):
        """在单独的线程中处理文件，防止GUI冻结"""
        output_folder = self.output_path.get()
        
        for i, file_path in enumerate(self.files):
            try:
                # 更新状态
                self.status_bar.config(text=f"正在处理: {os.path.basename(file_path)}")
                
                # 读取文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    cif_content = f.read()
                
                # 分析占据率
                occupancy_counts = analyze_occupancy(cif_content)
                result_text = f"文件: {os.path.basename(file_path)}\n"
                result_text += "原始占据率值统计:\n"
                for value, count in sorted(occupancy_counts.items()):
                    result_text += f"  {value:.4f}: {count}个原子\n"
                
                # 替换非1.0的占据率
                modified_content = replace_occupancy(cif_content)
                
                # 修复重复的loop_标签问题
                modified_content = fix_duplicate_loop_tags(modified_content)
                
                # 生成输出文件名
                base_name = os.path.basename(file_path)
                output_file = os.path.join(output_folder, base_name)
                
                # 写入输出文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                
                # 验证结果
                with open(output_file, 'r', encoding='utf-8') as f:
                    new_content = f.read()
                
                new_occupancy_counts = analyze_occupancy(new_content)
                result_text += "\n修改后的占据率值统计:\n"
                for value, count in sorted(new_occupancy_counts.items()):
                    result_text += f"  {value:.4f}: {count}个原子\n"
                
                result_text += f"\n处理完成! 修改后的文件已保存为: {output_file}\n"
                result_text += "-" * 50 + "\n\n"
                
                # 更新UI
                self.root.after(0, lambda t=result_text: self.result_text.insert(tk.END, t))
                
                # 更新进度条
                self.root.after(0, lambda v=i+1: self.progress_bar.config(value=v))
                
            except Exception as e:
                error_text = f"处理文件 '{os.path.basename(file_path)}' 时出错: {str(e)}\n"
                error_text += "-" * 50 + "\n\n"
                self.root.after(0, lambda t=error_text: self.result_text.insert(tk.END, t))
        
        # 处理完成后打开输出文件夹
        try:
            if os.path.exists(output_folder):
                self.open_output_folder(output_folder)
        except Exception:
            pass
            
        # 处理完成，恢复按钮状态
        self.root.after(0, self.enable_buttons)
        self.root.after(0, lambda: self.status_bar.config(text=f"处理完成，文件已保存至: {output_folder}"))
    
    def open_output_folder(self, folder_path):
        """打开输出文件夹"""
        try:
            if sys.platform == 'win32':
                os.startfile(folder_path)
            elif sys.platform == 'darwin':  # macOS
                import subprocess
                subprocess.Popen(['open', folder_path])
            else:  # linux
                import subprocess
                subprocess.Popen(['xdg-open', folder_path])
        except Exception:
            pass
    
    def enable_buttons(self):
        """恢复按钮状态"""
        self.process_button.config(state=tk.NORMAL)
        self.add_files_button.config(state=tk.NORMAL)
        self.clear_files_button.config(state=tk.NORMAL)
        self.select_output_button.config(state=tk.NORMAL)
        self.about_button.config(state=tk.NORMAL)


def main():
    """主函数"""
    # 创建GUI应用
    root = tk.Tk()
    app = CifOccupancyFixerApp(root)
    
    # 如果有命令行参数，添加到文件列表
    if len(sys.argv) > 1:
        for file_path in sys.argv[1:]:
            if os.path.isfile(file_path) and file_path not in app.files:
                app.files.append(file_path)
                app.file_list.insert(tk.END, os.path.basename(file_path))
    
    # 运行应用
    root.mainloop()


if __name__ == "__main__":
    main()