@echo off
echo MS软件完全卸载工具启动中...
echo 请确保您以管理员身份运行此程序

:: 检查管理员权限
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"
if %errorlevel% neq 0 (
    echo 需要管理员权限！请右键点击本批处理文件，选择"以管理员身份运行"。
    pause
    exit /b
)

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 未找到Python，请安装Python后再运行此程序。
    pause
    exit /b
)

:: 检查是否已生成exe文件
if exist "dist\MS卸载工具.exe" (
    echo 已找到可执行文件，正在启动...
    start "" "dist\MS卸载工具.exe"
) else (
    echo 未找到可执行文件，将直接运行Python脚本...
    
    :: 检查必要的库是否安装
    echo 正在检查所需的Python库...
    
    python -c "import pyautogui" >nul 2>&1
    if %errorlevel% neq 0 (
        echo 正在安装pyautogui...
        pip install pyautogui
    )
    
    python -c "import cv2" >nul 2>&1
    if %errorlevel% neq 0 (
        echo 正在安装opencv-python...
        pip install opencv-python
    )
    
    python -c "import PIL" >nul 2>&1
    if %errorlevel% neq 0 (
        echo 正在安装pillow...
        pip install pillow
    )
    
    echo 正在运行MS卸载工具...
    python "Other+MS_Until.py"
)

echo 程序已结束
pause 