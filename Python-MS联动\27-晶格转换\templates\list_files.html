<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件列表 - CIF晶格转换工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .file-list {
            margin-bottom: 30px;
        }
        .file-item {
            background-color: #f8f9fa;
            border-left: 3px solid #3498db;
            padding: 10px 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .file-item:hover {
            background-color: #e9f7fe;
        }
        .file-name {
            flex-grow: 1;
            font-family: Consolas, monospace;
        }
        .actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }
        .btn {
            display: inline-block;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            color: white;
        }
        .btn-view {
            background-color: #3498db;
        }
        .btn-view:hover {
            background-color: #2980b9;
        }
        .btn-back {
            background-color: #95a5a6;
        }
        .btn-back:hover {
            background-color: #7f8c8d;
        }
        .btn-download-all {
            background-color: #27ae60;
            padding: 10px 20px;
        }
        .btn-download-all:hover {
            background-color: #219955;
        }
        .empty-message {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>转换后的文件列表</h1>
        
        {% if files %}
        <div class="file-list">
            {% for file in files %}
            <div class="file-item">
                <div class="file-name">{{ file }}</div>
                <a href="/view/{{ session_id }}/{{ file }}" class="btn btn-view">查看</a>
            </div>
            {% endfor %}
        </div>
        
        <div class="actions">
            <a href="/download/{{ session_id }}" class="btn btn-download-all">下载所有文件 (ZIP)</a>
        </div>
        {% else %}
        <div class="empty-message">
            未找到任何转换后的文件。
        </div>
        {% endif %}
        
        <div class="actions">
            <a href="/" class="btn btn-back">返回首页</a>
        </div>
    </div>
</body>
</html> 