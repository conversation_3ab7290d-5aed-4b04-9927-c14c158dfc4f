import os
import sys
import shutil
import subprocess
from datetime import datetime
import uuid
import random
import string
import json
import tkinter as tk
from tkinter import messagebox
import platform

# 配置文件存储位置
def get_config_path():
    """获取配置文件路径"""
    if os.name == 'nt':  # Windows
        app_data = os.environ.get('APPDATA', '')
        return os.path.join(app_data, 'MSUninstaller')
    else:  # Linux/Mac
        home = os.environ.get('HOME', '')
        return os.path.join(home, '.msuninstaller')

def get_machine_id(machine_id_input=None):
    """获取计算机唯一标识符"""
    if machine_id_input:
        return machine_id_input  # 激活工具使用输入的机器码
        
    # 卸载程序获取本机机器码
    machine_id = str(uuid.getnode())
    disk_serial = ''
    try:
        c_drive = 'C:\\'
        if os.path.exists(c_drive):
            volume_info = subprocess.check_output('vol C:', shell=True).decode('utf-8')
            for line in volume_info.split('\n'):
                if 'Volume Serial Number' in line:
                    disk_serial = line.split(':')[1].strip()
                    break
    except:
        pass
    
    # 确保返回完整的原始机器码，不做任何处理
    return machine_id + disk_serial

def generate_activation_code(machine_id):
    """生成激活码 - 两端使用完全相同的算法"""
    if not machine_id:
        return "无效的机器码"
    
    # 标准化处理：统一转为字符串，仅保留字母数字
    machine_id = ''.join(c for c in str(machine_id) if c in string.ascii_letters + string.digits)
    
    # 使用确定性种子
    random.seed(f"MS_ACTIVATION_{machine_id}")
    
    # 生成16位固定长度激活码
    activation_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=16))
    return activation_code

def generate_reset_code(machine_id):
    """生成重置码"""
    # 标准化处理
    machine_id = ''.join(c for c in str(machine_id) if c in string.ascii_letters + string.digits)
    
    # 使用不同的种子与激活码区分
    random.seed(f"MS_RESET_{machine_id}")
    
    # 生成10位重置码
    reset_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
    return reset_code

def build():
    """构建MS卸载助手及相关工具"""
    # 创建输出目录
    output_dir = "dist"
    if os.path.exists(output_dir):
        try:
            shutil.rmtree(output_dir)
        except PermissionError:
            print("错误: 无法删除dist目录，因为一些文件正在被占用。")
            print("请关闭所有可能正在运行的MS卸载助手程序，然后重试。")
            print("或者手动删除dist目录后再次运行此脚本。")
            return
        except Exception as e:
            print(f"删除dist目录时出错: {e}")
            return
    os.makedirs(output_dir)
    
    # 使用PyInstaller打包主程序
    print("使用PyInstaller打包主程序...")
    cmd = [
        sys.executable,  # 当前Python解释器路径
        "-m", "PyInstaller",
        "--name=MS卸载助手",
        "--onefile",
        "--windowed",
        "--clean",
        "--noconfirm",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy",
        "--exclude-module=scipy",
        "--exclude-module=pandas",
        "--exclude-module=pytest",
        "ms_uninstaller.py"
    ]
    
    try:
        subprocess.run(cmd, check=True)
        # 移动生成的exe到输出目录
        if os.path.exists(os.path.join("dist", "MS卸载助手.exe")):
            shutil.move(os.path.join("dist", "MS卸载助手.exe"), 
                      os.path.join(output_dir, "MS卸载助手.exe"))
    except subprocess.CalledProcessError as e:
        print(f"打包主程序失败: {e}")
        return
    except Exception as e:
        print(f"移动主程序文件失败: {e}")
    
    # 打包激活工具
    print("使用PyInstaller打包激活工具...")
    cmd_activation = [
        sys.executable,
        "-m", "PyInstaller",
        "--name=激活工具",
        "--onefile",
        "--windowed",
        "--clean",
        "--noconfirm",
        "激活工具.py"
    ]
    
    try:
        subprocess.run(cmd_activation, check=True)
        # 移动生成的exe到输出目录
        if os.path.exists(os.path.join("dist", "激活工具.exe")):
            shutil.move(os.path.join("dist", "激活工具.exe"), 
                      os.path.join(output_dir, "激活工具.exe"))
    except subprocess.CalledProcessError as e:
        print(f"打包激活工具失败: {e}")
    except Exception as e:
        print(f"移动激活工具文件失败: {e}")
    
    # 打包重置码生成器
    print("使用PyInstaller打包重置码生成器...")
    cmd_reset = [
        sys.executable,
        "-m", "PyInstaller",
        "--name=重置码生成器",
        "--onefile",
        "--windowed",
        "--clean",
        "--noconfirm",
        "重置码生成器.py"
    ]
    
    try:
        subprocess.run(cmd_reset, check=True)
        # 移动生成的exe到输出目录
        if os.path.exists(os.path.join("dist", "重置码生成器.exe")):
            shutil.move(os.path.join("dist", "重置码生成器.exe"), 
                      os.path.join(output_dir, "重置码生成器.exe"))
    except subprocess.CalledProcessError as e:
        print(f"打包重置码生成器失败: {e}")
    except Exception as e:
        print(f"移动重置码生成器文件失败: {e}")
    
    # 创建requirements.txt（如果不存在）
    if not os.path.exists("requirements.txt"):
        with open("requirements.txt", "w") as f:
            f.write("pywin32>=300\npywinauto>=0.6.8\npyautogui>=0.9.53\n")
        print("已创建 requirements.txt 文件")
    
    # 复制requirements.txt到输出目录
    if os.path.exists("requirements.txt"):
        try:
            shutil.copy("requirements.txt", os.path.join(output_dir, "requirements.txt"))
            print("已复制 requirements.txt 到输出目录")
        except Exception as e:
            print(f"复制 requirements.txt 失败: {e}")
    
    # 创建说明文件
    print("创建README.txt文件...")
    readme_text = """MS卸载助手 - 使用说明
=====================

文件说明：
- MS卸载助手.exe       主程序，用于卸载Materials Studio
- 激活工具.exe         激活工具，用于生成激活码
- 重置码生成器.exe      重置工具，用于生成重置码
- requirements.txt    Python依赖包列表（仅开发使用）

使用方法：
1. 以管理员身份运行MS卸载助手.exe
2. 获取机器码后，使用激活工具生成激活码
3. 输入激活码并开始卸载过程
4. 如需再次使用，请使用重置码生成器生成重置码

如遇问题，请联系管理员或技术支持。
"""
    
    with open(os.path.join(output_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_text)
    
    print(f"构建完成！输出目录: {os.path.abspath(output_dir)}")

# 标记程序已被使用
def mark_as_used():
    config_path = get_config_path()
    config = {'used': True, 'timestamp': datetime.now().isoformat()}
    with open(config_path, 'w') as f:
        json.dump(config, f)

# 验证程序是否已使用过
def check_if_used():
    config_path = get_config_path()
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
                if config.get('used', False):
                    return True
        except:
            pass
    return False

def reset_used_state(reset_code):
    machine_id = get_machine_id()
    expected_reset_code = generate_reset_code(machine_id)
    
    # 验证重置码
    if reset_code.upper().strip() == expected_reset_code:
        # 删除配置文件以重置状态
        config_path = get_config_path()
        if os.path.exists(config_path):
            os.remove(config_path)
        return True
    return False

# 重置对话框
def show_reset_dialog():
    """显示重置对话框"""
    # 导入ms_uninstaller模块中的函数
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from ms_uninstaller import get_machine_id, generate_reset_code
    
    machine_id = get_machine_id()
    reset_code = generate_reset_code(machine_id)
    
    print(f"\n机器码: {machine_id[:12]}")
    print(f"重置码: {reset_code}")
    print("\n请使用此重置码重置程序使用状态。")

def validate_activation_code(code, machine_id):
    # 添加调试输出
    print(f"输入的机器码: {machine_id}")
    print(f"清理后的机器码: {''.join(c for c in str(machine_id) if c in string.ascii_letters + string.digits)}")
    print(f"输入的激活码: {code}")
    
    # ... 验证代码 ...
    
    # 生成期望的激活码并输出
    expected_code = generate_activation_code(machine_id)
    print(f"期望的激活码: {expected_code}")
    
    return code.upper().strip() == expected_code

def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--reset":
            show_reset_dialog()
            return
    
    # 默认执行构建
    build()

if __name__ == "__main__":
    main() 