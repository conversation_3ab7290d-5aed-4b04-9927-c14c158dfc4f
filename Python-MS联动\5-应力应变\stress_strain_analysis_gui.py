#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
from scipy.signal import savgol_filter
from scipy.interpolate import make_interp_spline, splev, splrep
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib as mpl
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.ticker as ticker

# 设置绘图参数，使图表达到期刊要求
plt.rcParams['font.family'] = 'Arial'
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.5
plt.rcParams['xtick.direction'] = 'in'
plt.rcParams['ytick.direction'] = 'in'
plt.rcParams['ytick.right'] = True
plt.rcParams['xtick.top'] = True
plt.rcParams['xtick.major.width'] = 1.5
plt.rcParams['ytick.major.width'] = 1.5
plt.rcParams['xtick.major.size'] = 5
plt.rcParams['ytick.major.size'] = 5
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['figure.dpi'] = 100

# 定义高质量的颜色方案
COLORS = ['#0077BB', '#33BBEE', '#009988', '#EE7733', '#CC3311', '#EE3377', '#BBBBBB']
MARKERS = ['o', 's', '^', 'd', 'v', '<', '>']


def smooth_data(x, y, method='savgol', **kwargs):
    """
    平滑数据点
    
    参数:
    x, y: 原始数据
    method: 平滑方法，支持 'savgol'(Savitzky-Golay滤波), 'spline'(样条插值), 'moving_avg'(移动平均)
    kwargs: 额外参数
        - savgol: window_length(默认11), polyorder(默认3)
        - spline: k(默认3，样条阶数), n(默认100，插值点数)
        - moving_avg: window(默认5，窗口大小)
    
    返回:
    x_smooth, y_smooth: 平滑后的数据
    """
    if method == 'savgol':
        window_length = kwargs.get('window_length', 11)
        polyorder = kwargs.get('polyorder', 3)
        
        if len(y) < window_length:
            window_length = len(y) - 1 if len(y) % 2 == 0 else len(y)
        
        if window_length < polyorder:
            polyorder = window_length - 1
            
        # 确保window_length是奇数
        if window_length % 2 == 0:
            window_length -= 1
            
        y_smooth = savgol_filter(y, window_length, polyorder)
        return x, y_smooth
    
    elif method == 'spline':
        k = kwargs.get('k', 3)  # 样条阶数
        n = kwargs.get('n', 100)  # 插值点数
        
        # 如果数据点太少，降低样条阶数
        if len(x) <= k:
            k = len(x) - 1
            
        # 创建样条
        spl = make_interp_spline(x, y, k=k)
        x_smooth = np.linspace(min(x), max(x), n)
        y_smooth = spl(x_smooth)
        return x_smooth, y_smooth
    
    elif method == 'moving_avg':
        window = kwargs.get('window', 5)
        
        # 如果数据点太少，调整窗口大小
        if len(y) < window:
            window = len(y)
            
        y_smooth = np.convolve(y, np.ones(window)/window, mode='same')
        
        # 处理边缘效应
        half_win = window // 2
        y_smooth[:half_win] = y[:half_win]
        y_smooth[-half_win:] = y[-half_win:]
        
        return x, y_smooth
    
    else:
        # 默认返回原始数据
        return x, y


def linear_fit(x, y, initial_points=7):
    """对数据的弹性区进行线性拟合，计算弹性模量"""
    def linear(x, k, b):
        return k * x + b
    
    # 使用前initial_points个点进行拟合
    x_fit = x[:initial_points]
    y_fit = y[:initial_points]
    
    # 确保有足够的点进行拟合
    if len(x_fit) < 2:
        return 0, 0, 0
    
    # 检查数据的单调性，应力应变曲线的弹性区应该近似单调
    if len(x_fit) > 2:
        is_monotonic = all(y_fit[i] <= y_fit[i+1] for i in range(len(y_fit)-1)) or \
                       all(y_fit[i] >= y_fit[i+1] for i in range(len(y_fit)-1))
        if not is_monotonic:
            # 尝试减少点数直到找到单调区域
            for points in range(initial_points-1, 1, -1):
                x_fit = x[:points]
                y_fit = y[:points]
                is_monotonic = all(y_fit[i] <= y_fit[i+1] for i in range(len(y_fit)-1)) or \
                              all(y_fit[i] >= y_fit[i+1] for i in range(len(y_fit)-1))
                if is_monotonic:
                    break
    
    # 线性拟合
    try:
        popt, pcov = curve_fit(linear, x_fit, y_fit)
        k, b = popt
        r_squared = np.corrcoef(y_fit, linear(x_fit, k, b))[0, 1]**2
    except:
        return 0, 0, 0
    
    # 检查弹性模量的合理性
    if k < 0 or abs(k) < 0.01 or abs(k) > 1000:  # 弹性模量应为正值且在合理范围内
        return 0, 0, 0
    
    return k, b, r_squared


def nonlinear_fit(x, y, model='ramberg-osgood', **kwargs):
    """
    对应力-应变数据进行非线性拟合
    
    参数:
    x, y: 应变和应力数据
    model: 模型类型，支持 'ramberg-osgood', 'power-law', 'bilinear'
    kwargs: 模型参数的初始猜测值
    
    返回:
    params: 拟合参数
    r_squared: 拟合优度
    y_fit: 拟合曲线
    """
    if model == 'ramberg-osgood':
        # Ramberg-Osgood 模型: ε = σ/E + α(σ/E)^n
        def ramberg_osgood(x, E, alpha, n):
            return x/E + alpha * (x/E)**n
        
        E_init = kwargs.get('E', 200)  # 初始弹性模量猜测
        alpha_init = kwargs.get('alpha', 0.5)  # 初始alpha猜测
        n_init = kwargs.get('n', 5)  # 初始n猜测
        
        # 拟合
        try:
            popt, pcov = curve_fit(ramberg_osgood, y, x, p0=[E_init, alpha_init, n_init], 
                                  bounds=([1, 0.001, 1], [1000, 10, 20]))
            E, alpha, n = popt
            
            # 计算拟合曲线
            y_fit = np.linspace(min(y), max(y), 100)
            x_fit = ramberg_osgood(y_fit, E, alpha, n)
            
            # 计算R^2
            x_pred = ramberg_osgood(y, E, alpha, n)
            ss_tot = np.sum((x - np.mean(x))**2)
            ss_res = np.sum((x - x_pred)**2)
            r_squared = 1 - (ss_res / ss_tot)
            
            return {'E': E, 'alpha': alpha, 'n': n}, r_squared, {'strain': x_fit, 'stress': y_fit}
        except:
            return None, 0, None
    
    elif model == 'power-law':
        # Power-law 模型: σ = K * ε^n
        def power_law(x, K, n):
            return K * (x ** n)
        
        K_init = kwargs.get('K', 100)  # 初始强度系数猜测
        n_init = kwargs.get('n', 0.2)  # 初始应变硬化指数猜测
        
        # 拟合
        try:
            popt, pcov = curve_fit(power_law, x, y, p0=[K_init, n_init])
            K, n = popt
            
            # 计算拟合曲线
            x_fit = np.linspace(min(x), max(x), 100)
            y_fit = power_law(x_fit, K, n)
            
            # 计算R^2
            y_pred = power_law(x, K, n)
            ss_tot = np.sum((y - np.mean(y))**2)
            ss_res = np.sum((y - y_pred)**2)
            r_squared = 1 - (ss_res / ss_tot)
            
            return {'K': K, 'n': n}, r_squared, {'strain': x_fit, 'stress': y_fit}
        except:
            return None, 0, None
    
    elif model == 'bilinear':
        # 双线性模型：弹性区和塑性区分别线性拟合
        def bilinear(x, E1, E2, yield_point):
            result = np.zeros_like(x)
            for i, xi in enumerate(x):
                if xi <= yield_point:
                    result[i] = E1 * xi
                else:
                    result[i] = E1 * yield_point + E2 * (xi - yield_point)
            return result
        
        E1_init = kwargs.get('E1', 200)  # 初始弹性模量猜测
        E2_init = kwargs.get('E2', 20)   # 初始塑性模量猜测
        yield_init = kwargs.get('yield_point', 0.02)  # 初始屈服点猜测
        
        # 拟合
        try:
            popt, pcov = curve_fit(bilinear, x, y, p0=[E1_init, E2_init, yield_init],
                                  bounds=([1, 0.1, 0.001], [1000, 100, 0.5]))
            E1, E2, yield_point = popt
            
            # 计算拟合曲线
            x_fit = np.linspace(min(x), max(x), 100)
            y_fit = bilinear(x_fit, E1, E2, yield_point)
            
            # 计算R^2
            y_pred = bilinear(x, E1, E2, yield_point)
            ss_tot = np.sum((y - np.mean(y))**2)
            ss_res = np.sum((y - y_pred)**2)
            r_squared = 1 - (ss_res / ss_tot)
            
            return {'E1': E1, 'E2': E2, 'yield_point': yield_point}, r_squared, {'strain': x_fit, 'stress': y_fit}
        except:
            return None, 0, None
    
    else:
        return None, 0, None


def filter_outliers(x, y, method='zscore', threshold=3.0):
    """
    去除异常点
    
    参数:
    x, y: 原始数据
    method: 'zscore' (Z-score方法) 或 'iqr' (四分位距法)
    threshold: 判定为异常的阈值 (Z-score法的标准差倍数或IQR法的IQR倍数)
    
    返回:
    x_clean, y_clean: 去除异常点后的数据
    """
    if method == 'zscore':
        # Z-score方法
        z_scores = np.abs((y - np.mean(y)) / np.std(y))
        mask = z_scores < threshold
        return x[mask], y[mask]
    
    elif method == 'iqr':
        # 四分位距法
        q1 = np.percentile(y, 25)
        q3 = np.percentile(y, 75)
        iqr = q3 - q1
        lower_bound = q1 - threshold * iqr
        upper_bound = q3 + threshold * iqr
        mask = (y >= lower_bound) & (y <= upper_bound)
        return x[mask], y[mask]
    
    else:
        # 默认返回原始数据
        return x, y


class StressStrainAnalyzerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("应力应变数据分析工具")
        self.root.geometry("1200x800")
        
        # 数据存储
        self.data = None
        self.results = None
        
        # 设置参数
        self.smooth_method = tk.StringVar(value='savgol')
        self.fit_model = tk.StringVar(value='linear')
        self.outlier_method = tk.StringVar(value='none')
        self.initial_points = tk.IntVar(value=7)
        self.dpi = tk.IntVar(value=300)
        self.figure_width = tk.DoubleVar(value=8.0)
        self.figure_height = tk.DoubleVar(value=6.0)
        self.line_width = tk.DoubleVar(value=2.0)
        self.marker_size = tk.DoubleVar(value=6.0)
        self.font_size = tk.IntVar(value=12)
        self.axis_width = tk.DoubleVar(value=1.5)
        self.grid_on = tk.BooleanVar(value=True)
        self.publication_quality = tk.BooleanVar(value=True)
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建左右分割
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self.options_notebook = ttk.Notebook(left_frame)
        self.options_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 数据输入选项卡
        input_frame = ttk.Frame(self.options_notebook)
        self.options_notebook.add(input_frame, text="数据输入")
        
        # 处理设置选项卡
        process_frame = ttk.Frame(self.options_notebook)
        self.options_notebook.add(process_frame, text="数据处理")
        
        # 图表设置选项卡
        plot_settings_frame = ttk.Frame(self.options_notebook)
        self.options_notebook.add(plot_settings_frame, text="图表设置")
        
        # 创建表格（数据输入选项卡）
        self.create_data_table(input_frame)
        
        # 创建数据处理选项（数据处理选项卡）
        self.create_processing_options(process_frame)
        
        # 创建图表设置选项（图表设置选项卡）
        self.create_plot_settings(plot_settings_frame)
        
        # 创建按钮区域
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="导入数据", command=self.import_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="分析数据", command=self.analyze_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="导出结果", command=self.export_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空数据", command=self.clear_data).pack(side=tk.LEFT, padx=5)
        
        # 创建结果显示区域（右侧）
        result_frame = ttk.LabelFrame(right_frame, text="分析结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(result_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建图表选项卡
        self.plot_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.plot_frame, text="应力-应变曲线")
        
        self.elastic_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.elastic_frame, text="弹性区拟合")
        
        self.nonlinear_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.nonlinear_frame, text="非线性拟合")
        
        # 创建文本结果选项卡
        self.text_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.text_frame, text="文本结果")
        
        # 创建文本结果区域
        self.result_text = tk.Text(self.text_frame, wrap=tk.WORD)
        self.result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_processing_options(self, parent):
        """创建数据处理选项"""
        # 数据预处理选项
        preprocess_frame = ttk.LabelFrame(parent, text="数据预处理")
        preprocess_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 异常值过滤
        ttk.Label(preprocess_frame, text="异常值过滤:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        outlier_combo = ttk.Combobox(preprocess_frame, textvariable=self.outlier_method, 
                                   values=["none", "zscore", "iqr"], width=10)
        outlier_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(preprocess_frame, text="阈值:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.outlier_threshold = tk.DoubleVar(value=3.0)
        ttk.Entry(preprocess_frame, textvariable=self.outlier_threshold, width=5).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # 数据平滑
        ttk.Label(preprocess_frame, text="数据平滑:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        smooth_combo = ttk.Combobox(preprocess_frame, textvariable=self.smooth_method,
                                values=["none", "savgol", "spline", "moving_avg"], width=10)
        smooth_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 平滑参数
        ttk.Label(preprocess_frame, text="窗口大小/阶数:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        self.smooth_window = tk.IntVar(value=11)
        ttk.Entry(preprocess_frame, textvariable=self.smooth_window, width=5).grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)
        
        # 拟合选项
        fit_frame = ttk.LabelFrame(parent, text="数据拟合")
        fit_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 弹性模量计算
        ttk.Label(fit_frame, text="弹性区拟合:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(fit_frame, text="使用前N个点:").grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(fit_frame, textvariable=self.initial_points, width=5).grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # 非线性拟合模型
        ttk.Label(fit_frame, text="非线性拟合:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        fit_combo = ttk.Combobox(fit_frame, textvariable=self.fit_model, 
                               values=["linear", "ramberg-osgood", "power-law", "bilinear"], width=15)
        fit_combo.grid(row=1, column=1, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # 分析方向选择
        direction_frame = ttk.LabelFrame(parent, text="分析方向")
        direction_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.analyze_xx = tk.BooleanVar(value=True)
        self.analyze_yy = tk.BooleanVar(value=True)
        self.analyze_zz = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(direction_frame, text="XX 方向", variable=self.analyze_xx).grid(row=0, column=0, padx=5, pady=5)
        ttk.Checkbutton(direction_frame, text="YY 方向", variable=self.analyze_yy).grid(row=0, column=1, padx=5, pady=5)
        ttk.Checkbutton(direction_frame, text="ZZ 方向", variable=self.analyze_zz).grid(row=0, column=2, padx=5, pady=5)
    
    def create_plot_settings(self, parent):
        """创建图表设置选项"""
        # 图表尺寸设置
        size_frame = ttk.LabelFrame(parent, text="图表尺寸")
        size_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(size_frame, text="宽度 (英寸):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(size_frame, textvariable=self.figure_width, width=5).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(size_frame, text="高度 (英寸):").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(size_frame, textvariable=self.figure_height, width=5).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(size_frame, text="DPI:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(size_frame, textvariable=self.dpi, width=5).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 线条和标记设置
        style_frame = ttk.LabelFrame(parent, text="线条和标记")
        style_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(style_frame, text="线宽:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(style_frame, textvariable=self.line_width, width=5).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(style_frame, text="标记大小:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(style_frame, textvariable=self.marker_size, width=5).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # 字体设置
        font_frame = ttk.LabelFrame(parent, text="字体设置")
        font_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(font_frame, text="字体大小:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(font_frame, textvariable=self.font_size, width=5).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(font_frame, text="坐标轴线宽:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(font_frame, textvariable=self.axis_width, width=5).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # 其他选项
        other_frame = ttk.LabelFrame(parent, text="其他选项")
        other_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Checkbutton(other_frame, text="显示网格", variable=self.grid_on).grid(row=0, column=0, padx=5, pady=5)
        ttk.Checkbutton(other_frame, text="期刊质量模式", variable=self.publication_quality).grid(row=0, column=1, padx=5, pady=5)
        
        # 标签设置
        label_frame = ttk.LabelFrame(parent, text="标签设置")
        label_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(label_frame, text="X轴标签:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.x_label = tk.StringVar(value="Strain")
        ttk.Entry(label_frame, textvariable=self.x_label, width=20).grid(row=0, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        
        ttk.Label(label_frame, text="Y轴标签:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.y_label = tk.StringVar(value="Stress (GPa)")
        ttk.Entry(label_frame, textvariable=self.y_label, width=20).grid(row=1, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
        
        ttk.Label(label_frame, text="图表标题:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.plot_title = tk.StringVar(value="Stress-Strain Curves")
        ttk.Entry(label_frame, textvariable=self.plot_title, width=20).grid(row=2, column=1, sticky=tk.W+tk.E, padx=5, pady=5)
    
    def create_data_table(self, parent):
        # 创建表格框架
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建滚动条
        yscroll = ttk.Scrollbar(table_frame, orient=tk.VERTICAL)
        yscroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        xscroll = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        xscroll.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建表格
        columns = ("frame", "strain", "stress_xx", "stress_yy", "stress_zz")
        self.table = ttk.Treeview(table_frame, columns=columns, show="headings",
                                 yscrollcommand=yscroll.set, xscrollcommand=xscroll.set)
        
        # 设置列宽和标题
        self.table.column("frame", width=150)
        self.table.column("strain", width=100)
        self.table.column("stress_xx", width=120)
        self.table.column("stress_yy", width=120)
        self.table.column("stress_zz", width=120)
        
        self.table.heading("frame", text="Frame document")
        self.table.heading("strain", text="Strain")
        self.table.heading("stress_xx", text="Stress XX (GPa)")
        self.table.heading("stress_yy", text="Stress YY (GPa)")
        self.table.heading("stress_zz", text="Stress ZZ (GPa)")
        
        # 放置表格
        self.table.pack(fill=tk.BOTH, expand=True)
        
        # 连接滚动条
        yscroll.config(command=self.table.yview)
        xscroll.config(command=self.table.xview)
        
        # 添加右键菜单
        self.context_menu = tk.Menu(self.table, tearoff=0)
        self.context_menu.add_command(label="添加行", command=self.add_row)
        self.context_menu.add_command(label="删除行", command=self.delete_row)
        self.table.bind("<Button-3>", self.show_context_menu)
        
        # 双击事件，用于编辑单元格
        self.table.bind("<Double-1>", self.edit_cell)
        
    def show_context_menu(self, event):
        """显示右键菜单"""
        self.context_menu.post(event.x_root, event.y_root)
        
    def add_row(self):
        """添加新行"""
        # 获取当前选中的行
        selected = self.table.selection()
        if selected:
            # 在选中行之后插入
            idx = self.table.index(selected[0]) + 1
        else:
            # 如果没有选中，则在最后添加
            idx = len(self.table.get_children())
        
        # 插入新行
        values = [f"strain_analysis_{idx}", f"{idx*0.02:.2f}", "0.0", "0.0", "0.0"]
        self.table.insert("", idx, values=values)
        
    def delete_row(self):
        """删除选中行"""
        selected = self.table.selection()
        if selected:
            for item in selected:
                self.table.delete(item)
                
    def edit_cell(self, event):
        """编辑单元格内容"""
        # 获取点击的单元格
        cell = self.table.identify("cell", event.x, event.y)
        if not cell:
            return
        
        column = int(cell[1])
        item = cell[0]
        
        # 如果是第一列（帧名称），不可编辑
        if column == 0:
            return
        
        # 获取当前值
        current_value = self.table.item(item, "values")[column]
        
        # 创建编辑框
        edit_frame = tk.Frame(self.table)
        edit_entry = tk.Entry(edit_frame)
        edit_entry.insert(0, current_value)
        edit_entry.pack()
        edit_entry.select_range(0, tk.END)
        
        # 计算编辑框位置
        x, y, width, height = self.table.bbox(item, column)
        edit_frame.place(x=x, y=y, width=width, height=height)
        
        # 焦点给编辑框
        edit_entry.focus_set()
        
        def save_edit(event=None):
            """保存编辑结果"""
            # 获取新值
            new_value = edit_entry.get()
            
            # 获取当前行的所有值
            current_values = list(self.table.item(item, "values"))
            
            # 更新对应列的值
            current_values[column] = new_value
            
            # 更新表格
            self.table.item(item, values=current_values)
            
            # 移除编辑框
            edit_frame.destroy()
            
        # 绑定事件
        edit_entry.bind("<Return>", save_edit)
        edit_entry.bind("<FocusOut>", save_edit)
        
    def import_data(self):
        """导入数据文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[
                ("所有支持的文件", "*.csv *.txt *.xls *.xlsx"),
                ("CSV文件", "*.csv"),
                ("文本文件", "*.txt"),
                ("Excel文件", "*.xls *.xlsx"),
                ("所有文件", "*.*")
            ]
        )
        
        if not file_path:
            return
        
        try:
            # 读取文件
            if file_path.endswith(('.csv', '.txt')):
                data = pd.read_csv(file_path, sep=None, engine='python')
            elif file_path.endswith(('.xls', '.xlsx')):
                data = pd.read_excel(file_path)
            else:
                messagebox.showerror("错误", "不支持的文件格式")
                return
            
            # 检查必要的列是否存在
            required_columns = ["Strain", "Stress XX (GPa)", "Stress YY (GPa)", "Stress ZZ (GPa)"]
            for col in required_columns:
                if col not in data.columns:
                    messagebox.showerror("错误", f"缺少必要的列: {col}")
                    return
            
            # 清空表格
            for item in self.table.get_children():
                self.table.delete(item)
            
            # 填充表格
            for i, row in data.iterrows():
                frame_name = row.get("Frame document", f"strain_analysis_{i}")
                values = [
                    frame_name, 
                    row["Strain"], 
                    row["Stress XX (GPa)"],
                    row["Stress YY (GPa)"],
                    row["Stress ZZ (GPa)"]
                ]
                self.table.insert("", "end", values=values)
            
            # 存储数据
            self.data = data
            
            messagebox.showinfo("成功", "数据导入成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"导入数据时出错:\n{str(e)}")
            
    def table_to_dataframe(self):
        """将表格数据转换为DataFrame"""
        data = []
        for item in self.table.get_children():
            values = self.table.item(item, "values")
            data.append({
                "Frame document": values[0],
                "Strain": float(values[1]),
                "Stress XX (GPa)": float(values[2]),
                "Stress YY (GPa)": float(values[3]),
                "Stress ZZ (GPa)": float(values[4])
            })
        
        return pd.DataFrame(data)
    
    def clear_data(self):
        """清空表格数据"""
        for item in self.table.get_children():
            self.table.delete(item)
        self.data = None
        
        # 清空图表和结果
        for widget in self.plot_frame.winfo_children():
            widget.destroy()
        for widget in self.elastic_frame.winfo_children():
            widget.destroy()
        self.result_text.delete(1.0, tk.END)
    
    def analyze_data(self):
        """分析数据"""
        # 检查是否有数据
        if not self.table.get_children():
            messagebox.showerror("错误", "没有数据可分析")
            return
        
        try:
            # 从表格获取数据
            self.data = self.table_to_dataframe()
            
            # 确保数据排序
            self.data = self.data.sort_values(by="Strain")
            
            # 分析数据
            self.results = self.analyze_stress_strain_data(self.data)
            
            # 更新文本结果
            self.update_text_results()
            
            messagebox.showinfo("成功", "数据分析完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"分析数据时出错:\n{str(e)}")
    
    def analyze_stress_strain_data(self, data):
        """分析应力应变数据"""
        # 获取基础数据
        strain = data["Strain"].values
        stress_xx = data["Stress XX (GPa)"].values if self.analyze_xx.get() else None
        stress_yy = data["Stress YY (GPa)"].values if self.analyze_yy.get() else None
        stress_zz = data["Stress ZZ (GPa)"].values if self.analyze_zz.get() else None
        
        results = {'data': data}
        
        # 应用数据预处理
        # 1. 异常值过滤
        if self.outlier_method.get() != 'none':
            if stress_xx is not None:
                strain_xx, stress_xx = filter_outliers(strain, stress_xx, 
                                                     method=self.outlier_method.get(), 
                                                     threshold=self.outlier_threshold.get())
            if stress_yy is not None:
                strain_yy, stress_yy = filter_outliers(strain, stress_yy, 
                                                     method=self.outlier_method.get(), 
                                                     threshold=self.outlier_threshold.get())
            if stress_zz is not None:
                strain_zz, stress_zz = filter_outliers(strain, stress_zz, 
                                                     method=self.outlier_method.get(), 
                                                     threshold=self.outlier_threshold.get())
        else:
            strain_xx, strain_yy, strain_zz = strain, strain, strain
        
        # 2. 数据平滑
        if self.smooth_method.get() != 'none':
            smooth_params = {'window_length': self.smooth_window.get(),
                            'polyorder': 3,
                            'window': self.smooth_window.get(),
                            'k': 3,
                            'n': 100}
            
            strain_xx_smooth, stress_xx_smooth = (smooth_data(strain_xx, stress_xx, 
                                                            method=self.smooth_method.get(), 
                                                            **smooth_params) 
                                                if stress_xx is not None else (None, None))
            
            strain_yy_smooth, stress_yy_smooth = (smooth_data(strain_yy, stress_yy, 
                                                            method=self.smooth_method.get(), 
                                                            **smooth_params)
                                                if stress_yy is not None else (None, None))
            
            strain_zz_smooth, stress_zz_smooth = (smooth_data(strain_zz, stress_zz, 
                                                            method=self.smooth_method.get(), 
                                                            **smooth_params)
                                                if stress_zz is not None else (None, None))
        else:
            strain_xx_smooth, stress_xx_smooth = strain_xx, stress_xx
            strain_yy_smooth, stress_yy_smooth = strain_yy, stress_yy
            strain_zz_smooth, stress_zz_smooth = strain_zz, stress_zz
        
        # 计算弹性模量
        elastic_results = {}
        if stress_zz is not None:
            k, b, r_squared = linear_fit(strain_zz, stress_zz, self.initial_points.get())
            elastic_results['zz'] = {
                'elastic_modulus': k,
                'r_squared': r_squared,
                'intercept': b
            }
        
        if stress_xx is not None:
            k, b, r_squared = linear_fit(strain_xx, stress_xx, self.initial_points.get())
            elastic_results['xx'] = {
                'elastic_modulus': k,
                'r_squared': r_squared,
                'intercept': b
            }
            
        if stress_yy is not None:
            k, b, r_squared = linear_fit(strain_yy, stress_yy, self.initial_points.get())
            elastic_results['yy'] = {
                'elastic_modulus': k,
                'r_squared': r_squared,
                'intercept': b
            }
        
        results['elastic'] = elastic_results
        
        # 非线性拟合
        nl_results = {}
        model = self.fit_model.get()
        if model != 'linear':
            if stress_zz is not None:
                params, r2, fit_data = nonlinear_fit(strain_zz, stress_zz, model=model)
                if params:
                    nl_results['zz'] = {
                        'params': params,
                        'r_squared': r2,
                        'fit_data': fit_data
                    }
            
            if stress_xx is not None:
                params, r2, fit_data = nonlinear_fit(strain_xx, stress_xx, model=model)
                if params:
                    nl_results['xx'] = {
                        'params': params,
                        'r_squared': r2,
                        'fit_data': fit_data
                    }
                
            if stress_yy is not None:
                params, r2, fit_data = nonlinear_fit(strain_yy, stress_yy, model=model)
                if params:
                    nl_results['yy'] = {
                        'params': params,
                        'r_squared': r2,
                        'fit_data': fit_data
                    }
        
        results['nonlinear'] = nl_results
        
        # 查找屈服点（应力达到最大值后开始下降的点）
        yield_results = {}
        if stress_zz is not None:
            zz_max_idx = np.argmax(stress_zz)
            yield_results['zz'] = {
                'yield_strain': strain_zz[zz_max_idx],
                'yield_stress': stress_zz[zz_max_idx],
                'max_stress': np.max(stress_zz)
            }
            
        if stress_xx is not None:
            xx_max_idx = np.argmax(stress_xx)
            yield_results['xx'] = {
                'yield_strain': strain_xx[xx_max_idx],
                'yield_stress': stress_xx[xx_max_idx],
                'max_stress': np.max(stress_xx)
            }
            
        if stress_yy is not None:
            yy_max_idx = np.argmax(stress_yy)
            yield_results['yy'] = {
                'yield_strain': strain_yy[yy_max_idx],
                'yield_stress': stress_yy[yy_max_idx],
                'max_stress': np.max(stress_yy)
            }
        
        results['yield'] = yield_results
        
        # 存储处理后的数据
        results['processed'] = {
            'xx': {'strain': strain_xx_smooth, 'stress': stress_xx_smooth} if stress_xx is not None else None,
            'yy': {'strain': strain_yy_smooth, 'stress': stress_yy_smooth} if stress_yy is not None else None,
            'zz': {'strain': strain_zz_smooth, 'stress': stress_zz_smooth} if stress_zz is not None else None
        }
        
        # 绘制曲线图
        self.plot_stress_strain_curve(results)
        
        # 绘制弹性区拟合图
        self.plot_elastic_region(results)
        
        # 绘制非线性拟合图
        if model != 'linear':
            self.plot_nonlinear_fit(results)
        
        return results
    
    def plot_stress_strain_curve(self, results):
        """绘制应力-应变曲线"""
        # 清空旧图
        for widget in self.plot_frame.winfo_children():
            widget.destroy()
        
        # 创建图形
        fig = Figure(figsize=(self.figure_width.get(), self.figure_height.get()), dpi=self.dpi.get())
        ax = fig.add_subplot(111)
        
        # 应用期刊质量设置
        if self.publication_quality.get():
            self.apply_publication_style(fig, ax)
        
        # 绘制三个方向的应力-应变曲线
        marker_size = self.marker_size.get()
        line_width = self.line_width.get()
        
        # 获取处理后的数据
        processed = results['processed']
        
        # 绘制XX方向
        if processed['xx'] is not None:
            strain_xx = processed['xx']['strain']
            stress_xx = processed['xx']['stress']
            ax.plot(strain_xx, stress_xx, marker=MARKERS[0], color=COLORS[0], 
                    label=f'Stress XX', markersize=marker_size, linewidth=line_width)
        
        # 绘制YY方向
        if processed['yy'] is not None:
            strain_yy = processed['yy']['strain'] 
            stress_yy = processed['yy']['stress']
            ax.plot(strain_yy, stress_yy, marker=MARKERS[1], color=COLORS[1], 
                    label=f'Stress YY', markersize=marker_size, linewidth=line_width)
        
        # 绘制ZZ方向
        if processed['zz'] is not None:
            strain_zz = processed['zz']['strain']
            stress_zz = processed['zz']['stress']
            ax.plot(strain_zz, stress_zz, marker=MARKERS[2], color=COLORS[2], 
                    label=f'Stress ZZ', markersize=marker_size, linewidth=line_width)
        
        # 创建图例，其中包含弹性模量信息
        legend_labels = []
        legend_handles = []
        
        # 获取已绘制线条的图例句柄
        handles, labels = ax.get_legend_handles_labels()
        for h, l in zip(handles, labels):
            legend_handles.append(h)
            
            # 检查是否有对应的弹性模量数据，将弹性模量添加到图例标签中
            if 'xx' in l.lower() and 'xx' in results.get('elastic', {}) and results['elastic']['xx']['elastic_modulus'] > 0:
                E_xx = results['elastic']['xx']['elastic_modulus']
                legend_labels.append(f'{l} (E = {E_xx:.1f} GPa)')
            elif 'yy' in l.lower() and 'yy' in results.get('elastic', {}) and results['elastic']['yy']['elastic_modulus'] > 0:
                E_yy = results['elastic']['yy']['elastic_modulus']
                legend_labels.append(f'{l} (E = {E_yy:.1f} GPa)')
            elif 'zz' in l.lower() and 'zz' in results.get('elastic', {}) and results['elastic']['zz']['elastic_modulus'] > 0:
                E_zz = results['elastic']['zz']['elastic_modulus']
                legend_labels.append(f'{l} (E = {E_zz:.1f} GPa)')
            else:
                legend_labels.append(l)
        
        # 添加网格线
        if self.grid_on.get():
            ax.grid(True, linestyle='--', alpha=0.4, linewidth=0.5)  # 降低网格线的显著性
        
        # 设置坐标轴标签和标题
        ax.set_xlabel(self.x_label.get(), fontsize=self.font_size.get(), fontweight='bold')
        ax.set_ylabel(self.y_label.get(), fontsize=self.font_size.get(), fontweight='bold')
        ax.set_title(self.plot_title.get(), fontsize=self.font_size.get() + 2, fontweight='bold')
        
        # 创建自定义图例，优化布局和大小
        legend = ax.legend(legend_handles, legend_labels, fontsize=self.font_size.get() - 2, 
                         frameon=True, loc='upper right', framealpha=0.9, 
                         edgecolor='#555555', ncol=3)
        legend.get_frame().set_linewidth(0.5)
        
        # 设置坐标轴范围和刻度
        all_strains = []
        all_stresses = []
        
        if processed['xx'] is not None:
            all_strains.extend(processed['xx']['strain'])
            all_stresses.extend(processed['xx']['stress'])
        if processed['yy'] is not None:
            all_strains.extend(processed['yy']['strain'])
            all_stresses.extend(processed['yy']['stress'])
        if processed['zz'] is not None:
            all_strains.extend(processed['zz']['strain'])
            all_stresses.extend(processed['zz']['stress'])
        
        if all_strains and all_stresses:
            xmin, xmax = 0, max(all_strains) * 1.05
            ymin = min(0, min(all_stresses) * 1.1)
            ymax = max(all_stresses) * 1.1
            
            ax.set_xlim(xmin, xmax)
            ax.set_ylim(ymin, ymax)
        
        # 优化图表布局
        fig.tight_layout()
        
        # 在Tkinter窗口中显示图表
        canvas = FigureCanvasTkAgg(fig, master=self.plot_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加工具栏
        from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
        toolbar = NavigationToolbar2Tk(canvas, self.plot_frame)
        toolbar.update()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def plot_elastic_region(self, results):
        """绘制弹性区应力-应变曲线及拟合线"""
        # 清空旧图
        for widget in self.elastic_frame.winfo_children():
            widget.destroy()
        
        # 创建图形
        fig = Figure(figsize=(self.figure_width.get(), self.figure_height.get()), dpi=self.dpi.get())
        ax = fig.add_subplot(111)
        
        # 应用期刊质量设置
        if self.publication_quality.get():
            self.apply_publication_style(fig, ax)
        
        # 获取数据
        processed = results['processed']
        elastic = results['elastic']
        points_used = self.initial_points.get()
        
        markers = MARKERS[:3]
        colors = COLORS[:3]
        
        # 为每个方向绘制弹性区拟合
        for i, direction in enumerate(['zz', 'xx', 'yy']):
            if direction in elastic and processed[direction] is not None:
                # 跳过弹性模量无效的方向
                if elastic[direction]['elastic_modulus'] <= 0:
                    continue
                    
                strain = processed[direction]['strain']
                stress = processed[direction]['stress']
                
                # 确保有数据
                if strain is None or stress is None or len(strain) == 0 or len(stress) == 0:
                    continue
                    
                # 绘制原始数据点
                ax.scatter(strain, stress, color=colors[i], 
                          label=f'Data ({direction.upper()})', s=self.marker_size.get()*10, alpha=0.5)
                
                # 绘制弹性区的数据点（用不同颜色突出显示）
                elastic_points = min(points_used, len(strain))
                if elastic_points > 0:
                    ax.scatter(strain[:elastic_points], stress[:elastic_points], 
                              color='red', marker=markers[i], 
                              label=f'Elastic region ({direction.upper()})', 
                              s=self.marker_size.get()*10, alpha=0.9)
                
                # 获取弹性模量参数
                E = elastic[direction]['elastic_modulus']
                b = elastic[direction]['intercept']
                r2 = elastic[direction]['r_squared']
                
                # 绘制拟合线
                x_range = np.max(strain) * 0.4  # 绘制到40%的应变范围
                x_fit = np.linspace(0, x_range, 100)
                y_fit = E * x_fit + b
                
                # 使用更简洁的标签格式
                ax.plot(x_fit, y_fit, '--', color=colors[i], 
                        label=f'Fit ({direction.upper()}): E = {E:.1f} GPa', 
                        linewidth=self.line_width.get())
        
        # 添加网格线
        if self.grid_on.get():
            ax.grid(True, linestyle='--', alpha=0.5, linewidth=0.5)
        
        # 设置坐标轴标签和标题
        ax.set_xlabel('Strain', fontsize=self.font_size.get(), fontweight='bold')
        ax.set_ylabel('Stress (GPa)', fontsize=self.font_size.get(), fontweight='bold')
        ax.set_title('Elastic Region and Linear Fitting', fontsize=self.font_size.get() + 2, fontweight='bold')
        
        # 添加图例 - 优化布局和大小
        legend = ax.legend(fontsize=self.font_size.get() - 2, frameon=True, 
                          loc='upper left', ncol=2, framealpha=0.9, 
                          edgecolor='#555555')
        legend.get_frame().set_linewidth(0.5)
        
        # 优化图表布局
        fig.tight_layout()
        
        # 在Tkinter窗口中显示图表
        canvas = FigureCanvasTkAgg(fig, master=self.elastic_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加工具栏
        from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
        toolbar = NavigationToolbar2Tk(canvas, self.elastic_frame)
        toolbar.update()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def plot_nonlinear_fit(self, results):
        """绘制非线性拟合曲线"""
        # 清空旧图
        for widget in self.nonlinear_frame.winfo_children():
            widget.destroy()
        
        # 如果没有非线性拟合结果，则返回
        if not results.get('nonlinear'):
            return
        
        # 创建图形
        fig = Figure(figsize=(self.figure_width.get(), self.figure_height.get()), dpi=self.dpi.get())
        ax = fig.add_subplot(111)
        
        # 应用期刊质量设置
        if self.publication_quality.get():
            self.apply_publication_style(fig, ax)
        
        # 获取数据
        processed = results['processed']
        nonlinear = results['nonlinear']
        model_name = self.fit_model.get()
        
        markers = MARKERS[:3]
        colors = COLORS[:3]
        
        # 为每个方向绘制非线性拟合
        for i, direction in enumerate(['zz', 'xx', 'yy']):
            if direction in nonlinear and processed[direction] is not None:
                # 原始数据
                strain = processed[direction]['strain']
                stress = processed[direction]['stress']
                
                # 确保有数据
                if strain is None or stress is None or len(strain) == 0 or len(stress) == 0:
                    continue
                
                # 绘制原始数据点
                ax.scatter(strain, stress, color=colors[i], marker=markers[i],
                          label=f'Data ({direction.upper()})', s=self.marker_size.get()*10, alpha=0.7)
                
                # 获取拟合结果
                fit_data = nonlinear[direction]['fit_data']
                r2 = nonlinear[direction]['r_squared']
                params = nonlinear[direction]['params']
                
                if fit_data is None:
                    continue
                
                # 根据模型类型，构建更简洁的标签
                if model_name == 'ramberg-osgood':
                    param_str = f"E={params['E']:.1f}, α={params['alpha']:.2f}, n={params['n']:.1f}"
                elif model_name == 'power-law':
                    param_str = f"K={params['K']:.1f}, n={params['n']:.2f}"
                elif model_name == 'bilinear':
                    param_str = f"E1={params['E1']:.1f}, E2={params['E2']:.1f}"
                else:
                    param_str = str(params)
                
                # 绘制拟合曲线
                ax.plot(fit_data['strain'], fit_data['stress'], '-', color=colors[i],
                       label=f'{model_name.capitalize()} ({direction.upper()}): {param_str}',
                       linewidth=self.line_width.get())
        
        # 添加网格线
        if self.grid_on.get():
            ax.grid(True, linestyle='--', alpha=0.5, linewidth=0.5)
        
        # 设置坐标轴标签和标题
        ax.set_xlabel('Strain', fontsize=self.font_size.get(), fontweight='bold')
        ax.set_ylabel('Stress (GPa)', fontsize=self.font_size.get(), fontweight='bold')
        ax.set_title(f'{model_name.capitalize()} Model Fitting', fontsize=self.font_size.get() + 2, fontweight='bold')
        
        # 添加图例 - 优化布局和大小
        legend = ax.legend(fontsize=self.font_size.get() - 3, frameon=True, 
                          loc='best', ncol=2, framealpha=0.9, 
                          edgecolor='#555555')
        legend.get_frame().set_linewidth(0.5)
        
        # 优化图表布局
        fig.tight_layout()
        
        # 在Tkinter窗口中显示图表
        canvas = FigureCanvasTkAgg(fig, master=self.nonlinear_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加工具栏
        from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
        toolbar = NavigationToolbar2Tk(canvas, self.nonlinear_frame)
        toolbar.update()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def apply_publication_style(self, fig, ax):
        """应用符合期刊要求的图表样式"""
        # 设置字体和线宽
        font_size = self.font_size.get()
        axis_width = self.axis_width.get()
        
        # 字体设置
        plt.rcParams['font.family'] = 'Arial'
        plt.rcParams['font.size'] = font_size
        
        # 坐标轴设置
        for axis in ['top', 'bottom', 'left', 'right']:
            ax.spines[axis].set_linewidth(axis_width)
            
        # 刻度设置
        ax.tick_params(which='major', width=axis_width, length=axis_width*3, 
                      labelsize=font_size, direction='in', top=True, right=True)
        ax.tick_params(which='minor', width=axis_width*0.8, length=axis_width*1.5, 
                      direction='in', top=True, right=True)
        
        # 启用次要刻度
        ax.xaxis.set_minor_locator(ticker.AutoMinorLocator())
        ax.yaxis.set_minor_locator(ticker.AutoMinorLocator())
        
        # 设置图表背景和边框
        fig.patch.set_facecolor('white')
        ax.set_facecolor('white')
        
        # 调整绘图区域，为图表元素预留足够空间
        fig.subplots_adjust(left=0.15, right=0.95, top=0.92, bottom=0.15)
    
    def update_text_results(self):
        """更新文本结果区域"""
        if not self.results:
            return
        
        # 清空文本区域
        self.result_text.delete(1.0, tk.END)
        
        # 添加标题
        self.result_text.insert(tk.END, "====== 应力应变数据分析报告 ======\n\n")
        
        # 添加基本信息
        data = self.results['data']
        self.result_text.insert(tk.END, f"数据点数: {len(data)}\n")
        self.result_text.insert(tk.END, f"应变范围: {data['Strain'].min():.4f} - {data['Strain'].max():.4f}\n")
        
        # 添加数据处理信息
        self.result_text.insert(tk.END, "\n==== 数据处理设置 ====\n")
        self.result_text.insert(tk.END, f"异常值过滤: {self.outlier_method.get()}\n")
        self.result_text.insert(tk.END, f"数据平滑方法: {self.smooth_method.get()}\n")
        self.result_text.insert(tk.END, f"拟合模型: {self.fit_model.get()}\n\n")
        
        # 添加各方向的弹性模量和屈服信息
        directions = ['XX', 'YY', 'ZZ']
        dir_keys = ['xx', 'yy', 'zz']
        
        for i, (dir_name, dir_key) in enumerate(zip(directions, dir_keys)):
            if dir_key in self.results.get('elastic', {}) and dir_key in self.results.get('yield', {}):
                elastic_data = self.results['elastic'][dir_key]
                yield_data = self.results['yield'][dir_key]
                
                self.result_text.insert(tk.END, f"==== {dir_name} 方向分析结果 ====\n")
                
                # 检查弹性模量是否有效
                if elastic_data['elastic_modulus'] <= 0:
                    self.result_text.insert(tk.END, f"弹性模量: 数据不可靠或不适合线性拟合\n")
                else:
                    self.result_text.insert(tk.END, f"弹性模量: {elastic_data['elastic_modulus']:.4f} GPa\n")
                    self.result_text.insert(tk.END, f"拟合优度 R²: {elastic_data['r_squared']:.4f}\n")
                
                self.result_text.insert(tk.END, f"最大应力: {yield_data['max_stress']:.4f} GPa\n")
                self.result_text.insert(tk.END, f"屈服点应变: {yield_data['yield_strain']:.4f}\n")
                self.result_text.insert(tk.END, f"屈服点应力: {yield_data['yield_stress']:.4f} GPa\n")
                
                # 如果有非线性拟合结果
                if dir_key in self.results.get('nonlinear', {}):
                    nl_data = self.results['nonlinear'][dir_key]
                    model = self.fit_model.get()
                    
                    self.result_text.insert(tk.END, f"\n{model.capitalize()} 模型参数:\n")
                    for param_name, param_value in nl_data['params'].items():
                        self.result_text.insert(tk.END, f"  {param_name}: {param_value:.4f}\n")
                    self.result_text.insert(tk.END, f"  拟合优度 R²: {nl_data['r_squared']:.4f}\n")
                
                self.result_text.insert(tk.END, "\n")
        
        # 添加分析结论
        self.result_text.insert(tk.END, "====== 材料特性分析 ======\n\n")
        
        # 获取ZZ方向的弹性模量（如果有效）
        if 'zz' in self.results.get('elastic', {}) and self.results['elastic']['zz']['elastic_modulus'] > 0:
            elastic_modulus = self.results['elastic']['zz']['elastic_modulus']
            
            # 根据弹性模量判断材料刚性
            if elastic_modulus > 300:
                rigidity = "极高"
            elif elastic_modulus > 200:
                rigidity = "高"
            elif elastic_modulus > 100:
                rigidity = "中等"
            else:
                rigidity = "低"
                
            self.result_text.insert(tk.END, f"1. 材料刚性: {rigidity}（弹性模量 {elastic_modulus:.2f} GPa）\n")
        else:
            self.result_text.insert(tk.END, "1. 材料刚性: 无法可靠估计（弹性模量数据不可靠）\n")
        
        # 分析应力方向特性
        valid_yield_data = True
        for dir_key in dir_keys:
            if dir_key not in self.results.get('yield', {}):
                valid_yield_data = False
                break
        
        if valid_yield_data:
            xx_max = self.results['yield']['xx']['max_stress']
            yy_max = self.results['yield']['yy']['max_stress']
            zz_max = self.results['yield']['zz']['max_stress']
            
            if zz_max > 2 * xx_max and zz_max > 2 * yy_max:
                anisotropy = "强烈各向异性，Z方向强度显著高于X和Y方向"
            elif zz_max > xx_max and zz_max > yy_max:
                anisotropy = "一定程度的各向异性，Z方向强度较高"
            else:
                anisotropy = "相对各向同性，三个方向强度相近"
                
            self.result_text.insert(tk.END, f"2. 方向性: {anisotropy}\n")
        else:
            self.result_text.insert(tk.END, "2. 方向性: 无法分析（缺少某些方向的屈服数据）\n")
        
        # 屈服特性
        if 'zz' in self.results.get('yield', {}):
            yield_strain = self.results['yield']['zz']['yield_strain']
            
            if yield_strain > 0.2:
                yield_character = "高延展性"
            elif yield_strain > 0.1:
                yield_character = "中等延展性"
            else:
                yield_character = "低延展性/脆性"
                
            self.result_text.insert(tk.END, f"3. 屈服特性: {yield_character}（屈服应变 {yield_strain:.2f}）\n\n")
        else:
            self.result_text.insert(tk.END, "3. 屈服特性: 无法分析（缺少Z方向的屈服数据）\n\n")
        
        # 添加潜在应用建议
        self.result_text.insert(tk.END, "====== 潜在应用建议 ======\n\n")
        
        if 'zz' in self.results.get('elastic', {}) and 'zz' in self.results.get('yield', {}):
            elastic_modulus = self.results['elastic']['zz']['elastic_modulus']
            yield_stress = self.results['yield']['zz']['yield_stress']
            yield_strain = self.results['yield']['zz']['yield_strain']
            
            # 只有当弹性模量有效时才进行评估
            if elastic_modulus > 0:
                if elastic_modulus > 300 and yield_stress > 80:
                    self.result_text.insert(tk.END, "高刚性高强度材料，适合结构支撑和高负载场景\n")
                elif elastic_modulus > 200 and yield_strain > 0.15:
                    self.result_text.insert(tk.END, "综合性能良好，同时具有较高刚性和延展性\n")
                elif yield_strain > 0.2:
                    self.result_text.insert(tk.END, "高延展性材料，适合需要大形变的场景\n")
                else:
                    self.result_text.insert(tk.END, "一般用途材料，根据实际需求选择应用场景\n")
            else:
                self.result_text.insert(tk.END, "由于弹性模量数据不可靠，无法给出可靠的应用建议\n")
    
    def export_results(self):
        """导出分析结果到文本文件和高质量图像"""
        if not self.results:
            messagebox.showerror("错误", "没有结果可导出")
            return
        
        # 创建文件夹选择对话框
        export_dir = filedialog.askdirectory(title="选择导出结果的文件夹")
        
        if not export_dir:
            return
        
        try:
            # 创建时间戳作为文件名前缀
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            base_path = os.path.join(export_dir, f"stress_strain_analysis_{timestamp}")
            
            # 导出文本报告
            text_content = self.result_text.get(1.0, tk.END)
            with open(f"{base_path}_report.txt", "w", encoding="utf-8") as file:
                file.write(text_content)
            
            # 导出CSV数据
            if self.data is not None:
                self.data.to_csv(f"{base_path}_data.csv", index=False)
            
            # 导出高质量图表
            dpi = self.dpi.get()
            
            # 保存应力-应变曲线
            for widget in self.plot_frame.winfo_children():
                if isinstance(widget, FigureCanvasTkAgg):
                    widget.figure.savefig(f"{base_path}_stress_strain_curve.png", dpi=dpi, bbox_inches='tight')
                    widget.figure.savefig(f"{base_path}_stress_strain_curve.pdf", format='pdf', bbox_inches='tight')
                    widget.figure.savefig(f"{base_path}_stress_strain_curve.eps", format='eps', bbox_inches='tight')
                    break
            
            # 保存弹性区拟合图
            for widget in self.elastic_frame.winfo_children():
                if isinstance(widget, FigureCanvasTkAgg):
                    widget.figure.savefig(f"{base_path}_elastic_fit.png", dpi=dpi, bbox_inches='tight')
                    widget.figure.savefig(f"{base_path}_elastic_fit.pdf", format='pdf', bbox_inches='tight')
                    widget.figure.savefig(f"{base_path}_elastic_fit.eps", format='eps', bbox_inches='tight')
                    break
            
            # 保存非线性拟合图（如果有）
            if self.fit_model.get() != 'linear':
                for widget in self.nonlinear_frame.winfo_children():
                    if isinstance(widget, FigureCanvasTkAgg):
                        widget.figure.savefig(f"{base_path}_nonlinear_fit.png", dpi=dpi, bbox_inches='tight')
                        widget.figure.savefig(f"{base_path}_nonlinear_fit.pdf", format='pdf', bbox_inches='tight')
                        widget.figure.savefig(f"{base_path}_nonlinear_fit.eps", format='eps', bbox_inches='tight')
                        break
            
            messagebox.showinfo("成功", f"分析结果已保存至:\n{export_dir}")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出结果时出错:\n{str(e)}")


if __name__ == "__main__":
    root = tk.Tk()
    app = StressStrainAnalyzerApp(root)
    root.mainloop() 