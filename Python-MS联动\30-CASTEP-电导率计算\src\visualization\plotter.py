"""
数据可视化模块
提供电导率计算结果的图表绘制功能
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Optional, Dict, Any, Tuple
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ConductivityPlotter:
    """电导率数据绘图器"""
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8), dpi: int = 100):
        """
        初始化绘图器
        
        Args:
            figsize (Tuple[int, int]): 图形尺寸
            dpi (int): 图形分辨率
        """
        self.figsize = figsize
        self.dpi = dpi
        self.colors = {
            'epsilon': '#1f77b4',
            'conductivity': '#ff7f0e',
            'fit': '#2ca02c',
            'data_points': '#d62728'
        }
    
    def plot_epsilon_spectrum(self, energy: np.ndarray, epsilon_1: np.ndarray, 
                            epsilon_2: np.ndarray, save_path: Optional[str] = None) -> None:
        """
        绘制介电函数谱
        
        Args:
            energy (np.ndarray): 能量数组 (eV)
            epsilon_1 (np.ndarray): 介电函数实部
            epsilon_2 (np.ndarray): 介电函数虚部
            save_path (Optional[str]): 保存路径
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figsize, dpi=self.dpi)
        
        # 绘制实部
        ax1.plot(energy, epsilon_1, color=self.colors['epsilon'], linewidth=2, label='ε₁(ω)')
        ax1.set_xlabel('能量 (eV)')
        ax1.set_ylabel('介电函数实部 ε₁')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.set_title('介电函数实部谱')
        
        # 绘制虚部
        ax2.plot(energy, epsilon_2, color=self.colors['conductivity'], linewidth=2, label='ε₂(ω)')
        ax2.set_xlabel('能量 (eV)')
        ax2.set_ylabel('介电函数虚部 ε₂')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.set_title('介电函数虚部谱')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            print(f"介电函数谱已保存至: {save_path}")
        
        plt.show()
    
    def plot_conductivity_spectrum(self, energy: np.ndarray, sigma_omega: np.ndarray,
                                 log_scale: bool = True, save_path: Optional[str] = None) -> None:
        """
        绘制光学导率谱
        
        Args:
            energy (np.ndarray): 能量数组 (eV)
            sigma_omega (np.ndarray): 光学导率数组 (S/m)
            log_scale (bool): 是否使用对数坐标
            save_path (Optional[str]): 保存路径
        """
        fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        
        if log_scale:
            ax.loglog(energy, sigma_omega, color=self.colors['conductivity'], 
                     linewidth=2, label='σ(ω)')
            ax.set_ylabel('光学导率 σ(ω) (S/m) [对数坐标]')
        else:
            ax.plot(energy, sigma_omega, color=self.colors['conductivity'], 
                   linewidth=2, label='σ(ω)')
            ax.set_ylabel('光学导率 σ(ω) (S/m)')
        
        ax.set_xlabel('能量 (eV)')
        ax.grid(True, alpha=0.3)
        ax.legend()
        ax.set_title('光学导率谱')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            print(f"光学导率谱已保存至: {save_path}")
        
        plt.show()
    
    def plot_dc_extrapolation(self, energy: np.ndarray, sigma_omega: np.ndarray,
                            fit_result: Dict[str, Any], save_path: Optional[str] = None) -> None:
        """
        绘制直流电导率外推图
        
        Args:
            energy (np.ndarray): 能量数组 (eV)
            sigma_omega (np.ndarray): 光学导率数组 (S/m)
            fit_result (Dict[str, Any]): 拟合结果字典
            save_path (Optional[str]): 保存路径
        """
        fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        
        # 绘制完整数据
        ax.plot(energy, sigma_omega, color=self.colors['conductivity'], 
               linewidth=2, alpha=0.7, label='σ(ω) 完整数据')
        
        # 绘制拟合区域的数据点
        fit_indices = fit_result.get('fit_indices', [])
        if len(fit_indices) > 0:
            fit_energy = energy[fit_indices]
            fit_sigma = sigma_omega[fit_indices]
            
            ax.scatter(fit_energy, fit_sigma, color=self.colors['data_points'], 
                      s=50, zorder=5, label=f'拟合数据点 (n={len(fit_indices)})')
            
            # 绘制拟合曲线
            if 'fit_function' in fit_result:
                energy_fit_extended = np.linspace(0, fit_energy[-1], 100)
                sigma_fit_extended = fit_result['fit_function'](energy_fit_extended)
                
                ax.plot(energy_fit_extended, sigma_fit_extended, 
                       color=self.colors['fit'], linewidth=2, linestyle='--',
                       label=f'{fit_result.get("method", "拟合")}拟合')
        
        # 标记直流电导率
        sigma_dc = fit_result.get('sigma_dc', 0)
        ax.axhline(y=sigma_dc, color='red', linestyle=':', linewidth=2,
                  label=f'σ_dc = {sigma_dc:.2e} S/m')
        
        # 添加拟合质量信息
        r_squared = fit_result.get('r_squared', 0)
        fit_error = fit_result.get('fit_error', 0)
        
        textstr = f'R² = {r_squared:.4f}\n拟合误差 = {fit_error:.2e} S/m'
        props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
        ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=10,
               verticalalignment='top', bbox=props)
        
        ax.set_xlabel('能量 (eV)')
        ax.set_ylabel('光学导率 σ(ω) (S/m)')
        ax.grid(True, alpha=0.3)
        ax.legend()
        ax.set_title('直流电导率外推分析')
        
        # 设置x轴范围，突出低能区域
        if len(fit_indices) > 0:
            max_energy = max(fit_energy[-1] * 3, energy[min(20, len(energy)-1)])
            ax.set_xlim(0, max_energy)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            print(f"直流电导率外推图已保存至: {save_path}")
        
        plt.show()
    
    def plot_comprehensive_analysis(self, energy: np.ndarray, epsilon_1: np.ndarray,
                                  epsilon_2: np.ndarray, sigma_omega: np.ndarray,
                                  fit_result: Optional[Dict[str, Any]] = None,
                                  save_path: Optional[str] = None) -> None:
        """
        绘制综合分析图
        
        Args:
            energy (np.ndarray): 能量数组 (eV)
            epsilon_1 (np.ndarray): 介电函数实部
            epsilon_2 (np.ndarray): 介电函数虚部
            sigma_omega (np.ndarray): 光学导率数组 (S/m)
            fit_result (Optional[Dict[str, Any]]): 拟合结果字典
            save_path (Optional[str]): 保存路径
        """
        fig = plt.figure(figsize=(15, 10), dpi=self.dpi)
        
        # 子图1: 介电函数实部
        ax1 = plt.subplot(2, 3, 1)
        ax1.plot(energy, epsilon_1, color=self.colors['epsilon'], linewidth=2)
        ax1.set_xlabel('能量 (eV)')
        ax1.set_ylabel('ε₁')
        ax1.grid(True, alpha=0.3)
        ax1.set_title('介电函数实部')
        
        # 子图2: 介电函数虚部
        ax2 = plt.subplot(2, 3, 2)
        ax2.plot(energy, epsilon_2, color=self.colors['conductivity'], linewidth=2)
        ax2.set_xlabel('能量 (eV)')
        ax2.set_ylabel('ε₂')
        ax2.grid(True, alpha=0.3)
        ax2.set_title('介电函数虚部')
        
        # 子图3: 光学导率(线性坐标)
        ax3 = plt.subplot(2, 3, 3)
        ax3.plot(energy, sigma_omega, color=self.colors['conductivity'], linewidth=2)
        ax3.set_xlabel('能量 (eV)')
        ax3.set_ylabel('σ(ω) (S/m)')
        ax3.grid(True, alpha=0.3)
        ax3.set_title('光学导率(线性)')
        
        # 子图4: 光学导率(对数坐标)
        ax4 = plt.subplot(2, 3, 4)
        ax4.loglog(energy, sigma_omega, color=self.colors['conductivity'], linewidth=2)
        ax4.set_xlabel('能量 (eV)')
        ax4.set_ylabel('σ(ω) (S/m)')
        ax4.grid(True, alpha=0.3)
        ax4.set_title('光学导率(对数)')
        
        # 子图5: 直流电导率外推
        ax5 = plt.subplot(2, 3, 5)
        ax5.plot(energy, sigma_omega, color=self.colors['conductivity'], 
                linewidth=2, alpha=0.7, label='σ(ω)')
        
        if fit_result:
            fit_indices = fit_result.get('fit_indices', [])
            if len(fit_indices) > 0:
                fit_energy = energy[fit_indices]
                fit_sigma = sigma_omega[fit_indices]
                
                ax5.scatter(fit_energy, fit_sigma, color=self.colors['data_points'], 
                           s=30, zorder=5, label='拟合点')
                
                if 'fit_function' in fit_result:
                    energy_fit = np.linspace(0, fit_energy[-1], 50)
                    sigma_fit = fit_result['fit_function'](energy_fit)
                    ax5.plot(energy_fit, sigma_fit, color=self.colors['fit'], 
                            linewidth=2, linestyle='--', label='拟合')
                
                sigma_dc = fit_result.get('sigma_dc', 0)
                ax5.axhline(y=sigma_dc, color='red', linestyle=':', 
                           label=f'σ_dc={sigma_dc:.1e}')
        
        ax5.set_xlabel('能量 (eV)')
        ax5.set_ylabel('σ(ω) (S/m)')
        ax5.grid(True, alpha=0.3)
        ax5.legend(fontsize=8)
        ax5.set_title('直流电导率外推')
        
        # 子图6: 计算摘要
        ax6 = plt.subplot(2, 3, 6)
        ax6.axis('off')
        
        # 准备摘要文本
        summary_text = f"数据点数: {len(energy)}\n"
        summary_text += f"能量范围: {energy[0]:.3f} - {energy[-1]:.1f} eV\n"
        summary_text += f"σ(ω)范围: {np.min(sigma_omega):.2e} - {np.max(sigma_omega):.2e} S/m\n"
        
        if fit_result:
            summary_text += f"\n拟合方法: {fit_result.get('method', 'N/A')}\n"
            summary_text += f"拟合点数: {fit_result.get('fit_points', 'N/A')}\n"
            summary_text += f"R²: {fit_result.get('r_squared', 0):.4f}\n"
            summary_text += f"σ_dc: {fit_result.get('sigma_dc', 0):.3e} S/m"
        
        ax6.text(0.1, 0.9, summary_text, transform=ax6.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        ax6.set_title('计算摘要')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            print(f"综合分析图已保存至: {save_path}")
        
        plt.show()
    
    def save_all_plots(self, energy: np.ndarray, epsilon_1: np.ndarray,
                      epsilon_2: np.ndarray, sigma_omega: np.ndarray,
                      fit_result: Optional[Dict[str, Any]] = None,
                      output_dir: str = "plots") -> None:
        """
        保存所有图表到指定目录
        
        Args:
            energy (np.ndarray): 能量数组
            epsilon_1 (np.ndarray): 介电函数实部
            epsilon_2 (np.ndarray): 介电函数虚部
            sigma_omega (np.ndarray): 光学导率数组
            fit_result (Optional[Dict[str, Any]]): 拟合结果
            output_dir (str): 输出目录
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存各种图表
        self.plot_epsilon_spectrum(energy, epsilon_1, epsilon_2, 
                                  os.path.join(output_dir, "epsilon_spectrum.png"))
        
        self.plot_conductivity_spectrum(energy, sigma_omega, log_scale=True,
                                      save_path=os.path.join(output_dir, "conductivity_log.png"))
        
        self.plot_conductivity_spectrum(energy, sigma_omega, log_scale=False,
                                      save_path=os.path.join(output_dir, "conductivity_linear.png"))
        
        if fit_result:
            self.plot_dc_extrapolation(energy, sigma_omega, fit_result,
                                     save_path=os.path.join(output_dir, "dc_extrapolation.png"))
        
        self.plot_comprehensive_analysis(energy, epsilon_1, epsilon_2, sigma_omega, fit_result,
                                       save_path=os.path.join(output_dir, "comprehensive_analysis.png"))
        
        print(f"所有图表已保存至目录: {output_dir}")
