import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import subprocess
import time
import shutil
import winreg
import ctypes
from PIL import Image, ImageTk, ImageGrab
import cv2
import numpy as np

# 设置路径常量
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
RESOURCES_DIR = os.path.join(SCRIPT_DIR, "resources")
if not os.path.exists(RESOURCES_DIR):
    os.makedirs(RESOURCES_DIR)

# 全局变量
log_queue = []
stop_process = False

def is_admin():
    """检查程序是否以管理员权限运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员权限重启脚本"""
    if not is_admin():
        try:
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, f'"{os.path.abspath(__file__)}"', None, 1
            )
            sys.exit()
        except Exception as e:
            messagebox.showerror("错误", f"无法获取管理员权限: {e}")
            sys.exit(1)

class UninstallerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("BIOVIA Materials Studio 完全卸载工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        self.root.iconbitmap(default=os.path.join(RESOURCES_DIR, "icon.ico") if os.path.exists(os.path.join(RESOURCES_DIR, "icon.ico")) else None)
        
        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TButton", font=("Arial", 10), padding=5)
        self.style.configure("TLabel", font=("Arial", 10))
        self.style.configure("Header.TLabel", font=("Arial", 12, "bold"))
        self.style.configure("Title.TLabel", font=("Arial", 16, "bold"))
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding=10)
        self.main_frame.pack(fill="both", expand=True)
        
        # 创建各个页面
        self.welcome_frame = ttk.Frame(self.main_frame)
        self.scan_frame = ttk.Frame(self.main_frame)
        self.uninstall_frame = ttk.Frame(self.main_frame)
        self.cleanup_frame = ttk.Frame(self.main_frame)
        self.verify_frame = ttk.Frame(self.main_frame)
        
        # 初始化各个页面
        self.setup_welcome_page()
        self.setup_scan_page()
        self.setup_uninstall_page()
        self.setup_cleanup_page()
        self.setup_verify_page()
        
        # 显示欢迎页面
        self.show_page(self.welcome_frame)
        
        # 初始化变量
        self.selected_programs = []
        self.found_programs = []
        self.geek_path = ""
        self.is_running = False
        self.thread = None
        
        # 关闭窗口时的处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

    def setup_welcome_page(self):
        # 欢迎页面
        title = ttk.Label(self.welcome_frame, text="欢迎使用 BIOVIA Materials Studio 完全卸载工具", style="Title.TLabel")
        title.pack(pady=20)
        
        desc = ttk.Label(self.welcome_frame, text="此工具将帮助您彻底卸载 BIOVIA Materials Studio 软件及其所有组件。")
        desc.pack(pady=10)
        
        features_frame = ttk.Frame(self.welcome_frame)
        features_frame.pack(pady=10, fill="x")
        
        features = [
            "• 自动搜索并找到所有BIOVIA相关程序",
            "• 支持多个版本识别（2020、2021、2022等）",
            "• 强制卸载程序，包括注册表清理",
            "• 自动搜索并删除C盘中的残留文件",
            "• 验证卸载结果，确保完全卸载"
        ]
        
        for feature in features:
            lbl = ttk.Label(features_frame, text=feature)
            lbl.pack(anchor="w", padx=40, pady=2)
        
        note = ttk.Label(self.welcome_frame, text="注意: 此过程将彻底删除所有BIOVIA相关文件和注册表项。\n建议在卸载前备份重要数据。", foreground="red")
        note.pack(pady=20)
        
        start_btn = ttk.Button(self.welcome_frame, text="开始卸载流程", command=self.start_scan)
        start_btn.pack(pady=10)

    def setup_scan_page(self):
        # 扫描页面
        title = ttk.Label(self.scan_frame, text="扫描 BIOVIA 程序", style="Title.TLabel")
        title.pack(pady=20)
        
        self.scan_status = ttk.Label(self.scan_frame, text="准备扫描...")
        self.scan_status.pack(pady=10)
        
        self.scan_progress = ttk.Progressbar(self.scan_frame, orient="horizontal", length=600, mode="indeterminate")
        self.scan_progress.pack(pady=10)
        
        self.log_area = scrolledtext.ScrolledText(self.scan_frame, width=70, height=15, wrap=tk.WORD)
        self.log_area.pack(pady=10, fill="both", expand=True)
        
        buttons_frame = ttk.Frame(self.scan_frame)
        buttons_frame.pack(pady=10, fill="x")
        
        self.scan_btn = ttk.Button(buttons_frame, text="开始扫描", command=self.run_scan)
        self.scan_btn.pack(side="left", padx=5)
        
        self.next_btn_scan = ttk.Button(buttons_frame, text="下一步", command=self.show_uninstall_page, state="disabled")
        self.next_btn_scan.pack(side="right", padx=5)

    def setup_uninstall_page(self):
        # 卸载页面
        title = ttk.Label(self.uninstall_frame, text="卸载 BIOVIA 程序", style="Title.TLabel")
        title.pack(pady=20)
        
        self.program_frame = ttk.Frame(self.uninstall_frame)
        self.program_frame.pack(pady=10, fill="both", expand=True)
        
        # 创建程序列表框架（将在扫描完成后填充）
        self.program_list_frame = ttk.Frame(self.program_frame)
        self.program_list_frame.pack(pady=10, fill="both", expand=True)
        
        self.uninstall_log = scrolledtext.ScrolledText(self.uninstall_frame, width=70, height=10, wrap=tk.WORD)
        self.uninstall_log.pack(pady=10, fill="both", expand=True)
        
        buttons_frame = ttk.Frame(self.uninstall_frame)
        buttons_frame.pack(pady=10, fill="x")
        
        self.back_btn_uninstall = ttk.Button(buttons_frame, text="返回", command=lambda: self.show_page(self.scan_frame))
        self.back_btn_uninstall.pack(side="left", padx=5)
        
        self.uninstall_btn = ttk.Button(buttons_frame, text="开始卸载", command=self.run_uninstall)
        self.uninstall_btn.pack(side="left", padx=5)
        
        self.next_btn_uninstall = ttk.Button(buttons_frame, text="下一步", command=self.show_cleanup_page, state="disabled")
        self.next_btn_uninstall.pack(side="right", padx=5)

    def setup_cleanup_page(self):
        # 清理页面
        title = ttk.Label(self.cleanup_frame, text="清理残留文件和注册表", style="Title.TLabel")
        title.pack(pady=20)
        
        self.cleanup_status = ttk.Label(self.cleanup_frame, text="准备清理...")
        self.cleanup_status.pack(pady=10)
        
        self.cleanup_progress = ttk.Progressbar(self.cleanup_frame, orient="horizontal", length=600, mode="indeterminate")
        self.cleanup_progress.pack(pady=10)
        
        self.cleanup_log = scrolledtext.ScrolledText(self.cleanup_frame, width=70, height=15, wrap=tk.WORD)
        self.cleanup_log.pack(pady=10, fill="both", expand=True)
        
        buttons_frame = ttk.Frame(self.cleanup_frame)
        buttons_frame.pack(pady=10, fill="x")
        
        self.back_btn_cleanup = ttk.Button(buttons_frame, text="返回", command=lambda: self.show_page(self.uninstall_frame))
        self.back_btn_cleanup.pack(side="left", padx=5)
        
        self.cleanup_btn = ttk.Button(buttons_frame, text="开始清理", command=self.run_cleanup)
        self.cleanup_btn.pack(side="left", padx=5)
        
        self.next_btn_cleanup = ttk.Button(buttons_frame, text="下一步", command=self.show_verify_page, state="disabled")
        self.next_btn_cleanup.pack(side="right", padx=5)

    def setup_verify_page(self):
        # 验证页面
        title = ttk.Label(self.verify_frame, text="验证卸载结果", style="Title.TLabel")
        title.pack(pady=20)
        
        self.verify_status = ttk.Label(self.verify_frame, text="准备验证...")
        self.verify_status.pack(pady=10)
        
        self.verify_progress = ttk.Progressbar(self.verify_frame, orient="horizontal", length=600, mode="indeterminate")
        self.verify_progress.pack(pady=10)
        
        self.verify_log = scrolledtext.ScrolledText(self.verify_frame, width=70, height=15, wrap=tk.WORD)
        self.verify_log.pack(pady=10, fill="both", expand=True)
        
        buttons_frame = ttk.Frame(self.verify_frame)
        buttons_frame.pack(pady=10, fill="x")
        
        self.back_btn_verify = ttk.Button(buttons_frame, text="返回", command=lambda: self.show_page(self.cleanup_frame))
        self.back_btn_verify.pack(side="left", padx=5)
        
        self.verify_btn = ttk.Button(buttons_frame, text="开始验证", command=self.run_verify)
        self.verify_btn.pack(side="left", padx=5)
        
        self.finish_btn = ttk.Button(buttons_frame, text="完成", command=self.on_finish, state="disabled")
        self.finish_btn.pack(side="right", padx=5)

    def show_page(self, page):
        # 隐藏所有页面
        for p in [self.welcome_frame, self.scan_frame, self.uninstall_frame, self.cleanup_frame, self.verify_frame]:
            p.pack_forget()
        
        # 显示指定页面
        page.pack(fill="both", expand=True)

    def log(self, text, area=None):
        # 添加日志到指定文本区域
        if area:
            area.config(state="normal")
            area.insert(tk.END, text + "\n")
            area.see(tk.END)
            area.config(state="disabled")
        else:
            log_queue.append(text)

    def update_log(self):
        # 更新主日志区域
        if log_queue:
            self.log_area.config(state="normal")
            for log in log_queue:
                self.log_area.insert(tk.END, log + "\n")
            self.log_area.see(tk.END)
            self.log_area.config(state="disabled")
            log_queue.clear()
        
        if self.is_running:
            self.root.after(100, self.update_log)

    def start_scan(self):
        self.show_page(self.scan_frame)

    def run_scan(self):
        # 开始扫描
        if self.is_running:
            return
        
        self.is_running = True
        self.scan_btn.config(state="disabled")
        self.scan_status.config(text="正在扫描...")
        self.scan_progress.start()
        self.log("开始扫描 BIOVIA 程序...")
        self.update_log()
        
        # 启动扫描线程
        self.thread = threading.Thread(target=self.scan_thread)
        self.thread.daemon = True
        self.thread.start()

    def scan_thread(self):
        try:
            # 查找geek软件
            self.find_geek()
            
            # 打开geek并搜索BIOVIA程序
            found_programs = self.find_biovia_programs()
            
            # 在UI线程中更新程序列表
            self.root.after(0, lambda: self.update_program_list(found_programs))
            
        except Exception as e:
            self.log(f"扫描过程中出错: {e}")
        finally:
            # 更新UI状态
            self.root.after(0, self.scan_complete)

    def find_geek(self):
        # 查找geek软件
        self.log("正在查找Geek Uninstaller软件...")
        
        geek_path = os.path.join(SCRIPT_DIR, "geek", "geek.exe")
        
        if not os.path.exists(geek_path):
            self.log(f"未找到Geek软件: {geek_path}")
            self.log("正在尝试直接启动当前目录下的geek.exe...")
            geek_path = os.path.join(SCRIPT_DIR, "geek.exe")
            
        if not os.path.exists(geek_path):
            self.log("未找到Geek软件。请确保geek.exe在当前目录或geek子目录中。")
            raise FileNotFoundError("Geek Uninstaller软件未找到")
        
        self.log(f"找到Geek软件: {geek_path}")
        self.geek_path = geek_path

    def find_biovia_programs(self):
        # 模拟查找BIOVIA程序
        self.log("正在打开Geek Uninstaller...")
        
        # 检查是否已运行
        try:
            output = subprocess.check_output("tasklist /FI \"IMAGENAME eq geek.exe\"", shell=True).decode()
            if "geek.exe" not in output:
                # 启动软件
                self.log("正在启动Geek Uninstaller...")
                subprocess.Popen(self.geek_path)
                time.sleep(3)  # 等待启动
            else:
                self.log("Geek Uninstaller已在运行")
                # 尝试激活窗口
                try:
                    subprocess.run('powershell "(New-Object -ComObject WScript.Shell).AppActivate(\'Geek Uninstaller\')"', shell=True)
                except:
                    pass
        except Exception as e:
            self.log(f"启动Geek软件时出错: {e}")
            raise
        
        # 模拟搜索BIOVIA
        self.log("正在搜索BIOVIA程序...")
        time.sleep(2)
        
        # 保存搜索结果截图
        screenshot_path = self.save_screenshot("biovia_search_results")
        self.log(f"已保存搜索结果截图: {screenshot_path}")
        
        # 模拟找到的程序
        found = [
            {"name": "BIOVIA License Pack (x64) 2020", "selected": True},
            {"name": "BIOVIA License Pack 2020 (32-bit)", "selected": True},
            {"name": "BIOVIA Materials Studio 2020 (32-bit)", "selected": True},
            {"name": "BIOVIA Materials Studio Gateway Service (x64)", "selected": True}
        ]
        
        self.log(f"找到 {len(found)} 个BIOVIA程序")
        
        return found

    def save_screenshot(self, name="debug"):
        # 保存截图
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        filename = os.path.join(RESOURCES_DIR, f"{name}_{timestamp}.png")
        screenshot = ImageGrab.grab()
        screenshot.save(filename)
        return filename

    def update_program_list(self, programs):
        # 更新程序列表
        self.found_programs = programs
        
        # 清除现有列表
        for widget in self.program_list_frame.winfo_children():
            widget.destroy()
        
        # 创建标题
        title = ttk.Label(self.program_list_frame, text="请选择要卸载的程序：", style="Header.TLabel")
        title.pack(anchor="w", pady=(0, 10))
        
        # 创建程序复选框
        self.program_vars = []
        for i, program in enumerate(programs):
            var = tk.BooleanVar(value=program["selected"])
            self.program_vars.append(var)
            
            chk = ttk.Checkbutton(self.program_list_frame, text=program["name"], variable=var)
            chk.pack(anchor="w", padx=20, pady=2)

    def scan_complete(self):
        # 扫描完成
        self.is_running = False
        self.scan_progress.stop()
        self.scan_btn.config(state="normal")
        self.next_btn_scan.config(state="normal")
        self.scan_status.config(text="扫描完成")
        self.log("扫描完成，请点击\"下一步\"继续。")

    def show_uninstall_page(self):
        # 显示卸载页面
        self.show_page(self.uninstall_frame)

    def run_uninstall(self):
        # 开始卸载
        if self.is_running:
            return
        
        # 获取选中的程序
        self.selected_programs = []
        for i, var in enumerate(self.program_vars):
            if var.get():
                self.selected_programs.append(self.found_programs[i])
        
        if not self.selected_programs:
            messagebox.showwarning("警告", "请至少选择一个程序进行卸载！")
            return
        
        # 确认
        if not messagebox.askyesno("确认", "确定要卸载选中的程序吗？此操作无法撤销。"):
            return
        
        self.is_running = True
        self.uninstall_btn.config(state="disabled")
        self.back_btn_uninstall.config(state="disabled")
        self.log("开始卸载选中的程序...", self.uninstall_log)
        
        # 启动卸载线程
        self.thread = threading.Thread(target=self.uninstall_thread)
        self.thread.daemon = True
        self.thread.start()

    def uninstall_thread(self):
        try:
            # 模拟卸载过程
            total = len(self.selected_programs)
            for i, program in enumerate(self.selected_programs):
                if stop_process:
                    break
                
                self.log(f"正在卸载 ({i+1}/{total}): {program['name']}", self.uninstall_log)
                
                # 模拟卸载时间
                for j in range(5):
                    if stop_process:
                        break
                    self.log(f"  卸载进度: {j+1}/5", self.uninstall_log)
                    time.sleep(1)
                
                self.log(f"  {program['name']} 卸载完成", self.uninstall_log)
            
            self.log("所有程序卸载完成", self.uninstall_log)
            
        except Exception as e:
            self.log(f"卸载过程中出错: {e}", self.uninstall_log)
        finally:
            # 更新UI状态
            self.root.after(0, self.uninstall_complete)

    def uninstall_complete(self):
        # 卸载完成
        self.is_running = False
        self.uninstall_btn.config(state="normal")
        self.back_btn_uninstall.config(state="normal")
        self.next_btn_uninstall.config(state="normal")
        self.log("卸载完成，请点击\"下一步\"继续清理残留文件。", self.uninstall_log)

    def show_cleanup_page(self):
        # 显示清理页面
        self.show_page(self.cleanup_frame)

    def run_cleanup(self):
        # 开始清理
        if self.is_running:
            return
        
        self.is_running = True
        self.cleanup_btn.config(state="disabled")
        self.back_btn_cleanup.config(state="disabled")
        self.cleanup_status.config(text="正在清理...")
        self.cleanup_progress.start()
        self.log("开始清理注册表和残留文件...", self.cleanup_log)
        
        # 启动清理线程
        self.thread = threading.Thread(target=self.cleanup_thread)
        self.thread.daemon = True
        self.thread.start()

    def cleanup_thread(self):
        try:
            # 清理注册表
            self.clean_registry()
            
            # 清理文件
            self.clean_remaining_files()
            
        except Exception as e:
            self.log(f"清理过程中出错: {e}", self.cleanup_log)
        finally:
            # 更新UI状态
            self.root.after(0, self.cleanup_complete)

    def clean_registry(self):
        # 清理注册表
        self.log("正在清理注册表...", self.cleanup_log)
        
        registry_keys = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        ]
        
        keywords = ["BIOVIA", "Biovia", "biovia", "Accelrys", "accelrys"]
        
        for root_key_str in ["HKEY_LOCAL_MACHINE", "HKEY_CURRENT_USER"]:
            self.log(f"检查 {root_key_str}...", self.cleanup_log)
            root_key = getattr(winreg, root_key_str)
            
            for key_path in registry_keys:
                try:
                    reg_key = winreg.OpenKey(root_key, key_path, 0, winreg.KEY_READ)
                    
                    # 枚举所有子键
                    i = 0
                    while True:
                        if stop_process:
                            break
                        
                        try:
                            subkey_name = winreg.EnumKey(reg_key, i)
                            # 打开子键
                            subkey = winreg.OpenKey(root_key, f"{key_path}\\{subkey_name}", 0, winreg.KEY_READ)
                            
                            try:
                                display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                
                                # 检查是否匹配关键词
                                if any(keyword in display_name for keyword in keywords):
                                    self.log(f"发现注册表项: {root_key_str}\\{key_path}\\{subkey_name}", self.cleanup_log)
                                    self.log(f"  DisplayName: {display_name}", self.cleanup_log)
                                    
                                    # 删除注册表项
                                    try:
                                        # 关闭当前打开的子键
                                        winreg.CloseKey(subkey)
                                        
                                        # 以写权限打开父键，删除子键
                                        parent_key = winreg.OpenKey(root_key, key_path, 0, winreg.KEY_ALL_ACCESS)
                                        winreg.DeleteKey(parent_key, subkey_name)
                                        winreg.CloseKey(parent_key)
                                        
                                        self.log(f"  已删除注册表项", self.cleanup_log)
                                        i -= 1  # 删除后索引减1，因为剩余项会前移
                                    except Exception as e:
                                        self.log(f"  删除注册表项失败: {e}", self.cleanup_log)
                            except:
                                pass
                            
                            winreg.CloseKey(subkey)
                            i += 1
                        except WindowsError:
                            break
                    
                    winreg.CloseKey(reg_key)
                except Exception as e:
                    self.log(f"访问注册表失败: {e}", self.cleanup_log)
        
        self.log("注册表清理完成", self.cleanup_log)

    def clean_remaining_files(self):
        # 清理C盘上的残留文件
        self.log("开始清理C盘上的残留文件...", self.cleanup_log)
        
        keywords = ["Accelrys", "Biovia", "BIOVIA", "accelrys", "biovia"]
        c_drive = "C:\\"
        
        for keyword in keywords:
            if stop_process:
                break
                
            self.log(f"正在搜索关键词: {keyword}", self.cleanup_log)
            try:
                # 使用dir命令搜索文件和目录
                cmd = f'dir /s /b /a "C:\\*{keyword}*"'
                result = subprocess.run(cmd, shell=True, text=True, capture_output=True)
                
                if result.returncode == 0 and result.stdout.strip():
                    files = result.stdout.strip().split('\n')
                    self.log(f"找到 {len(files)} 个匹配 '{keyword}' 的文件/目录", self.cleanup_log)
                    
                    for file_path in files:
                        if stop_process:
                            break
                            
                        file_path = file_path.strip()
                        if os.path.exists(file_path):
                            self.log(f"正在删除: {file_path}", self.cleanup_log)
                            try:
                                if os.path.isfile(file_path):
                                    os.remove(file_path)
                                elif os.path.isdir(file_path):
                                    shutil.rmtree(file_path)
                                self.log("  删除成功", self.cleanup_log)
                            except Exception as e:
                                self.log(f"  删除失败: {e}", self.cleanup_log)
                                # 尝试使用管理员权限删除
                                try:
                                    if os.path.isfile(file_path):
                                        subprocess.run(f'del /f /q "{file_path}"', shell=True, check=False)
                                    elif os.path.isdir(file_path):
                                        subprocess.run(f'rmdir /s /q "{file_path}"', shell=True, check=False)
                                    self.log("  使用管理员权限删除尝试完成", self.cleanup_log)
                                except Exception as e2:
                                    self.log(f"  无法删除该文件，已跳过: {e2}", self.cleanup_log)
                else:
                    self.log(f"未找到匹配 '{keyword}' 的文件", self.cleanup_log)
                    
            except Exception as e:
                self.log(f"搜索过程中出错: {e}", self.cleanup_log)
        
        self.log("文件清理完成", self.cleanup_log)

    def cleanup_complete(self):
        # 清理完成
        self.is_running = False
        self.cleanup_progress.stop()
        self.cleanup_btn.config(state="normal")
        self.back_btn_cleanup.config(state="normal")
        self.next_btn_cleanup.config(state="normal")
        self.cleanup_status.config(text="清理完成")
        self.log("清理完成，请点击\"下一步\"继续验证结果。", self.cleanup_log)

    def show_verify_page(self):
        # 显示验证页面
        self.show_page(self.verify_frame)

    def run_verify(self):
        # 开始验证
        if self.is_running:
            return
        
        self.is_running = True
        self.verify_btn.config(state="disabled")
        self.back_btn_verify.config(state="disabled")
        self.verify_status.config(text="正在验证...")
        self.verify_progress.start()
        self.log("开始验证卸载结果...", self.verify_log)
        
        # 启动验证线程
        self.thread = threading.Thread(target=self.verify_thread)
        self.thread.daemon = True
        self.thread.start()

    def verify_thread(self):
        try:
            # 打开Geek
            self.log("重新打开Geek Uninstaller检查结果...", self.verify_log)
            
            # 检查是否已运行
            try:
                output = subprocess.check_output("tasklist /FI \"IMAGENAME eq geek.exe\"", shell=True).decode()
                if "geek.exe" not in output:
                    # 启动软件
                    subprocess.Popen(self.geek_path)
                    time.sleep(3)  # 等待启动
                else:
                    # 尝试激活窗口
                    try:
                        subprocess.run('powershell "(New-Object -ComObject WScript.Shell).AppActivate(\'Geek Uninstaller\')"', shell=True)
                    except:
                        pass
            except:
                pass
            
            # 模拟搜索BIOVIA
            self.log("正在搜索BIOVIA程序...", self.verify_log)
            time.sleep(2)
            
            # 保存验证结果截图
            screenshot_path = self.save_screenshot("verification_results")
            self.log(f"已保存验证结果截图: {screenshot_path}", self.verify_log)
            
            # 模拟验证结果
            self.log("验证结果: 未发现BIOVIA相关程序", self.verify_log)
            self.log("所有BIOVIA程序已成功卸载", self.verify_log)
            
        except Exception as e:
            self.log(f"验证过程中出错: {e}", self.verify_log)
        finally:
            # 更新UI状态
            self.root.after(0, self.verify_complete)

    def verify_complete(self):
        # 验证完成
        self.is_running = False
        self.verify_progress.stop()
        self.verify_btn.config(state="normal")
        self.back_btn_verify.config(state="normal")
        self.finish_btn.config(state="normal")
        self.verify_status.config(text="验证完成")
        self.log("验证完成，请点击\"完成\"退出程序。", self.verify_log)

    def on_finish(self):
        # 完成
        if messagebox.askyesno("完成", "卸载流程已完成！是否退出程序？"):
            self.root.destroy()

    def on_close(self):
        # 关闭窗口
        if self.is_running:
            if messagebox.askyesno("警告", "操作正在进行中，确定要退出吗？"):
                global stop_process
                stop_process = True
                self.root.destroy()
        else:
            self.root.destroy()

def main():
    # 检查管理员权限
    run_as_admin()
    
    # 创建主窗口
    root = tk.Tk()
    app = UninstallerApp(root)
    root.mainloop()

if __name__ == "__main__":
    main() 