# 应力应变数据分析工具

此工具用于处理和分析Materials Studio分子模拟软件生成的应力应变数据，并生成适合期刊发表的高质量图表。

## 功能特点

1. 数据处理与分析
   - 支持多种文件格式（TSV、CSV、Excel）
   - 自动计算弹性模量
   - 识别屈服点
   - 提供详细分析报告

2. 图表生成
   - 生成三个方向(XX、YY、ZZ)的应力-应变曲线
   - 弹性区线性拟合与可视化
   - 符合期刊要求的高分辨率图表
   - 自定义图表样式和参数

## 使用方法

1. 准备数据文件（制表符分隔的文本文件、CSV或Excel格式）
2. 运行脚本：

```bash
python stress_strain_analysis.py
```

3. 脚本将自动:
   - 读取"1.数据"文件
   - 分析应力应变数据
   - 生成图表并保存
   - 输出分析报告

## 输出结果

1. 应力-应变曲线图
2. 弹性区拟合图
3. 控制台输出的详细分析报告，包括:
   - 弹性模量
   - 屈服强度
   - 屈服应变
   - 拟合优度(R²)

## 依赖库

- pandas
- numpy
- matplotlib
- scipy

## 数据格式要求

数据文件应包含以下列:
- Strain: 应变值
- Stress XX (GPa): X方向应力
- Stress YY (GPa): Y方向应力
- Stress ZZ (GPa): Z方向应力
- 可选: Elastic Modulus (GPa), R-squared, Elastic Points Used

## 自定义

可修改代码中的绘图参数以适应不同期刊的要求:
- 图表尺寸
- 字体样式
- 线条粗细
- 颜色方案
- DPI分辨率 