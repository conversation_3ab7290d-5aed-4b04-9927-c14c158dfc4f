##################################################################################################################
# perl                                                                                                           #
#                                                                                                                #
# Author: IMATSOFT.DM                                                                                            #
# Version: 1.3                                                                                                   #
# Tested on: Materials Studio 2020                                                                               #
#                                                                                                                #
# Required modules: Materials Visualizer, Forcite                                                                #
# This script aims to calculate hydrogen bonds for each molecule in each Set within each frame of a trajectory   #
# file. For each molecule, it copies only that molecule to a temporary file and then calculates hydrogen bonds.  #
# The results are recorded in separate Study Table files, one for each Set, documenting                          #
# the frame number, molecule index, number of hydrogen bonds, and detailed hydrogen bond geometry information    #
# (H-A distances and D-H-A angles).                                                                              #
#                                                                                                                #
# Date: 2024-07-02                                                                                               #
#                                                                                                                #
##################################################################################################################

use strict;
use warnings;
use MaterialsScript qw(:all);
use Math::Trig;  # 添加这行以支持反余弦函数
use Getopt::Long; # 添加参数处理模块

my $starttime = time;
my $timedoc = Documents->New("Time run cost.txt");
my $debugDoc = Documents->New("Debug_Info.txt");
$debugDoc->Append("开始执行脚本\n");

# 用于跟踪所有临时文件
my @tempFileNames = ();

#################################################################################################################
#                                         BEGIN USER INPUT                                                      #
#################################################################################################################

# 定义参数哈希表用于接收GUI传入的参数
my %Args;
GetOptions(\%Args, 
    "Start_Frame=i",
    "End_Frame=i",
    "Max_H_A_Distance=f", 
    "Min_D_H_A_Angle=f", 
    "Hydrogen_Bond_Elements=s", # 以逗号分隔的元素列表
    "Output_Detailed_Geometry=s", # 是否输出详细氢键几何数据，Yes或No
    "Sets_To_Analyze=s" # 要分析的特定Set（以逗号分隔）,留空则分析全部Set
);

# 读取轨迹文件
# 直接使用活动文档
my $trajectory = Documents->ActiveDocument;
if (!defined $trajectory) {
    $debugDoc->Append("错误：没有活动文档，请先打开轨迹文件\n");
    die "没有活动文档，请先打开轨迹文件";
}

# 检查文件类型，Materials Studio中轨迹文件可能有多种类型描述
my $docType = $trajectory->Type;
$debugDoc->Append("检测到活动文档类型: $docType\n");

# 更宽松的类型检查，接受多种可能的轨迹文件类型
if ($docType ne "Trajectory" && $docType ne "Trajectory Document" && $docType ne "TrajectoryDocument" && $docType !~ /Trajectory/) {
    $debugDoc->Append("警告：活动文档类型 '$docType' 可能不是轨迹文件，但将尝试处理\n");
    # 不再直接终止程序，而是继续尝试处理
    # 检查是否存在Trajectory或Frames属性作为额外验证
    eval {
        my $numFrames = $trajectory->NumFrames;
        if (defined $numFrames && $numFrames > 0) {
            $debugDoc->Append("活动文档包含 $numFrames 帧，将继续处理\n");
        } else {
            $debugDoc->Append("错误：活动文档没有帧数据，不是有效的轨迹文件\n");
            die "活动文档没有帧数据，不是有效的轨迹文件";
        }
    };
    if ($@) {
        $debugDoc->Append("错误：无法获取活动文档的帧数，请确保是轨迹文件\n");
        die "无法获取活动文档的帧数，请确保是轨迹文件: $@";
    }
}

my $trajectoryName = $trajectory->Name;
$debugDoc->Append("使用活动轨迹文档: $trajectoryName\n");

my $NumTrj = $trajectory->NumFrames;
$debugDoc->Append("轨迹文件帧数: $NumTrj\n");

# 确定分析的帧范围
my $startFrame = defined $Args{Start_Frame} ? $Args{Start_Frame} : 1;
my $endFrame = defined $Args{End_Frame} ? $Args{End_Frame} : $NumTrj;

# 验证帧范围有效性
if ($startFrame < 1) {
    $startFrame = 1;
    $debugDoc->Append("起始帧小于1，已调整为: 1\n");
}
if ($endFrame > $NumTrj) {
    $endFrame = $NumTrj;
    $debugDoc->Append("结束帧超出轨迹最大帧数，已调整为: $NumTrj\n");
}
if ($startFrame > $endFrame) {
    $startFrame = 1;
    $debugDoc->Append("起始帧大于结束帧，已调整起始帧为: 1\n");
}
$debugDoc->Append("将分析帧范围: $startFrame 至 $endFrame\n");

# 氢键计算参数设置
my $maxHydrogenAcceptorDistance = defined $Args{Max_H_A_Distance} ? $Args{Max_H_A_Distance} : 2.5;  # 氢键最大距离
my $minDonorHydrogenAcceptorAngle = defined $Args{Min_D_H_A_Angle} ? $Args{Min_D_H_A_Angle} : 90; # 氢键最小角度（度）
$debugDoc->Append("氢键参数设置 - 最大距离: $maxHydrogenAcceptorDistance Å, 最小角度: $minDonorHydrogenAcceptorAngle°\n");

# 是否输出详细几何表格
my $outputDetailedGeometry = defined $Args{Output_Detailed_Geometry} ? 
    ($Args{Output_Detailed_Geometry} eq "Yes" ? 1 : 0) : 0; # 默认为No
$debugDoc->Append("是否输出详细氢键几何数据: " . ($outputDetailedGeometry ? "是" : "否") . "\n");

# 元素列表作为氢键给体和受体
my @speciesList;
if (defined $Args{Hydrogen_Bond_Elements} && $Args{Hydrogen_Bond_Elements} ne "") {
    # 从GUI参数解析元素列表（以逗号分隔）
    @speciesList = split(/\s*,\s*/, $Args{Hydrogen_Bond_Elements});
    $debugDoc->Append("从GUI获取氢键元素: " . join(", ", @speciesList) . "\n");
} else {
    @speciesList = ("N", "O", "S", "F", "Cl"); # 默认元素列表
    $debugDoc->Append("使用默认氢键元素: " . join(", ", @speciesList) . "\n");
}

#################################################################################################################
#                                         END USER INPUT                                                        #
#################################################################################################################

# Function to calculate distance between two atoms
sub CalculateDistance {
    my ($atom1, $atom2) = @_;
    my $dx = $atom1->X - $atom2->X;
    my $dy = $atom1->Y - $atom2->Y;
    my $dz = $atom1->Z - $atom2->Z;
    
    return sqrt($dx*$dx + $dy*$dy + $dz*$dz);
}

# Function to calculate angle between three atoms (in degrees)
sub CalculateAngle {
    my ($atom1, $atom2, $atom3) = @_;
    
    # Vector from atom2 to atom1 (D-H)
    my $v1x = $atom1->X - $atom2->X;
    my $v1y = $atom1->Y - $atom2->Y;
    my $v1z = $atom1->Z - $atom2->Z;
    
    # Vector from atom2 to atom3 (H-A)
    my $v2x = $atom3->X - $atom2->X;
    my $v2y = $atom3->Y - $atom2->Y;
    my $v2z = $atom3->Z - $atom2->Z;
    
    # Dot product
    my $dotProduct = $v1x*$v2x + $v1y*$v2y + $v1z*$v2z;
    
    # Magnitude of vectors
    my $mag1 = sqrt($v1x*$v1x + $v1y*$v1y + $v1z*$v1z);
    my $mag2 = sqrt($v2x*$v2x + $v2y*$v2y + $v2z*$v2z);
    
    # Avoid division by zero
    if ($mag1 < 0.0001 || $mag2 < 0.0001) {
        return 0;
    }
    
    # Calculate angle in degrees
    my $cosTheta = $dotProduct / ($mag1 * $mag2);
    
    # Handle numerical precision issues
    if ($cosTheta > 1.0) { $cosTheta = 1.0; }
    if ($cosTheta < -1.0) { $cosTheta = -1.0; }
    
    my $angleRad = acos($cosTheta);
    my $angleDeg = $angleRad * 180.0 / 3.14159265358979;
    
    return $angleDeg;
}

# 创建一个索引文档，记录所有创建的Study Table
my $indexDoc = Documents->New("HBondStats_Index.txt");
$indexDoc->Append("Set名称\tStudy Table文件名\n");

# 创建一个哈希表来存储每个Set的Study Table文档
my %setDocuments;

# 创建一个哈希表来跟踪每个Set在其表格中的行索引
my %setRowIndices;

# 创建一个哈希表来存储每个Set的几何特性表格
my %geometryDocuments;

# 创建一个哈希表来跟踪每个Set的几何表格行索引
my %geometryRowIndices;

# 创建一个哈希表来存储每个Set的文本格式几何数据
my %geometryDataFiles;

# 添加氢键受体和给体元素
Tools->BondCalculation->HBonds->ClearDonors;
Tools->BondCalculation->HBonds->ClearAcceptors;
foreach my $element (@speciesList) {
    Tools->BondCalculation->HBonds->AddDonor($element);
    Tools->BondCalculation->HBonds->AddAcceptor($element);
}

# 存储所有检测到的Set名称
my %allSetNames;

# 解析用户指定的Set名称（如果有）
my %setsToAnalyze;
if (defined $Args{Sets_To_Analyze} && $Args{Sets_To_Analyze} ne "") {
    my @specifiedSets = split(/\s*,\s*/, $Args{Sets_To_Analyze});
    foreach my $setName (@specifiedSets) {
        $setsToAnalyze{$setName} = 1;
    }
    $debugDoc->Append("将只分析用户指定的Set: " . join(", ", @specifiedSets) . "\n");
} else {
    $debugDoc->Append("将分析所有可用的Set\n");
}

# 遍历每一帧
for (my $frame = $startFrame; $frame <= $endFrame; ++$frame) {
    $trajectory->CurrentFrame = $frame;
    $debugDoc->Append("处理帧 $frame\n");
   
    # 创建一个临时文档用于当前帧
    my $frameDoc = Documents->New("Frame_$frame.xsd");
    push @tempFileNames, "Frame_$frame.xsd";  # 添加到跟踪列表
    $frameDoc->CopyFrom($trajectory);
    
    # 检查是否有Sets
    my @setNames = ();
    my $sets = $frameDoc->UnitCell->Sets;
    my $setCount = $sets->Count;
    
    # 通过循环获取所有Set名称
    for (my $i = 0; $i < $setCount; $i++) {
        my $setName = $sets->Item($i)->Name;
        
        # 如果用户指定了要分析的Set，则只处理这些Set
        if (keys %setsToAnalyze > 0) {
            if (exists $setsToAnalyze{$setName}) {
                push @setNames, $setName;
                $allSetNames{$setName} = 1;  # 记录所有Set名称
            }
        } else {
            # 如果用户没有指定，则处理所有Set
            push @setNames, $setName;
            $allSetNames{$setName} = 1;  # 记录所有Set名称
        }
    }
    
    $debugDoc->Append("帧 $frame 中的Set数量: " . scalar(@setNames) . "\n");
    if (scalar(@setNames) > 0) {
        $debugDoc->Append("将处理以下Set: " . join(", ", @setNames) . "\n");
    }
    
    if ($setCount == 0) {
        $debugDoc->Append("警告：帧 $frame 没有任何Set集合，将整个结构作为一个整体处理\n");
        
        # 为WholeStructure创建Study Table（如果尚未创建）
        if (!exists $setDocuments{"WholeStructure"}) {
            $setDocuments{"WholeStructure"} = Documents->New("HBondStats_WholeStructure.std");
            $setRowIndices{"WholeStructure"} = 0;
            
            # 设置列标题
            $setDocuments{"WholeStructure"}->ColumnHeading(0) = "Frame";
            $setDocuments{"WholeStructure"}->ColumnHeading(1) = "Structure";
            $setDocuments{"WholeStructure"}->ColumnHeading(2) = "Total H-Bonds";
            $setDocuments{"WholeStructure"}->ColumnHeading(3) = "Avg H-A Distance (Å)";  # 新增列
            $setDocuments{"WholeStructure"}->ColumnHeading(4) = "Avg D-H-A Angle (°)";   # 新增列
            
            # 记录到索引文件
            $indexDoc->Append("WholeStructure\tHBondStats_WholeStructure.std\n");
            
            # 创建几何特性表格
            if ($outputDetailedGeometry) {
                $geometryDocuments{"WholeStructure"} = Documents->New("HBondGeometry_WholeStructure.std");
                $geometryDocuments{"WholeStructure"}->ColumnHeading(0) = "Frame";
                $geometryDocuments{"WholeStructure"}->ColumnHeading(1) = "HBond ID";
                $geometryDocuments{"WholeStructure"}->ColumnHeading(2) = "H-A Distance (Å)";
                $geometryDocuments{"WholeStructure"}->ColumnHeading(3) = "D-H-A Angle (°)";
                $geometryDocuments{"WholeStructure"}->ColumnHeading(4) = "Donor";
                $geometryDocuments{"WholeStructure"}->ColumnHeading(5) = "Acceptor";
                
                $geometryRowIndices{"WholeStructure"} = 0;
                
                # 创建文本格式的几何数据文件
                $geometryDataFiles{"WholeStructure"} = Documents->New("HBondGeometryData_WholeStructure.txt");
                $geometryDataFiles{"WholeStructure"}->Append("Frame\tHBond_ID\tH-A_Distance(Å)\tD-H-A_Angle(°)\tDonor\tAcceptor\n");
                
                $indexDoc->Append("WholeStructure\tHBondGeometry_WholeStructure.std\n");
            }
            
            # 立即保存确保文档被创建
            $setDocuments{"WholeStructure"}->Save;
            if ($outputDetailedGeometry) {
                $geometryDocuments{"WholeStructure"}->Save;
                $geometryDataFiles{"WholeStructure"}->Save;
            }
            
            # 计算整个结构的氢键
            Tools->BondCalculation->HBonds->Calculate($frameDoc, Settings(
                MaxHydrogenAcceptorDistance => "$maxHydrogenAcceptorDistance",
                MinDonorHydrogenAcceptorAngle => "$minDonorHydrogenAcceptorAngle"
            ));
            my $hbonds = $frameDoc->UnitCell->HydrogenBonds;
            my $hbondCount = scalar(@$hbonds);
            $debugDoc->Append("整个结构中的氢键数: $hbondCount\n");
            
            # 计算平均氢键距离和角度
            my $totalDistance = 0;
            my $totalAngle = 0;
            my $geometryRowIndex = $outputDetailedGeometry ? $geometryRowIndices{"WholeStructure"} : 0;
            
            $debugDoc->Append("开始处理整体结构氢键几何信息\n");

            foreach my $hbond (@$hbonds) {
                my $donorAtom = $hbond->Donor;
                my $hydrogenAtom = $hbond->Hydrogen;
                my $acceptorAtom = $hbond->Acceptor;
                
                my $haDistance = CalculateDistance($hydrogenAtom, $acceptorAtom);
                my $dhaAngle = CalculateAngle($donorAtom, $hydrogenAtom, $acceptorAtom);
                
                $totalDistance += $haDistance;
                $totalAngle += $dhaAngle;
                
                # 只有当需要详细几何数据时才记录每个氢键的几何信息
                if ($outputDetailedGeometry && defined $geometryDocuments{"WholeStructure"}) {
                    $geometryDocuments{"WholeStructure"}->InsertRow($geometryRowIndex);
                    $geometryDocuments{"WholeStructure"}->Cell($geometryRowIndex, 0) = $frame;
                    $geometryDocuments{"WholeStructure"}->Cell($geometryRowIndex, 1) = $geometryRowIndex - $geometryRowIndices{"WholeStructure"} + 1;  # HBond ID
                    $geometryDocuments{"WholeStructure"}->Cell($geometryRowIndex, 2) = $haDistance;
                    $geometryDocuments{"WholeStructure"}->Cell($geometryRowIndex, 3) = $dhaAngle;
                    $geometryDocuments{"WholeStructure"}->Cell($geometryRowIndex, 4) = $donorAtom->ElementSymbol . $donorAtom->ID;
                    $geometryDocuments{"WholeStructure"}->Cell($geometryRowIndex, 5) = $acceptorAtom->ElementSymbol . $acceptorAtom->ID;
                    
                    # 同时记录到文本文件
                    $geometryDataFiles{"WholeStructure"}->Append("$frame\t" . ($geometryRowIndex - $geometryRowIndices{"WholeStructure"} + 1) . "\t$haDistance\t$dhaAngle\t" . $donorAtom->ElementSymbol . $donorAtom->ID . "\t" . $acceptorAtom->ElementSymbol . $acceptorAtom->ID . "\n");
                    
                    # 每10个氢键保存一次，减少文件操作频率但确保数据不丢失
                    if ($geometryRowIndex % 10 == 0) {
                        $geometryDocuments{"WholeStructure"}->Save;
                    }
                    
                    $geometryRowIndex++;
                }
            }

            # 最后一定要保存表格
            if ($outputDetailedGeometry && defined $geometryDocuments{"WholeStructure"}) {
                $geometryDocuments{"WholeStructure"}->Save;
                $geometryDataFiles{"WholeStructure"}->Save;
                
                # 更新几何表格的行索引
                $geometryRowIndices{"WholeStructure"} = $geometryRowIndex;
                $debugDoc->Append("完成整体结构氢键几何信息记录，共计: " . ($geometryRowIndex - $geometryRowIndices{"WholeStructure"} + $hbondCount) . " 个氢键\n");
            }
            
            # 计算平均值
            my $avgDistance = $hbondCount > 0 ? $totalDistance / $hbondCount : 0;
            my $avgAngle = $hbondCount > 0 ? $totalAngle / $hbondCount : 0;
            
            # 获取当前行索引
            my $wholeRowIndex = $setRowIndices{"WholeStructure"};
            
            # 记录结果到Study Table
            $setDocuments{"WholeStructure"}->InsertRow($wholeRowIndex);
            $setDocuments{"WholeStructure"}->Cell($wholeRowIndex, 0) = $frame;
            $setDocuments{"WholeStructure"}->Cell($wholeRowIndex, 1) = $frameDoc;
            $setDocuments{"WholeStructure"}->Cell($wholeRowIndex, 2) = $hbondCount;
            $setDocuments{"WholeStructure"}->Cell($wholeRowIndex, 3) = $avgDistance;  # 新增列数据：平均H-A距离
            $setDocuments{"WholeStructure"}->Cell($wholeRowIndex, 4) = $avgAngle;     # 新增列数据：平均D-H-A角度
            
            # 更新行索引
            $setRowIndices{"WholeStructure"}++;
            
            # 保存文档确保数据被记录
            $setDocuments{"WholeStructure"}->Save;
            $geometryDocuments{"WholeStructure"}->Save if defined $geometryDocuments{"WholeStructure"};
            $geometryDataFiles{"WholeStructure"}->Save if defined $geometryDataFiles{"WholeStructure"};
            $debugDoc->Append("已保存帧 $frame 的整体数据，共 $hbondCount 个氢键，平均距离: $avgDistance Å，平均角度: $avgAngle °\n");
            
        }
        
        # 计算整个结构的氢键
        Tools->BondCalculation->HBonds->Calculate($frameDoc, Settings(
            MaxHydrogenAcceptorDistance => "$maxHydrogenAcceptorDistance",
            MinDonorHydrogenAcceptorAngle => "$minDonorHydrogenAcceptorAngle"
        ));
        my $hbonds = $frameDoc->UnitCell->HydrogenBonds;
        my $hbondCount = scalar(@$hbonds);
        $debugDoc->Append("整个结构中的氢键数: $hbondCount\n");
        
        # 计算平均氢键距离和角度
        my $totalDistance = 0;
        my $totalAngle = 0;
        my $geometryRowIndex = $outputDetailedGeometry ? $geometryRowIndices{"WholeStructure"} : 0;
        
        $debugDoc->Append("开始处理整体结构氢键几何信息\n");

        foreach my $hbond (@$hbonds) {
            my $donorAtom = $hbond->Donor;
            my $hydrogenAtom = $hbond->Hydrogen;
            my $acceptorAtom = $hbond->Acceptor;
            
            my $haDistance = CalculateDistance($hydrogenAtom, $acceptorAtom);
            my $dhaAngle = CalculateAngle($donorAtom, $hydrogenAtom, $acceptorAtom);
            
            $totalDistance += $haDistance;
            $totalAngle += $dhaAngle;
            
            # 只有当需要详细几何数据时才记录每个氢键的几何信息
            if ($outputDetailedGeometry && defined $geometryDocuments{"WholeStructure"}) {
                $geometryDocuments{"WholeStructure"}->InsertRow($geometryRowIndex);
                $geometryDocuments{"WholeStructure"}->Cell($geometryRowIndex, 0) = $frame;
                $geometryDocuments{"WholeStructure"}->Cell($geometryRowIndex, 1) = $geometryRowIndex - $geometryRowIndices{"WholeStructure"} + 1;  # HBond ID
                $geometryDocuments{"WholeStructure"}->Cell($geometryRowIndex, 2) = $haDistance;
                $geometryDocuments{"WholeStructure"}->Cell($geometryRowIndex, 3) = $dhaAngle;
                $geometryDocuments{"WholeStructure"}->Cell($geometryRowIndex, 4) = $donorAtom->ElementSymbol . $donorAtom->ID;
                $geometryDocuments{"WholeStructure"}->Cell($geometryRowIndex, 5) = $acceptorAtom->ElementSymbol . $acceptorAtom->ID;
                
                # 同时记录到文本文件
                $geometryDataFiles{"WholeStructure"}->Append("$frame\t" . ($geometryRowIndex - $geometryRowIndices{"WholeStructure"} + 1) . "\t$haDistance\t$dhaAngle\t" . $donorAtom->ElementSymbol . $donorAtom->ID . "\t" . $acceptorAtom->ElementSymbol . $acceptorAtom->ID . "\n");
                
                # 每10个氢键保存一次，减少文件操作频率但确保数据不丢失
                if ($geometryRowIndex % 10 == 0) {
                    $geometryDocuments{"WholeStructure"}->Save;
                }
                
                $geometryRowIndex++;
            }
        }

        # 最后一定要保存表格
        if ($outputDetailedGeometry && defined $geometryDocuments{"WholeStructure"}) {
            $geometryDocuments{"WholeStructure"}->Save;
            $geometryDataFiles{"WholeStructure"}->Save;
            
            # 更新几何表格的行索引
            $geometryRowIndices{"WholeStructure"} = $geometryRowIndex;
            $debugDoc->Append("完成整体结构氢键几何信息记录，共计: " . ($geometryRowIndex - $geometryRowIndices{"WholeStructure"} + $hbondCount) . " 个氢键\n");
        }
        
        # 计算平均值
        my $avgDistance = $hbondCount > 0 ? $totalDistance / $hbondCount : 0;
        my $avgAngle = $hbondCount > 0 ? $totalAngle / $hbondCount : 0;
        
        # 获取当前行索引
        my $wholeRowIndex = $setRowIndices{"WholeStructure"};
        
        # 记录结果到Study Table
        $setDocuments{"WholeStructure"}->InsertRow($wholeRowIndex);
        $setDocuments{"WholeStructure"}->Cell($wholeRowIndex, 0) = $frame;
        $setDocuments{"WholeStructure"}->Cell($wholeRowIndex, 1) = $frameDoc;
        $setDocuments{"WholeStructure"}->Cell($wholeRowIndex, 2) = $hbondCount;
        $setDocuments{"WholeStructure"}->Cell($wholeRowIndex, 3) = $avgDistance;  # 新增列数据：平均H-A距离
        $setDocuments{"WholeStructure"}->Cell($wholeRowIndex, 4) = $avgAngle;     # 新增列数据：平均D-H-A角度
        
        # 更新行索引
        $setRowIndices{"WholeStructure"}++;
        
        # 保存文档确保数据被记录
        $setDocuments{"WholeStructure"}->Save;
        $geometryDocuments{"WholeStructure"}->Save if defined $geometryDocuments{"WholeStructure"};
        $geometryDataFiles{"WholeStructure"}->Save if defined $geometryDataFiles{"WholeStructure"};
        $debugDoc->Append("已保存帧 $frame 的整体数据，共 $hbondCount 个氢键，平均距离: $avgDistance Å，平均角度: $avgAngle °\n");
        
    } else {
        # 有Set的情况，遍历每个Set
        foreach my $setName (@setNames) {
            $debugDoc->Append("处理Set: $setName\n");
            
            # 为该Set创建Study Table（如果尚未创建）
            if (!exists $setDocuments{$setName}) {
                # 创建文件名，替换可能在文件名中造成问题的字符
                my $safeSetName = $setName;
                $safeSetName =~ s/[\\\/\:\*\?\"\<\>\|]/_/g;  # 替换不合法的文件名字符
                
                $setDocuments{$setName} = Documents->New("HBondStats_Set_${safeSetName}.std");
                $setRowIndices{$setName} = 0;
                
                # 设置列标题
                $setDocuments{$setName}->ColumnHeading(0) = "Frame";
                $setDocuments{$setName}->ColumnHeading(1) = "Structure";
                $setDocuments{$setName}->ColumnHeading(2) = "Total H-Bonds in Set";
                $setDocuments{$setName}->ColumnHeading(3) = "Avg H-A Distance (Å)";  # 新增列
                $setDocuments{$setName}->ColumnHeading(4) = "Avg D-H-A Angle (°)";   # 新增列
                
                # 创建几何特性表格
                if ($outputDetailedGeometry) {
                    $geometryDocuments{$setName} = Documents->New("HBondGeometry_Set_${safeSetName}.std");
                    $geometryDocuments{$setName}->ColumnHeading(0) = "Frame";
                    $geometryDocuments{$setName}->ColumnHeading(1) = "Molecule ID";
                    $geometryDocuments{$setName}->ColumnHeading(2) = "HBond ID";
                    $geometryDocuments{$setName}->ColumnHeading(3) = "H-A Distance (Å)";
                    $geometryDocuments{$setName}->ColumnHeading(4) = "D-H-A Angle (°)";
                    $geometryDocuments{$setName}->ColumnHeading(5) = "Donor";
                    $geometryDocuments{$setName}->ColumnHeading(6) = "Acceptor";
                    
                    $geometryRowIndices{$setName} = 0;
                    
                    # 创建文本格式的几何数据文件
                    $geometryDataFiles{$setName} = Documents->New("HBondGeometryData_Set_${safeSetName}.txt");
                    $geometryDataFiles{$setName}->Append("Frame\tMolecule_ID\tHBond_ID\tH-A_Distance(Å)\tD-H-A_Angle(°)\tDonor\tAcceptor\n");
                    
                    $indexDoc->Append("$setName\tHBondGeometry_Set_${safeSetName}.std\n");
                }
                
                # 记录到索引文件
                $indexDoc->Append("$setName\tHBondStats_Set_${safeSetName}.std\n");
                
                # 立即保存确保文档被创建
                $setDocuments{$setName}->Save;
                if ($outputDetailedGeometry) {
                    $geometryDocuments{$setName}->Save;
                    $geometryDataFiles{$setName}->Save;
                }
            }
            
            # 创建临时文件并只复制当前Set的所有原子
            my $tempSetDoc = Documents->New("TempSet_${frame}_${setName}.xsd");
            push @tempFileNames, "TempSet_${frame}_${setName}.xsd";  # 添加到跟踪列表
            
            # 获取当前Set
            my $set = $frameDoc->UnitCell->Sets($setName);
            
            # 获取Set中的所有原子
            my $setAtoms = $set->Atoms;
            my $atomCount = scalar(@$setAtoms);
            $debugDoc->Append("Set $setName 中的原子数: $atomCount\n");
            
            # 跳过空Set
            if ($atomCount == 0) {
                $debugDoc->Append("警告：Set $setName 没有原子，跳过\n");
                $tempSetDoc->Delete;
                next;
            }
            
            # 复制Set中的所有原子到临时文档
            foreach my $atom (@$setAtoms) {
                my $newAtom = $tempSetDoc->UnitCell->CreateAtom(
                    $atom->ElementSymbol,
                    $atom->XYZ
                );
                
                # 复制原子属性（如果需要）
                $newAtom->ForcefieldType = $atom->ForcefieldType if defined $atom->ForcefieldType;
                $newAtom->Charge = $atom->Charge if defined $atom->Charge;
            }
            
            # 重新计算键
            eval {
                # 尝试使用可能的键计算函数
                Tools->BondCalculation->Calculate($tempSetDoc);
            };
            if ($@) {
                eval {
                    # 第二种可能的键计算方法
                    Tools->Bonding->Calculate($tempSetDoc);
                };
                if ($@) {
                    # 第三种方法：直接尝试自动计算键
                    $debugDoc->Append("尝试使用另一种方法计算键\n");
                    eval {
                        $tempSetDoc->CalculateBonds;
                    };
                    if ($@) {
                        $debugDoc->Append("警告：无法使用已知方法计算键，将直接进行氢键计算\n");
                    }
                }
            }
            
            # 计算Set内的氢键
            Tools->BondCalculation->HBonds->Calculate($tempSetDoc, Settings(
                MaxHydrogenAcceptorDistance => "$maxHydrogenAcceptorDistance",
                MinDonorHydrogenAcceptorAngle => "$minDonorHydrogenAcceptorAngle"
            ));
            my $hbonds = $tempSetDoc->UnitCell->HydrogenBonds;
            my $hbondCount = scalar(@$hbonds);
            $debugDoc->Append("Set $setName 中的氢键数: $hbondCount\n");
            
            # 计算平均氢键距离和角度
            my $totalDistance = 0;
            my $totalAngle = 0;
            my $geometryRowIndex = $outputDetailedGeometry ? $geometryRowIndices{$setName} : 0;
            
            $debugDoc->Append("开始处理Set $setName 的氢键几何信息\n");

            foreach my $hbond (@$hbonds) {
                my $donorAtom = $hbond->Donor;
                my $hydrogenAtom = $hbond->Hydrogen;
                my $acceptorAtom = $hbond->Acceptor;
                
                my $haDistance = CalculateDistance($hydrogenAtom, $acceptorAtom);
                my $dhaAngle = CalculateAngle($donorAtom, $hydrogenAtom, $acceptorAtom);
                
                $totalDistance += $haDistance;
                $totalAngle += $dhaAngle;
                
                # 只有当需要详细几何数据时才记录
                if ($outputDetailedGeometry && defined $geometryDocuments{$setName}) {
                    # 记录每个氢键的几何信息到几何表格
                    $geometryDocuments{$setName}->InsertRow($geometryRowIndex);
                    $geometryDocuments{$setName}->Cell($geometryRowIndex, 0) = $frame;
                    $geometryDocuments{$setName}->Cell($geometryRowIndex, 1) = 0;  # Set级别氢键，分子ID设为0
                    $geometryDocuments{$setName}->Cell($geometryRowIndex, 2) = $geometryRowIndex - $geometryRowIndices{$setName} + 1;  # HBond ID
                    $geometryDocuments{$setName}->Cell($geometryRowIndex, 3) = $haDistance;
                    $geometryDocuments{$setName}->Cell($geometryRowIndex, 4) = $dhaAngle;
                    $geometryDocuments{$setName}->Cell($geometryRowIndex, 5) = $donorAtom->ElementSymbol . $donorAtom->ID;
                    $geometryDocuments{$setName}->Cell($geometryRowIndex, 6) = $acceptorAtom->ElementSymbol . $acceptorAtom->ID;
                    
                    # 同时记录到文本文件
                    $geometryDataFiles{$setName}->Append("$frame\t0\t" . ($geometryRowIndex - $geometryRowIndices{$setName} + 1) . "\t$haDistance\t$dhaAngle\t" . $donorAtom->ElementSymbol . $donorAtom->ID . "\t" . $acceptorAtom->ElementSymbol . $acceptorAtom->ID . "\n");
                    
                    # 每10个氢键保存一次，减少文件操作频率但确保数据不丢失
                    if ($geometryRowIndex % 10 == 0) {
                        $geometryDocuments{$setName}->Save;
                    }
                    
                    $geometryRowIndex++;
                }
            }

            # 最后一定要保存表格
            if ($outputDetailedGeometry && defined $geometryDocuments{$setName}) {
                $geometryDocuments{$setName}->Save;
                $geometryDataFiles{$setName}->Save;
                
                # 更新几何表格的行索引
                $geometryRowIndices{$setName} = $geometryRowIndex;
                $debugDoc->Append("完成Set $setName 的氢键几何信息记录，共计: " . ($geometryRowIndex - $geometryRowIndices{$setName} + $hbondCount) . " 个氢键\n");
            }
            
            # 计算平均值
            my $avgDistance = $hbondCount > 0 ? $totalDistance / $hbondCount : 0;
            my $avgAngle = $hbondCount > 0 ? $totalAngle / $hbondCount : 0;
            
            # 获取当前行索引
            my $setRowIndex = $setRowIndices{$setName};
            
            # 记录结果到对应Set的Study Table
            $setDocuments{$setName}->InsertRow($setRowIndex);
            $setDocuments{$setName}->Cell($setRowIndex, 0) = $frame;
            
            # 直接引用临时结构，这样表格中能显示结构，但不创建永久文件
            $setDocuments{$setName}->Cell($setRowIndex, 1) = $tempSetDoc;
            
            $setDocuments{$setName}->Cell($setRowIndex, 2) = $hbondCount;
            $setDocuments{$setName}->Cell($setRowIndex, 3) = $avgDistance;  # 新增列数据：平均H-A距离
            $setDocuments{$setName}->Cell($setRowIndex, 4) = $avgAngle;     # 新增列数据：平均D-H-A角度
            
            # 保存表格，以便结构引用能正常显示
            $setDocuments{$setName}->Save;
            
            # 尝试识别Set中的分子并计算每个分子的氢键数
            my $molecules = $tempSetDoc->UnitCell->Molecules;
            my $moleculeCount = scalar(@$molecules);
            $debugDoc->Append("Set $setName 中的分子数: $moleculeCount\n");
            
            # 保存分子信息以防止在删除文档后丢失
            my @moleculeData = ();
            for (my $molIdx = 0; $molIdx < $moleculeCount; $molIdx++) {
                my $molecule = $molecules->[$molIdx];
                my $moleculeAtoms = $molecule->Atoms;
                my $molAtomCount = scalar(@$moleculeAtoms);
                
                my @atomData = ();
                foreach my $atom (@$moleculeAtoms) {
                    push @atomData, {
                        ElementSymbol => $atom->ElementSymbol,
                        XYZ => $atom->XYZ,
                        ForcefieldType => defined $atom->ForcefieldType ? $atom->ForcefieldType : undef,
                        Charge => defined $atom->Charge ? $atom->Charge : undef
                    };
                }
                
                push @moleculeData, {
                    AtomCount => $molAtomCount,
                    Atoms => [@atomData]
                };
            }
            
            # 删除临时Set文档，释放内存
            $tempSetDoc->Delete;
            $debugDoc->Append("已删除临时Set文档，释放内存\n");
            
            # 如果有分子，为每个分子添加列（如果尚未添加）
            if ($moleculeCount > 0) {
                # 确保表格有足够的列来存放每个分子的氢键数
                for (my $i = 0; $i < $moleculeCount; $i++) {
                    my $baseColumnIndex = 5 + $i * 3;  # 修改起始列索引，每个分子占用3列
                    if (!defined $setDocuments{$setName}->ColumnHeading($baseColumnIndex)) {
                        $setDocuments{$setName}->ColumnHeading($baseColumnIndex) = "Molecule " . ($i + 1) . " H-Bonds";
                        $setDocuments{$setName}->ColumnHeading($baseColumnIndex + 1) = "Molecule " . ($i + 1) . " Avg Distance (Å)";
                        $setDocuments{$setName}->ColumnHeading($baseColumnIndex + 2) = "Molecule " . ($i + 1) . " Avg Angle (°)";
                    }
                }
                
                # 处理每个分子
                for (my $molIdx = 0; $molIdx < $moleculeCount; $molIdx++) {
                    # 使用之前保存的分子数据
                    my $molData = $moleculeData[$molIdx];
                    my $molAtomCount = $molData->{AtomCount};
                    
                    # 创建临时文件并只复制当前分子
                    my $tempMoleculeDoc = Documents->New("TempMolecule_${frame}_${setName}_${molIdx}.xsd");
                    push @tempFileNames, "TempMolecule_${frame}_${setName}_${molIdx}.xsd";  # 添加到跟踪列表
                    
                    if ($molAtomCount > 0) {
                        foreach my $atomData (@{$molData->{Atoms}}) {
                            my $newAtom = $tempMoleculeDoc->UnitCell->CreateAtom(
                                $atomData->{ElementSymbol},
                                $atomData->{XYZ}
                            );
                            
                            # 复制原子属性（如果需要）
                            $newAtom->ForcefieldType = $atomData->{ForcefieldType} if defined $atomData->{ForcefieldType};
                            $newAtom->Charge = $atomData->{Charge} if defined $atomData->{Charge};
                        }
                        
                        # 重新计算键
                        eval {
                            Tools->BondCalculation->Calculate($tempMoleculeDoc);
                        };
                        if ($@) {
                            eval {
                                Tools->Bonding->Calculate($tempMoleculeDoc);
                            };
                            if ($@) {
                                eval {
                                    $tempMoleculeDoc->CalculateBonds;
                                };
                            }
                        }
                        
                        # 计算分子内的氢键
                        Tools->BondCalculation->HBonds->Calculate($tempMoleculeDoc, Settings(
                            MaxHydrogenAcceptorDistance => "$maxHydrogenAcceptorDistance",
                            MinDonorHydrogenAcceptorAngle => "$minDonorHydrogenAcceptorAngle"
                        ));
                        my $molHbonds = $tempMoleculeDoc->UnitCell->HydrogenBonds;
                        my $molHbondCount = scalar(@$molHbonds);
                        $debugDoc->Append("Set $setName 中分子 " . ($molIdx + 1) . " 的氢键数: $molHbondCount\n");
                        
                        # 计算分子内氢键的平均距离和角度
                        my $molTotalDistance = 0;
                        my $molTotalAngle = 0;
                        my $molGeometryRowCount = 0;
                        
                        $debugDoc->Append("开始处理Set $setName 分子 " . ($molIdx + 1) . " 的氢键几何信息\n");

                        foreach my $molHbond (@$molHbonds) {
                            my $donorAtom = $molHbond->Donor;
                            my $hydrogenAtom = $molHbond->Hydrogen;
                            my $acceptorAtom = $molHbond->Acceptor;
                            
                            my $haDistance = CalculateDistance($hydrogenAtom, $acceptorAtom);
                            my $dhaAngle = CalculateAngle($donorAtom, $hydrogenAtom, $acceptorAtom);
                            
                            $molTotalDistance += $haDistance;
                            $molTotalAngle += $dhaAngle;
                            
                            # 只有当需要详细几何数据时才记录
                            if ($outputDetailedGeometry && defined $geometryDocuments{$setName}) {
                                # 记录每个氢键的几何信息到几何表格
                                $geometryDocuments{$setName}->InsertRow($geometryRowIndex);
                                $geometryDocuments{$setName}->Cell($geometryRowIndex, 0) = $frame;
                                $geometryDocuments{$setName}->Cell($geometryRowIndex, 1) = $molIdx + 1;  # Molecule ID
                                $geometryDocuments{$setName}->Cell($geometryRowIndex, 2) = $molGeometryRowCount + 1;  # HBond ID within molecule
                                $geometryDocuments{$setName}->Cell($geometryRowIndex, 3) = $haDistance;
                                $geometryDocuments{$setName}->Cell($geometryRowIndex, 4) = $dhaAngle;
                                $geometryDocuments{$setName}->Cell($geometryRowIndex, 5) = $donorAtom->ElementSymbol . $donorAtom->ID;
                                $geometryDocuments{$setName}->Cell($geometryRowIndex, 6) = $acceptorAtom->ElementSymbol . $acceptorAtom->ID;
                                
                                # 同时记录到文本文件
                                $geometryDataFiles{$setName}->Append("$frame\t" . ($molIdx + 1) . "\t" . ($molGeometryRowCount + 1) . "\t$haDistance\t$dhaAngle\t" . $donorAtom->ElementSymbol . $donorAtom->ID . "\t" . $acceptorAtom->ElementSymbol . $acceptorAtom->ID . "\n");
                                
                                # 每10个氢键保存一次，减少文件操作频率但确保数据不丢失
                                if ($geometryRowIndex % 10 == 0) {
                                    $geometryDocuments{$setName}->Save;
                                }
                                
                                $geometryRowIndex++;
                                $molGeometryRowCount++;
                            }
                        }

                        # 最后一定要保存几何表格
                        $geometryDocuments{$setName}->Save;
                        $geometryDataFiles{$setName}->Save;

                        $debugDoc->Append("完成Set $setName 分子 " . ($molIdx + 1) . " 的氢键几何信息记录，共计 $molGeometryRowCount 个氢键\n");
                        
                        # 计算分子内氢键的平均值
                        my $molAvgDistance = $molHbondCount > 0 ? $molTotalDistance / $molHbondCount : 0;
                        my $molAvgAngle = $molHbondCount > 0 ? $molTotalAngle / $molHbondCount : 0;
                        
                        # 记录分子的氢键数和平均几何特性到对应Set的Study Table
                        my $baseColumnIndex = 5 + $molIdx * 3;  # 更新列索引计算
                        $setDocuments{$setName}->Cell($setRowIndex, $baseColumnIndex) = $molHbondCount;
                        $setDocuments{$setName}->Cell($setRowIndex, $baseColumnIndex + 1) = $molAvgDistance;
                        $setDocuments{$setName}->Cell($setRowIndex, $baseColumnIndex + 2) = $molAvgAngle;
                    } else {
                        # 如果分子没有原子，则氢键数和平均几何特性都为0
                        my $baseColumnIndex = 5 + $molIdx * 3;
                        $setDocuments{$setName}->Cell($setRowIndex, $baseColumnIndex) = 0;
                        $setDocuments{$setName}->Cell($setRowIndex, $baseColumnIndex + 1) = 0;
                        $setDocuments{$setName}->Cell($setRowIndex, $baseColumnIndex + 2) = 0;
                    }
                    
                    # 删除临时分子文档
                    $tempMoleculeDoc->Delete;
                    $debugDoc->Append("已删除临时分子文档，释放内存\n");
                }
            }
            
            # 更新行索引
            $setRowIndices{$setName}++;
            
            # 保存文档确保数据被记录
            $setDocuments{$setName}->Save;
            if ($outputDetailedGeometry) {
                if (defined $geometryDocuments{$setName}) {
                    $geometryDocuments{$setName}->Save;
                }
                if (defined $geometryDataFiles{$setName}) {
                    $geometryDataFiles{$setName}->Save;
                }
            }
            $debugDoc->Append("已保存Set $setName 帧 $frame 的数据（行 $setRowIndex）\n");
        }
    }
    
    # 删除当前帧文档
    $frameDoc->Delete;
    $debugDoc->Append("已删除帧 $frame 的临时文档，释放内存\n");
    
    # 强制执行垃圾回收
    eval {
        GC->Collect();
    };
    if ($@) {
        $debugDoc->Append("垃圾回收不可用，继续处理\n");
    }
}

# 总结统计信息
$debugDoc->Append("\n========= 氢键分析总结 =========\n");
my $totalSets = scalar(keys %allSetNames);
$debugDoc->Append("总共处理了 $totalSets 个Set:\n");
foreach my $setName (sort keys %allSetNames) {
    my $rowCount = exists $setRowIndices{$setName} ? $setRowIndices{$setName} : 0;
    my $geoRowCount = defined $geometryRowIndices{$setName} ? $geometryRowIndices{$setName} : 0;
    $debugDoc->Append("Set $setName:\n");
    $debugDoc->Append("  - 主表格行数: $rowCount\n");
    
    if ($outputDetailedGeometry) {
        $debugDoc->Append("  - 几何表格行数: $geoRowCount\n");
        # 检查几何表格是否有数据
        if ($geoRowCount == 0 && $rowCount > 0) {
            $debugDoc->Append("  - 警告: 几何表格没有数据，但主表格有数据!\n");
        }
    }
}

# 如果用户指定了特定Set但未找到
if (keys %setsToAnalyze > 0) {
    my $foundCount = 0;
    foreach my $specifiedSet (keys %setsToAnalyze) {
        if (exists $allSetNames{$specifiedSet}) {
            $foundCount++;
        } else {
            $debugDoc->Append("警告: 未找到用户指定的Set '$specifiedSet'\n");
        }
    }
    if ($foundCount == 0 && keys %setsToAnalyze > 0) {
        $debugDoc->Append("严重警告: 未找到任何用户指定的Set! 请检查Set名称拼写是否正确。\n");
    }
}

$debugDoc->Append("===========================\n\n");

# 尝试删除可能剩余的临时文件
$debugDoc->Append("开始清理临时文件...\n");
foreach my $fileName (@tempFileNames) {
    eval {
        # 直接尝试获取文档，如果不存在会在eval中捕获错误
        my $doc = $Documents{$fileName};
        if (defined $doc) {
            $doc->Delete;
            $debugDoc->Append("已删除临时文件: $fileName\n");
        }
    };
    if ($@) {
        # 如果有错误，可能是文件已经不存在了，或者其他原因
        $debugDoc->Append("临时文件 $fileName 可能已被删除或无法访问\n");
    }
}
$debugDoc->Append("临时文件清理完成\n");

# 保存索引文档
$indexDoc->Save;
$debugDoc->Save;

# 计算运行时间并输出
my $endtime = time;
sub FormatTimeOut {
    my ($secs) = @_;
    if ($secs > 3600) {
        my $h = int($secs / 3600);
        my $m = int(($secs - 3600 * $h) / 60);
        my $s = $secs - 3600 * $h - 60 * $m;
        $timedoc->Append("Time Used: $h hours $m minutes $s seconds");
    } elsif ($secs == 3600) {
        $timedoc->Append("Time Used: 1 hour");
    } elsif ($secs > 60) {
        my $m = int($secs / 60);
        my $s = $secs - 60 * $m;
        $timedoc->Append("Time Used: $m minutes $s seconds");
    } elsif ($secs == 60) {
        $timedoc->Append("Time Used: 1 minute");
    } else {
        $timedoc->Append("Time Used: $secs seconds");
    }
}
FormatTimeOut($endtime-$starttime);
$timedoc->Save;

# 在运行结束时，输出氢键数据文件的位置
$debugDoc->Append("\n========= 氢键数据文件 =========\n");
$debugDoc->Append("主统计表格:\n");
foreach my $setName (sort keys %setDocuments) {
    $debugDoc->Append("  - " . $setName . ": " . $setDocuments{$setName}->Name . "\n");
}

if ($outputDetailedGeometry) {
    $debugDoc->Append("\n详细几何数据表格:\n");
    foreach my $setName (sort keys %geometryDocuments) {
        if (defined $geometryDocuments{$setName}) {
            $debugDoc->Append("  - " . $setName . ": " . $geometryDocuments{$setName}->Name . "\n");
            if (defined $geometryDataFiles{$setName}) {
                $debugDoc->Append("    文本格式: " . $geometryDataFiles{$setName}->Name . "\n");
            }
        }
    }
}
$debugDoc->Append("==============================\n");
$debugDoc->Save;
