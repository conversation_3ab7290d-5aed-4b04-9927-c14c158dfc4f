#!/usr/bin/env python3
# coding = utf-8

from ast import While
import sys
import subprocess as sp
import numpy as np
import time
from math import *

#	usage:
#	./TooCloseAtoms.py > POSCAR_REV.vasp
#	Input file: POSCAR
#	Output file: stdout

def Main():
	CriticalDist = 0.5
	#AtomsOfInterest = ["I","Br"]	#	types of atoms for which too close atoms will be checked, set -1 if check all atoms
	AtomsOfInterest = -1	#	types of atoms for which too close atoms will be checked, set -1 if check all atoms
	print("CriticalDist = %.2f"%CriticalDist,"AtomsOfInterest =",AtomsOfInterest)

	TV_List,AtomNameList,AtomCntList,AtomCoordList = ReadPOSCAR("POSCAR")
	if (AtomsOfInterest==-1):
		AtomsOfInterest = AtomNameList
	else:
		AtomsOfInterest = [x for x in AtomsOfInterest if x in AtomNameList]

	AtomNameLongList = []	#	like ["I","I","I","I","Cs","Cs",..]
	for i in range(len(AtomCntList)):
		AtomNameLongList = AtomNameLongList + AtomCntList[i]*[AtomNameList[i]]	#	Like 4*"Br"
	for i in range(len(AtomCoordList)):
		AtomCoordList[i] = TranslateIntoUnitCell(AtomCoordList[i],TV_List)

	RefinedAtomCntList = []
	RefinedAtomCoordList = []
	for i in range(len(AtomNameList)):
		AtomName = AtomNameList[i]
		SelectedAtomList = [j for j in range(len(AtomNameLongList)) if AtomNameLongList[j]==AtomName]
		AtomCoordShortList = [AtomCoordList[j] for j in SelectedAtomList]
		Flag = AtomName in AtomsOfInterest
		while (Flag):
			Flag = False
			for j in range(1,len(AtomCoordShortList)):
				if (Flag):
					break
				for k in range(j):
					nxyz = RealDistCloserThan(AtomCoordShortList[j],AtomCoordShortList[k],TV_List,CriticalDist)
					if (nxyz):
						AtomCoordShortList[j] = AtomCoordShortList[j] + nxyz[0]*TV_List[0]+nxyz[1]*TV_List[1]+nxyz[2]*TV_List[2]
						AvrgAtom = average(AtomCoordShortList[j],AtomCoordShortList[k])
						#print("Deleting atoms",AtomCoordShortList[j],AtomCoordShortList[k],"(dist =",RealDist(AtomCoordShortList[j],AtomCoordShortList[k],TV_List),"), Add AvrgAtom:",AvrgAtom)
						AtomCoordShortList.pop(j)
						AtomCoordShortList.pop(k)
						AtomCoordShortList.append(AvrgAtom)
						Flag = True
						break
		RefinedLen = len(AtomCoordShortList)
		RefinedAtomCntList.append(RefinedLen)
		RefinedAtomCoordList = RefinedAtomCoordList + AtomCoordShortList

	print("1.0")
	for v in TV_List:
		PrintVect(v)
	PrintVect(AtomNameList,format="%s ")
	PrintVect(RefinedAtomCntList,format="%d ")
	print("Cartes")
	for AtomCoord in RefinedAtomCoordList:
		PrintVect(AtomCoord)
	print()

def ReadGridFile(FileName="LOCPOT"):
	#	Read headers, compute header length
	LOCPOT = open(FileName,"r")
	TempStdin = sys.stdin
	sys.stdin = LOCPOT
	Header_Length = 0

	SysName = input()
	linespl = input().split()
	Header_Length += 2

	GlobalScaler = float(linespl[0])
	TV_List = []
	for i in range(3):
		linespl = input().split()
		Header_Length += 1
		tv = [float(x) for x in linespl]
		TV_List.append(tv)
	TV_List = np.array(TV_List)*GlobalScaler

	linespl = input().split()
	AtomNameList =list(linespl)
	linespl = input().split()
	Header_Length += 2
	AtomSum = 0
	try:
		AtomCntList = [int(x) for x in linespl]
		AtomSum = sum(AtomCntList)
	except:
		print("Error reading number of atoms")
		exit()
	assert(len(AtomCntList)==len(AtomNameList))
	
	#	Direct or Cartesian
	IsDirect = True
	while (True):
		linespl = input().split()
		Header_Length += 1
		if (linespl[0][0] in "Dd"):
			break
		if (linespl[0][0] in "Cc"):
			IsDirect = False
			break

	#	Read atom coord list
	i = 0
	AtomCoordList = []
	while (i<AtomSum):
		linespl = input().split()
		Header_Length += 1
		try:
			AtomCoordList.append(np.array([float(linespl[0]),float(linespl[1]),float(linespl[2])]))
			i += 1
		except:
			0
	if (IsDirect):
		for i in range(len(AtomCoordList)):
			AtomCoordList[i] = np.matmul(AtomCoordList[i],TV_List)

	#	Read main data
	NGZF,NGYF,NGXF = 0,0,0
	while (True):
		linespl = input().split()
		Header_Length += 1
		try:
			NGXF,NGYF,NGZF = int(linespl[0]),int(linespl[1]),int(linespl[2])
			break
		except:
			0
	print("NGXF =",NGXF,"NGYF =",NGYF,"NGZF =",NGZF)
	#print("Header Length:",Header_Length)

	#StartTime = time.time()
	TotSize = NGZF*NGYF*NGXF
	Grid_3D = np.fromstring(sys.stdin.read(), sep=' ',count=TotSize)
	Grid_3D.resize(NGZF,NGYF,NGXF)
	#print("Read grids in",time.time()-StartTime,"s")
	sys.stdin.close()
	sys.stdin = TempStdin

	return TV_List,Grid_3D,AtomNameList,AtomCntList,AtomCoordList,NGXF,NGYF,NGZF,Header_Length

def ReadPOSCAR(FileName="POSCAR"):
	#	Read headers, compute header length
	POSCAR = open(FileName,"r")
	TempStdin = sys.stdin
	sys.stdin = POSCAR
	Header_Length = 0

	SysName = input()
	linespl = input().split()
	Header_Length += 2

	GlobalScaler = float(linespl[0])
	TV_List = []
	for i in range(3):
		linespl = input().split()
		Header_Length += 1
		tv = [float(x) for x in linespl]
		TV_List.append(tv)
	TV_List = np.array(TV_List)*GlobalScaler

	linespl = input().split()
	AtomNameList =list(linespl)
	linespl = input().split()
	Header_Length += 2
	AtomSum = 0
	try:
		AtomCntList = [int(x) for x in linespl]
		AtomSum = sum(AtomCntList)
	except:
		print("Error reading number of atoms")
		exit()
	assert(len(AtomCntList)==len(AtomNameList))
	
	#	Direct or Cartesian
	IsDirect = True
	while (True):
		linespl = input().split()
		Header_Length += 1
		if (linespl[0][0] in "Dd"):
			break
		if (linespl[0][0] in "Cc"):
			IsDirect = False
			break

	#	Read atom coord list
	i = 0
	AtomCoordList = []
	while (i<AtomSum):
		linespl = input().split()
		Header_Length += 1
		try:
			AtomCoordList.append(np.array([float(linespl[0]),float(linespl[1]),float(linespl[2])]))
			i += 1
		except:
			0
	if (IsDirect):
		for i in range(len(AtomCoordList)):
			AtomCoordList[i] = np.matmul(AtomCoordList[i],TV_List)

	sys.stdin.close()
	sys.stdin = TempStdin

	return TV_List,AtomNameList,AtomCntList,AtomCoordList

def TranslateIntoUnitCell(v,TV_List):
	v_frac = np.matmul(v,np.linalg.inv(TV_List))
	v_unitcell_frac = v_frac-np.floor(v_frac)
	return np.matmul(v_unitcell_frac,TV_List)

def dist(atom1,atom2):
	return sqrt( (atom1[0]-atom2[0])**2 + (atom1[1]-atom2[1])**2 + (atom1[2]-atom2[2])**2 )

def minus(atom1,atom2):
	return [ (atom1[0]-atom2[0]), (atom1[1]-atom2[1]), (atom1[2]-atom2[2]) ]

def average(atom1,atom2):
	return [ (atom1[0]+atom2[0])/2, (atom1[1]+atom2[1])/2, (atom1[2]+atom2[2])/2 ]

def dist_closer_than(atom1,atom2,d):
	v = minus(atom1,atom2)
	if (v[0]>=d or v[1]>=d or v[2]>=d):
		return False
	return dist(atom1,atom2)<d

def RealDist(v1,v2,TV_List):
	DistList = []
	for nx in [-1,0,1]:
		for ny in [-1,0,1]:
			for nz in [-1,0,1]:
				DistList.append(np.linalg.norm(v1-v2+nx*TV_List[0]+ny*TV_List[1]+nz*TV_List[2]))
	return min(DistList)

def RealDistCloserThan(v1,v2,TV_List,d):
	DistList = []
	for nx in [-1,0,1]:
		for ny in [-1,0,1]:
			for nz in [-1,0,1]:
				if dist_closer_than(v1+nx*TV_List[0]+ny*TV_List[1]+nz*TV_List[2],v2,d):
					return [nx,ny,nz]
	return False

def PrintVect(v,format="%.10f  "):
	for x in v:
		print(format%x,end="")
	print()

if (__name__=='__main__'):
	Main()
