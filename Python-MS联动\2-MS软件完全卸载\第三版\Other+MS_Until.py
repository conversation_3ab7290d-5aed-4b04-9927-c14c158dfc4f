import os
import subprocess
import time
import pyautogui
import shutil
import re
import winreg
from pathlib import Path
import ctypes
import sys
import cv2
import numpy as np
from PIL import Image, ImageGrab
import tempfile
import threading

# 设置pyautogui的安全特性
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 0.5

# 程序截图和资源目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
RESOURCES_DIR = os.path.join(SCRIPT_DIR, "resources")

# 如果资源目录不存在则创建
if not os.path.exists(RESOURCES_DIR):
    os.makedirs(RESOURCES_DIR)

def is_admin():
    """检查程序是否以管理员权限运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员权限重启脚本"""
    if not is_admin():
        print("正在请求管理员权限...")
        ctypes.windll.shell32.ShellExecuteW(
            None, "runas", sys.executable, f'"{os.path.abspath(__file__)}"', None, 1
        )
        sys.exit()

def take_screenshot(region=None):
    """获取屏幕截图"""
    if region:
        screenshot = ImageGrab.grab(bbox=region)
    else:
        screenshot = ImageGrab.grab()
    return np.array(screenshot)

def save_debug_screenshot(name="debug"):
    """保存调试截图"""
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    filename = os.path.join(RESOURCES_DIR, f"{name}_{timestamp}.png")
    screenshot = ImageGrab.grab()
    screenshot.save(filename)
    print(f"已保存调试截图: {filename}")
    return filename

def locate_on_screen(template_path, confidence=0.8, timeout=10, region=None):
    """在屏幕上查找图像，带有超时机制"""
    if not os.path.exists(template_path):
        print(f"模板图像不存在: {template_path}")
        return None
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            # 使用pyautogui的位置查找
            location = pyautogui.locateOnScreen(template_path, confidence=confidence, region=region)
            if location:
                return location
            
            # 尝试使用OpenCV进行更精确的匹配
            screenshot = take_screenshot(region)
            template = cv2.imread(template_path)
            
            # 转换颜色空间
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            
            # 执行模板匹配
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= confidence:
                h, w = template_gray.shape
                # 计算匹配区域
                if region:
                    x_offset, y_offset = region[0], region[1]
                else:
                    x_offset, y_offset = 0, 0
                
                return (max_loc[0] + x_offset, max_loc[1] + y_offset, w, h)
        except Exception as e:
            print(f"查找图像时出错: {e}")
        
        time.sleep(0.5)
    
    print(f"超时: 未能在屏幕上找到图像 {template_path}")
    return None

def click_if_exists(template_path, confidence=0.8, timeout=10, region=None):
    """如果图像存在，则点击它"""
    location = locate_on_screen(template_path, confidence, timeout, region)
    if location:
        center_x = location[0] + location[2] // 2
        center_y = location[1] + location[3] // 2
        pyautogui.click(center_x, center_y)
        print(f"点击了坐标: ({center_x}, {center_y})")
        return True
    return False

def capture_templates():
    """捕获界面元素的模板图像"""
    print("准备捕获界面元素模板...")
    
    # 要捕获的界面元素列表
    elements = [
        {"name": "search_box", "prompt": "请将鼠标放在Geek软件的搜索框上，然后按Enter"},
        {"name": "force_uninstall_button", "prompt": "请将鼠标放在'强制卸载'按钮上，然后按Enter"},
        {"name": "confirm_button", "prompt": "请将鼠标放在卸载确认对话框的'确认'按钮上，然后按Enter"}
    ]
    
    for element in elements:
        template_path = os.path.join(RESOURCES_DIR, f"{element['name']}.png")
        
        # 如果模板已存在，询问是否重新捕获
        if os.path.exists(template_path):
            choice = input(f"模板 {element['name']}.png 已存在。是否重新捕获？(y/n): ")
            if choice.lower() != 'y':
                continue
        
        print(element["prompt"])
        input("准备好后按Enter...")
        
        # 获取鼠标位置
        x, y = pyautogui.position()
        print(f"捕获位置: ({x}, {y})")
        
        # 捕获鼠标周围区域的屏幕截图
        region = (x - 50, y - 25, 100, 50)  # 调整区域大小以适应目标元素
        screenshot = ImageGrab.grab(bbox=region)
        screenshot.save(template_path)
        print(f"已保存模板: {template_path}")
    
    print("所有模板捕获完成")

def open_geek():
    """打开Geek软件"""
    geek_path = os.path.join(SCRIPT_DIR, "geek", "geek.exe")
    
    if not os.path.exists(geek_path):
        print(f"未找到Geek软件: {geek_path}")
        print("正在尝试直接启动当前目录下的geek.exe...")
        geek_path = os.path.join(SCRIPT_DIR, "geek.exe")
        
    if not os.path.exists(geek_path):
        print("未找到Geek软件。请确保geek.exe在当前目录或geek子目录中。")
        return False
    
    print(f"正在打开Geek软件: {geek_path}")
    
    # 检查是否已运行
    try:
        output = subprocess.check_output("tasklist /FI \"IMAGENAME eq geek.exe\"", shell=True).decode()
        if "geek.exe" in output:
            print("Geek软件已在运行")
            # 尝试激活窗口
            try:
                subprocess.run('powershell "(New-Object -ComObject WScript.Shell).AppActivate(\'Geek Uninstaller\')"', shell=True)
            except:
                pass
            return True
    except:
        pass
    
    # 启动软件
    try:
        subprocess.Popen(geek_path)
        print("等待Geek软件启动...")
        time.sleep(5)  # 等待启动
        return True
    except Exception as e:
        print(f"启动Geek软件时出错: {e}")
        return False

def find_and_select_biovia_programs():
    """在Geek软件中找到并选择BIOVIA程序"""
    print("正在搜索BIOVIA程序...")
    
    # 点击搜索框
    search_box_path = os.path.join(RESOURCES_DIR, "search_box.png")
    
    if not os.path.exists(search_box_path):
        print("搜索框模板不存在，尝试自动定位...")
        # 尝试通过自动定位搜索框
        try:
            # 假设搜索框在窗口顶部
            window_width, window_height = pyautogui.size()
            pyautogui.click(window_width // 4, 100)  # 点击可能的搜索框位置
            time.sleep(0.5)
        except:
            print("无法自动定位搜索框")
    else:
        if not click_if_exists(search_box_path, confidence=0.7, timeout=5):
            print("未找到搜索框，尝试其他方法...")
            save_debug_screenshot("search_box_not_found")
            # 尝试键盘快捷键
            pyautogui.hotkey('ctrl', 'f')
            time.sleep(0.5)
    
    # 输入搜索关键词
    pyautogui.typewrite("BIOVIA")
    pyautogui.press('enter')
    time.sleep(2)
    
    # 保存搜索结果的截图
    search_result_screenshot = save_debug_screenshot("biovia_search_results")
    print("已保存搜索结果截图，请查看是否有BIOVIA程序")
    
    # 找到所有BIOVIA程序
    biovia_programs = []
    
    # 尝试识别程序图标和名称
    # 这里需要实现对搜索结果的分析
    # 简化版：询问用户是否找到程序并希望卸载
    print("\n请查看搜索结果截图，确认找到的BIOVIA程序")
    found = input("是否找到BIOVIA程序？(y/n): ")
    
    if found.lower() == 'y':
        program_count = input("找到几个BIOVIA程序？输入数字: ")
        try:
            count = int(program_count)
            for i in range(count):
                program_name = input(f"请输入第{i+1}个程序的名称: ")
                biovia_programs.append(program_name)
        except:
            print("输入无效，假设找到4个标准BIOVIA程序")
            biovia_programs = [
                "BIOVIA License Pack (x64) 2020",
                "BIOVIA License Pack 2020 (32-bit)",
                "BIOVIA Materials Studio 2020 (32-bit)",
                "BIOVIA Materials Studio Gateway Service (x64)"
            ]
    else:
        print("未找到BIOVIA程序，请确认是否已安装")
    
    return biovia_programs

def force_uninstall_programs(programs):
    """强制卸载选定的程序"""
    if not programs:
        print("没有找到BIOVIA程序可卸载")
        return
    
    print(f"准备卸载 {len(programs)} 个BIOVIA程序...")
    
    # 强制卸载按钮模板
    force_button_path = os.path.join(RESOURCES_DIR, "force_uninstall_button.png")
    confirm_button_path = os.path.join(RESOURCES_DIR, "confirm_button.png")
    
    for program in programs:
        print(f"正在卸载: {program}")
        
        # 保存当前屏幕截图
        current_screen = save_debug_screenshot(f"before_uninstall_{program.replace(' ', '_')}")
        
        # 选择程序
        print("请在Geek软件中选择该程序，然后按Enter继续...")
        input("准备好后按Enter...")
        
        # 点击强制卸载按钮
        if os.path.exists(force_button_path):
            if not click_if_exists(force_button_path, confidence=0.7, timeout=5):
                print("未找到强制卸载按钮，请手动点击")
                input("点击强制卸载按钮后按Enter继续...")
        else:
            print("没有强制卸载按钮模板，请手动点击强制卸载按钮")
            input("点击强制卸载按钮后按Enter继续...")
        
        # 确认卸载
        time.sleep(1)
        if os.path.exists(confirm_button_path):
            if not click_if_exists(confirm_button_path, confidence=0.7, timeout=5):
                print("未找到确认按钮，请手动确认")
                input("确认卸载后按Enter继续...")
        else:
            print("没有确认按钮模板，请手动确认卸载")
            input("确认卸载后按Enter继续...")
        
        # 等待卸载完成
        print("等待卸载完成...")
        input("卸载完成后按Enter继续...")
    
    print("所有程序卸载完成")

def clean_remaining_files():
    """清理C盘上的Accelrys和Biovia相关文件"""
    print("开始清理C盘上的残留文件...")
    
    keywords = ["Accelrys", "Biovia", "BIOVIA", "accelrys", "biovia"]
    c_drive = "C:\\"
    
    for keyword in keywords:
        print(f"正在搜索关键词: {keyword}")
        try:
            # 使用dir命令搜索文件和目录
            cmd = f'dir /s /b /a "C:\\*{keyword}*"'
            result = subprocess.run(cmd, shell=True, text=True, capture_output=True)
            
            if result.returncode == 0 and result.stdout.strip():
                files = result.stdout.strip().split('\n')
                print(f"找到 {len(files)} 个匹配 '{keyword}' 的文件/目录")
                
                for file_path in files:
                    file_path = file_path.strip()
                    if os.path.exists(file_path):
                        print(f"正在删除: {file_path}")
                        try:
                            if os.path.isfile(file_path):
                                os.remove(file_path)
                            elif os.path.isdir(file_path):
                                shutil.rmtree(file_path)
                            print("  删除成功")
                        except Exception as e:
                            print(f"  删除失败: {e}")
                            # 尝试使用管理员权限删除
                            try:
                                if os.path.isfile(file_path):
                                    subprocess.run(f'del /f /q "{file_path}"', shell=True, check=False)
                                elif os.path.isdir(file_path):
                                    subprocess.run(f'rmdir /s /q "{file_path}"', shell=True, check=False)
                                print("  使用管理员权限删除尝试完成")
                            except Exception as e2:
                                print(f"  无法删除该文件，已跳过: {e2}")
            else:
                print(f"未找到匹配 '{keyword}' 的文件")
                
        except Exception as e:
            print(f"搜索过程中出错: {e}")
    
    print("文件清理完成")

def clean_registry():
    """清理注册表中的BIOVIA和Accelrys条目"""
    print("正在清理注册表...")
    
    registry_keys = [
        r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
        r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
    ]
    
    keywords = ["BIOVIA", "Biovia", "biovia", "Accelrys", "accelrys"]
    
    for root_key_str in ["HKEY_LOCAL_MACHINE", "HKEY_CURRENT_USER"]:
        root_key = getattr(winreg, root_key_str)
        
        for key_path in registry_keys:
            try:
                reg_key = winreg.OpenKey(root_key, key_path, 0, winreg.KEY_READ)
                
                # 枚举所有子键
                i = 0
                while True:
                    try:
                        subkey_name = winreg.EnumKey(reg_key, i)
                        # 打开子键
                        subkey = winreg.OpenKey(root_key, f"{key_path}\\{subkey_name}", 0, winreg.KEY_READ)
                        
                        try:
                            display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                            
                            # 检查是否匹配关键词
                            if any(keyword in display_name for keyword in keywords):
                                print(f"发现注册表项: {root_key_str}\\{key_path}\\{subkey_name}")
                                print(f"DisplayName: {display_name}")
                                
                                # 删除注册表项
                                try:
                                    # 关闭当前打开的子键
                                    winreg.CloseKey(subkey)
                                    
                                    # 以写权限打开父键，删除子键
                                    parent_key = winreg.OpenKey(root_key, key_path, 0, winreg.KEY_ALL_ACCESS)
                                    winreg.DeleteKey(parent_key, subkey_name)
                                    winreg.CloseKey(parent_key)
                                    
                                    print(f"已删除注册表项")
                                    i -= 1  # 删除后索引减1，因为剩余项会前移
                                except Exception as e:
                                    print(f"删除注册表项失败: {e}")
                        except:
                            pass
                        
                        winreg.CloseKey(subkey)
                        i += 1
                    except WindowsError:
                        break
                
                winreg.CloseKey(reg_key)
            except Exception as e:
                print(f"访问注册表失败: {e}")
    
    print("注册表清理完成")

def verify_uninstallation():
    """重新打开Geek软件验证程序是否已卸载"""
    print("正在验证卸载结果...")
    
    # 重新打开Geek
    open_geek()
    time.sleep(3)
    
    # 搜索BIOVIA
    search_box_path = os.path.join(RESOURCES_DIR, "search_box.png")
    if os.path.exists(search_box_path):
        if click_if_exists(search_box_path, confidence=0.7, timeout=5):
            pyautogui.typewrite("BIOVIA")
            pyautogui.press('enter')
            time.sleep(2)
    else:
        # 尝试键盘快捷键
        pyautogui.hotkey('ctrl', 'f')
        time.sleep(0.5)
        pyautogui.typewrite("BIOVIA")
        pyautogui.press('enter')
        time.sleep(2)
    
    # 保存验证结果截图
    verification_screenshot = save_debug_screenshot("verification_results")
    
    # 询问用户是否还有BIOVIA程序
    print("请查看验证结果截图，确认是否还有BIOVIA程序")
    remaining = input("是否还有BIOVIA程序？(y/n): ")
    
    if remaining.lower() == 'y':
        print("警告: 仍有BIOVIA程序未完全卸载")
        return False
    else:
        print("验证成功: 所有BIOVIA程序已卸载")
        return True

def create_setup_instructions():
    """创建安装说明文件"""
    instructions = """
MS软件完全卸载工具使用说明
==========================

本工具用于彻底卸载BIOVIA Materials Studio软件及其所有组件。

准备工作:
1. 确保以管理员权限运行本程序
2. 建议在卸载前备份重要数据

使用步骤:
1. 运行程序后，会自动打开Geek Uninstaller软件
2. 程序会搜索所有BIOVIA相关程序
3. 按照提示逐一卸载每个组件
4. 程序会自动清理注册表和残留文件
5. 最后验证是否完全卸载成功

注意事项:
- 卸载过程中可能需要您手动点击某些按钮
- 对于无法自动删除的文件，可能需要手动处理
- 如果遇到问题，可查看生成的调试截图

要求的Python库:
- pyautogui
- opencv-python
- pillow

安装依赖:
pip install pyautogui opencv-python pillow

"""
    
    instruction_path = os.path.join(SCRIPT_DIR, "使用说明.txt")
    with open(instruction_path, "w", encoding="utf-8") as f:
        f.write(instructions)
    print(f"已创建使用说明文件: {instruction_path}")

def main():
    """主函数"""
    # 检查管理员权限
    run_as_admin()
    
    print("=== BIOVIA软件完全卸载工具 ===")
    print("此工具将执行以下操作:")
    print("1. 使用Geek软件卸载BIOVIA程序")
    print("2. 清理注册表")
    print("3. 删除C盘中残留的相关文件")
    print("4. 验证卸载结果")
    print("\n注意: 此过程将彻底删除所有BIOVIA相关文件和注册表项")
    
    # 创建使用说明
    create_setup_instructions()
    
    # 询问是否需要捕获界面元素模板
    capture_templates_choice = input("是否需要捕获界面元素模板？首次使用建议选择是 (y/n): ")
    if capture_templates_choice.lower() == 'y':
        capture_templates()
    
    # 确认继续
    confirmation = input("是否继续执行卸载过程？(y/n): ")
    if confirmation.lower() != 'y':
        print("操作已取消")
        return
    
    # 打开Geek软件
    if not open_geek():
        print("无法打开Geek软件，程序终止")
        return
    
    # 查找并选择BIOVIA程序
    programs = find_and_select_biovia_programs()
    
    if not programs:
        print("未找到BIOVIA程序，请确认是否已安装或手动搜索")
        return
    
    # 强制卸载选定的程序
    force_uninstall_programs(programs)
    
    # 清理注册表
    clean_registry()
    
    # 清理C盘上的残留文件
    clean_remaining_files()
    
    # 验证卸载结果
    verify_uninstallation()
    
    print("=== 卸载过程完成 ===")
    print("如果仍有问题，请查看生成的调试截图和日志")

if __name__ == "__main__":
    main()
