# 晶格方向转换工具

这个工具集用于将CIF文件转换为不同晶面朝上的正交晶胞。它可以处理任意晶系的晶体结构，并生成指定Miller面导向的正交晶胞。

## 功能特性

- 支持所有晶系的CIF文件转换
- 可以生成不同Miller面朝上的晶胞 (如100, 110, 111等)
- 自动将晶胞转换为正交晶胞（所有角度为90°）
- 支持批量处理多个CIF文件

## 环境要求

- Python 3.6+
- pymatgen
- numpy

安装依赖:

```bash
pip install pymatgen numpy
```

## 使用方法

### 1. 处理单个CIF文件

```bash
python cif_orientation_converter.py input.cif [--output-dir OUTPUT_DIR]
```

### 2. 运行示例

```bash
python convert_examples.py
```

这将处理当前目录下的示例CIF文件（CrTe-Pre.cif 和 CrTe(1).cif），并在各自的输出目录中生成不同方向的正交晶胞。

### 3. 批量处理CIF文件

```bash
python batch_convert.py INPUT_DIR [--output-dir OUTPUT_DIR] [--pattern "*.cif"]
```

例如，处理当前目录下的所有CIF文件：

```bash
python batch_convert.py .
```

## 脚本说明

- `cif_orientation_converter.py`: 核心转换工具，实现晶格方向转换功能
- `convert_examples.py`: 示例脚本，展示如何使用转换工具处理样例文件
- `batch_convert.py`: 批量处理工具，可处理整个目录中的CIF文件

## 技术原理

1. **晶体方向转换**：使用旋转矩阵将指定的Miller面转向Z轴方向（即001方向）
2. **正交化处理**：使用Gram-Schmidt正交化方法将任意晶胞转换为正交晶胞
3. **坐标变换**：保持原子的相对位置不变，只转换晶胞的朝向

## 转换示例

对于一个输入的CIF文件，工具会生成以下几个方向的正交晶胞：

- (100)：a轴朝上
- (010)：b轴朝上
- (001)：c轴朝上
- (110)：ab对角线方向朝上
- (101)：ac对角线方向朝上
- (011)：bc对角线方向朝上
- (111)：abc对角线方向朝上

每个输出文件都会被命名为 `{原文件名}_{hkl}.cif`，如 `CrTe-Pre_100.cif`。 