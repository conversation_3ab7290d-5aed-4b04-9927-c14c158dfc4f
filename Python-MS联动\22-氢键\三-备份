##################################################################################################################
# perl                                                                                                           #
#                                                                                                                #
# Author: IMATSOFT.DM                                                                                            #
# Version: 1.2                                                                                                   #
# Tested on: Materials Studio 2020                                                                               #
#                                                                                                                #
# Required modules: Materials Visualizer                                                                         #
# This script provides comprehensive hydrogen bond analysis between two sets in a molecular structure.           #
# It analyzes trajectory files and identifies hydrogen bonds where the donor atom is in one set and the          #
# acceptor atom is in another set. For each frame, the script calculates:                                        #
# 1. Number of hydrogen bonds between specified sets                                                             #
# 2. Total number of hydrogen bonds in the structure                                                             #
# 3. Geometric features (H-A distances and D-H-A angles)                                                         #
# 4. Average hydrogen bond distance and angle across all frames                                                  #
#                                                                                                                #
# The results are presented in organized tables with detailed statistics, providing a complete picture           #
# of hydrogen bonding interactions between different components in molecular systems.                            #
#                                                                                                                #
# Date: 2023-05-15                                                                                               #
#                                                                                                                #
#                                                                                                                #
##################################################################################################################

use strict;
use warnings;
use MaterialsScript qw(:all);
use Math::Trig;


################################
#      USER INPUT SECTION      #
################################

# Input file name - change to your file
my $inputDocument = $Documents{"Target.xtd"};

# Names of the two sets to analyze
my $firstSetName = "Target1";
my $secondSetName = "Target2";

# Elements that can participate in hydrogen bonds
my @hydrogenBondElements = ("N", "O", "Cl", "F", "S");

# Hydrogen bond parameters
my $maxHydrogenAcceptorDistance = 2.5; # Maximum hydrogen-acceptor distance in Angstroms
my $minDonorHydrogenAcceptorAngle = 90; # Minimum angle for hydrogen bonds (D-H...A) in degrees

# Create results table
my $resultsTable = Documents->New("HBondResults.std");
$resultsTable->ColumnHeading(0) = "Frame";
$resultsTable->ColumnHeading(1) = "FrameTime (ps)";
$resultsTable->ColumnHeading(2) = "H-Bonds Between Sets";
$resultsTable->ColumnHeading(3) = "Total H-Bonds";

# Create geometry features table
my $geometryTable = Documents->New("HBondGeometry.std");
$geometryTable->ColumnHeading(0) = "Frame";
$geometryTable->ColumnHeading(1) = "FrameTime (ps)";
$geometryTable->ColumnHeading(2) = "HBond ID";
$geometryTable->ColumnHeading(3) = "H-A Distance (A)";
$geometryTable->ColumnHeading(4) = "D-H-A Angle (Du)";
$geometryTable->ColumnHeading(5) = "Donor Set";
$geometryTable->ColumnHeading(6) = "Acceptor Set";

# Function to get atom IDs belonging to a specific set
sub GetAtomsInSet {
    my ($document, $setName) = @_;
    my %atomIDMap = ();
    
    # Get all atoms in the specified set
    my $setAtoms = $document->UnitCell->Sets($setName)->Atoms;
    foreach my $atom (@$setAtoms) {
        $atomIDMap{$atom->ID} = 1;
    }
    
    return %atomIDMap;
}

# Function to calculate distance between two atoms
sub CalculateDistance {
    my ($atom1, $atom2) = @_;
    my $dx = $atom1->X - $atom2->X;
    my $dy = $atom1->Y - $atom2->Y;
    my $dz = $atom1->Z - $atom2->Z;
    
    return sqrt($dx*$dx + $dy*$dy + $dz*$dz);
}

# Function to calculate angle between three atoms (in degrees)
sub CalculateAngle {
    my ($atom1, $atom2, $atom3) = @_;
    
    # Vector from atom2 to atom1 (D-H)
    my $v1x = $atom1->X - $atom2->X;
    my $v1y = $atom1->Y - $atom2->Y;
    my $v1z = $atom1->Z - $atom2->Z;
    
    # Vector from atom2 to atom3 (H-A)
    my $v2x = $atom3->X - $atom2->X;
    my $v2y = $atom3->Y - $atom2->Y;
    my $v2z = $atom3->Z - $atom2->Z;
    
    # Dot product
    my $dotProduct = $v1x*$v2x + $v1y*$v2y + $v1z*$v2z;
    
    # Magnitude of vectors
    my $mag1 = sqrt($v1x*$v1x + $v1y*$v1y + $v1z*$v1z);
    my $mag2 = sqrt($v2x*$v2x + $v2y*$v2y + $v2z*$v2z);
    
    # Avoid division by zero
    if ($mag1 < 0.0001 || $mag2 < 0.0001) {
        return 0;
    }
    
    # Calculate angle in degrees
    my $cosTheta = $dotProduct / ($mag1 * $mag2);
    
    # Handle numerical precision issues
    if ($cosTheta > 1.0) { $cosTheta = 1.0; }
    if ($cosTheta < -1.0) { $cosTheta = -1.0; }
    
    my $angleRad = acos($cosTheta);
    my $angleDeg = $angleRad * 180.0 / 3.14159265358979;
    
    return $angleDeg;
}

################################
#       MAIN CALCULATION       #
################################

# Process each frame in the trajectory
my $totalFrames = $inputDocument->Trajectory->NumFrames;
my $totalGeometryRows = 0;  # Total counter for all rows across all frames

# Variables to calculate average values
my $totalHADistance = 0.0;
my $totalDHAAngle = 0.0;
my $totalHBondCount = 0;

for (my $frameIndex = 1; $frameIndex <= $totalFrames; ++$frameIndex) {
    $inputDocument->Trajectory->CurrentFrame = $frameIndex;
    
    # Get current frame time directly from trajectory
    my $frameTime = $inputDocument->Trajectory->FrameTime;
    
    # Clear and configure hydrogen bond calculation parameters
    Tools->BondCalculation->HBonds->ClearDonors;
    Tools->BondCalculation->HBonds->ClearAcceptors;
    
    # Set elements that can participate in hydrogen bonds
    foreach my $element (@hydrogenBondElements) {
        Tools->BondCalculation->HBonds->AddDonor($element);
        Tools->BondCalculation->HBonds->AddAcceptor($element);
    }
    
    # Calculate hydrogen bonds for the current frame
    Tools->BondCalculation->HBonds->Calculate($inputDocument, 
        Settings(
            MaxHydrogenAcceptorDistance => $maxHydrogenAcceptorDistance,
            MinDonorHydrogenAcceptorAngle => $minDonorHydrogenAcceptorAngle
        ));
    
    # Get all hydrogen bonds
    my $hydrogenBonds = $inputDocument->UnitCell->HydrogenBonds;
    
    # Get total number of hydrogen bonds in this frame
    my $frameTotalHBondCount = $hydrogenBonds->Count;
    
    # Get atom IDs for both sets
    my %firstSetAtoms = GetAtomsInSet($inputDocument, $firstSetName);
    my %secondSetAtoms = GetAtomsInSet($inputDocument, $secondSetName);
    
    # Count hydrogen bonds between the two sets
    my $intersetHBondCount = 0;
    my $frameGeometryRowCount = 0;  # Counter for geometry rows in this frame
    
    foreach my $hBond (@$hydrogenBonds) {
        # Get donor and acceptor atoms for this hydrogen bond
        my $donorAtom = $hBond->Donor;
        my $acceptorAtom = $hBond->Acceptor;
        
        # Check which set each atom belongs to
        my $donorInFirstSet = exists $firstSetAtoms{$donorAtom->ID};
        my $donorInSecondSet = exists $secondSetAtoms{$donorAtom->ID};
        my $acceptorInFirstSet = exists $firstSetAtoms{$acceptorAtom->ID};
        my $acceptorInSecondSet = exists $secondSetAtoms{$acceptorAtom->ID};
        
        # Count bond if donor and acceptor are in different sets
        if (($donorInFirstSet && $acceptorInSecondSet) || 
            ($donorInSecondSet && $acceptorInFirstSet)) {
            $intersetHBondCount++;
            
            # Get hydrogen atom (needed for geometric features)
            my $hydrogenAtom = $hBond->Hydrogen;
            
            # Calculate H-A distance directly from atom coordinates
            my $haDistance = CalculateDistance($hydrogenAtom, $acceptorAtom);
            
            # Calculate D-H-A angle directly from atom coordinates
            my $dhaAngle = CalculateAngle($donorAtom, $hydrogenAtom, $acceptorAtom);
            
            # Add to totals for average calculation
            $totalHADistance += $haDistance;
            $totalDHAAngle += $dhaAngle;
            $totalHBondCount++;
            
            # Determine which set contains the donor and acceptor
            my $donorSetName = $donorInFirstSet ? $firstSetName : $secondSetName;
            my $acceptorSetName = $acceptorInFirstSet ? $firstSetName : $secondSetName;
            
            # Record geometry features in the table
            $geometryTable->Cell($totalGeometryRows, 0) = $frameIndex;
            $geometryTable->Cell($totalGeometryRows, 1) = $frameTime;
            $geometryTable->Cell($totalGeometryRows, 2) = $frameGeometryRowCount + 1;  # HBond ID within frame
            $geometryTable->Cell($totalGeometryRows, 3) = $haDistance;
            $geometryTable->Cell($totalGeometryRows, 4) = $dhaAngle;
            $geometryTable->Cell($totalGeometryRows, 5) = $donorSetName;
            $geometryTable->Cell($totalGeometryRows, 6) = $acceptorSetName;
            
            # Increment both counters
            $frameGeometryRowCount++;
            $totalGeometryRows++;
        }
    }
    
    # Save results to table
    $resultsTable->Cell($frameIndex-1, 0) = $frameIndex;
    $resultsTable->Cell($frameIndex-1, 1) = $frameTime;
    $resultsTable->Cell($frameIndex-1, 2) = $intersetHBondCount;
    $resultsTable->Cell($frameIndex-1, 3) = $frameTotalHBondCount;
    
    # Display progress
    my $progressPercent = int(($frameIndex / $totalFrames) * 100);
    printf("Processing frame %d (Progress: %d%%): Found %d hydrogen bonds between sets (Total: %d)\n", 
           $frameIndex, $progressPercent, $intersetHBondCount, $frameTotalHBondCount);
}

# Calculate averages if there were any hydrogen bonds
my $avgHADistance = 0.0;
my $avgDHAAngle = 0.0;

if ($totalHBondCount > 0) {
    $avgHADistance = $totalHADistance / $totalHBondCount;
    $avgDHAAngle = $totalDHAAngle / $totalHBondCount;
}

# Add a row for averages at the end of the geometry table
$geometryTable->Cell($totalGeometryRows, 0) = "Average";
$geometryTable->Cell($totalGeometryRows, 1) = "-";
$geometryTable->Cell($totalGeometryRows, 2) = "-";
$geometryTable->Cell($totalGeometryRows, 3) = $avgHADistance;
$geometryTable->Cell($totalGeometryRows, 4) = $avgDHAAngle;
$geometryTable->Cell($totalGeometryRows, 5) = "-";
$geometryTable->Cell($totalGeometryRows, 6) = "-";

# Display completion message
printf("\nCalculation complete!\n");
printf("Results saved to HBondResults.std and HBondGeometry.std\n");
printf("Total hydrogen bonds between sets: %d\n", $totalHBondCount);
printf("Average H-A distance: %.4f Angstroms\n", $avgHADistance);
printf("Average D-H-A angle: %.4f degrees\n", $avgDHAAngle);
printf("Analyzed %d frames for hydrogen bonds between '%s' and '%s'\n", 
        $totalFrames, $firstSetName, $secondSetName); 