import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Button
import pandas as pd
import os

# 使用英文字体，避免中文显示问题
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False

# 定义可拖动标注类
class DraggableAnnotation:
    def __init__(self, annotation, ax):
        self.annotation = annotation
        self.ax = ax
        self.press = None
        self.background = None
        self.connect()
        
    # 可拖动标注的方法保持不变...
    def connect(self):
        self.cidpress = self.annotation.figure.canvas.mpl_connect(
            'button_press_event', self.on_press)
        self.cidrelease = self.annotation.figure.canvas.mpl_connect(
            'button_release_event', self.on_release)
        self.cidmotion = self.annotation.figure.canvas.mpl_connect(
            'motion_notify_event', self.on_motion)
    
    def on_press(self, event):
        if event.inaxes != self.ax:
            return
        contains, attrd = self.annotation.contains(event)
        if not contains:
            return
        self.press = (self.annotation.get_position())
        self.annotation.set_animated(True)
        self.ax.figure.canvas.draw()
        self.background = self.ax.figure.canvas.copy_from_bbox(self.ax.bbox)
        self.ax.draw_artist(self.annotation)
        self.ax.figure.canvas.blit(self.ax.bbox)
    
    def on_motion(self, event):
        if self.press is None or event.inaxes != self.ax:
            return
        position = (event.xdata, event.ydata)
        self.annotation.set_position(position)
        self.ax.figure.canvas.restore_region(self.background)
        self.ax.draw_artist(self.annotation)
        self.ax.figure.canvas.blit(self.ax.bbox)
    
    def on_release(self, event):
        if self.press is None:
            return
        self.press = None
        self.annotation.set_animated(False)
        self.background = None
        self.ax.figure.canvas.draw()
    
    def disconnect(self):
        self.annotation.figure.canvas.mpl_disconnect(self.cidpress)
        self.annotation.figure.canvas.mpl_disconnect(self.cidrelease)
        self.annotation.figure.canvas.mpl_disconnect(self.cidmotion)

# 执行数据导出
def export_data(event):
    # 创建数据字典
    data = {
        'Temperature (K)': temperature,
        '1000/T (K^-1)': inv_T,
        'D_raw (Å²/ps)': D_raw,
        'D_vulcanized (Å²/ps)': D_vulcanized,
        'ln(D_raw)': ln_D_raw,
        'ln(D_vulcanized)': ln_D_vulcanized,
        'D_ratio (vulcanized/raw)': D_vulcanized / D_raw
    }
    
    # 创建拟合直线数据
    fit_data = {
        '1000/T (K^-1)': inv_T_range,
        'ln(D_raw) fit': ln_D_raw_fit,
        'ln(D_vulcanized) fit': ln_D_vulcanized_fit
    }
    
    # 创建一个参数表，包含斜率、截距、R²和活化能
    params = {
        'Parameter': ['Slope', 'Intercept', 'R²', 'Activation Energy (kJ/mol)'],
        'Raw Rubber': [slope_raw, intercept_raw, r_squared_raw, raw_Ea],
        'Vulcanized Rubber': [slope_vulcanized, intercept_vulcanized, r_squared_vulc, vulc_Ea]
    }
    
    # 导出CSV文件
    df_data = pd.DataFrame(data)
    df_fit = pd.DataFrame(fit_data)
    df_params = pd.DataFrame(params)
    df_data.to_csv('arrhenius_data.csv', index=False)
    df_fit.to_csv('arrhenius_fit_curve.csv', index=False)
    df_params.to_csv('arrhenius_parameters.csv', index=False)
    
    # 导出Excel文件
    with pd.ExcelWriter('arrhenius_complete_data.xlsx') as writer:
        df_data.to_excel(writer, sheet_name='Experimental Data', index=False)
        df_fit.to_excel(writer, sheet_name='Fit Curves', index=False)
        df_params.to_excel(writer, sheet_name='Parameters', index=False)
    
    # 显示导出成功信息
    plt.figtext(0.5, 0.01, "Data exported successfully!", 
                ha='center', fontsize=12, color='green', 
                bbox=dict(boxstyle="round,pad=0.5", fc="lightyellow"))
    plt.draw()
    print("\nData exported successfully to CSV and Excel files.")
    print(f"Files saved in: {os.getcwd()}")

# ===== 数据和计算部分 =====
# 输入数据
temperature = np.array([318, 309, 303, 293, 283])  # 温度，单位K
D_raw = np.array([0.00228645, 0.00214836, 0.00207599, 0.00146661, 0.00141343])  # 生胶中扩散系数
D_vulcanized = np.array([0.0021791, 0.00206026, 0.0019, 0.00136358, 0.00119709])  # 硫化胶中扩散系数

# 计算1/T和ln(D)
inv_T = 1000 / temperature
ln_D_raw = np.log(D_raw)
ln_D_vulcanized = np.log(D_vulcanized)

# 使用numpy的polyfit计算斜率和截距
# 生胶的线性拟合
slope_raw, intercept_raw = np.polyfit(inv_T, ln_D_raw, 1)
# 计算生胶的R²
ss_tot_raw = np.sum((ln_D_raw - np.mean(ln_D_raw))**2)
ss_res_raw = np.sum((ln_D_raw - (slope_raw * inv_T + intercept_raw))**2)
r_squared_raw = 1 - (ss_res_raw / ss_tot_raw)

# 硫化胶的线性拟合
slope_vulcanized, intercept_vulcanized = np.polyfit(inv_T, ln_D_vulcanized, 1)
# 计算硫化胶的R²
ss_tot_vulc = np.sum((ln_D_vulcanized - np.mean(ln_D_vulcanized))**2)
ss_res_vulc = np.sum((ln_D_vulcanized - (slope_vulcanized * inv_T + intercept_vulcanized))**2)
r_squared_vulc = 1 - (ss_res_vulc / ss_tot_vulc)

# Arrhenius方程的正确计算
# 当ln(D)对1000/T作图时，斜率k = -Ea/(R·1000)
# 因此Ea = -k·R·1000
R = 8.314 / 1000  # 气体常数，单位：kJ/(mol·K)
raw_Ea = -slope_raw * R * 1000  # 注意这里乘以1000
vulc_Ea = -slope_vulcanized * R * 1000  # 注意这里乘以1000

# 计算步骤详细打印
print("========== 活化能计算详细过程（修正） ==========")
print(f"气体常数 R = 8.314/1000 = {R} kJ/(mol·K)")
print("当ln(D)对1000/T作图时，斜率k = -Ea/(R·1000)")
print("因此Ea = -k·R·1000")
print(f"生胶斜率 k = {slope_raw}")
print(f"生胶活化能 Ea = -({slope_raw}) × {R} × 1000 = {raw_Ea} kJ/mol")
print(f"生胶活化能（保留两位小数）= {raw_Ea:.2f} kJ/mol")
print(f"硫化胶斜率 k = {slope_vulcanized}")
print(f"硫化胶活化能 Ea = -({slope_vulcanized}) × {R} × 1000 = {vulc_Ea} kJ/mol")
print(f"硫化胶活化能（保留两位小数）= {vulc_Ea:.2f} kJ/mol")

# 生成拟合直线
# 自动确定拟合范围，稍微扩展数据范围以获得更好的可视化效果
min_inv_T = min(inv_T)
max_inv_T = max(inv_T)
range_inv_T = max_inv_T - min_inv_T
inv_T_range = np.linspace(min_inv_T - range_inv_T * 0.05, max_inv_T + range_inv_T * 0.05, 100)

ln_D_raw_fit = slope_raw * inv_T_range + intercept_raw
ln_D_vulcanized_fit = slope_vulcanized * inv_T_range + intercept_vulcanized

# ===== 绘图部分 =====
# 创建图形，为按钮留出空间
fig = plt.figure(figsize=(10, 9))
ax = fig.add_axes([0.1, 0.15, 0.8, 0.75])  # [left, bottom, width, height]

# 设置坐标轴和绘制数据点
# 计算适当的轴范围
x_min = min(inv_T) - range_inv_T * 0.1
x_max = max(inv_T) + range_inv_T * 0.1

all_y_values = np.concatenate([ln_D_raw, ln_D_vulcanized, ln_D_raw_fit, ln_D_vulcanized_fit])
y_min = min(all_y_values) - 0.1
y_max = max(all_y_values) + 0.2  # 给上边留更多空间放置文本框

ax.set_xlim(x_min, x_max)
ax.set_ylim(y_min, y_max)
ax.grid(True, linestyle='-', alpha=0.3, color='gray')
ax.scatter(inv_T, ln_D_raw, marker='o', color='blue', s=80, label='Raw Rubber')
ax.scatter(inv_T, ln_D_vulcanized, marker='s', color='red', s=80, label='Vulcanized Rubber')
ax.plot(inv_T_range, ln_D_raw_fit, linestyle='-', color='blue')
ax.plot(inv_T_range, ln_D_vulcanized_fit, linestyle='-', color='red')

# 设置标题和标签
ax.set_title('Arrhenius Plot of Mustard Gas Diffusion Coefficient', fontsize=16)
ax.set_xlabel('1000/T (K$^{-1}$)', fontsize=14)
ax.set_ylabel('ln(D) [D: Å$^2$/ps]', fontsize=14)
ax.legend(fontsize=12, loc='upper right')

# ===== 文本标注部分 =====
# 使用计算得到的值创建文本
raw_Ea_rounded = round(raw_Ea, 2)  
vulc_Ea_rounded = round(vulc_Ea, 2)  

# 创建文本内容，使用计算得到的值
raw_Ea_text = f"Raw Rubber Ea: {raw_Ea_rounded} kJ/mol"
vulc_Ea_text = f"Vulcanized Rubber Ea: {vulc_Ea_rounded} kJ/mol"
raw_eq_text = f"ln(D) = {intercept_raw:.2f} + ({slope_raw:.2f})×(1000/T), R² = {r_squared_raw:.4f}"
vulc_eq_text = f"ln(D) = {intercept_vulcanized:.2f} + ({slope_vulcanized:.2f})×(1000/T), R² = {r_squared_vulc:.4f}"

print("\n===== 将要显示的文本内容 =====")
print("生胶活化能文本: " + raw_Ea_text)
print("硫化胶活化能文本: " + vulc_Ea_text)
print("生胶拟合方程: " + raw_eq_text)
print("硫化胶拟合方程: " + vulc_eq_text)

# 创建可拖动文本框函数
def create_draggable_textbox(text, x, y):
    ann = ax.annotate(text, xy=(x, y), xycoords='data',
                     bbox=dict(boxstyle="round,pad=0.5", fc="white", ec="black"),
                     fontsize=9)
    return DraggableAnnotation(ann, ax)

# 创建四个可拖动文本框
# 计算适当的文本位置
text_x = min_inv_T + (max_inv_T - min_inv_T) * 0.5
text_y_start = y_max - 0.05
text_y_step = -0.1

text_boxes = []
text_boxes.append(create_draggable_textbox(raw_Ea_text, text_x, text_y_start))
text_boxes.append(create_draggable_textbox(vulc_Ea_text, text_x, text_y_start + text_y_step))
text_boxes.append(create_draggable_textbox(raw_eq_text, text_x, text_y_start + 2*text_y_step))
text_boxes.append(create_draggable_textbox(vulc_eq_text, text_x, text_y_start + 3*text_y_step))

# 添加导出按钮
export_button_ax = fig.add_axes([0.1, 0.001, 0.3, 0.05])
export_button = Button(export_button_ax, 'Export Data', color='lightblue', hovercolor='skyblue')
export_button.on_clicked(export_data)

# 保存和显示图形
plt.savefig('arrhenius_plot_dynamic_calc.png', dpi=300)
plt.show()

# 打印计算过程总结和物理意义
print("\n==== 活化能计算过程总结 ====")
print("根据Arrhenius方程：D = D₀exp(-Ea/RT)")
print("取自然对数：ln(D) = ln(D₀) - Ea/(RT)")
print("当ln(D)对1000/T作图时，斜率k = -Ea/(R·1000)，因此Ea = -k·R·1000")
print(f"生胶斜率k = {slope_raw:.4f}，活化能Ea = {raw_Ea_rounded} kJ/mol")
print(f"硫化胶斜率k = {slope_vulcanized:.4f}，活化能Ea = {vulc_Ea_rounded} kJ/mol")
print("\n==== 活化能的物理意义 ====")
print("活化能表示分子扩散所需克服的能垒。硫化胶中的活化能更高，")
print("这表明交联结构增加了芥子气分子扩散所需克服的能垒，从而抑制了扩散过程。")
print("活化能的差异也解释了交联抑制效应在低温下更为显著的原因——")
print("在低温条件下，分子热运动能量较小，难以克服增加的能垒，因此扩散受到的抑制更为明显；")
print("而在高温条件下，增加的热能可以部分抵消能垒增加的影响，使抑制效应相对减弱。")