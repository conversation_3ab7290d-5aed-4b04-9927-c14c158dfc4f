<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抛体运动原理动画演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            margin: 0;
            padding: 20px;
            color: #2c3e50;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 25px;
        }
        h1 {
            color: #3498db;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 15px;
        }
        .animation-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }
        canvas {
            border: 1px solid #ddd;
            background-color: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .controls {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f1f8fe;
            padding: 10px 15px;
            border-radius: 6px;
        }
        label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #2980b9;
        }
        input[type=range] {
            width: 150px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #2980b9;
        }
        .explanation {
            margin-top: 30px;
            line-height: 1.6;
            background-color: #edfbff;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .parameter-display {
            margin-top: 15px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .parameter-display span {
            font-weight: bold;
            color: #e74c3c;
        }
        .formulas {
            margin: 20px 0;
            padding: 15px;
            background-color: #fdfdea;
            border-radius: 8px;
            border-left: 4px solid #f1c40f;
        }
        .time-display {
            font-size: 18px;
            margin-top: 10px;
            color: #16a085;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>抛体运动原理动画演示</h1>
        
        <div class="animation-container">
            <canvas id="projectileCanvas" width="800" height="400"></canvas>
            <div class="time-display">时间: <span id="timeValue">0.0</span> 秒</div>
            
            <div class="parameter-display">
                初速度: <span id="velocityDisplay">20</span> m/s, 
                角度: <span id="angleDisplay">45</span>°, 
                重力加速度: <span id="gravityDisplay">9.8</span> m/s²
            </div>
            
            <div class="controls">
                <div class="control-group">
                    <label for="velocitySlider">初速度 (m/s)</label>
                    <input type="range" id="velocitySlider" min="5" max="40" value="20" step="1">
                </div>
                
                <div class="control-group">
                    <label for="angleSlider">发射角度 (°)</label>
                    <input type="range" id="angleSlider" min="5" max="85" value="45" step="5">
                </div>
                
                <div class="control-group">
                    <label for="gravitySlider">重力加速度 (m/s²)</label>
                    <input type="range" id="gravitySlider" min="1" max="15" value="9.8" step="0.1">
                </div>
                
                <button id="startButton">启动演示</button>
                <button id="resetButton">重置</button>
            </div>
        </div>
        
        <div class="formulas">
            <h3>抛体运动公式:</h3>
            <p>水平位置: x = v₀·cos(θ)·t</p>
            <p>垂直位置: y = v₀·sin(θ)·t - ½·g·t²</p>
            <p>水平速度: vₓ = v₀·cos(θ) [恒定]</p>
            <p>垂直速度: vᵧ = v₀·sin(θ) - g·t</p>
        </div>
        
        <div class="explanation">
            <h3>抛体运动原理讲解:</h3>
            <p>抛体运动是一种复合运动，由两部分组成:</p>
            <ol>
                <li><strong>水平方向的匀速直线运动</strong>: 在水平方向上，没有外力作用，所以物体保持恒定的水平速度。</li>
                <li><strong>垂直方向的匀加速直线运动</strong>: 在垂直方向上，重力导致物体做加速运动，速度不断变化。</li>
            </ol>
            <p>动画中，您可以看到:</p>
            <ul>
                <li>蓝色曲线表示抛体轨迹，呈现抛物线形状</li>
                <li>红色箭头表示物体当前的速度方向和大小</li>
                <li>绿色箭头表示水平速度分量(保持不变)</li>
                <li>蓝色箭头表示垂直速度分量(不断变化)</li>
                <li>黄色箭头表示重力作用</li>
            </ul>
            <p>通过调节<strong>初速度</strong>、<strong>发射角度</strong>和<strong>重力加速度</strong>，您可以观察这些参数如何影响抛体运动的轨迹和时间。</p>
        </div>
    </div>

    <script>
        // 获取Canvas元素和上下文
        const canvas = document.getElementById('projectileCanvas');
        const ctx = canvas.getContext('2d');
        
        // 获取控制元素
        const velocitySlider = document.getElementById('velocitySlider');
        const angleSlider = document.getElementById('angleSlider');
        const gravitySlider = document.getElementById('gravitySlider');
        const startButton = document.getElementById('startButton');
        const resetButton = document.getElementById('resetButton');
        const timeValue = document.getElementById('timeValue');
        const velocityDisplay = document.getElementById('velocityDisplay');
        const angleDisplay = document.getElementById('angleDisplay');
        const gravityDisplay = document.getElementById('gravityDisplay');
        
        // 初始化参数
        let initialVelocity = 20; // m/s
        let launchAngle = 45; // 角度
        let gravity = 9.8; // m/s²
        let scale = 5; // 缩放因子，用于适应Canvas尺寸
        let animationId = null;
        let time = 0;
        let positions = [];
        let isAnimating = false;
        
        // 更新显示参数
        function updateDisplayValues() {
            velocityDisplay.textContent = initialVelocity;
            angleDisplay.textContent = launchAngle;
            gravityDisplay.textContent = gravity;
        }
        
        // 事件监听器
        velocitySlider.addEventListener('input', function() {
            initialVelocity = parseFloat(this.value);
            updateDisplayValues();
            if (!isAnimating) drawStaticScene();
        });
        
        angleSlider.addEventListener('input', function() {
            launchAngle = parseFloat(this.value);
            updateDisplayValues();
            if (!isAnimating) drawStaticScene();
        });
        
        gravitySlider.addEventListener('input', function() {
            gravity = parseFloat(this.value);
            updateDisplayValues();
            if (!isAnimating) drawStaticScene();
        });
        
        startButton.addEventListener('click', function() {
            if (!isAnimating) {
                startAnimation();
            }
        });
        
        resetButton.addEventListener('click', function() {
            stopAnimation();
            resetSimulation();
        });
        
        // 启动动画
        function startAnimation() {
            if (isAnimating) return;
            
            console.log("开始动画");
            isAnimating = true;
            time = 0;
            positions = [];
            animationId = requestAnimationFrame(animate);
        }
        
        // 停止动画
        function stopAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
                isAnimating = false;
            }
        }
        
        // 重置模拟
        function resetSimulation() {
            time = 0;
            positions = [];
            timeValue.textContent = "0.0";
            drawStaticScene();
        }
        
        // 动画循环
        function animate() {
            time += 0.05; // 时间增量
            timeValue.textContent = time.toFixed(1);
            
            const angleInRadians = (launchAngle * Math.PI) / 180;
            const vx = initialVelocity * Math.cos(angleInRadians);
            const vy = initialVelocity * Math.sin(angleInRadians);
            
            const x = 10 + vx * time * scale;
            
            const physical_y = (vy * time) - (0.5 * gravity * time * time); 
            const y = (canvas.height - 10) - (physical_y * scale);
            
            console.log(`时间: ${time.toFixed(1)}, x: ${x.toFixed(1)}, y: ${y.toFixed(1)}, 物理高度: ${physical_y.toFixed(2)}`);
            
            // 检查是否落地
            if (y >= canvas.height - 10) {
                console.log("物体落地，停止动画");
                stopAnimation();
                return;
            }
            
            positions.push({x, y});
            drawScene(time, x, y);
            
            animationId = requestAnimationFrame(animate);
        }
        
        // 绘制静态场景
        function drawStaticScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制地面
            ctx.beginPath();
            ctx.moveTo(0, canvas.height - 10);
            ctx.lineTo(canvas.width, canvas.height - 10);
            ctx.strokeStyle = "#2c3e50";
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制发射点
            ctx.beginPath();
            ctx.arc(10, canvas.height - 10, 5, 0, Math.PI * 2);
            ctx.fillStyle = "#e74c3c";
            ctx.fill();
            
            // 绘制预测轨迹
            ctx.beginPath();
            ctx.moveTo(10, canvas.height - 10);
            
            const angleInRadians = (launchAngle * Math.PI) / 180;
            const vx = initialVelocity * Math.cos(angleInRadians);
            const vy = initialVelocity * Math.sin(angleInRadians);
            
            const totalTime = (2 * vy) / gravity; // 估计的飞行总时间
            
            for (let t = 0; t <= totalTime; t += 0.1) {
                const px = 10 + vx * t * scale;
                const physical_py = (vy * t) - (0.5 * gravity * t * t);
                const py = (canvas.height - 10) - (physical_py * scale);
                ctx.lineTo(px, py);
            }
            
            ctx.strokeStyle = "#3498db";
            ctx.lineWidth = 1;
            ctx.stroke();
            
            // 绘制初始速度矢量
            drawVelocityVector(10, canvas.height - 10, initialVelocity, angleInRadians, 2);
        }
        
        // 绘制动画场景
        function drawScene(time, x, y) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制地面
            ctx.beginPath();
            ctx.moveTo(0, canvas.height - 10);
            ctx.lineTo(canvas.width, canvas.height - 10);
            ctx.strokeStyle = "#2c3e50";
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制轨迹
            if (positions.length > 1) {
                ctx.beginPath();
                ctx.moveTo(10, canvas.height - 10);
                
                for (const pos of positions) {
                    ctx.lineTo(pos.x, pos.y);
                }
                
                ctx.strokeStyle = "#3498db";
                ctx.lineWidth = 2;
                ctx.stroke();
            }
            
            // 绘制物体
            ctx.beginPath();
            ctx.arc(x, y, 8, 0, Math.PI * 2);
            ctx.fillStyle = "#e74c3c";
            ctx.fill();
            
            // 绘制速度分量
            const angleInRadians = (launchAngle * Math.PI) / 180;
            const vx = initialVelocity * Math.cos(angleInRadians);
            const vy_initial = initialVelocity * Math.sin(angleInRadians);
            const vy_current = vy_initial - gravity * time;
            
            const currentAngle = Math.atan2(vy_current, vx);
            const currentSpeed = Math.sqrt(vx*vx + vy_current*vy_current);
            
            // 绘制当前速度向量(红色)
            drawVelocityVector(x, y, currentSpeed, currentAngle, 1.5, "#e74c3c");
            
            // 绘制水平速度分量(绿色)
            drawVelocityVector(x, y, vx, 0, 1.5, "#2ecc71");
            
            // 绘制垂直速度分量(蓝色)
            drawVelocityVector(x, y, Math.abs(vy_current), vy_current > 0 ? -Math.PI/2 : Math.PI/2, 1.5, "#3498db");
            
            // 绘制重力向量(黄色)
            drawVelocityVector(x, y, gravity, Math.PI/2, 1.5, "#f1c40f");
            
            // 绘制发射点
            ctx.beginPath();
            ctx.arc(10, canvas.height - 10, 5, 0, Math.PI * 2);
            ctx.fillStyle = "#95a5a6";
            ctx.fill();
        }
        
        // 绘制速度矢量
        function drawVelocityVector(x, y, magnitude, angle, scale = 1, color = "#e74c3c") {
            const vectorLength = magnitude * scale;
            const endX = x + vectorLength * Math.cos(angle);
            const endY = y + vectorLength * Math.sin(angle);
            
            // 绘制线段
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(endX, endY);
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制箭头
            const arrowSize = 7;
            const arrowAngle = 0.5;
            
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(
                endX - arrowSize * Math.cos(angle - arrowAngle),
                endY - arrowSize * Math.sin(angle - arrowAngle)
            );
            ctx.lineTo(
                endX - arrowSize * Math.cos(angle + arrowAngle),
                endY - arrowSize * Math.sin(angle + arrowAngle)
            );
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
        }
        
        // 初始化
        updateDisplayValues();
        drawStaticScene();
    </script>
</body>
</html> 