#!perl

use strict;
use warnings;
use MaterialsScript qw(:all);

# 特定方向导通率(Directional Percolation Rate)计算脚本
# 基于Materials Studio的分子动力学模拟轨迹
# 计算导电填料之间的导通率A = 导通的构型数/总构型数
# 当两个导电填料之间的距离小于隧穿距离TD时，认为它们导通
# 特别地，本脚本可以计算在X、Y、Z三个特定方向上的导通率

# 配置参数
my $inputFileName = "Target";  # 轨迹文件基本名称(.xtd)
my $tunnelDistance = 3.0;      # 隧穿距离TD(Å)，当两个导电填料之间距离小于此值时认为导通
my $timeInterval = 0.01;       # 每个构型的输出时间间隔(ps)
my $totalFrames = 5000;        # 需要统计的总构型数
my $conductiveFiller = "C";    # 导电填料原子类型(默认为碳原子，可根据需要修改)

# 方向定义参数
my $boundaryTolerance = 0.1;   # 边界容差，当原子坐标接近边界时的判断容差

# 初始化输出文档
my $resultsTable = Documents->New("$inputFileName"."_Directional_Conductivity.std");  # 创建结果表格
my $logFile = Documents->New("$inputFileName"."_Directional_Conductivity_Log.txt");   # 创建日志文件

# 记录标题信息
$logFile->Append("方向性导通率计算脚本\n");
$logFile->Append("隧穿距离(TD): $tunnelDistance Å\n");
$logFile->Append("每个构型的时间间隔: $timeInterval ps\n");
$logFile->Append("总构型数: $totalFrames\n");
$logFile->Append("导电填料类型: $conductiveFiller\n");
$logFile->Append("===========================================\n\n");
$logFile->Save;

# 设置结果表格标题
$resultsTable->ColumnHeading(0) = "帧序号";
$resultsTable->ColumnHeading(1) = "时间(ps)";
$resultsTable->ColumnHeading(2) = "整体导通状态";
$resultsTable->ColumnHeading(3) = "X方向导通";
$resultsTable->ColumnHeading(4) = "Y方向导通";
$resultsTable->ColumnHeading(5) = "Z方向导通";
$resultsTable->ColumnHeading(6) = "团簇数";
$resultsTable->ColumnHeading(7) = "最大团簇尺寸";

# 检查轨迹文件是否存在
if (!defined($Documents{"$inputFileName.xtd"})) {
    $logFile->Append("错误: 未找到输入轨迹文件 '$inputFileName.xtd'\n");
    $logFile->Append("请检查文件名并确保轨迹文件已正确导入\n");
    $logFile->Save;
    die "错误: 未找到输入轨迹文件 '$inputFileName.xtd'，进程终止";
}

# 打开轨迹文件
my $trajectoryDoc = $Documents{"$inputFileName.xtd"};
my $trajectory = $trajectoryDoc->Trajectory;

# 验证轨迹有效
if (!defined($trajectory)) {
    $logFile->Append("错误: 轨迹对象无效\n");
    $logFile->Save;
    die "错误: 轨迹对象无效，进程终止";
}

# 记录轨迹信息
my $totalAvailableFrames = $trajectory->NumFrames;
$logFile->Append("轨迹信息:\n");
$logFile->Append("  总帧数: $totalAvailableFrames\n");
$logFile->Append("  将分析的帧数: " . ($totalFrames > $totalAvailableFrames ? $totalAvailableFrames : $totalFrames) . "\n\n");
$logFile->Save;

# 限制要分析的帧数
$totalFrames = $totalAvailableFrames if $totalFrames > $totalAvailableFrames;

# 统计变量
my $conductiveFrames = 0;         # 整体导通的构型数
my $conductiveFramesX = 0;        # X方向导通的构型数
my $conductiveFramesY = 0;        # Y方向导通的构型数
my $conductiveFramesZ = 0;        # Z方向导通的构型数
my %frameResults;                 # 存储每个帧的结果

# 遍历轨迹的每一帧
for (my $frameIndex = 0; $frameIndex < $totalFrames; $frameIndex++) {
    # 设置当前帧
    $trajectory->CurrentFrame = $frameIndex;
    my $currentTime = $frameIndex * $timeInterval;
    
    # 记录处理状态
    if ($frameIndex % 100 == 0) {
        $logFile->Append("正在处理帧 $frameIndex (时间: $currentTime ps)...\n");
        $logFile->Save;
    }
    
    # 获取当前帧的3D模型和晶胞信息
    my $currentStructure = $trajectoryDoc->CurrentUnitCell;
    my $lattice = $trajectoryDoc->Lattice3D;
    
    # 获取晶格参数
    my $aVector = $lattice->VectorA;
    my $bVector = $lattice->VectorB;
    my $cVector = $lattice->VectorC;
    my $aLength = $lattice->LengthA;
    my $bLength = $lattice->LengthB;
    my $cLength = $lattice->LengthC;
    
    # 获取所有导电填料原子
    my @fillerAtoms = $currentStructure->Atoms($conductiveFiller);
    my $totalFillers = scalar(@fillerAtoms);
    
    if ($totalFillers == 0) {
        $logFile->Append("警告: 帧 $frameIndex 中未找到导电填料原子\n");
        $logFile->Save;
        next;
    }
    
    # 创建序号映射 - 将每个填料分配从1到N的唯一序号
    my %fillerIndices;
    my %fillerPositions;  # 存储原子的分数坐标
    
    for (my $i = 0; $i < $totalFillers; $i++) {
        my $atomId = $fillerAtoms[$i]->ID;
        $fillerIndices{$atomId} = $i + 1; # 从1开始编号
        
        # 获取原子的分数坐标
        my $fracX = $fillerAtoms[$i]->FractionalXYZ->X;
        my $fracY = $fillerAtoms[$i]->FractionalXYZ->Y;
        my $fracZ = $fillerAtoms[$i]->FractionalXYZ->Z;
        
        $fillerPositions{$i + 1} = {
            'x' => $fracX,
            'y' => $fracY,
            'z' => $fracZ
        };
    }
    
    # 创建团簇映射表 - 初始时每个填料都是独立团簇
    my %clusterMap;
    for (my $i = 0; $i < $totalFillers; $i++) {
        $clusterMap{$i + 1} = $i + 1; # 初始时每个填料的团簇ID等于其自身序号
    }
    
    # 实现查找函数 - 查找填料所属的团簇
    sub findCluster {
        my $id = shift;
        if ($clusterMap{$id} != $id) {
            $clusterMap{$id} = findCluster($clusterMap{$id});
        }
        return $clusterMap{$id};
    }
    
    # 实现合并函数 - 合并两个团簇
    sub unionClusters {
        my ($id1, $id2) = @_;
        my $root1 = findCluster($id1);
        my $root2 = findCluster($id2);
        
        if ($root1 == $root2) {
            return; # 已经在同一团簇中
        }
        
        # 合并两个团簇，取较小的ID作为新团簇ID
        if ($root1 < $root2) {
            $clusterMap{$root2} = $root1;
        } else {
            $clusterMap{$root1} = $root2;
        }
    }
    
    # 检查填料间距并形成团簇
    for (my $i = 0; $i < $totalFillers; $i++) {
        for (my $j = $i + 1; $j < $totalFillers; $j++) {
            my $distance = $fillerAtoms[$i]->Distance($fillerAtoms[$j]);
            
            if ($distance < $tunnelDistance) {
                # 如果距离小于隧穿距离，合并团簇
                unionClusters($i + 1, $j + 1);
            }
        }
    }
    
    # 计算最终团簇
    my %finalClusters;
    for (my $i = 1; $i <= $totalFillers; $i++) {
        my $clusterId = findCluster($i);
        $finalClusters{$clusterId} = [] unless exists $finalClusters{$clusterId};
        push @{$finalClusters{$clusterId}}, $i;
    }
    
    # 计算团簇数量和最大团簇尺寸
    my $numClusters = scalar(keys %finalClusters);
    my $maxClusterSize = 0;
    my $largestClusterId = 0;
    
    foreach my $clusterId (keys %finalClusters) {
        my $size = scalar(@{$finalClusters{$clusterId}});
        if ($size > $maxClusterSize) {
            $maxClusterSize = $size;
            $largestClusterId = $clusterId;
        }
    }
    
    # 判断是否导通 (整体)
    my $isPercolated = ($maxClusterSize / $totalFillers > 0.5) ? 1 : 0;
    $conductiveFrames += $isPercolated;
    
    # 分析最大团簇在X、Y、Z方向上的导通性
    my $isPercolatedX = 0;
    my $isPercolatedY = 0;
    my $isPercolatedZ = 0;
    
    if ($isPercolated && $largestClusterId > 0) {
        # 获取最大团簇中的所有原子
        my @clusterAtoms = @{$finalClusters{$largestClusterId}};
        
        # 检查X方向导通
        my $minX = 1.0;
        my $maxX = 0.0;
        
        # 检查Y方向导通
        my $minY = 1.0;
        my $maxY = 0.0;
        
        # 检查Z方向导通
        my $minZ = 1.0;
        my $maxZ = 0.0;
        
        # 遍历团簇中的所有原子，找出各方向的最小和最大坐标
        foreach my $atomId (@clusterAtoms) {
            my $posX = $fillerPositions{$atomId}->{'x'};
            my $posY = $fillerPositions{$atomId}->{'y'};
            my $posZ = $fillerPositions{$atomId}->{'z'};
            
            # 处理周期性边界条件
            # 注意：这里简化处理，实际上需要考虑周期性边界条件下的连通性
            $minX = $posX if $posX < $minX;
            $maxX = $posX if $posX > $maxX;
            
            $minY = $posY if $posY < $minY;
            $maxY = $posY if $posY > $maxY;
            
            $minZ = $posZ if $posZ < $minZ;
            $maxZ = $posZ if $posZ > $maxZ;
        }
        
        # 判断各方向是否导通 (跨越80%以上的晶胞长度)
        $isPercolatedX = (($maxX - $minX) > 0.8) ? 1 : 0;
        $isPercolatedY = (($maxY - $minY) > 0.8) ? 1 : 0;
        $isPercolatedZ = (($maxZ - $minZ) > 0.8) ? 1 : 0;
        
        # 更精确的方法：检查是否有原子接近周期性边界的两侧
        # 这里使用一个简单的算法，检查是否有原子在边界附近(距离小于边界容差)
        my $hasNearLowX = 0;
        my $hasNearHighX = 0;
        my $hasNearLowY = 0;
        my $hasNearHighY = 0;
        my $hasNearLowZ = 0;
        my $hasNearHighZ = 0;
        
        foreach my $atomId (@clusterAtoms) {
            my $posX = $fillerPositions{$atomId}->{'x'};
            my $posY = $fillerPositions{$atomId}->{'y'};
            my $posZ = $fillerPositions{$atomId}->{'z'};
            
            $hasNearLowX = 1 if $posX < $boundaryTolerance;
            $hasNearHighX = 1 if $posX > (1.0 - $boundaryTolerance);
            
            $hasNearLowY = 1 if $posY < $boundaryTolerance;
            $hasNearHighY = 1 if $posY > (1.0 - $boundaryTolerance);
            
            $hasNearLowZ = 1 if $posZ < $boundaryTolerance;
            $hasNearHighZ = 1 if $posZ > (1.0 - $boundaryTolerance);
        }
        
        # 如果团簇在某方向上同时接近两个边界，则认为在该方向上导通
        $isPercolatedX = 1 if ($hasNearLowX && $hasNearHighX);
        $isPercolatedY = 1 if ($hasNearLowY && $hasNearHighY);
        $isPercolatedZ = 1 if ($hasNearLowZ && $hasNearHighZ);
    }
    
    # 统计各方向导通的帧数
    $conductiveFramesX += $isPercolatedX;
    $conductiveFramesY += $isPercolatedY;
    $conductiveFramesZ += $isPercolatedZ;
    
    # 将结果保存到表格
    $resultsTable->Cell($frameIndex, 0) = $frameIndex;
    $resultsTable->Cell($frameIndex, 1) = $currentTime;
    $resultsTable->Cell($frameIndex, 2) = $isPercolated;
    $resultsTable->Cell($frameIndex, 3) = $isPercolatedX;
    $resultsTable->Cell($frameIndex, 4) = $isPercolatedY;
    $resultsTable->Cell($frameIndex, 5) = $isPercolatedZ;
    $resultsTable->Cell($frameIndex, 6) = $numClusters;
    $resultsTable->Cell($frameIndex, 7) = $maxClusterSize;
    
    # 存储帧结果
    $frameResults{$frameIndex} = {
        'time' => $currentTime,
        'isPercolated' => $isPercolated,
        'isPercolatedX' => $isPercolatedX,
        'isPercolatedY' => $isPercolatedY,
        'isPercolatedZ' => $isPercolatedZ,
        'numClusters' => $numClusters,
        'maxClusterSize' => $maxClusterSize
    };
}

# 计算整体导通率和各方向导通率
my $conductivityA = $conductiveFrames / $totalFrames;
my $conductivityX = $conductiveFramesX / $totalFrames;
my $conductivityY = $conductiveFramesY / $totalFrames;
my $conductivityZ = $conductiveFramesZ / $totalFrames;

# 添加导通率结果到日志
$logFile->Append("\n===========================================\n");
$logFile->Append("导通率计算结果:\n");
$logFile->Append("  总构型数: $totalFrames\n");
$logFile->Append("  整体导通的构型数: $conductiveFrames\n");
$logFile->Append("  整体导通率 A = $conductivityA\n");
$logFile->Append("  X方向导通的构型数: $conductiveFramesX\n");
$logFile->Append("  X方向导通率 = $conductivityX\n");
$logFile->Append("  Y方向导通的构型数: $conductiveFramesY\n");
$logFile->Append("  Y方向导通率 = $conductivityY\n");
$logFile->Append("  Z方向导通的构型数: $conductiveFramesZ\n");
$logFile->Append("  Z方向导通率 = $conductivityZ\n");
$logFile->Append("===========================================\n");
$logFile->Save;

# 添加导通率结果到表格
$resultsTable->Cell($totalFrames + 1, 0) = "导通率结果";
$resultsTable->Cell($totalFrames + 1, 2) = $conductivityA;
$resultsTable->Cell($totalFrames + 1, 3) = $conductivityX;
$resultsTable->Cell($totalFrames + 1, 4) = $conductivityY;
$resultsTable->Cell($totalFrames + 1, 5) = $conductivityZ;

# 输出完成信息
$logFile->Append("\n计算完成\n");
$logFile->Save;

print "方向性导通率计算完成，结果已保存到 $inputFileName"."_Directional_Conductivity.std 文件\n"; 