#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
逸度与压强转换工具
作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)
功能: 基于PR状态方程进行流体的逸度与压强之间的转换，并提供可视化界面

概念说明:
- 逸度(Fugacity): 用于描述非理想气体的热力学性质，与压力具有相同单位(Pa)
- 逸度系数(Fugacity Coefficient): 逸度与压力之比，无量纲
- PR状态方程: Peng-Robinson状态方程，一种用于计算实际气体性质的状态方程

使用方法:
1. 如果有图形界面库(tkinter和matplotlib)，将自动启动图形界面
2. 如果缺少相关库，将使用命令行界面
3. 输入物质的临界参数(临界温度、临界压力、偏心因子)和计算条件(温度、压力)
4. 计算结果包括逸度、逸度系数和压缩因子

注意:
- 所有输入参数必须使用SI单位(温度K，压力Pa)
- 零基础用户可以直接使用默认值进行计算
- 计算结果的显示精度已优化为10位小数
"""

import math
from math import log, exp, sqrt
import sys
import os
import csv
import numpy as np
from datetime import datetime
from tkinter import filedialog
import base64
import time

# 尝试导入可视化相关的库
MATPLOTLIB_AVAILABLE = False
TKINTER_AVAILABLE = False

try:
    import tkinter as tk
    from tkinter import ttk, messagebox
    TKINTER_AVAILABLE = True
    
    try:
        import matplotlib
        matplotlib.use('TkAgg')
        import matplotlib.pyplot as plt
        from matplotlib.figure import Figure
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用黑体显示中文
        plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号
        MATPLOTLIB_AVAILABLE = True
    except ImportError:
        print("警告: 未找到matplotlib库，将使用命令行界面。")
        print("如果需要可视化功能，请安装matplotlib: pip install matplotlib")
except ImportError:
    print("警告: 未找到tkinter库，将使用命令行界面。")
    print("如果需要图形界面，请确保Python安装时包含了tkinter。")

print(sys.executable)  # 查看当前使用的Python解释器路径

# 用户需要输入的变量
# 示例：氮气 (N2) 物性参数 - 以下默认值来自于热力学数据库
DEFAULT_SUBSTANCE = "氮气 (N2)"
DEFAULT_Tc = 126.2  # 临界温度 (K)
DEFAULT_Pc = 3.4e6  # 临界压力 (Pa)
DEFAULT_OMEGA = 0.0372  # 偏心因子

# 默认计算条件 - 常温常压下
DEFAULT_T = 300  # 温度 (K) 约等于室温
DEFAULT_P = 1e6  # 压力 (Pa) = 1 MPa (约10个大气压)

# 精度控制参数 - 确保计算结果精确
CONVERGENCE_TOL = 1e-12  # 收敛容差，提高精度
MAX_ITERATIONS = 500     # 最大迭代次数，避免无限循环
ROOT_SELECTION_TOL = 1e-15  # 用于判断虚根的容差

# 可视化参数 - 控制图表范围
DEFAULT_P_MIN = 0.1e6  # 压力最小值 (Pa) = 0.1 MPa
DEFAULT_P_MAX = 10e6   # 压力最大值 (Pa) = 10 MPa
DEFAULT_POINTS = 200   # 曲线上的点数，增加点数提高曲线精度

# 添加常见物质的物性数据库
SUBSTANCE_DATABASE = {
    "氮气 (N2)": {"Tc": 126.2, "Pc": 3.4e6, "omega": 0.0372},
    "氧气 (O2)": {"Tc": 154.6, "Pc": 5.04e6, "omega": 0.0222},
    "二氧化碳 (CO2)": {"Tc": 304.1, "Pc": 7.38e6, "omega": 0.2236},
    "甲烷 (CH4)": {"Tc": 190.6, "Pc": 4.60e6, "omega": 0.0115},
    "乙烷 (C2H6)": {"Tc": 305.4, "Pc": 4.88e6, "omega": 0.0995},
    "乙烯 (C2H4)": {"Tc": 282.3, "Pc": 5.04e6, "omega": 0.0866},
    "丙烷 (C3H8)": {"Tc": 370.0, "Pc": 4.25e6, "omega": 0.1523},
    "氨 (NH3)": {"Tc": 405.6, "Pc": 11.3e6, "omega": 0.2526},
    "水 (H2O)": {"Tc": 647.1, "Pc": 22.06e6, "omega": 0.3443},
    "氦气 (He)": {"Tc": 5.2, "Pc": 0.23e6, "omega": -0.3900},
    "氩气 (Ar)": {"Tc": 150.9, "Pc": 4.90e6, "omega": 0.0000},
    "氢气 (H2)": {"Tc": 33.2, "Pc": 1.30e6, "omega": -0.2160},
}

# 单位转换常数
ATM_TO_PA = 101325.0  # 1 atm = 101325 Pa
BAR_TO_PA = 1e5       # 1 bar = 100000 Pa
MPA_TO_PA = 1e6       # 1 MPa = 1000000 Pa
PSI_TO_PA = 6894.76   # 1 psi = 6894.76 Pa
MMHG_TO_PA = 133.322  # 1 mmHg = 133.322 Pa

def solve_cubic_equation(a, b, c, d):
    """
    纯Python实现求解三次方程: ax^3 + bx^2 + cx + d = 0
    采用改进的Cardano方法，增强数值稳定性
    
    参数:
    a, b, c, d: 三次方程系数
    
    返回:
    list: 三个根的列表(可能包含复根)
    """
    # 处理特殊情况：系数为零
    if abs(a) < 1e-15:  # 如果a接近于0，退化为二次方程
        return solve_quadratic_equation(b, c, d)
    
    # 标准化为 x^3 + px^2 + qx + r = 0
    p = b / a
    q = c / a
    r = d / a
    
    # 消去二次项，代换 x = y - p/3
    p_over_3 = p / 3.0
    p_over_3_squared = p_over_3 * p_over_3
    
    # 计算新的系数 y^3 + ay + b = 0 的a和b
    a_new = q - 3.0 * p_over_3_squared
    b_new = r + 2.0 * p_over_3 * p_over_3_squared - p_over_3 * q
    
    # 计算判别式 delta = (b/2)^2 + (a/3)^3
    a_new_over_3 = a_new / 3.0
    b_new_over_2 = b_new / 2.0
    
    # 使用更安全的计算方式来避免精度问题
    discriminant = b_new_over_2**2 + a_new_over_3**3
    
    # 根据判别式的符号确定根的性质
    roots = []
    
    # 使用容差判断判别式是否接近于零
    if abs(discriminant) < 1e-10:  # delta ≈ 0，有重根
        if abs(a_new) < 1e-10:  # a ≈ 0，有三重根
            # 三重根的情况
            y = 0.0 if abs(b_new) < 1e-10 else -b_new**(1/3.0)
            roots = [complex(y - p_over_3, 0)] * 3
        else:  # 一个单根和一个二重根
            # 使用更稳定的公式计算
            y1 = -3.0 * b_new / a_new if abs(a_new) > 1e-10 else 0.0
            y2 = -y1 / 2.0
            roots = [complex(y1 - p_over_3, 0), complex(y2 - p_over_3, 0), complex(y2 - p_over_3, 0)]
    elif discriminant > 0:  # delta > 0，有一个实根和一对共轭复根
        # 使用更稳定的数值方法
        sqrt_delta = math.sqrt(discriminant)
        
        # 计算立方根，确保数值稳定性
        if b_new_over_2 >= 0:
            u_base = -b_new_over_2 + sqrt_delta
            v_base = -b_new_over_2 - sqrt_delta
        else:
            u_base = -b_new_over_2 + sqrt_delta
            v_base = -b_new_over_2 - sqrt_delta
        
        # 计算立方根，处理负数情况
        u = complex(u_base, 0) ** (1/3.0) if u_base >= 0 else -complex(-u_base, 0) ** (1/3.0)
        v = complex(v_base, 0) ** (1/3.0) if v_base >= 0 else -complex(-v_base, 0) ** (1/3.0)
        
        # 确保u*v + a_new/3 = 0
        if abs(u.real * v.real + a_new_over_3) > 1e-10:
            # 修正符号
            if u.real > 0:
                v = complex(-abs(v.real), v.imag)
            else:
                u = complex(-abs(u.real), u.imag)
        
        # 实根
        y1 = u.real + v.real
        
        # 复根
        real_part = -(u.real + v.real) / 2.0
        imag_part = math.sqrt(3.0) * (u.real - v.real) / 2.0
        
        roots = [
            complex(y1 - p_over_3, 0),
            complex(real_part - p_over_3, imag_part),
            complex(real_part - p_over_3, -imag_part)
        ]
    else:  # delta < 0，有三个不相等的实根
        # 使用三角函数法
        a_new_abs = abs(a_new)
        sqrt_a_new_abs = math.sqrt(a_new_abs)
        
        # 确保数值稳定性
        if abs(b_new) < 1e-15 or abs(sqrt_a_new_abs) < 1e-15:
            # 如果b_new接近0或sqrt_a_new_abs接近0，使用特殊处理
            roots = [complex(-p_over_3, 0)] * 3
        else:
            # 计算角度，确保在合理范围内
            cos_theta = max(min(-b_new / (2.0 * sqrt_a_new_abs * a_new_abs / sqrt_a_new_abs), 1.0), -1.0)
            theta = math.acos(cos_theta)
            
            # 计算三个实根
            factor = 2.0 * sqrt_a_new_abs / sqrt_a_new_abs
            y1 = factor * math.cos(theta / 3.0)
            y2 = factor * math.cos((theta + 2.0 * math.pi) / 3.0)
            y3 = factor * math.cos((theta + 4.0 * math.pi) / 3.0)
            
            if a_new < 0:  # 如果a < 0，需要添加负号
                y1, y2, y3 = -y1, -y2, -y3
                
            roots = [
                complex(y1 - p_over_3, 0),
                complex(y2 - p_over_3, 0),
                complex(y3 - p_over_3, 0)
            ]
    
    # 精确到足够小的虚部设为0
    for i in range(len(roots)):
        if isinstance(roots[i], complex) and abs(roots[i].imag) < 1e-10:
            roots[i] = complex(roots[i].real, 0)
    
    return roots


def solve_quadratic_equation(a, b, c):
    """
    纯Python实现求解二次方程: ax^2 + bx + c = 0
    使用改进的公式，增强数值稳定性
    
    参数:
    a, b, c: 二次方程系数
    
    返回:
    list: 两个根的列表和一个0(作为三次方程的第三个根)
    """
    if abs(a) < 1e-15:  # 如果a接近于0，退化为一次方程
        if abs(b) < 1e-15:  # 如果b也接近于0，那么方程没有变量，要么无解要么无穷解
            return [complex(0, 0), complex(0, 0), complex(0, 0)]
        else:
            # bx + c = 0 => x = -c/b
            return [complex(-c / b, 0), complex(0, 0), complex(0, 0)]
    
    # 计算判别式
    delta = b * b - 4 * a * c
    
    if abs(delta) < 1e-15:  # 判别式接近0，有两个相等的实根
        root = -b / (2 * a)
        return [complex(root, 0), complex(root, 0), complex(0, 0)]
    elif delta > 0:  # 有两个不同的实根
        # 使用更稳定的公式，避免精度损失
        if b >= 0:
            q = -0.5 * (b + math.sqrt(delta))
        else:
            q = -0.5 * (b - math.sqrt(delta))
        
        if abs(q) < 1e-15:  # 如果q接近于0，直接计算
            root1 = root2 = -b / (2 * a)
        else:
            root1 = q / a
            root2 = c / q
            
        return [complex(root1, 0), complex(root2, 0), complex(0, 0)]
    else:  # 有两个共轭复根
        real_part = -b / (2 * a)
        imag_part = math.sqrt(abs(delta)) / (2 * abs(a))
        if a < 0:  # 确保符号正确
            imag_part = -imag_part
        return [complex(real_part, imag_part), complex(real_part, -imag_part), complex(0, 0)]

class PRStateEquation:
    """
    Peng-Robinson状态方程的实现
    用于计算逸度系数和压强等热力学性质
    
    改进点：
    1. 使用更精确的参数常数
    2. 增强数值稳定性
    3. 优化计算逻辑
    """
    
    # 气体常数 (J/(mol·K))
    R = 8.3144598  # 更精确的气体常数值
    
    # PR状态方程参数常数
    OMEGA_A = 0.45723553
    OMEGA_B = 0.07779607
    
    def __init__(self):
        """初始化PR状态方程类"""
        pass
    
    def calculate_a_b(self, Tc, Pc, omega):
        """
        计算PR方程的参数a和b
        
        参数:
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        
        返回:
        tuple: (a, b) PR方程的参数
        """
        # 计算b参数 - 使用精确常数
        b = self.OMEGA_B * self.R * Tc / Pc
        
        # 计算a参数
        a = self.OMEGA_A * ((self.R * Tc)**2) / Pc
        
        return a, b
    
    def calculate_kappa(self, omega):
        """
        计算kappa参数，用于计算alpha
        
        参数:
        omega (float): 偏心因子
        
        返回:
        float: kappa值
        """
        # 使用改进的kappa计算公式
        if omega <= 0.491:
            return 0.37464 + 1.54226 * omega - 0.26992 * omega**2
        else:
            # 对于高偏心因子的物质使用另一个公式
            return 0.379642 + 1.48503 * omega - 0.164423 * omega**2 + 0.016666 * omega**3
    
    def calculate_alpha(self, T, Tc, omega):
        """
        计算温度相关的alpha参数
        
        参数:
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        omega (float): 偏心因子
        
        返回:
        float: alpha值
        """
        Tr = T / Tc  # 约化温度
        kappa = self.calculate_kappa(omega)
        
        # 确保数值稳定性
        sqrt_Tr = math.sqrt(max(Tr, 1e-10))
        term = 1.0 + kappa * (1.0 - sqrt_Tr)
        return term * term
    
    def calculate_a_T(self, T, Tc, Pc, omega):
        """
        计算给定温度下的a(T)参数
        
        参数:
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        
        返回:
        float: a(T)参数值
        """
        a, _ = self.calculate_a_b(Tc, Pc, omega)
        alpha = self.calculate_alpha(T, Tc, omega)
        return a * alpha
    
    def select_proper_root(self, roots, P, T, Tc, Pc, B, physical_accuracy=False):
        """
        从三次方程的根中选择热力学上最合理的根
        
        优化点:
        1. 避免相变区域的不连续选择
        2. 提供更平滑的曲线，去除峰值
        3. 增强临界点附近的稳定性
        4. 修复复数比较错误
        
        参数:
        roots (list): 三次方程的根
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        B (float): PR方程中的B参数
        physical_accuracy (bool): 是否使用物理精确模式，默认为False
        
        返回:
        float: 选择的根
        """
        # 筛选实根 (使用更严格的判断标准)
        real_roots = []
        for root in roots:
            # 避免复数比较错误，确保只选择实根
            if isinstance(root, complex) and abs(root.imag) < ROOT_SELECTION_TOL:
                real_roots.append(root.real)
            elif isinstance(root, (int, float)):
                real_roots.append(float(root))
        
        if not real_roots:
            # 如果没有找到足够小虚部的根，尝试选择虚部最小的根
            if len(roots) > 0:
                min_imag_root = min(roots, key=lambda r: abs(r.imag) if isinstance(r, complex) else 0)
                if isinstance(min_imag_root, complex):
                    return min_imag_root.real
                return float(min_imag_root)
            raise ValueError(f"没有找到实根，无法计算压缩因子Z。压力={P} Pa，温度={T} K")
        
        # 如果只有一个实根，直接返回
        if len(real_roots) == 1:
            return real_roots[0]
        
        # 计算约化温度和约化压力
        Tr = T / Tc
        Pr = P / Pc
        
        # 定义临界区域的范围 - 扩大区域以确保稳定性
        is_near_critical = abs(Tr - 1.0) < 0.2 and abs(Pr - 1.0) < 0.2
        
        # 物理精确模式: 使用经典的热力学根选择策略
        if physical_accuracy:
            # 在临界点附近时采用特殊处理以避免突变
            if is_near_critical:
                # 对于三个实根，选择中间根以确保平滑过渡
                if len(real_roots) >= 3:
                    real_roots.sort()
                    return real_roots[1]  # 返回中间的根
                else:  # 两个实根的情况
                    return sum(real_roots) / len(real_roots)  # 返回平均值
            
            # 对于明显超临界温度(T > Tc * 1.1)的情况，只有一个物理有意义的根
            if T > Tc * 1.1:
                # 超临界温度，通常选择最大的实根
                return max(real_roots)
            
            # 针对亚临界温度(T < Tc * 0.9)且与临界温度相差较大的情况
            if T < Tc * 0.9:
                real_roots.sort()  # 从小到大排序
                
                if len(real_roots) >= 3:
                    # 三个实根的情况
                    # 在两相区域，通过压力区分液相和气相
                    
                    if P > Pc * 1.2:  # 高压区域，液相
                        return real_roots[0]  # 液相，最小根
                    elif P < Pc * 0.8:  # 低压区域，气相
                        return real_roots[2]  # 气相，最大根
                    else:  # 临界压力附近
                        # 根据行为过渡选择
                        weight = (P - Pc * 0.8) / (Pc * 0.4)  # 0到1的权重
                        return real_roots[2] * (1 - weight) + real_roots[0] * weight
                else:  # 两个实根的情况
                    if P > Pc:
                        return min(real_roots)  # 倾向于液相
                    else:
                        return max(real_roots)  # 倾向于气相
            
            # 对于接近临界温度但不在临界点附近的情况(0.9*Tc < T < 1.1*Tc)
            # 这是最复杂的区域，需要平滑过渡
            real_roots.sort()  # 从小到大排序
            
            if len(real_roots) >= 3:
                # 以压力为基础，在临界线上平滑过渡
                if P > Pc * 1.2:  # 高压区域
                    return real_roots[0]  # 液相行为
                elif P < Pc * 0.8:  # 低压区域
                    return real_roots[2]  # 气相行为
                else:  # 临界压力附近
                    # 使用基于压力的加权平均确保平滑过渡
                    weight = (P - Pc * 0.8) / (Pc * 0.4)  # 0到1的权重
                    
                    # 如果温度接近临界温度，则更倾向于中间根以避免突变
                    temp_weight = min(1.0, abs(T - Tc) / (0.1 * Tc))
                    middle_weight = 1.0 - temp_weight
                    
                    # 加权平均：临界点附近倾向中间根，远离临界点则在最大根和最小根之间过渡
                    weighted_root = (real_roots[2] * (1 - weight) + real_roots[0] * weight) * temp_weight + real_roots[1] * middle_weight
                    return weighted_root
            else:  # 两个实根的情况
                # 在临界温度附近，使用加权平均以确保平滑
                weight = min(1.0, abs(Pr - 1.0))
                return (max(real_roots) * (1 - weight) + min(real_roots) * weight) * 0.5 + sum(real_roots) / len(real_roots) * 0.5
        
        # 默认平滑模式: 使用增强的平滑过渡策略
        else:
            # 在临界点附近时采用更精细的平滑过渡
            if is_near_critical:
                real_roots.sort()
                # 对于三个实根的情况，我们使用经过加权的中间值
                if len(real_roots) >= 3:
                    # 根据与临界点的距离计算权重
                    critical_distance = math.sqrt((Tr - 1.0)**2 + (Pr - 1.0)**2)
                    # 越接近临界点，越倾向于真正的中间根
                    middle_weight = max(0.6, 1.0 - critical_distance * 5)
                    edge_weight = (1.0 - middle_weight) / 2
                    
                    # 三个根的加权平均
                    return real_roots[0] * edge_weight + real_roots[1] * middle_weight + real_roots[2] * edge_weight
                else:  # 两个根的情况
                    # 直接使用平均值确保平滑
                    return sum(real_roots) / len(real_roots)
            
            # 检查是否在超临界区域(T > Tc)
            if T > Tc:
                # 超临界区域，通常选择最大的根
                # 但在接近临界温度时需要平滑过渡
                if T < Tc * 1.1:  # 接近临界温度
                    real_roots.sort()
                    # 根据温度计算权重
                    weight = (T - Tc) / (0.1 * Tc)  # 0到1
                    if len(real_roots) >= 3:
                        # 在临界温度附近，从中间根过渡到最大根
                        return real_roots[1] * (1 - weight) + real_roots[2] * weight
                    else:
                        # 两个根的情况，从平均值过渡到最大根
                        avg_root = sum(real_roots) / len(real_roots)
                        max_root = max(real_roots)
                        return avg_root * (1 - weight) + max_root * weight
                else:
                    # 明显超临界温度，选择最大的根
                    return max(real_roots)
            
            # 亚临界区域(T < Tc)
            if T < Tc:
                real_roots.sort()  # 从小到大排序
                
                # 全局压力过渡策略，更平滑
                # 无论有几个根，在整个压力范围内构建平滑过渡函数
                p_ratio = P / Pc
                
                if len(real_roots) >= 3:
                    # 三个根的情况
                    if p_ratio > 1.5:  # 高压区域
                        return real_roots[0]  # 液相，最小根
                    elif p_ratio < 0.5:  # 低压区域
                        return real_roots[2]  # 气相，最大根
                    else:
                        # 中间压力区域，使用改进的插值平滑过渡
                        # 计算更精细的权重函数，确保平滑过渡
                        weight = (p_ratio - 0.5) / 1.0  # 0到1
                        
                        # 在临界压力附近，更倾向于中间根以避免突变
                        if 0.9 < p_ratio < 1.1:
                            # 临界压力附近的加权策略
                            center_weight = 1.0 - min(1.0, abs(p_ratio - 1.0) * 10)
                            edge_weight = (1.0 - center_weight) / 2
                            
                            # 三点加权平均
                            return (real_roots[0] * weight * edge_weight + 
                                   real_roots[1] * center_weight + 
                                   real_roots[2] * (1 - weight) * edge_weight)
                        else:
                            # 非临界压力区域的简单线性插值
                            return real_roots[0] * weight + real_roots[2] * (1 - weight)
                else:
                    # 两个根的情况，基于压力比例进行平滑插值
                    min_root = min(real_roots)
                    max_root = max(real_roots)
                    
                    if p_ratio > 1.2:  # 高压倾向于最小根
                        return min_root
                    elif p_ratio < 0.8:  # 低压倾向于最大根
                        return max_root
                    else:  # 临界压力附近平滑过渡
                        weight = (p_ratio - 0.8) / 0.4  # 0到1
                        return max_root * (1 - weight) + min_root * weight
            
            # 其他任何情况下，使用基于压力的插值策略
            if Pr > 1.0:  # 高压
                real_roots.sort()
                # 高压区域倾向于选择较小的根
                if len(real_roots) >= 3:
                    # 三根情况，考虑与临界压力的距离
                    if Pr > 1.5:  # 远高于临界压力
                        return real_roots[0]
                    else:  # 接近临界压力
                        weight = (Pr - 1.0) / 0.5  # 0到1
                        return real_roots[1] * (1 - weight) + real_roots[0] * weight
                else:
                    # 两根情况
                    return min(real_roots)
            else:  # 低压
                real_roots.sort()
                # 低压区域倾向于选择较大的根
                if len(real_roots) >= 3:
                    if Pr < 0.5:  # 远低于临界压力
                        return real_roots[2]
                    else:  # 接近临界压力
                        weight = (1.0 - Pr) / 0.5  # 0到1
                        return real_roots[1] * (1 - weight) + real_roots[2] * weight
                else:
                    # 两根情况
                    return max(real_roots)
    
    def calculate_Z_iterative(self, P, T, Tc, Pc, omega):
        """
        使用迭代法计算压缩因子Z
        
        参数:
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        
        返回:
        float: 压缩因子Z
        """
        # 计算PR方程参数
        a_T = self.calculate_a_T(T, Tc, Pc, omega)
        _, b = self.calculate_a_b(Tc, Pc, omega)
        
        # 计算无量纲参数
        A = a_T * P / ((self.R * T)**2)
        B = b * P / (self.R * T)
        
        # 确定更好的初始值
        # 对于T>Tc的情况，用1.0作为初始值
        # 对于T<Tc的情况，使用不同的初始值
        if T > Tc:
            Z = 1.0
        else:
            if P > Pc:
                # 高压区域，可能更接近液相
                Z = B + 0.1
            else:
                # 低压区域，可能更接近气相
                Z = 1.0
        
        # 迭代参数
        max_iter = 100
        tol = 1e-8
        
        # 迭代求解
        for i in range(max_iter):
            # 避免除零问题
            if abs(Z) < 1e-10:
                Z = 1e-10
                
            h = B / Z
            
            # 防止除零和不稳定值
            if abs(1.0-h) < 1e-10 or abs(1.0+2.0*h-h*h) < 1e-10:
                break
                
            Z_new = 1.0/(1.0-h) - h/(1.0+2.0*h-h*h) * (A/B)
            
            # 检查收敛性
            if abs(Z_new - Z) < tol:
                return Z_new
                
            # 应用松弛因子以提高稳定性
            alpha = 0.7  # 松弛因子
            Z = alpha * Z_new + (1-alpha) * Z
        
        # 如果未收敛，返回最后的估计值
        return Z
    
    def calculate_Z(self, P, T, Tc, Pc, omega, physical_accuracy=False, use_iterative=False):
        """
        计算压缩因子Z
        
        参数:
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        physical_accuracy (bool): 是否使用物理精确模式，默认为False
        use_iterative (bool): 是否使用迭代法计算，默认为False
        
        返回:
        float: 压缩因子Z (选择热力学上最合理的根)
        """
        # 如果使用迭代法，直接调用迭代计算方法
        if use_iterative:
            return self.calculate_Z_iterative(P, T, Tc, Pc, omega)
            
        # 否则使用原有的三次方程求解法
        # 计算PR方程参数
        a_T = self.calculate_a_T(T, Tc, Pc, omega)
        _, b = self.calculate_a_b(Tc, Pc, omega)
        
        # 计算无量纲参数
        A = a_T * P / ((self.R * T)**2)
        B = b * P / (self.R * T)
        
        # 求解三次方程: Z^3 - (1-B)Z^2 + (A-3B^2-2B)Z - (AB-B^2-B^3) = 0
        # 转换为标准形式 a*Z^3 + b*Z^2 + c*Z + d = 0
        a = 1.0
        b = -(1.0 - B)
        c = (A - 3.0*B**2 - 2.0*B)
        d = -(A*B - B**2 - B**3)
        
        # 使用纯Python实现求解
        roots = solve_cubic_equation(a, b, c, d)
        
        # 使用改进的根选择方法，传入物理精确模式参数
        return self.select_proper_root(roots, P, T, Tc, Pc, B, physical_accuracy)
    
    def calculate_fugacity_coefficient(self, P, T, Tc, Pc, omega, physical_accuracy=False, use_iterative=False):
        """
        计算逸度系数phi
        
        参数:
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        physical_accuracy (bool): 是否使用物理精确模式，默认为False
        use_iterative (bool): 是否使用迭代法计算，默认为False
        
        返回:
        float: 逸度系数phi
        """
        # 确保输入参数有效
        if P <= 0 or T <= 0 or Tc <= 0 or Pc <= 0:
            raise ValueError("压力、温度、临界温度和临界压力必须为正值")
        
        # 计算PR方程参数
        a_T = self.calculate_a_T(T, Tc, Pc, omega)
        _, b = self.calculate_a_b(Tc, Pc, omega)
        
        # 计算无量纲参数
        A = a_T * P / ((self.R * T)**2)
        B = b * P / (self.R * T)
        
        # 计算压缩因子，传递物理精确模式参数和迭代法参数
        Z = self.calculate_Z(P, T, Tc, Pc, omega, physical_accuracy, use_iterative)
        
        # 检查Z是否为复数，如果是则取实部
        if isinstance(Z, complex):
            if abs(Z.imag) < 1e-10:
                Z = Z.real
            else:
                # 如果虚部较大，抛出异常
                raise ValueError(f"压缩因子Z包含较大的虚部: {Z}，无法进行有效计算")
        
        # 计算辅助参数
        sqrt_2 = math.sqrt(2.0)
        
        # 计算逸度系数，采用更稳定的数值方法
        # 避免Z-B接近0导致的数值不稳定
        Z_minus_B = max(Z - B, 1e-15)  # 提高精度，从1e-10提高到1e-15
        
        # 计算ln(phi)的三个项
        term1 = Z - 1.0
        
        # 使用高精度对数计算
        try:
            term2 = math.log(Z_minus_B)
        except (ValueError, OverflowError):
            # 如果Z_minus_B太小导致对数计算问题，使用级数展开近似
            # ln(x) ≈ (x-1) - (x-1)^2/2 + (x-1)^3/3 - ... for x close to 1
            x = Z_minus_B / (B + 1e-15)  # 缩放参数避免x过小
            term2 = math.log(B + 1e-15) + (x - 1) - (x - 1)**2/2 + (x - 1)**3/3
        
        # 计算第三项，避免数值不稳定
        if abs(B) < 1e-15:  # 提高精度，从1e-10提高到1e-15
            term3 = 0.0
        else:
            # 计算分母，避免接近0的情况
            denominator1 = max(Z + (1.0 + sqrt_2) * B, 1e-15)  # 提高精度
            denominator2 = max(Z + (1.0 - sqrt_2) * B, 1e-15)  # 提高精度
            
            # 避免对负数或零取对数
            if denominator1 <= 0 or denominator2 <= 0:
                # 当分母可能为负时，使用改进的近似公式
                # 从原先的简单近似改为更精确的计算
                d1 = Z + B
                d2 = Z - B
                term3 = A / (2.0 * sqrt_2 * B) * (2.0 * B / d1 - 2.0 * B**2 / (d1**2) + 2.0 * B**3 / (3.0 * d1**3))
            else:
                # 使用更精确的对数计算方法
                if abs(denominator1 / denominator2 - 1.0) < 1e-10:
                    # 当分子和分母非常接近时，使用泰勒展开近似对数
                    ratio = denominator1 / denominator2
                    log_term = (ratio - 1) - (ratio - 1)**2/2 + (ratio - 1)**3/3
                else:
                    log_term = math.log(denominator1 / denominator2)
                
                term3 = A / (2.0 * sqrt_2 * B) * log_term
        
        # 计算ln(phi)并返回phi
        ln_phi = term1 - term2 - term3
        
        # 使用指数函数计算phi
        try:
            return math.exp(ln_phi)
        except OverflowError:
            # 处理可能的指数溢出
            if ln_phi > 700:  # exp(709)是双精度浮点数的上限附近
                print(f"警告: 逸度系数计算中指数溢出 (ln_phi={ln_phi})，返回最大值")
                return float('inf')
            elif ln_phi < -700:
                print(f"警告: 逸度系数计算中指数下溢 (ln_phi={ln_phi})，返回0")
                return 0.0
            else:
                raise  # 其他原因导致的溢出，重新抛出异常

class FugacityPressureConverter:
    """
    逸度与压强转换工具
    基于PR状态方程计算逸度系数
    
    特性:
    1. 支持常见物质的物性参数查询
    2. 支持多种压力单位的转换
    3. 增强的数值稳定性和精确度
    4. 用户友好的错误处理
    """
    
    def __init__(self):
        """初始化转换工具"""
        self.pr_equation = PRStateEquation()
        self.substance_database = SUBSTANCE_DATABASE
    
    def get_substance_properties(self, substance_name):
        """
        获取物质的物性参数
        
        参数:
        substance_name (str): 物质名称
        
        返回:
        dict: 包含Tc, Pc, omega的字典，如果未找到则返回None
        """
        return self.substance_database.get(substance_name)
    
    def list_available_substances(self):
        """
        列出所有可用的物质
        
        返回:
        list: 物质名称列表
        """
        return list(self.substance_database.keys())
    
    def convert_pressure_units(self, pressure, from_unit="Pa", to_unit="Pa"):
        """
        转换压力单位
        
        参数:
        pressure (float): 压力值
        from_unit (str): 源单位 ("Pa", "atm", "bar", "MPa", "psi", "mmHg")
        to_unit (str): 目标单位 ("Pa", "atm", "bar", "MPa", "psi", "mmHg")
        
        返回:
        float: 转换后的压力值
        """
        # 先转换为Pa
        if from_unit == "Pa":
            pressure_pa = pressure
        elif from_unit == "atm":
            pressure_pa = pressure * ATM_TO_PA
        elif from_unit == "bar":
            pressure_pa = pressure * BAR_TO_PA
        elif from_unit == "MPa":
            pressure_pa = pressure * MPA_TO_PA
        elif from_unit == "psi":
            pressure_pa = pressure * PSI_TO_PA
        elif from_unit == "mmHg":
            pressure_pa = pressure * MMHG_TO_PA
        else:
            raise ValueError(f"不支持的单位：{from_unit}")
        
        # 从Pa转换为目标单位
        if to_unit == "Pa":
            return pressure_pa
        elif to_unit == "atm":
            return pressure_pa / ATM_TO_PA
        elif to_unit == "bar":
            return pressure_pa / BAR_TO_PA
        elif to_unit == "MPa":
            return pressure_pa / MPA_TO_PA
        elif to_unit == "psi":
            return pressure_pa / PSI_TO_PA
        elif to_unit == "mmHg":
            return pressure_pa / MMHG_TO_PA
        else:
            raise ValueError(f"不支持的单位：{to_unit}")
    
    def convert_pressure(self, pressure, from_unit="Pa", to_unit="Pa"):
        """
        转换压力单位 (作为convert_pressure_units的别名)
        
        参数:
        pressure (float): 压力值
        from_unit (str): 源单位 ("Pa", "atm", "bar", "MPa", "psi", "mmHg")
        to_unit (str): 目标单位 ("Pa", "atm", "bar", "MPa", "psi", "mmHg")
        
        返回:
        float: 转换后的压力值
        """
        return self.convert_pressure_units(pressure, from_unit, to_unit)
    
    def pressure_to_fugacity(self, P, T, Tc, Pc, omega, physical_accuracy=False, use_iterative=False):
        """
        从压强计算逸度
        
        参数:
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        physical_accuracy (bool): 是否使用物理精确模式，默认为False
        use_iterative (bool): 是否使用迭代法计算，默认为False
        
        返回:
        float: 逸度 (Pa)
        """
        # 输入验证
        if P <= 0:
            raise ValueError("压力必须为正值")
        if T <= 0:
            raise ValueError("温度必须为正值")
        if Tc <= 0:
            raise ValueError("临界温度必须为正值")
        if Pc <= 0:
            raise ValueError("临界压力必须为正值")
        
        try:
            phi = self.pr_equation.calculate_fugacity_coefficient(P, T, Tc, Pc, omega, physical_accuracy, use_iterative)
            fugacity = phi * P
            return fugacity
        except Exception as e:
            raise ValueError(f"计算逸度失败: {str(e)}")
    
    def fugacity_to_pressure(self, fugacity, T, Tc, Pc, omega, P_initial=None, tol=None, max_iter=None, physical_accuracy=False, use_iterative=False):
        """
        从逸度计算压强 (使用迭代求解)
        
        参数:
        fugacity (float): 逸度 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        P_initial (float, optional): 压力初值 (Pa)，默认为None，会自动设置为fugacity
        tol (float, optional): 收敛容差
        max_iter (int, optional): 最大迭代次数
        physical_accuracy (bool): 是否使用物理精确模式，默认为False
        use_iterative (bool): 是否使用迭代法计算，默认为False
        
        返回:
        float: 压力 (Pa)
        """
        # 输入验证
        if fugacity <= 0:
            raise ValueError("逸度必须为正值")
        if T <= 0:
            raise ValueError("温度必须为正值")
        if Tc <= 0:
            raise ValueError("临界温度必须为正值")
        if Pc <= 0:
            raise ValueError("临界压力必须为正值")
        
        # 使用全局定义的精度控制参数
        tol = tol if tol is not None else CONVERGENCE_TOL
        max_iter = max_iter if max_iter is not None else MAX_ITERATIONS
        
        # 尝试使用Newton-Raphson方法快速接近解
        # 这种方法可以显著加快收敛速度
        if P_initial is None:
            # 首先尝试使用快速近似方法估计初始值
            P_approx = self.estimate_pressure_from_fugacity(fugacity, T, Tc, Pc, omega)
            if P_approx > 0:
                P = P_approx
            else:
                P = fugacity  # 初始猜测：理想气体(phi=1时)
        else:
            P = P_initial
        
        # 防止除零问题
        if abs(P) < 1e-10:
            P = 1e-10
        
        # 记录迭代历史，用于分析收敛性
        convergence_history = []
        
        # Newton-Raphson迭代
        for i in range(max_iter):
            try:
                phi = self.pr_equation.calculate_fugacity_coefficient(P, T, Tc, Pc, omega, physical_accuracy, use_iterative)
                f_current = phi * P
                
                # 计算函数值(实际逸度与目标逸度的差)
                func = f_current - fugacity
                
                # 计算导数：df/dP = phi + P*dphi/dP
                # 使用数值微分近似导数
                delta_P = max(P * 1e-6, 1e-10)  # 扰动步长
                phi_plus = self.pr_equation.calculate_fugacity_coefficient(P + delta_P, T, Tc, Pc, omega, physical_accuracy, use_iterative)
                f_plus = phi_plus * (P + delta_P)
                
                # 计算导数
                derivative = (f_plus - f_current) / delta_P
                
                # 防止导数过小
                if abs(derivative) < 1e-10:
                    derivative = 1e-10 if derivative >= 0 else -1e-10
                
                # Newton-Raphson步骤
                P_new = P - func / derivative
                
                # 确保P_new为正值
                if P_new <= 0:
                    P_new = P / 2  # 如果计算出负值，则取当前值的一半
                
                # 计算收敛误差
                rel_error = abs(P_new - P) / max(abs(P), 1e-10)
                abs_error = abs(P_new - P)
                
                # 记录迭代历史
                convergence_history.append((i, P, phi, rel_error))
                
                # 检查收敛性
                if rel_error < tol or abs_error < tol:
                    # 验证解的准确性
                    final_phi = self.pr_equation.calculate_fugacity_coefficient(P_new, T, Tc, Pc, omega, physical_accuracy, use_iterative)
                    final_f = final_phi * P_new
                    final_error = abs(final_f - fugacity) / fugacity
                    
                    # 如果误差仍然较大，继续迭代
                    if final_error < 10 * tol:
                        return P_new
                    # 否则继续迭代
                
                # 更新压力，使用自适应阻尼因子防止震荡
                if i > 0:
                    # 计算前后两次迭代的方向
                    current_direction = P_new - P
                    prev_direction = P - convergence_history[-2][1]
                    
                    if current_direction * prev_direction > 0:
                        # 方向一致，加大步长
                        damping = min(0.95, 0.7 + 0.1 * i / max_iter)
                    else:
                        # 方向改变，减小步长以防止震荡
                        damping = max(0.2, 0.5 - 0.05 * i / max_iter)
                        
                    # 应用阻尼因子
                    P = (1 - damping) * P + damping * P_new
                else:
                    # 第一次迭代使用较小的步长
                    P = 0.7 * P + 0.3 * P_new
                
            except Exception as e:
                # 如果当前迭代出错，尝试回退到前一个成功的点
                if convergence_history:
                    P = convergence_history[-1][1]
                    print(f"迭代过程中出错，回退到前一步: P = {P:.6e} Pa")
                    continue
                else:
                    raise ValueError(f"在迭代过程中出错 (迭代次数={i}): {str(e)}")
        
        # 如果未收敛，返回最佳估计并给出警告
        print(f"警告: 逸度转压强迭代未完全收敛，返回最佳估计值。相对误差={rel_error:.2e}")
        return P
    
    def estimate_pressure_from_fugacity(self, fugacity, T, Tc, Pc, omega, n_points=5):
        """
        使用插值法快速估计从逸度到压强的转换
        
        参数:
        fugacity (float): 逸度 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        n_points (int): 用于插值的点数
        
        返回:
        float: 估计的压力值 (Pa)，如果估计失败则返回-1
        """
        try:
            # 根据逸度范围设置合理的压力范围
            p_min = fugacity * 0.1
            p_max = fugacity * 10.0
            
            # 确保压力范围合理
            p_min = max(p_min, 1e-6)
            p_max = min(p_max, 1e15)
            
            # 在对数尺度上生成均匀分布的压力点
            log_p_min = math.log10(p_min)
            log_p_max = math.log10(p_max)
            step = (log_p_max - log_p_min) / (n_points - 1)
            
            pressures = []
            fugacities = []
            
            # 计算每个压力点对应的逸度
            for i in range(n_points):
                log_p = log_p_min + i * step
                P = 10 ** log_p
                
                try:
                    f = self.pressure_to_fugacity(P, T, Tc, Pc, omega)
                    pressures.append(P)
                    fugacities.append(f)
                except Exception:
                    continue
            
            # 检查是否有足够的点进行插值
            if len(pressures) < 3:
                return -1
            
            # 检查目标逸度是否在计算的逸度范围内
            if fugacity < min(fugacities) or fugacity > max(fugacities):
                # 尝试外推
                if abs(fugacity - min(fugacities)) < abs(fugacity - max(fugacities)):
                    # 接近最小值，使用最小的两个点外推
                    idx1, idx2 = 0, 1
                else:
                    # 接近最大值，使用最大的两个点外推
                    idx1, idx2 = -2, -1
                    
                # 对数线性外推
                log_f1 = math.log10(fugacities[idx1])
                log_f2 = math.log10(fugacities[idx2])
                log_p1 = math.log10(pressures[idx1])
                log_p2 = math.log10(pressures[idx2])
                log_f = math.log10(fugacity)
                
                # 线性插值
                log_p = log_p1 + (log_f - log_f1) * (log_p2 - log_p1) / (log_f2 - log_f1)
                return 10 ** log_p
            
            # 找到目标逸度所在的区间
            for i in range(len(fugacities) - 1):
                if (fugacities[i] <= fugacity <= fugacities[i+1]) or (fugacities[i] >= fugacity >= fugacities[i+1]):
                    # 在对数空间中进行线性插值
                    log_f1 = math.log10(fugacities[i])
                    log_f2 = math.log10(fugacities[i+1])
                    log_p1 = math.log10(pressures[i])
                    log_p2 = math.log10(pressures[i+1])
                    log_f = math.log10(fugacity)
                    
                    # 线性插值
                    log_p = log_p1 + (log_f - log_f1) * (log_p2 - log_p1) / (log_f2 - log_f1)
                    return 10 ** log_p
            
            return -1  # 未找到合适的插值区间
        except Exception as e:
            print(f"快速估计方法失败: {str(e)}")
            return -1
    
    def calculate_pressure_fugacity_points(self, p_min, p_max, T, Tc, Pc, omega, num_points=100, is_critical_check=False, physical_accuracy=False, use_iterative=False):
        """
        计算指定压力范围内的逸度、逸度系数和压缩因子
        
        参数:
        - p_min: 最小压力
        - p_max: 最大压力
        - T: 温度 (K)
        - Tc: 临界温度 (K)
        - Pc: 临界压力 (Pa)
        - omega: 偏心因子
        - num_points: 计算点数
        
        返回:
        - pressures: 压力数组
        - fugacities: 逸度数组
        - phis: 逸度系数数组
        - zs: 压缩因子数组
        """
        # 打印输入参数以便调试
        print(f"计算多点数据: p_min={p_min}, p_max={p_max}, T={T}, Tc={Tc}, Pc={Pc}, omega={omega}, num_points={num_points}")
        
        # 设置最小和最大因子（与临界压力相关）
        min_factor = 0.001  # 最小压力不低于临界压力的0.1%
        max_factor = 3.0    # 最大压力不超过临界压力的300%
        
        # 确保压力范围有效
        p_min = max(p_min, Pc * min_factor)
        p_max = min(p_max, Pc * max_factor)
        
        # 如果用户要求计算包含临界点附近的范围，确保临界压力被包含进来
        is_critical_range = is_critical_check and (p_min < Pc < p_max)
        
        # 使用集合存储压力点，避免重复
        pressure_set = set()
        
        # 根据临界状态判断是否需要在临界点附近增加密度
        if is_critical_range:
            # 对临界点附近增加计算密度
            # 在临界点附近区域使用更多的点
            pc_lower = max(p_min, Pc * 0.9)  # 临界压力下限（90%）
            pc_upper = min(p_max, Pc * 1.1)  # 临界压力上限（110%）
            
            # 确保最小值和临界点下限之间有足够的点
            if p_min < pc_lower:
                lower_points = max(int(num_points * 0.4), 10)  # 至少10个点
                lower_step = (pc_lower - p_min) / lower_points
                for i in range(lower_points):
                    pressure_set.add(p_min + i * lower_step)
            
            # 临界区域使用较多的点（40%）
            crit_points = max(int(num_points * 0.4), 20)  # 临界区域至少20个点
            crit_step = (pc_upper - pc_lower) / crit_points
            for i in range(crit_points + 1):
                pressure_set.add(pc_lower + i * crit_step)
            
            # 确保临界点上限和最大值之间有足够的点
            if pc_upper < p_max:
                upper_points = max(int(num_points * 0.2), 10)  # 至少10个点
                upper_step = (p_max - pc_upper) / upper_points
                for i in range(1, upper_points + 1):  # 从1开始避免与临界区域最后一个点重复
                    pressure_set.add(pc_upper + i * upper_step)
        else:
            # 常规均匀分布
            step = (p_max - p_min) / (num_points - 1)
            for i in range(num_points):
                pressure_set.add(p_min + i * step)
        
        # 确保包含最小和最大压力值
        pressure_set.add(p_min)
        pressure_set.add(p_max)
        
        # 如果临界压力在范围内，确保包含临界压力
        if is_critical_range:
            pressure_set.add(Pc)
        
        # 转换为有序列表并排序
        pressures = sorted(list(pressure_set))
        
        # 用于跟踪已计算的压力点，避免重复计算
        calculated_pressure_set = set()
        
        # 创建结果列表
        fugacities = []
        phis = []
        zs = []
        
        # 对每个压力点计算逸度、逸度系数和压缩因子
        for p in pressures:
            # 避免重复计算
            if p in calculated_pressure_set:
                continue
            
            # 标记为已计算
            calculated_pressure_set.add(p)
            
            # 计算逸度系数
            phi = self.pr_equation.calculate_fugacity_coefficient(
                p, T, Tc, Pc, omega, 
                physical_accuracy,
                use_iterative
            )
            
            # 计算逸度值
            fugacity = phi * p
            
            # 计算压缩因子
            z = self.pr_equation.calculate_Z(
                p, T, Tc, Pc, omega, 
                physical_accuracy,
                use_iterative
            )
            
            # 添加结果
            fugacities.append(fugacity)
            phis.append(phi)
            zs.append(z)
        
        # 检查计算的点数是否符合预期
        expected_points = len(pressures)
        actual_points = len(fugacities)
        if expected_points != actual_points:
            print(f"警告: 预期计算 {expected_points} 个点，实际计算了 {actual_points} 个点")
        
        # 检查是否有重复压力值
        pressure_check = set()
        duplicates = []
        for i, p in enumerate(pressures):
            if p in pressure_check:
                duplicates.append((i, p))
            pressure_check.add(p)
        
        if duplicates:
            print(f"警告: 发现 {len(duplicates)} 个重复压力值: {duplicates}")
        
        # 检查是否所有点都在用户指定的范围内
        if min(pressures) < p_min or max(pressures) > p_max:
            print(f"警告: 计算点超出指定范围 [{p_min}, {p_max}]: 实际范围 [{min(pressures)}, {max(pressures)}]")
        
        # 使用列表推导删除重复点，保留第一次出现的点
        if duplicates:
            unique_indices = []
            seen = set()
            for i, p in enumerate(pressures):
                if p not in seen:
                    seen.add(p)
                    unique_indices.append(i)
            
            pressures = [pressures[i] for i in unique_indices]
            fugacities = [fugacities[i] for i in unique_indices]
            phis = [phis[i] for i in unique_indices]
            zs = [zs[i] for i in unique_indices]
            
            print(f"删除重复点后，剩余 {len(pressures)} 个点")
        
        return pressures, fugacities, phis, zs
    
    def get_unit_choices(self):
        """
        获取所有可用的压力单位选项
        
        返回:
        list: 单位选项列表
        """
        return ["Pa", "MPa", "bar", "atm", "psi", "mmHg"]

def get_user_input():
    """获取用户输入的物性参数和计算条件"""
    print("\n" + "="*50)
    print("逸度与压强转换工具 (基于PR状态方程)")
    print("作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)")
    print("="*50)
    
    # 显示物质数据库中的可用物质
    print("\n可用的物质列表:")
    converter = FugacityPressureConverter()
    substances = converter.list_available_substances()
    
    # 分列显示物质列表
    columns = 3
    for i, substance in enumerate(substances):
        if i % columns == 0 and i > 0:
            print()
        print(f"{i+1}. {substance:15}", end=" ")
    print("\n")
    
    # 选择物质或输入自定义物质参数
    print("选择方式:")
    print("1. 从物质列表中选择")
    print("2. 手动输入物质参数")
    choice = input("请选择 (1/2) [默认:1]: ") or "1"
    
    if choice == "1":
        # 从列表选择
        valid_choice = False
        substance_index = -1
        while not valid_choice:
            try:
                substance_index = int(input(f"请输入物质编号 (1-{len(substances)}) [默认:1]: ") or "1") - 1
                if 0 <= substance_index < len(substances):
                    valid_choice = True
                else:
                    print(f"错误: 请输入1到{len(substances)}之间的数字")
            except ValueError:
                print("错误: 请输入有效的数字")
        
        substance = substances[substance_index]
        properties = converter.get_substance_properties(substance)
        Tc = properties["Tc"]
        Pc = properties["Pc"]
        omega = properties["omega"]
        
        print(f"\n已选择: {substance}")
        print(f"临界温度: {Tc} K")
        print(f"临界压力: {Pc/1e6:.6f} MPa")
        print(f"偏心因子: {omega:.6f}")
        
    else:
        # 手动输入
        print("\n请输入物质的物性参数:")
        substance = input("物质名称: ")
        
        # 获取临界温度
        valid_input = False
        while not valid_input:
            try:
                tc_input = input(f"临界温度 (K) [{DEFAULT_Tc}]: ")
                Tc = float(tc_input) if tc_input else DEFAULT_Tc
                if Tc <= 0:
                    print("错误: 临界温度必须为正值")
                else:
                    valid_input = True
            except ValueError:
                print("错误: 请输入有效的数字")
        
        # 获取临界压力
        valid_input = False
        while not valid_input:
            try:
                pc_unit = input("临界压力单位 (1=Pa, 2=MPa, 3=bar, 4=atm) [默认:2]: ") or "2"
                pc_input = input(f"临界压力 ({pc_unit}) [{DEFAULT_Pc/1e6 if pc_unit=='2' else DEFAULT_Pc}]: ")
                
                if pc_input:
                    pc_value = float(pc_input)
                    if pc_value <= 0:
                        print("错误: 临界压力必须为正值")
                        continue
                    
                    if pc_unit == "2":  # MPa
                        Pc = pc_value * 1e6
                    elif pc_unit == "3":  # bar
                        Pc = pc_value * BAR_TO_PA
                    elif pc_unit == "4":  # atm
                        Pc = pc_value * ATM_TO_PA
                    else:  # Pa
                        Pc = pc_value
                else:
                    Pc = DEFAULT_Pc
                
                valid_input = True
            except ValueError:
                print("错误: 请输入有效的数字")
        
        # 获取偏心因子
        valid_input = False
        while not valid_input:
            try:
                omega_input = input(f"偏心因子 [{DEFAULT_OMEGA}]: ")
                omega = float(omega_input) if omega_input else DEFAULT_OMEGA
                valid_input = True
            except ValueError:
                print("错误: 请输入有效的数字")
    
    print("\n请输入计算条件:")
    
    # 获取温度
    valid_input = False
    while not valid_input:
        try:
            t_input = input(f"温度 (K) [{DEFAULT_T}]: ")
            T = float(t_input) if t_input else DEFAULT_T
            if T <= 0:
                print("错误: 温度必须为正值")
            else:
                valid_input = True
        except ValueError:
            print("错误: 请输入有效的数字")
    
    # 获取压力
    valid_input = False
    while not valid_input:
        try:
            p_unit = input("压力单位 (1=Pa, 2=MPa, 3=bar, 4=atm, 5=psi, 6=mmHg) [默认:2]: ") or "2"
            p_input = input(f"压力 ({p_unit}) [{DEFAULT_P/1e6 if p_unit=='2' else DEFAULT_P}]: ")
            
            if p_input:
                p_value = float(p_input)
                if p_value <= 0:
                    print("错误: 压力必须为正值")
                    continue
                
                if p_unit == "2":  # MPa
                    P = p_value * 1e6
                elif p_unit == "3":  # bar
                    P = p_value * BAR_TO_PA
                elif p_unit == "4":  # atm
                    P = p_value * ATM_TO_PA
                elif p_unit == "5":  # psi
                    P = p_value * PSI_TO_PA
                elif p_unit == "6":  # mmHg
                    P = p_value * MMHG_TO_PA
                else:  # Pa
                    P = p_value
            else:
                P = DEFAULT_P
            
            valid_input = True
        except ValueError:
            print("错误: 请输入有效的数字")
    
    return substance, Tc, Pc, omega, T, P


def main_console():
    """命令行界面的主函数"""
    
    print("\n欢迎使用逸度与压强转换工具！")
    print("本工具基于Peng-Robinson状态方程计算实际流体的热力学性质。")
    print("作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)")
    print("对于零基础用户，只需按照提示操作或直接按回车使用默认值即可。")
    
    # 获取用户输入或使用默认值
    substance, Tc, Pc, omega, T, P = get_user_input()
    
    # 询问是否使用迭代法
    use_iterative = input("\n是否使用迭代法计算(提高临界点计算稳定性)? (y/n) [默认:n]: ").lower() == 'y'
    
    # 创建转换器实例
    converter = FugacityPressureConverter()
    
    print("\n计算结果:")
    print("="*50)
    
    print(f"物质: {substance}")
    print(f"临界温度: {Tc:.4f} K")
    print(f"临界压力: {Pc/1e6:.6f} MPa ({Pc:.2f} Pa)")
    print(f"偏心因子: {omega:.6f}")
    print("-"*50)
    
    try:
        # 计算逸度
        fugacity = converter.pressure_to_fugacity(P, T, Tc, Pc, omega, use_iterative=use_iterative)
        print(f"温度: {T:.4f} K")
        print(f"压力: {P/1e6:.10f} MPa ({P:.2f} Pa)")
        print(f"计算得到的逸度: {fugacity/1e6:.10f} MPa ({fugacity:.2f} Pa)")
    
        # 从逸度反向计算压力以验证
        P_calculated = converter.fugacity_to_pressure(fugacity, T, Tc, Pc, omega)
        print(f"从逸度反向计算得到的压力: {P_calculated/1e6:.10f} MPa")
    
        # 计算压缩因子和逸度系数
        Z = converter.pr_equation.calculate_Z(P, T, Tc, Pc, omega, use_iterative=use_iterative)
        phi = fugacity / P
        print(f"压缩因子 (Z): {Z:.10f}")
        print(f"逸度系数 (phi): {phi:.10f}")
        
        # 计算理想气体与实际气体的偏差
        ideal_gas_deviation = abs(Z - 1.0) * 100
        print(f"与理想气体的偏差: {ideal_gas_deviation:.4f}%")
    
        # 转换为常用单位
        print("\n其他单位下的结果:")
        print(f"压力: {converter.convert_pressure_units(P, 'Pa', 'bar'):.6f} bar")
        print(f"      {converter.convert_pressure_units(P, 'Pa', 'atm'):.6f} atm")
        print(f"      {converter.convert_pressure_units(P, 'Pa', 'psi'):.6f} psi")
        print(f"逸度: {converter.convert_pressure_units(fugacity, 'Pa', 'bar'):.6f} bar")
        print(f"      {converter.convert_pressure_units(fugacity, 'Pa', 'atm'):.6f} atm")
        print(f"      {converter.convert_pressure_units(fugacity, 'Pa', 'psi'):.6f} psi")
    
    except Exception as e:
        print(f"计算过程中出错: {str(e)}")
        print("请检查输入参数是否合理，或尝试使用不同的计算条件。")
    
    print("-"*50)
    
    # 询问是否显示详细的使用说明
    show_help = input("\n是否显示详细使用说明? (y/n) [默认:n]: ").lower() == 'y'
    if show_help:
        print("\n使用说明:")
        print("1. 逸度是用于描述非理想气体的热力学性质，与压力具有相同单位(Pa)")
        print("2. 逸度系数(phi)是逸度与压力之比，无量纲")
        print("3. 压缩因子(Z)用于描述气体的压缩性，理想气体Z=1")
        print("4. PR状态方程适用于大多数气体，但对于强极性分子或氢键较强的物质精度可能降低")
        print("5. 计算中使用的物性参数来源于热力学数据库，对于特殊物质可能需要查询更精确的数据")
        
    # 询问是否继续进行新的计算
    continue_calc = input("\n是否进行新的计算? (y/n) [默认:n]: ").lower() == 'y'
    if continue_calc:
        main_console()  # 递归调用
    else:
        print("\n感谢使用逸度与压强转换工具！作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)")
        print("祝您工作愉快！\n")

class FugacityVisualizer(tk.Tk):
    """
    逸度可视化工具的图形界面
    
    特性:
    1. 支持物质数据库选择
    2. 支持多种压力单位
    3. 实时更新计算结果
    4. 提供逸度-压力和逸度系数-压力曲线
    """
    
    def __init__(self):
        """初始化应用程序"""
        super().__init__()
        
        self.title("逸度-压力转换计算器")
        self.geometry("1200x800")
        self.minsize(1000, 700)
        
        # 设置图标
        try:
            import base64
            from io import BytesIO
            from PIL import Image, ImageTk
            
            # 图标数据（可以替换为自己的图标）
            icon_data = """
            iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAB
            B0lEQVR4nM2TsUrDUBSGv9Q0NcTSQsHFImRwEhcfwMVR8CGc3X0JZ5/Apsn9wcHZQRwUnNRBnHRp
            E5Eg2FI65CbNdalNrFoE/+1c7v2+c+45F/5arCoKrSh0FXRWFHNdqwPcG8a0F4bpBGEKnFrGtA2E
            iwucO47XXhgmQK8QrwPD42MXSFtRJMDZgth13azYvYk5JxB6nmc1PK+QQn++EzhF4TzW4k7mfEEA
            LuBFEXu2PfmQDCsK60GOjw9nCBz+BEFTEvyG4HIAl5dPDAbCcPjAaPTI3d0QkQkXF/eMx9e02/ek
            6YhW6wkRIU1vaTYHiDxnQPaLQu9A8uWdVw73ot3ZdV8B3DhKDJs5crAAAAAASUVORK5CYII=
            """
            
            # 将Base64编码的图标数据转换为图像
            icon_image = Image.open(BytesIO(base64.b64decode(icon_data)))
            self.icon = ImageTk.PhotoImage(icon_image)
            self.iconphoto(True, self.icon)
        except Exception as e:
            print(f"设置图标失败: {e}")
        
        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TButton", font=("SimSun", 10))
        self.style.configure("TLabel", font=("SimSun", 10))
        self.style.configure("TEntry", font=("SimSun", 10))
        self.style.configure("TCombobox", font=("SimSun", 10))
        self.style.configure("TCheckbutton", font=("SimSun", 10))
        self.style.configure("TFrame", background="#f0f0f0")
        
        # 创建颜色样式
        self.style.configure("Critical.TLabel", foreground="red")
        self.style.configure("Normal.TLabel", foreground="black")
        self.style.configure("Warning.TLabel", foreground="orange")
        self.style.configure("Info.TLabel", foreground="blue")
        
        # 设置应用程序变量
        self.setup_variables()
        
        # 创建数据库和单位转换器
        self.setup_database()
        self.converter = FugacityPressureConverter()
        
        # 创建图形用户界面组件
        self.create_widgets()
        
        # 在第一次运行时进行更新
        self.after(100, self.update_substance_info)
        
    def setup_variables(self):
        """初始化GUI中使用的所有变量"""
        # 物质信息变量
        self.substance_var = tk.StringVar()
        self.substance_info_var = tk.StringVar(value="选择物质以显示其信息")
        
        # 温度和压力变量
        self.t_var = tk.StringVar(value="298.15")  # 默认室温
        self.tc_var = tk.StringVar()
        self.pc_var = tk.StringVar()
        self.omega_var = tk.StringVar()
        
        # 单点压力及其单位
        self.p_var = tk.StringVar(value="1.0")
        self.p_single_unit_var = tk.StringVar(value="MPa")
        
        # 临界压力单位
        self.pc_unit_var = tk.StringVar(value="MPa")
        
        # 压力范围变量
        self.p_min_var = tk.StringVar(value="0.1")
        self.p_max_var = tk.StringVar(value="10.0")
        self.p_unit_var = tk.StringVar(value="MPa")
        
        # 计算结果变量
        self.fugacity_var = tk.StringVar()
        self.phi_var = tk.StringVar()
        self.z_var = tk.StringVar()
        self.ideal_deviation_var = tk.StringVar()
        
        # 临界状态判断变量
        self.critical_state_check = tk.BooleanVar(value=False)  # 默认关闭临界状态判断
        self.critical_state_result = tk.StringVar(value="")  # 临界状态判断结果
        self.critical_state_color = tk.StringVar(value="blue")  # 临界状态文本颜色
        self.critical_details = tk.StringVar(value="")  # 临界状态详细信息
        
        # 添加物理精确模式变量
        self.physical_accuracy_mode = tk.BooleanVar(value=False)  # 默认关闭物理精确模式(使用平滑处理)
        
        # 添加迭代法求解模式变量
        self.use_iterative_mode = tk.BooleanVar(value=False)  # 默认关闭迭代法求解模式(使用三次方程求解)
        
        # 存储单点计算结果
        self.single_point_results = {}
        
        # 存储多点计算结果
        self.calculation_results = None
        
    def create_widgets(self):
        """创建所有GUI组件并布局"""
        # 创建主框架
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧输入区域
        input_frame = ttk.Frame(main_frame, padding=5)
        input_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        
        # 物质选择区域
        substance_frame = ttk.LabelFrame(input_frame, text="物质选择", padding=5)
        substance_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(substance_frame, text="选择物质:").pack(anchor=tk.W, padx=5, pady=5)
        self.substance_combobox = ttk.Combobox(substance_frame, textvariable=self.substance_var, state="readonly")
        self.substance_combobox.pack(fill=tk.X, padx=5, pady=5)
        self.substance_combobox.bind("<<ComboboxSelected>>", lambda e: self.update_substance_info())
        
        # 显示物质信息
        self.substance_info_label = ttk.Label(substance_frame, textvariable=self.substance_info_var, 
                                         wraplength=300, justify=tk.LEFT)
        self.substance_info_label.pack(fill=tk.X, padx=5, pady=5)
        
        # 临界参数区域
        critical_frame = ttk.LabelFrame(input_frame, text="临界参数", padding=5)
        critical_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 临界温度
        tc_frame = ttk.Frame(critical_frame)
        tc_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(tc_frame, text="临界温度 (K):").pack(side=tk.LEFT, padx=5)
        ttk.Entry(tc_frame, textvariable=self.tc_var, width=10).pack(side=tk.LEFT, padx=5)
        
        # 临界压力
        pc_frame = ttk.Frame(critical_frame)
        pc_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(pc_frame, text="临界压力:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(pc_frame, textvariable=self.pc_var, width=10).pack(side=tk.LEFT, padx=5)
        self.pc_unit_combobox = ttk.Combobox(pc_frame, textvariable=self.pc_unit_var, 
                                        values=["Pa", "kPa", "MPa", "bar", "atm"], 
                                        width=5, state="readonly")
        self.pc_unit_combobox.pack(side=tk.LEFT, padx=5)
        self.pc_unit_combobox.current(2)  # 默认选择MPa
        
        # 偏心因子
        omega_frame = ttk.Frame(critical_frame)
        omega_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(omega_frame, text="偏心因子:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(omega_frame, textvariable=self.omega_var, width=10).pack(side=tk.LEFT, padx=5)
        
        # 计算条件区域
        condition_frame = ttk.LabelFrame(input_frame, text="计算条件", padding=5)
        condition_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 温度
        t_frame = ttk.Frame(condition_frame)
        t_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(t_frame, text="温度 (K):").pack(side=tk.LEFT, padx=5)
        ttk.Entry(t_frame, textvariable=self.t_var, width=10).pack(side=tk.LEFT, padx=5)
        
        # 单点压力
        p_frame = ttk.Frame(condition_frame)
        p_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(p_frame, text="压力:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(p_frame, textvariable=self.p_var, width=10).pack(side=tk.LEFT, padx=5)
        self.p_single_unit_combobox = ttk.Combobox(p_frame, textvariable=self.p_single_unit_var, 
                                             values=["Pa", "kPa", "MPa", "bar", "atm"], 
                                             width=5, state="readonly")
        self.p_single_unit_combobox.pack(side=tk.LEFT, padx=5)
        self.p_single_unit_combobox.current(2)  # 默认选择MPa
        
        # 压力范围
        p_range_frame = ttk.Frame(condition_frame)
        p_range_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Label(p_range_frame, text="压力范围:").pack(side=tk.LEFT, padx=5)
        ttk.Entry(p_range_frame, textvariable=self.p_min_var, width=8).pack(side=tk.LEFT, padx=5)
        ttk.Label(p_range_frame, text="~").pack(side=tk.LEFT)
        ttk.Entry(p_range_frame, textvariable=self.p_max_var, width=8).pack(side=tk.LEFT, padx=5)
        self.p_range_unit_combobox = ttk.Combobox(p_range_frame, textvariable=self.p_unit_var, 
                                            values=["Pa", "kPa", "MPa", "bar", "atm"], 
                                            width=5, state="readonly")
        self.p_range_unit_combobox.pack(side=tk.LEFT, padx=5)
        self.p_range_unit_combobox.current(2)  # 默认选择MPa
        
        # 高级选项区域
        advanced_frame = ttk.LabelFrame(input_frame, text="高级选项", padding=5)
        advanced_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 物理精确模式
        phys_mode_frame = ttk.Frame(advanced_frame)
        phys_mode_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Checkbutton(phys_mode_frame, text="启用物理精确模式", variable=self.physical_accuracy_mode, 
                       command=lambda: self.update_plots()).pack(side=tk.LEFT, padx=5)
        ttk.Button(phys_mode_frame, text="?", width=2, 
                  command=self.show_physical_mode_help).pack(side=tk.LEFT, padx=0)
        
        # 迭代法求解模式
        iter_mode_frame = ttk.Frame(advanced_frame)
        iter_mode_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Checkbutton(iter_mode_frame, text="使用迭代法求解", variable=self.use_iterative_mode, 
                       command=lambda: self.update_plots()).pack(side=tk.LEFT, padx=5)
        ttk.Button(iter_mode_frame, text="?", width=2, 
                  command=self.show_iterative_mode_help).pack(side=tk.LEFT, padx=0)
        
        # 添加临界状态判断复选框
        critical_check_frame = ttk.Frame(advanced_frame)
        critical_check_frame.pack(fill=tk.X, padx=5, pady=5)
        self.critical_checkbox = ttk.Checkbutton(critical_check_frame, text="启用临界状态判断", 
                                            variable=self.critical_state_check, 
                                            command=self.toggle_critical_state_check)
        self.critical_checkbox.pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(critical_check_frame, text="?", width=2, 
                  command=self.show_critical_state_help).pack(side=tk.LEFT, padx=0, pady=5)
        self.critical_text_label = ttk.Label(critical_check_frame, textvariable=self.critical_state_result,
                                       style="Normal.TLabel")
        self.critical_text_label.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
        
        # 添加临界状态详细信息区域
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
逸度与压强转换工具
作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)
功能: 基于PR状态方程进行流体的逸度与压强之间的转换，并提供可视化界面

概念说明:
- 逸度(Fugacity): 用于描述非理想气体的热力学性质，与压力具有相同单位(Pa)
- 逸度系数(Fugacity Coefficient): 逸度与压力之比，无量纲
- PR状态方程: Peng-Robinson状态方程，一种用于计算实际气体性质的状态方程

使用方法:
1. 如果有图形界面库(tkinter和matplotlib)，将自动启动图形界面
2. 如果缺少相关库，将使用命令行界面
3. 输入物质的临界参数(临界温度、临界压力、偏心因子)和计算条件(温度、压力)
4. 计算结果包括逸度、逸度系数和压缩因子

注意:
- 所有输入参数必须使用SI单位(温度K，压力Pa)
- 零基础用户可以直接使用默认值进行计算
- 计算结果的显示精度已优化为10位小数
"""

import math
from math import log, exp, sqrt
import sys
import os
import csv
import numpy as np
from datetime import datetime
from tkinter import filedialog
import base64
import time

# 尝试导入可视化相关的库
MATPLOTLIB_AVAILABLE = False
TKINTER_AVAILABLE = False

try:
    import tkinter as tk
    from tkinter import ttk, messagebox
    TKINTER_AVAILABLE = True
    
    try:
        import matplotlib
        matplotlib.use('TkAgg')
        import matplotlib.pyplot as plt
        from matplotlib.figure import Figure
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用黑体显示中文
        plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号
        MATPLOTLIB_AVAILABLE = True
    except ImportError:
        print("警告: 未找到matplotlib库，将使用命令行界面。")
        print("如果需要可视化功能，请安装matplotlib: pip install matplotlib")
except ImportError:
    print("警告: 未找到tkinter库，将使用命令行界面。")
    print("如果需要图形界面，请确保Python安装时包含了tkinter。")

print(sys.executable)  # 查看当前使用的Python解释器路径

# 用户需要输入的变量
# 示例：氮气 (N2) 物性参数 - 以下默认值来自于热力学数据库
DEFAULT_SUBSTANCE = "氮气 (N2)"
DEFAULT_Tc = 126.2  # 临界温度 (K)
DEFAULT_Pc = 3.4e6  # 临界压力 (Pa)
DEFAULT_OMEGA = 0.0372  # 偏心因子

# 默认计算条件 - 常温常压下
DEFAULT_T = 300  # 温度 (K) 约等于室温
DEFAULT_P = 1e6  # 压力 (Pa) = 1 MPa (约10个大气压)

# 精度控制参数 - 确保计算结果精确
CONVERGENCE_TOL = 1e-12  # 收敛容差，提高精度
MAX_ITERATIONS = 500     # 最大迭代次数，避免无限循环
ROOT_SELECTION_TOL = 1e-15  # 用于判断虚根的容差

# 可视化参数 - 控制图表范围
DEFAULT_P_MIN = 0.1e6  # 压力最小值 (Pa) = 0.1 MPa
DEFAULT_P_MAX = 10e6   # 压力最大值 (Pa) = 10 MPa
DEFAULT_POINTS = 200   # 曲线上的点数，增加点数提高曲线精度

# 添加常见物质的物性数据库
SUBSTANCE_DATABASE = {
    "氮气 (N2)": {"Tc": 126.2, "Pc": 3.4e6, "omega": 0.0372},
    "氧气 (O2)": {"Tc": 154.6, "Pc": 5.04e6, "omega": 0.0222},
    "二氧化碳 (CO2)": {"Tc": 304.1, "Pc": 7.38e6, "omega": 0.2236},
    "甲烷 (CH4)": {"Tc": 190.6, "Pc": 4.60e6, "omega": 0.0115},
    "乙烷 (C2H6)": {"Tc": 305.4, "Pc": 4.88e6, "omega": 0.0995},
    "乙烯 (C2H4)": {"Tc": 282.3, "Pc": 5.04e6, "omega": 0.0866},
    "丙烷 (C3H8)": {"Tc": 370.0, "Pc": 4.25e6, "omega": 0.1523},
    "氨 (NH3)": {"Tc": 405.6, "Pc": 11.3e6, "omega": 0.2526},
    "水 (H2O)": {"Tc": 647.1, "Pc": 22.06e6, "omega": 0.3443},
    "氦气 (He)": {"Tc": 5.2, "Pc": 0.23e6, "omega": -0.3900},
    "氩气 (Ar)": {"Tc": 150.9, "Pc": 4.90e6, "omega": 0.0000},
    "氢气 (H2)": {"Tc": 33.2, "Pc": 1.30e6, "omega": -0.2160},
}

# 单位转换常数
ATM_TO_PA = 101325.0  # 1 atm = 101325 Pa
BAR_TO_PA = 1e5       # 1 bar = 100000 Pa
MPA_TO_PA = 1e6       # 1 MPa = 1000000 Pa
PSI_TO_PA = 6894.76   # 1 psi = 6894.76 Pa
MMHG_TO_PA = 133.322  # 1 mmHg = 133.322 Pa

def solve_cubic_equation(a, b, c, d):
    """
    纯Python实现求解三次方程: ax^3 + bx^2 + cx + d = 0
    采用改进的Cardano方法，增强数值稳定性
    
    参数:
    a, b, c, d: 三次方程系数
    
    返回:
    list: 三个根的列表(可能包含复根)
    """
    # 处理特殊情况：系数为零
    if abs(a) < 1e-15:  # 如果a接近于0，退化为二次方程
        return solve_quadratic_equation(b, c, d)
    
    # 标准化为 x^3 + px^2 + qx + r = 0
    p = b / a
    q = c / a
    r = d / a
    
    # 消去二次项，代换 x = y - p/3
    p_over_3 = p / 3.0
    p_over_3_squared = p_over_3 * p_over_3
    
    # 计算新的系数 y^3 + ay + b = 0 的a和b
    a_new = q - 3.0 * p_over_3_squared
    b_new = r + 2.0 * p_over_3 * p_over_3_squared - p_over_3 * q
    
    # 计算判别式 delta = (b/2)^2 + (a/3)^3
    a_new_over_3 = a_new / 3.0
    b_new_over_2 = b_new / 2.0
    
    # 使用更安全的计算方式来避免精度问题
    discriminant = b_new_over_2**2 + a_new_over_3**3
    
    # 根据判别式的符号确定根的性质
    roots = []
    
    # 使用容差判断判别式是否接近于零
    if abs(discriminant) < 1e-10:  # delta ≈ 0，有重根
        if abs(a_new) < 1e-10:  # a ≈ 0，有三重根
            # 三重根的情况
            y = 0.0 if abs(b_new) < 1e-10 else -b_new**(1/3.0)
            roots = [complex(y - p_over_3, 0)] * 3
        else:  # 一个单根和一个二重根
            # 使用更稳定的公式计算
            y1 = -3.0 * b_new / a_new if abs(a_new) > 1e-10 else 0.0
            y2 = -y1 / 2.0
            roots = [complex(y1 - p_over_3, 0), complex(y2 - p_over_3, 0), complex(y2 - p_over_3, 0)]
    elif discriminant > 0:  # delta > 0，有一个实根和一对共轭复根
        # 使用更稳定的数值方法
        sqrt_delta = math.sqrt(discriminant)
        
        # 计算立方根，确保数值稳定性
        if b_new_over_2 >= 0:
            u_base = -b_new_over_2 + sqrt_delta
            v_base = -b_new_over_2 - sqrt_delta
        else:
            u_base = -b_new_over_2 + sqrt_delta
            v_base = -b_new_over_2 - sqrt_delta
        
        # 计算立方根，处理负数情况
        u = complex(u_base, 0) ** (1/3.0) if u_base >= 0 else -complex(-u_base, 0) ** (1/3.0)
        v = complex(v_base, 0) ** (1/3.0) if v_base >= 0 else -complex(-v_base, 0) ** (1/3.0)
        
        # 确保u*v + a_new/3 = 0
        if abs(u.real * v.real + a_new_over_3) > 1e-10:
            # 修正符号
            if u.real > 0:
                v = complex(-abs(v.real), v.imag)
            else:
                u = complex(-abs(u.real), u.imag)
        
        # 实根
        y1 = u.real + v.real
        
        # 复根
        real_part = -(u.real + v.real) / 2.0
        imag_part = math.sqrt(3.0) * (u.real - v.real) / 2.0
        
        roots = [
            complex(y1 - p_over_3, 0),
            complex(real_part - p_over_3, imag_part),
            complex(real_part - p_over_3, -imag_part)
        ]
    else:  # delta < 0，有三个不相等的实根
        # 使用三角函数法
        a_new_abs = abs(a_new)
        sqrt_a_new_abs = math.sqrt(a_new_abs)
        
        # 确保数值稳定性
        if abs(b_new) < 1e-15 or abs(sqrt_a_new_abs) < 1e-15:
            # 如果b_new接近0或sqrt_a_new_abs接近0，使用特殊处理
            roots = [complex(-p_over_3, 0)] * 3
        else:
            # 计算角度，确保在合理范围内
            cos_theta = max(min(-b_new / (2.0 * sqrt_a_new_abs * a_new_abs / sqrt_a_new_abs), 1.0), -1.0)
            theta = math.acos(cos_theta)
            
            # 计算三个实根
            factor = 2.0 * sqrt_a_new_abs / sqrt_a_new_abs
            y1 = factor * math.cos(theta / 3.0)
            y2 = factor * math.cos((theta + 2.0 * math.pi) / 3.0)
            y3 = factor * math.cos((theta + 4.0 * math.pi) / 3.0)
            
            if a_new < 0:  # 如果a < 0，需要添加负号
                y1, y2, y3 = -y1, -y2, -y3
                
            roots = [
                complex(y1 - p_over_3, 0),
                complex(y2 - p_over_3, 0),
                complex(y3 - p_over_3, 0)
            ]
    
    # 精确到足够小的虚部设为0
    for i in range(len(roots)):
        if isinstance(roots[i], complex) and abs(roots[i].imag) < 1e-10:
            roots[i] = complex(roots[i].real, 0)
    
    return roots


def solve_quadratic_equation(a, b, c):
    """
    纯Python实现求解二次方程: ax^2 + bx + c = 0
    使用改进的公式，增强数值稳定性
    
    参数:
    a, b, c: 二次方程系数
    
    返回:
    list: 两个根的列表和一个0(作为三次方程的第三个根)
    """
    if abs(a) < 1e-15:  # 如果a接近于0，退化为一次方程
        if abs(b) < 1e-15:  # 如果b也接近于0，那么方程没有变量，要么无解要么无穷解
            return [complex(0, 0), complex(0, 0), complex(0, 0)]
        else:
            # bx + c = 0 => x = -c/b
            return [complex(-c / b, 0), complex(0, 0), complex(0, 0)]
    
    # 计算判别式
    delta = b * b - 4 * a * c
    
    if abs(delta) < 1e-15:  # 判别式接近0，有两个相等的实根
        root = -b / (2 * a)
        return [complex(root, 0), complex(root, 0), complex(0, 0)]
    elif delta > 0:  # 有两个不同的实根
        # 使用更稳定的公式，避免精度损失
        if b >= 0:
            q = -0.5 * (b + math.sqrt(delta))
        else:
            q = -0.5 * (b - math.sqrt(delta))
        
        if abs(q) < 1e-15:  # 如果q接近于0，直接计算
            root1 = root2 = -b / (2 * a)
        else:
            root1 = q / a
            root2 = c / q
            
        return [complex(root1, 0), complex(root2, 0), complex(0, 0)]
    else:  # 有两个共轭复根
        real_part = -b / (2 * a)
        imag_part = math.sqrt(abs(delta)) / (2 * abs(a))
        if a < 0:  # 确保符号正确
            imag_part = -imag_part
        return [complex(real_part, imag_part), complex(real_part, -imag_part), complex(0, 0)]

class PRStateEquation:
    """
    Peng-Robinson状态方程的实现
    用于计算逸度系数和压强等热力学性质
    
    改进点：
    1. 使用更精确的参数常数
    2. 增强数值稳定性
    3. 优化计算逻辑
    """
    
    # 气体常数 (J/(mol·K))
    R = 8.3144598  # 更精确的气体常数值
    
    # PR状态方程参数常数
    OMEGA_A = 0.45723553
    OMEGA_B = 0.07779607
    
    def __init__(self):
        """初始化PR状态方程类"""
        pass
    
    def calculate_a_b(self, Tc, Pc, omega):
        """
        计算PR方程的参数a和b
        
        参数:
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        
        返回:
        tuple: (a, b) PR方程的参数
        """
        # 计算b参数 - 使用精确常数
        b = self.OMEGA_B * self.R * Tc / Pc
        
        # 计算a参数
        a = self.OMEGA_A * ((self.R * Tc)**2) / Pc
        
        return a, b
    
    def calculate_kappa(self, omega):
        """
        计算kappa参数，用于计算alpha
        
        参数:
        omega (float): 偏心因子
        
        返回:
        float: kappa值
        """
        # 使用改进的kappa计算公式
        if omega <= 0.491:
            return 0.37464 + 1.54226 * omega - 0.26992 * omega**2
        else:
            # 对于高偏心因子的物质使用另一个公式
            return 0.379642 + 1.48503 * omega - 0.164423 * omega**2 + 0.016666 * omega**3
    
    def calculate_alpha(self, T, Tc, omega):
        """
        计算温度相关的alpha参数
        
        参数:
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        omega (float): 偏心因子
        
        返回:
        float: alpha值
        """
        Tr = T / Tc  # 约化温度
        kappa = self.calculate_kappa(omega)
        
        # 确保数值稳定性
        sqrt_Tr = math.sqrt(max(Tr, 1e-10))
        term = 1.0 + kappa * (1.0 - sqrt_Tr)
        return term * term
    
    def calculate_a_T(self, T, Tc, Pc, omega):
        """
        计算给定温度下的a(T)参数
        
        参数:
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        
        返回:
        float: a(T)参数值
        """
        a, _ = self.calculate_a_b(Tc, Pc, omega)
        alpha = self.calculate_alpha(T, Tc, omega)
        return a * alpha
    
    def select_proper_root(self, roots, P, T, Tc, Pc, B, physical_accuracy=False):
        """
        从三次方程的根中选择热力学上最合理的根
        
        优化点:
        1. 避免相变区域的不连续选择
        2. 提供更平滑的曲线，去除峰值
        3. 增强临界点附近的稳定性
        4. 修复复数比较错误
        
        参数:
        roots (list): 三次方程的根
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        B (float): PR方程中的B参数
        physical_accuracy (bool): 是否使用物理精确模式，默认为False
        
        返回:
        float: 选择的根
        """
        # 筛选实根 (使用更严格的判断标准)
        real_roots = []
        for root in roots:
            # 避免复数比较错误，确保只选择实根
            if isinstance(root, complex) and abs(root.imag) < ROOT_SELECTION_TOL:
                real_roots.append(root.real)
            elif isinstance(root, (int, float)):
                real_roots.append(float(root))
        
        if not real_roots:
            # 如果没有找到足够小虚部的根，尝试选择虚部最小的根
            if len(roots) > 0:
                min_imag_root = min(roots, key=lambda r: abs(r.imag) if isinstance(r, complex) else 0)
                if isinstance(min_imag_root, complex):
                    return min_imag_root.real
                return float(min_imag_root)
            raise ValueError(f"没有找到实根，无法计算压缩因子Z。压力={P} Pa，温度={T} K")
        
        # 如果只有一个实根，直接返回
        if len(real_roots) == 1:
            return real_roots[0]
        
        # 计算约化温度和约化压力
        Tr = T / Tc
        Pr = P / Pc
        
        # 定义临界区域的范围 - 扩大区域以确保稳定性
        is_near_critical = abs(Tr - 1.0) < 0.2 and abs(Pr - 1.0) < 0.2
        
        # 物理精确模式: 使用经典的热力学根选择策略
        if physical_accuracy:
            # 在临界点附近时采用特殊处理以避免突变
            if is_near_critical:
                # 对于三个实根，选择中间根以确保平滑过渡
                if len(real_roots) >= 3:
                    real_roots.sort()
                    return real_roots[1]  # 返回中间的根
                else:  # 两个实根的情况
                    return sum(real_roots) / len(real_roots)  # 返回平均值
            
            # 对于明显超临界温度(T > Tc * 1.1)的情况，只有一个物理有意义的根
            if T > Tc * 1.1:
                # 超临界温度，通常选择最大的实根
                return max(real_roots)
            
            # 针对亚临界温度(T < Tc * 0.9)且与临界温度相差较大的情况
            if T < Tc * 0.9:
                real_roots.sort()  # 从小到大排序
                
                if len(real_roots) >= 3:
                    # 三个实根的情况
                    # 在两相区域，通过压力区分液相和气相
                    
                    if P > Pc * 1.2:  # 高压区域，液相
                        return real_roots[0]  # 液相，最小根
                    elif P < Pc * 0.8:  # 低压区域，气相
                        return real_roots[2]  # 气相，最大根
                    else:  # 临界压力附近
                        # 根据行为过渡选择
                        weight = (P - Pc * 0.8) / (Pc * 0.4)  # 0到1的权重
                        return real_roots[2] * (1 - weight) + real_roots[0] * weight
                else:  # 两个实根的情况
                    if P > Pc:
                        return min(real_roots)  # 倾向于液相
                    else:
                        return max(real_roots)  # 倾向于气相
            
            # 对于接近临界温度但不在临界点附近的情况(0.9*Tc < T < 1.1*Tc)
            # 这是最复杂的区域，需要平滑过渡
            real_roots.sort()  # 从小到大排序
            
            if len(real_roots) >= 3:
                # 以压力为基础，在临界线上平滑过渡
                if P > Pc * 1.2:  # 高压区域
                    return real_roots[0]  # 液相行为
                elif P < Pc * 0.8:  # 低压区域
                    return real_roots[2]  # 气相行为
                else:  # 临界压力附近
                    # 使用基于压力的加权平均确保平滑过渡
                    weight = (P - Pc * 0.8) / (Pc * 0.4)  # 0到1的权重
                    
                    # 如果温度接近临界温度，则更倾向于中间根以避免突变
                    temp_weight = min(1.0, abs(T - Tc) / (0.1 * Tc))
                    middle_weight = 1.0 - temp_weight
                    
                    # 加权平均：临界点附近倾向中间根，远离临界点则在最大根和最小根之间过渡
                    weighted_root = (real_roots[2] * (1 - weight) + real_roots[0] * weight) * temp_weight + real_roots[1] * middle_weight
                    return weighted_root
            else:  # 两个实根的情况
                # 在临界温度附近，使用加权平均以确保平滑
                weight = min(1.0, abs(Pr - 1.0))
                return (max(real_roots) * (1 - weight) + min(real_roots) * weight) * 0.5 + sum(real_roots) / len(real_roots) * 0.5
        
        # 默认平滑模式: 使用增强的平滑过渡策略
        else:
            # 在临界点附近时采用更精细的平滑过渡
            if is_near_critical:
                real_roots.sort()
                # 对于三个实根的情况，我们使用经过加权的中间值
                if len(real_roots) >= 3:
                    # 根据与临界点的距离计算权重
                    critical_distance = math.sqrt((Tr - 1.0)**2 + (Pr - 1.0)**2)
                    # 越接近临界点，越倾向于真正的中间根
                    middle_weight = max(0.6, 1.0 - critical_distance * 5)
                    edge_weight = (1.0 - middle_weight) / 2
                    
                    # 三个根的加权平均
                    return real_roots[0] * edge_weight + real_roots[1] * middle_weight + real_roots[2] * edge_weight
                else:  # 两个根的情况
                    # 直接使用平均值确保平滑
                    return sum(real_roots) / len(real_roots)
            
            # 检查是否在超临界区域(T > Tc)
            if T > Tc:
                # 超临界区域，通常选择最大的根
                # 但在接近临界温度时需要平滑过渡
                if T < Tc * 1.1:  # 接近临界温度
                    real_roots.sort()
                    # 根据温度计算权重
                    weight = (T - Tc) / (0.1 * Tc)  # 0到1
                    if len(real_roots) >= 3:
                        # 在临界温度附近，从中间根过渡到最大根
                        return real_roots[1] * (1 - weight) + real_roots[2] * weight
                    else:
                        # 两个根的情况，从平均值过渡到最大根
                        avg_root = sum(real_roots) / len(real_roots)
                        max_root = max(real_roots)
                        return avg_root * (1 - weight) + max_root * weight
                else:
                    # 明显超临界温度，选择最大的根
                    return max(real_roots)
            
            # 亚临界区域(T < Tc)
            if T < Tc:
                real_roots.sort()  # 从小到大排序
                
                # 全局压力过渡策略，更平滑
                # 无论有几个根，在整个压力范围内构建平滑过渡函数
                p_ratio = P / Pc
                
                if len(real_roots) >= 3:
                    # 三个根的情况
                    if p_ratio > 1.5:  # 高压区域
                        return real_roots[0]  # 液相，最小根
                    elif p_ratio < 0.5:  # 低压区域
                        return real_roots[2]  # 气相，最大根
                    else:
                        # 中间压力区域，使用改进的插值平滑过渡
                        # 计算更精细的权重函数，确保平滑过渡
                        weight = (p_ratio - 0.5) / 1.0  # 0到1
                        
                        # 在临界压力附近，更倾向于中间根以避免突变
                        if 0.9 < p_ratio < 1.1:
                            # 临界压力附近的加权策略
                            center_weight = 1.0 - min(1.0, abs(p_ratio - 1.0) * 10)
                            edge_weight = (1.0 - center_weight) / 2
                            
                            # 三点加权平均
                            return (real_roots[0] * weight * edge_weight + 
                                   real_roots[1] * center_weight + 
                                   real_roots[2] * (1 - weight) * edge_weight)
                        else:
                            # 非临界压力区域的简单线性插值
                            return real_roots[0] * weight + real_roots[2] * (1 - weight)
                else:
                    # 两个根的情况，基于压力比例进行平滑插值
                    min_root = min(real_roots)
                    max_root = max(real_roots)
                    
                    if p_ratio > 1.2:  # 高压倾向于最小根
                        return min_root
                    elif p_ratio < 0.8:  # 低压倾向于最大根
                        return max_root
                    else:  # 临界压力附近平滑过渡
                        weight = (p_ratio - 0.8) / 0.4  # 0到1
                        return max_root * (1 - weight) + min_root * weight
            
            # 其他任何情况下，使用基于压力的插值策略
            if Pr > 1.0:  # 高压
                real_roots.sort()
                # 高压区域倾向于选择较小的根
                if len(real_roots) >= 3:
                    # 三根情况，考虑与临界压力的距离
                    if Pr > 1.5:  # 远高于临界压力
                        return real_roots[0]
                    else:  # 接近临界压力
                        weight = (Pr - 1.0) / 0.5  # 0到1
                        return real_roots[1] * (1 - weight) + real_roots[0] * weight
                else:
                    # 两根情况
                    return min(real_roots)
            else:  # 低压
                real_roots.sort()
                # 低压区域倾向于选择较大的根
                if len(real_roots) >= 3:
                    if Pr < 0.5:  # 远低于临界压力
                        return real_roots[2]
                    else:  # 接近临界压力
                        weight = (1.0 - Pr) / 0.5  # 0到1
                        return real_roots[1] * (1 - weight) + real_roots[2] * weight
                else:
                    # 两根情况
                    return max(real_roots)
    
    def calculate_Z_iterative(self, P, T, Tc, Pc, omega):
        """
        使用迭代法计算压缩因子Z
        
        参数:
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        
        返回:
        float: 压缩因子Z
        """
        # 计算PR方程参数
        a_T = self.calculate_a_T(T, Tc, Pc, omega)
        _, b = self.calculate_a_b(Tc, Pc, omega)
        
        # 计算无量纲参数
        A = a_T * P / ((self.R * T)**2)
        B = b * P / (self.R * T)
        
        # 确定更好的初始值
        # 对于T>Tc的情况，用1.0作为初始值
        # 对于T<Tc的情况，使用不同的初始值
        if T > Tc:
            Z = 1.0
        else:
            if P > Pc:
                # 高压区域，可能更接近液相
                Z = B + 0.1
            else:
                # 低压区域，可能更接近气相
                Z = 1.0
        
        # 迭代参数
        max_iter = 100
        tol = 1e-8
        
        # 迭代求解
        for i in range(max_iter):
            # 避免除零问题
            if abs(Z) < 1e-10:
                Z = 1e-10
                
            h = B / Z
            
            # 防止除零和不稳定值
            if abs(1.0-h) < 1e-10 or abs(1.0+2.0*h-h*h) < 1e-10:
                break
                
            Z_new = 1.0/(1.0-h) - h/(1.0+2.0*h-h*h) * (A/B)
            
            # 检查收敛性
            if abs(Z_new - Z) < tol:
                return Z_new
                
            # 应用松弛因子以提高稳定性
            alpha = 0.7  # 松弛因子
            Z = alpha * Z_new + (1-alpha) * Z
        
        # 如果未收敛，返回最后的估计值
        return Z
    
    def calculate_Z(self, P, T, Tc, Pc, omega, physical_accuracy=False, use_iterative=False):
        """
        计算压缩因子Z
        
        参数:
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        physical_accuracy (bool): 是否使用物理精确模式，默认为False
        use_iterative (bool): 是否使用迭代法计算，默认为False
        
        返回:
        float: 压缩因子Z (选择热力学上最合理的根)
        """
        # 如果使用迭代法，直接调用迭代计算方法
        if use_iterative:
            return self.calculate_Z_iterative(P, T, Tc, Pc, omega)
            
        # 否则使用原有的三次方程求解法
        # 计算PR方程参数
        a_T = self.calculate_a_T(T, Tc, Pc, omega)
        _, b = self.calculate_a_b(Tc, Pc, omega)
        
        # 计算无量纲参数
        A = a_T * P / ((self.R * T)**2)
        B = b * P / (self.R * T)
        
        # 求解三次方程: Z^3 - (1-B)Z^2 + (A-3B^2-2B)Z - (AB-B^2-B^3) = 0
        # 转换为标准形式 a*Z^3 + b*Z^2 + c*Z + d = 0
        a = 1.0
        b = -(1.0 - B)
        c = (A - 3.0*B**2 - 2.0*B)
        d = -(A*B - B**2 - B**3)
        
        # 使用纯Python实现求解
        roots = solve_cubic_equation(a, b, c, d)
        
        # 使用改进的根选择方法，传入物理精确模式参数
        return self.select_proper_root(roots, P, T, Tc, Pc, B, physical_accuracy)
    
    def calculate_fugacity_coefficient(self, P, T, Tc, Pc, omega, physical_accuracy=False, use_iterative=False):
        """
        计算逸度系数phi
        
        参数:
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        physical_accuracy (bool): 是否使用物理精确模式，默认为False
        use_iterative (bool): 是否使用迭代法计算，默认为False
        
        返回:
        float: 逸度系数phi
        """
        # 确保输入参数有效
        if P <= 0 or T <= 0 or Tc <= 0 or Pc <= 0:
            raise ValueError("压力、温度、临界温度和临界压力必须为正值")
        
        # 计算PR方程参数
        a_T = self.calculate_a_T(T, Tc, Pc, omega)
        _, b = self.calculate_a_b(Tc, Pc, omega)
        
        # 计算无量纲参数
        A = a_T * P / ((self.R * T)**2)
        B = b * P / (self.R * T)
        
        # 计算压缩因子，传递物理精确模式参数和迭代法参数
        Z = self.calculate_Z(P, T, Tc, Pc, omega, physical_accuracy, use_iterative)
        
        # 检查Z是否为复数，如果是则取实部
        if isinstance(Z, complex):
            if abs(Z.imag) < 1e-10:
                Z = Z.real
            else:
                # 如果虚部较大，抛出异常
                raise ValueError(f"压缩因子Z包含较大的虚部: {Z}，无法进行有效计算")
        
        # 计算辅助参数
        sqrt_2 = math.sqrt(2.0)
        
        # 计算逸度系数，采用更稳定的数值方法
        # 避免Z-B接近0导致的数值不稳定
        Z_minus_B = max(Z - B, 1e-15)  # 提高精度，从1e-10提高到1e-15
        
        # 计算ln(phi)的三个项
        term1 = Z - 1.0
        
        # 使用高精度对数计算
        try:
            term2 = math.log(Z_minus_B)
        except (ValueError, OverflowError):
            # 如果Z_minus_B太小导致对数计算问题，使用级数展开近似
            # ln(x) ≈ (x-1) - (x-1)^2/2 + (x-1)^3/3 - ... for x close to 1
            x = Z_minus_B / (B + 1e-15)  # 缩放参数避免x过小
            term2 = math.log(B + 1e-15) + (x - 1) - (x - 1)**2/2 + (x - 1)**3/3
        
        # 计算第三项，避免数值不稳定
        if abs(B) < 1e-15:  # 提高精度，从1e-10提高到1e-15
            term3 = 0.0
        else:
            # 计算分母，避免接近0的情况
            denominator1 = max(Z + (1.0 + sqrt_2) * B, 1e-15)  # 提高精度
            denominator2 = max(Z + (1.0 - sqrt_2) * B, 1e-15)  # 提高精度
            
            # 避免对负数或零取对数
            if denominator1 <= 0 or denominator2 <= 0:
                # 当分母可能为负时，使用改进的近似公式
                # 从原先的简单近似改为更精确的计算
                d1 = Z + B
                d2 = Z - B
                term3 = A / (2.0 * sqrt_2 * B) * (2.0 * B / d1 - 2.0 * B**2 / (d1**2) + 2.0 * B**3 / (3.0 * d1**3))
            else:
                # 使用更精确的对数计算方法
                if abs(denominator1 / denominator2 - 1.0) < 1e-10:
                    # 当分子和分母非常接近时，使用泰勒展开近似对数
                    ratio = denominator1 / denominator2
                    log_term = (ratio - 1) - (ratio - 1)**2/2 + (ratio - 1)**3/3
                else:
                    log_term = math.log(denominator1 / denominator2)
                
                term3 = A / (2.0 * sqrt_2 * B) * log_term
        
        # 计算ln(phi)并返回phi
        ln_phi = term1 - term2 - term3
        
        # 使用指数函数计算phi
        try:
            return math.exp(ln_phi)
        except OverflowError:
            # 处理可能的指数溢出
            if ln_phi > 700:  # exp(709)是双精度浮点数的上限附近
                print(f"警告: 逸度系数计算中指数溢出 (ln_phi={ln_phi})，返回最大值")
                return float('inf')
            elif ln_phi < -700:
                print(f"警告: 逸度系数计算中指数下溢 (ln_phi={ln_phi})，返回0")
                return 0.0
            else:
                raise  # 其他原因导致的溢出，重新抛出异常

class FugacityPressureConverter:
    """
    逸度与压强转换工具
    基于PR状态方程计算逸度系数
    
    特性:
    1. 支持常见物质的物性参数查询
    2. 支持多种压力单位的转换
    3. 增强的数值稳定性和精确度
    4. 用户友好的错误处理
    """
    
    def __init__(self):
        """初始化转换工具"""
        self.pr_equation = PRStateEquation()
        self.substance_database = SUBSTANCE_DATABASE
    
    def get_substance_properties(self, substance_name):
        """
        获取物质的物性参数
        
        参数:
        substance_name (str): 物质名称
        
        返回:
        dict: 包含Tc, Pc, omega的字典，如果未找到则返回None
        """
        return self.substance_database.get(substance_name)
    
    def list_available_substances(self):
        """
        列出所有可用的物质
        
        返回:
        list: 物质名称列表
        """
        return list(self.substance_database.keys())
    
    def convert_pressure_units(self, pressure, from_unit="Pa", to_unit="Pa"):
        """
        转换压力单位
        
        参数:
        pressure (float): 压力值
        from_unit (str): 源单位 ("Pa", "atm", "bar", "MPa", "psi", "mmHg")
        to_unit (str): 目标单位 ("Pa", "atm", "bar", "MPa", "psi", "mmHg")
        
        返回:
        float: 转换后的压力值
        """
        # 先转换为Pa
        if from_unit == "Pa":
            pressure_pa = pressure
        elif from_unit == "atm":
            pressure_pa = pressure * ATM_TO_PA
        elif from_unit == "bar":
            pressure_pa = pressure * BAR_TO_PA
        elif from_unit == "MPa":
            pressure_pa = pressure * MPA_TO_PA
        elif from_unit == "psi":
            pressure_pa = pressure * PSI_TO_PA
        elif from_unit == "mmHg":
            pressure_pa = pressure * MMHG_TO_PA
        else:
            raise ValueError(f"不支持的单位：{from_unit}")
        
        # 从Pa转换为目标单位
        if to_unit == "Pa":
            return pressure_pa
        elif to_unit == "atm":
            return pressure_pa / ATM_TO_PA
        elif to_unit == "bar":
            return pressure_pa / BAR_TO_PA
        elif to_unit == "MPa":
            return pressure_pa / MPA_TO_PA
        elif to_unit == "psi":
            return pressure_pa / PSI_TO_PA
        elif to_unit == "mmHg":
            return pressure_pa / MMHG_TO_PA
        else:
            raise ValueError(f"不支持的单位：{to_unit}")
    
    def convert_pressure(self, pressure, from_unit="Pa", to_unit="Pa"):
        """
        转换压力单位 (作为convert_pressure_units的别名)
        
        参数:
        pressure (float): 压力值
        from_unit (str): 源单位 ("Pa", "atm", "bar", "MPa", "psi", "mmHg")
        to_unit (str): 目标单位 ("Pa", "atm", "bar", "MPa", "psi", "mmHg")
        
        返回:
        float: 转换后的压力值
        """
        return self.convert_pressure_units(pressure, from_unit, to_unit)
    
    def pressure_to_fugacity(self, P, T, Tc, Pc, omega, physical_accuracy=False, use_iterative=False):
        """
        从压强计算逸度
        
        参数:
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        physical_accuracy (bool): 是否使用物理精确模式，默认为False
        use_iterative (bool): 是否使用迭代法计算，默认为False
        
        返回:
        float: 逸度 (Pa)
        """
        # 输入验证
        if P <= 0:
            raise ValueError("压力必须为正值")
        if T <= 0:
            raise ValueError("温度必须为正值")
        if Tc <= 0:
            raise ValueError("临界温度必须为正值")
        if Pc <= 0:
            raise ValueError("临界压力必须为正值")
        
        try:
            phi = self.pr_equation.calculate_fugacity_coefficient(P, T, Tc, Pc, omega, physical_accuracy, use_iterative)
            fugacity = phi * P
            return fugacity
        except Exception as e:
            raise ValueError(f"计算逸度失败: {str(e)}")
    
    def fugacity_to_pressure(self, fugacity, T, Tc, Pc, omega, P_initial=None, tol=None, max_iter=None, physical_accuracy=False, use_iterative=False):
        """
        从逸度计算压强 (使用迭代求解)
        
        参数:
        fugacity (float): 逸度 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        P_initial (float, optional): 压力初值 (Pa)，默认为None，会自动设置为fugacity
        tol (float, optional): 收敛容差
        max_iter (int, optional): 最大迭代次数
        physical_accuracy (bool): 是否使用物理精确模式，默认为False
        use_iterative (bool): 是否使用迭代法计算，默认为False
        
        返回:
        float: 压力 (Pa)
        """
        # 输入验证
        if fugacity <= 0:
            raise ValueError("逸度必须为正值")
        if T <= 0:
            raise ValueError("温度必须为正值")
        if Tc <= 0:
            raise ValueError("临界温度必须为正值")
        if Pc <= 0:
            raise ValueError("临界压力必须为正值")
        
        # 使用全局定义的精度控制参数
        tol = tol if tol is not None else CONVERGENCE_TOL
        max_iter = max_iter if max_iter is not None else MAX_ITERATIONS
        
        # 尝试使用Newton-Raphson方法快速接近解
        # 这种方法可以显著加快收敛速度
        if P_initial is None:
            # 首先尝试使用快速近似方法估计初始值
            P_approx = self.estimate_pressure_from_fugacity(fugacity, T, Tc, Pc, omega)
            if P_approx > 0:
                P = P_approx
            else:
                P = fugacity  # 初始猜测：理想气体(phi=1时)
        else:
            P = P_initial
        
        # 防止除零问题
        if abs(P) < 1e-10:
            P = 1e-10
        
        # 记录迭代历史，用于分析收敛性
        convergence_history = []
        
        # Newton-Raphson迭代
        for i in range(max_iter):
            try:
                phi = self.pr_equation.calculate_fugacity_coefficient(P, T, Tc, Pc, omega, physical_accuracy, use_iterative)
                f_current = phi * P
                
                # 计算函数值(实际逸度与目标逸度的差)
                func = f_current - fugacity
                
                # 计算导数：df/dP = phi + P*dphi/dP
                # 使用数值微分近似导数
                delta_P = max(P * 1e-6, 1e-10)  # 扰动步长
                phi_plus = self.pr_equation.calculate_fugacity_coefficient(P + delta_P, T, Tc, Pc, omega, physical_accuracy, use_iterative)
                f_plus = phi_plus * (P + delta_P)
                
                # 计算导数
                derivative = (f_plus - f_current) / delta_P
                
                # 防止导数过小
                if abs(derivative) < 1e-10:
                    derivative = 1e-10 if derivative >= 0 else -1e-10
                
                # Newton-Raphson步骤
                P_new = P - func / derivative
                
                # 确保P_new为正值
                if P_new <= 0:
                    P_new = P / 2  # 如果计算出负值，则取当前值的一半
                
                # 计算收敛误差
                rel_error = abs(P_new - P) / max(abs(P), 1e-10)
                abs_error = abs(P_new - P)
                
                # 记录迭代历史
                convergence_history.append((i, P, phi, rel_error))
                
                # 检查收敛性
                if rel_error < tol or abs_error < tol:
                    # 验证解的准确性
                    final_phi = self.pr_equation.calculate_fugacity_coefficient(P_new, T, Tc, Pc, omega, physical_accuracy, use_iterative)
                    final_f = final_phi * P_new
                    final_error = abs(final_f - fugacity) / fugacity
                    
                    # 如果误差仍然较大，继续迭代
                    if final_error < 10 * tol:
                        return P_new
                    # 否则继续迭代
                
                # 更新压力，使用自适应阻尼因子防止震荡
                if i > 0:
                    # 计算前后两次迭代的方向
                    current_direction = P_new - P
                    prev_direction = P - convergence_history[-2][1]
                    
                    if current_direction * prev_direction > 0:
                        # 方向一致，加大步长
                        damping = min(0.95, 0.7 + 0.1 * i / max_iter)
                    else:
                        # 方向改变，减小步长以防止震荡
                        damping = max(0.2, 0.5 - 0.05 * i / max_iter)
                        
                    # 应用阻尼因子
                    P = (1 - damping) * P + damping * P_new
                else:
                    # 第一次迭代使用较小的步长
                    P = 0.7 * P + 0.3 * P_new
                
            except Exception as e:
                # 如果当前迭代出错，尝试回退到前一个成功的点
                if convergence_history:
                    P = convergence_history[-1][1]
                    print(f"迭代过程中出错，回退到前一步: P = {P:.6e} Pa")
                    continue
                else:
                    raise ValueError(f"在迭代过程中出错 (迭代次数={i}): {str(e)}")
        
        # 如果未收敛，返回最佳估计并给出警告
        print(f"警告: 逸度转压强迭代未完全收敛，返回最佳估计值。相对误差={rel_error:.2e}")
        return P
    
    def estimate_pressure_from_fugacity(self, fugacity, T, Tc, Pc, omega, n_points=5):
        """
        使用插值法快速估计从逸度到压强的转换
        
        参数:
        fugacity (float): 逸度 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        n_points (int): 用于插值的点数
        
        返回:
        float: 估计的压力值 (Pa)，如果估计失败则返回-1
        """
        try:
            # 根据逸度范围设置合理的压力范围
            p_min = fugacity * 0.1
            p_max = fugacity * 10.0
            
            # 确保压力范围合理
            p_min = max(p_min, 1e-6)
            p_max = min(p_max, 1e15)
            
            # 在对数尺度上生成均匀分布的压力点
            log_p_min = math.log10(p_min)
            log_p_max = math.log10(p_max)
            step = (log_p_max - log_p_min) / (n_points - 1)
            
            pressures = []
            fugacities = []
            
            # 计算每个压力点对应的逸度
            for i in range(n_points):
                log_p = log_p_min + i * step
                P = 10 ** log_p
                
                try:
                    f = self.pressure_to_fugacity(P, T, Tc, Pc, omega)
                    pressures.append(P)
                    fugacities.append(f)
                except Exception:
                    continue
            
            # 检查是否有足够的点进行插值
            if len(pressures) < 3:
                return -1
            
            # 检查目标逸度是否在计算的逸度范围内
            if fugacity < min(fugacities) or fugacity > max(fugacities):
                # 尝试外推
                if abs(fugacity - min(fugacities)) < abs(fugacity - max(fugacities)):
                    # 接近最小值，使用最小的两个点外推
                    idx1, idx2 = 0, 1
                else:
                    # 接近最大值，使用最大的两个点外推
                    idx1, idx2 = -2, -1
                    
                # 对数线性外推
                log_f1 = math.log10(fugacities[idx1])
                log_f2 = math.log10(fugacities[idx2])
                log_p1 = math.log10(pressures[idx1])
                log_p2 = math.log10(pressures[idx2])
                log_f = math.log10(fugacity)
                
                # 线性插值
                log_p = log_p1 + (log_f - log_f1) * (log_p2 - log_p1) / (log_f2 - log_f1)
                return 10 ** log_p
            
            # 找到目标逸度所在的区间
            for i in range(len(fugacities) - 1):
                if (fugacities[i] <= fugacity <= fugacities[i+1]) or (fugacities[i] >= fugacity >= fugacities[i+1]):
                    # 在对数空间中进行线性插值
                    log_f1 = math.log10(fugacities[i])
                    log_f2 = math.log10(fugacities[i+1])
                    log_p1 = math.log10(pressures[i])
                    log_p2 = math.log10(pressures[i+1])
                    log_f = math.log10(fugacity)
                    
                    # 线性插值
                    log_p = log_p1 + (log_f - log_f1) * (log_p2 - log_p1) / (log_f2 - log_f1)
                    return 10 ** log_p
            
            return -1  # 未找到合适的插值区间
        except Exception as e:
            print(f"快速估计方法失败: {str(e)}")
            return -1
    
    def calculate_pressure_fugacity_points(self, p_min, p_max, T, Tc, Pc, omega, num_points=100, is_critical_check=False, physical_accuracy=False, use_iterative=False):
        """
        计算指定压力范围内的逸度、逸度系数和压缩因子
        
        参数:
        - p_min: 最小压力
        - p_max: 最大压力
        - T: 温度 (K)
        - Tc: 临界温度 (K)
        - Pc: 临界压力 (Pa)
        - omega: 偏心因子
        - num_points: 计算点数
        
        返回:
        - pressures: 压力数组
        - fugacities: 逸度数组
        - phis: 逸度系数数组
        - zs: 压缩因子数组
        """
        # 打印输入参数以便调试
        print(f"计算多点数据: p_min={p_min}, p_max={p_max}, T={T}, Tc={Tc}, Pc={Pc}, omega={omega}, num_points={num_points}")
        
        # 设置最小和最大因子（与临界压力相关）
        min_factor = 0.001  # 最小压力不低于临界压力的0.1%
        max_factor = 3.0    # 最大压力不超过临界压力的300%
        
        # 确保压力范围有效
        p_min = max(p_min, Pc * min_factor)
        p_max = min(p_max, Pc * max_factor)
        
        # 如果用户要求计算包含临界点附近的范围，确保临界压力被包含进来
        is_critical_range = is_critical_check and (p_min < Pc < p_max)
        
        # 使用集合存储压力点，避免重复
        pressure_set = set()
        
        # 根据临界状态判断是否需要在临界点附近增加密度
        if is_critical_range:
            # 对临界点附近增加计算密度
            # 在临界点附近区域使用更多的点
            pc_lower = max(p_min, Pc * 0.9)  # 临界压力下限（90%）
            pc_upper = min(p_max, Pc * 1.1)  # 临界压力上限（110%）
            
            # 确保最小值和临界点下限之间有足够的点
            if p_min < pc_lower:
                lower_points = max(int(num_points * 0.4), 10)  # 至少10个点
                lower_step = (pc_lower - p_min) / lower_points
                for i in range(lower_points):
                    pressure_set.add(p_min + i * lower_step)
            
            # 临界区域使用较多的点（40%）
            crit_points = max(int(num_points * 0.4), 20)  # 临界区域至少20个点
            crit_step = (pc_upper - pc_lower) / crit_points
            for i in range(crit_points + 1):
                pressure_set.add(pc_lower + i * crit_step)
            
            # 确保临界点上限和最大值之间有足够的点
            if pc_upper < p_max:
                upper_points = max(int(num_points * 0.2), 10)  # 至少10个点
                upper_step = (p_max - pc_upper) / upper_points
                for i in range(1, upper_points + 1):  # 从1开始避免与临界区域最后一个点重复
                    pressure_set.add(pc_upper + i * upper_step)
        else:
            # 常规均匀分布
            step = (p_max - p_min) / (num_points - 1)
            for i in range(num_points):
                pressure_set.add(p_min + i * step)
        
        # 确保包含最小和最大压力值
        pressure_set.add(p_min)
        pressure_set.add(p_max)
        
        # 如果临界压力在范围内，确保包含临界压力
        if is_critical_range:
            pressure_set.add(Pc)
        
        # 转换为有序列表并排序
        pressures = sorted(list(pressure_set))
        
        # 用于跟踪已计算的压力点，避免重复计算
        calculated_pressure_set = set()
        
        # 创建结果列表
        fugacities = []
        phis = []
        zs = []
        
        # 对每个压力点计算逸度、逸度系数和压缩因子
        for p in pressures:
            # 避免重复计算
            if p in calculated_pressure_set:
                continue
            
            # 标记为已计算
            calculated_pressure_set.add(p)
            
            # 计算逸度系数
            phi = self.pr_equation.calculate_fugacity_coefficient(
                p, T, Tc, Pc, omega, 
                physical_accuracy,
                use_iterative
            )
            
            # 计算逸度值
            fugacity = phi * p
            
            # 计算压缩因子
            z = self.pr_equation.calculate_Z(
                p, T, Tc, Pc, omega, 
                physical_accuracy,
                use_iterative
            )
            
            # 添加结果
            fugacities.append(fugacity)
            phis.append(phi)
            zs.append(z)
        
        # 检查计算的点数是否符合预期
        expected_points = len(pressures)
        actual_points = len(fugacities)
        if expected_points != actual_points:
            print(f"警告: 预期计算 {expected_points} 个点，实际计算了 {actual_points} 个点")
        
        # 检查是否有重复压力值
        pressure_check = set()
        duplicates = []
        for i, p in enumerate(pressures):
            if p in pressure_check:
                duplicates.append((i, p))
            pressure_check.add(p)
        
        if duplicates:
            print(f"警告: 发现 {len(duplicates)} 个重复压力值: {duplicates}")
        
        # 检查是否所有点都在用户指定的范围内
        if min(pressures) < p_min or max(pressures) > p_max:
            print(f"警告: 计算点超出指定范围 [{p_min}, {p_max}]: 实际范围 [{min(pressures)}, {max(pressures)}]")
        
        # 使用列表推导删除重复点，保留第一次出现的点
        if duplicates:
            unique_indices = []
            seen = set()
            for i, p in enumerate(pressures):
                if p not in seen:
                    seen.add(p)
                    unique_indices.append(i)
            
            pressures = [pressures[i] for i in unique_indices]
            fugacities = [fugacities[i] for i in unique_indices]
            phis = [phis[i] for i in unique_indices]
            zs = [zs[i] for i in unique_indices]
            
            print(f"删除重复点后，剩余 {len(pressures)} 个点")
        
        return pressures, fugacities, phis, zs
    
    def get_unit_choices(self):
        """
        获取所有可用的压力单位选项
        
        返回:
        list: 单位选项列表
        """
        return ["Pa", "MPa", "bar", "atm", "psi", "mmHg"]

def get_user_input():
    """获取用户输入的物性参数和计算条件"""
    print("\n" + "="*50)
    print("逸度与压强转换工具 (基于PR状态方程)")
    print("作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)")
    print("="*50)
    
    # 显示物质数据库中的可用物质
    print("\n可用的物质列表:")
    converter = FugacityPressureConverter()
    substances = converter.list_available_substances()
    
    # 分列显示物质列表
    columns = 3
    for i, substance in enumerate(substances):
        if i % columns == 0 and i > 0:
            print()
        print(f"{i+1}. {substance:15}", end=" ")
    print("\n")
    
    # 选择物质或输入自定义物质参数
    print("选择方式:")
    print("1. 从物质列表中选择")
    print("2. 手动输入物质参数")
    choice = input("请选择 (1/2) [默认:1]: ") or "1"
    
    if choice == "1":
        # 从列表选择
        valid_choice = False
        substance_index = -1
        while not valid_choice:
            try:
                substance_index = int(input(f"请输入物质编号 (1-{len(substances)}) [默认:1]: ") or "1") - 1
                if 0 <= substance_index < len(substances):
                    valid_choice = True
                else:
                    print(f"错误: 请输入1到{len(substances)}之间的数字")
            except ValueError:
                print("错误: 请输入有效的数字")
        
        substance = substances[substance_index]
        properties = converter.get_substance_properties(substance)
        Tc = properties["Tc"]
        Pc = properties["Pc"]
        omega = properties["omega"]
        
        print(f"\n已选择: {substance}")
        print(f"临界温度: {Tc} K")
        print(f"临界压力: {Pc/1e6:.6f} MPa")
        print(f"偏心因子: {omega:.6f}")
        
    else:
        # 手动输入
        print("\n请输入物质的物性参数:")
        substance = input("物质名称: ")
        
        # 获取临界温度
        valid_input = False
        while not valid_input:
            try:
                tc_input = input(f"临界温度 (K) [{DEFAULT_Tc}]: ")
                Tc = float(tc_input) if tc_input else DEFAULT_Tc
                if Tc <= 0:
                    print("错误: 临界温度必须为正值")
                else:
                    valid_input = True
            except ValueError:
                print("错误: 请输入有效的数字")
        
        # 获取临界压力
        valid_input = False
        while not valid_input:
            try:
                pc_unit = input("临界压力单位 (1=Pa, 2=MPa, 3=bar, 4=atm) [默认:2]: ") or "2"
                pc_input = input(f"临界压力 ({pc_unit}) [{DEFAULT_Pc/1e6 if pc_unit=='2' else DEFAULT_Pc}]: ")
                
                if pc_input:
                    pc_value = float(pc_input)
                    if pc_value <= 0:
                        print("错误: 临界压力必须为正值")
                        continue
                    
                    if pc_unit == "2":  # MPa
                        Pc = pc_value * 1e6
                    elif pc_unit == "3":  # bar
                        Pc = pc_value * BAR_TO_PA
                    elif pc_unit == "4":  # atm
                        Pc = pc_value * ATM_TO_PA
                    else:  # Pa
                        Pc = pc_value
                else:
                    Pc = DEFAULT_Pc
                
                valid_input = True
            except ValueError:
                print("错误: 请输入有效的数字")
        
        # 获取偏心因子
        valid_input = False
        while not valid_input:
            try:
                omega_input = input(f"偏心因子 [{DEFAULT_OMEGA}]: ")
                omega = float(omega_input) if omega_input else DEFAULT_OMEGA
                valid_input = True
            except ValueError:
                print("错误: 请输入有效的数字")
    
    print("\n请输入计算条件:")
    
    # 获取温度
    valid_input = False
    while not valid_input:
        try:
            t_input = input(f"温度 (K) [{DEFAULT_T}]: ")
            T = float(t_input) if t_input else DEFAULT_T
            if T <= 0:
                print("错误: 温度必须为正值")
            else:
                valid_input = True
        except ValueError:
            print("错误: 请输入有效的数字")
    
    # 获取压力
    valid_input = False
    while not valid_input:
        try:
            p_unit = input("压力单位 (1=Pa, 2=MPa, 3=bar, 4=atm, 5=psi, 6=mmHg) [默认:2]: ") or "2"
            p_input = input(f"压力 ({p_unit}) [{DEFAULT_P/1e6 if p_unit=='2' else DEFAULT_P}]: ")
            
            if p_input:
                p_value = float(p_input)
                if p_value <= 0:
                    print("错误: 压力必须为正值")
                    continue
                
                if p_unit == "2":  # MPa
                    P = p_value * 1e6
                elif p_unit == "3":  # bar
                    P = p_value * BAR_TO_PA
                elif p_unit == "4":  # atm
                    P = p_value * ATM_TO_PA
                elif p_unit == "5":  # psi
                    P = p_value * PSI_TO_PA
                elif p_unit == "6":  # mmHg
                    P = p_value * MMHG_TO_PA
                else:  # Pa
                    P = p_value
            else:
                P = DEFAULT_P
            
            valid_input = True
        except ValueError:
            print("错误: 请输入有效的数字")
    
    return substance, Tc, Pc, omega, T, P


def main_console():
    """命令行界面的主函数"""
    
    print("\n欢迎使用逸度与压强转换工具！")
    print("本工具基于Peng-Robinson状态方程计算实际流体的热力学性质。")
    print("作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)")
    print("对于零基础用户，只需按照提示操作或直接按回车使用默认值即可。")
    
    # 获取用户输入或使用默认值
    substance, Tc, Pc, omega, T, P = get_user_input()
    
    # 询问是否使用迭代法
    use_iterative = input("\n是否使用迭代法计算(提高临界点计算稳定性)? (y/n) [默认:n]: ").lower() == 'y'
    
    # 创建转换器实例
    converter = FugacityPressureConverter()
    
    print("\n计算结果:")
    print("="*50)
    
    print(f"物质: {substance}")
    print(f"临界温度: {Tc:.4f} K")
    print(f"临界压力: {Pc/1e6:.6f} MPa ({Pc:.2f} Pa)")
    print(f"偏心因子: {omega:.6f}")
    print("-"*50)
    
    try:
        # 计算逸度
        fugacity = converter.pressure_to_fugacity(P, T, Tc, Pc, omega, use_iterative=use_iterative)
        print(f"温度: {T:.4f} K")
        print(f"压力: {P/1e6:.10f} MPa ({P:.2f} Pa)")
        print(f"计算得到的逸度: {fugacity/1e6:.10f} MPa ({fugacity:.2f} Pa)")
    
        # 从逸度反向计算压力以验证
        P_calculated = converter.fugacity_to_pressure(fugacity, T, Tc, Pc, omega)
        print(f"从逸度反向计算得到的压力: {P_calculated/1e6:.10f} MPa")
    
        # 计算压缩因子和逸度系数
        Z = converter.pr_equation.calculate_Z(P, T, Tc, Pc, omega, use_iterative=use_iterative)
        phi = fugacity / P
        print(f"压缩因子 (Z): {Z:.10f}")
        print(f"逸度系数 (phi): {phi:.10f}")
        
        # 计算理想气体与实际气体的偏差
        ideal_gas_deviation = abs(Z - 1.0) * 100
        print(f"与理想气体的偏差: {ideal_gas_deviation:.4f}%")
    
        # 转换为常用单位
        print("\n其他单位下的结果:")
        print(f"压力: {converter.convert_pressure_units(P, 'Pa', 'bar'):.6f} bar")
        print(f"      {converter.convert_pressure_units(P, 'Pa', 'atm'):.6f} atm")
        print(f"      {converter.convert_pressure_units(P, 'Pa', 'psi'):.6f} psi")
        print(f"逸度: {converter.convert_pressure_units(fugacity, 'Pa', 'bar'):.6f} bar")
        print(f"      {converter.convert_pressure_units(fugacity, 'Pa', 'atm'):.6f} atm")
        print(f"      {converter.convert_pressure_units(fugacity, 'Pa', 'psi'):.6f} psi")
    
    except Exception as e:
        print(f"计算过程中出错: {str(e)}")
        print("请检查输入参数是否合理，或尝试使用不同的计算条件。")
    
    print("-"*50)
    
    # 询问是否显示详细的使用说明
    show_help = input("\n是否显示详细使用说明? (y/n) [默认:n]: ").lower() == 'y'
    if show_help:
        print("\n使用说明:")
        print("1. 逸度是用于描述非理想气体的热力学性质，与压力具有相同单位(Pa)")
        print("2. 逸度系数(phi)是逸度与压力之比，无量纲")
        print("3. 压缩因子(Z)用于描述气体的压缩性，理想气体Z=1")
        print("4. PR状态方程适用于大多数气体，但对于强极性分子或氢键较强的物质精度可能降低")
        print("5. 计算中使用的物性参数来源于热力学数据库，对于特殊物质可能需要查询更精确的数据")
        
    # 询问是否继续进行新的计算
    continue_calc = input("\n是否进行新的计算? (y/n) [默认:n]: ").lower() == 'y'
    if continue_calc:
        main_console()  # 递归调用
    else:
        print("\n感谢使用逸度与压强转换工具！作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)")
        print("祝您工作愉快！\n")

class FugacityVisualizer(tk.Tk):
    """
    逸度可视化工具的图形界面
    
    特性:
    1. 支持物质数据库选择
    2. 支持多种压力单位
    3. 实时更新计算结果
    4. 提供逸度-压力和逸度系数-压力曲线
    """
    
    def __init__(self):
        # 确保没有其他tk根窗口
        if tk._default_root is not None:
            try:
                tk._default_root.destroy()
            except:
                pass
            tk._default_root = None
            
        super().__init__()
        self.title("逸度与压强转换可视化工具 - 作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)")
        self.geometry("1100x850")
        
        # 设置图标和背景
        self.configure(bg="#f0f0f0")
        
        # 创建转换器实例
        self.converter = FugacityPressureConverter()
        
        # 创建变量
        self.setup_variables()
        
        # 创建界面组件
        self.create_widgets()
        
        # 添加帮助提示信息
        self.create_tooltips()
        
        # 初始化物质参数显示
        self.initialize_substance_display()
        
        # 初始化图表
        self.update_plots()
        
        # 添加延迟检查，确保界面完全初始化后参数显示正确
        self.after(100, self.check_parameters_display)
    
    def setup_variables(self):
        """初始化GUI中使用的所有变量"""
        # 物质选择相关变量
        self.substance_selection = tk.StringVar(value="database")  # 默认使用数据库选择
        self.substance_var = tk.StringVar(value=DEFAULT_SUBSTANCE)  # 默认物质
        self.custom_substance_var = tk.StringVar(value="自定义物质")  # 自定义物质名称
        
        # 临界参数相关变量
        self.tc_var = tk.DoubleVar(value=DEFAULT_Tc)  # 临界温度
        self.pc_var = tk.DoubleVar(value=DEFAULT_Pc/1e6)  # 临界压力，默认显示单位为MPa
        
        # 偏心因子
        self.omega_var = tk.DoubleVar(value=DEFAULT_OMEGA)
        
        # 计算条件相关变量
        self.t_var = tk.DoubleVar(value=DEFAULT_T)  # 温度
        self.p_var = tk.DoubleVar(value=DEFAULT_P/1e6)  # 单点压力，默认显示单位为MPa
        self.p_single_unit_var = tk.StringVar(value="MPa")  # 单点压力单位
        
        # 压力范围变量
        self.p_min_var = tk.DoubleVar(value=DEFAULT_P_MIN/1e6)  # 最小压力，默认显示单位为MPa
        self.p_max_var = tk.DoubleVar(value=DEFAULT_P_MAX/1e6)  # 最大压力，默认显示单位为MPa
        self.p_unit_var = tk.StringVar(value="MPa")  # 压力范围单位
        self.points_var = tk.IntVar(value=DEFAULT_POINTS)  # 计算点数
        
        # 计算结果变量
        self.fugacity_var = tk.StringVar(value="")  # 逸度
        self.phi_var = tk.StringVar(value="")  # 逸度系数
        self.z_var = tk.StringVar(value="")  # 压缩因子
        self.deviation_var = tk.StringVar(value="")  # 理想气体偏差
        
        # 临界状态判断变量
        self.critical_state_check = tk.BooleanVar(value=False)  # 默认关闭临界状态判断
        self.critical_state_result = tk.StringVar(value="")  # 临界状态判断结果
        self.critical_state_color = tk.StringVar(value="blue")  # 临界状态文本颜色
        self.critical_details = tk.StringVar(value="")  # 临界状态详细信息
        
        # 添加物理精确模式变量
        self.physical_accuracy_mode = tk.BooleanVar(value=False)  # 默认关闭物理精确模式(使用平滑处理)
        
        # 添加迭代法求解模式变量
        self.use_iterative_mode = tk.BooleanVar(value=False)  # 默认关闭迭代法求解模式(使用三次方程求解)
    
    def create_widgets(self):
        """创建所有GUI组件并布局"""
        # 创建主框架
        main_frame = ttk.Frame(self, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建左侧输入面板
        input_frame = ttk.LabelFrame(main_frame, text="输入参数", padding=10)
        input_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=5, pady=5)
        
        # 创建右侧计算结果和图表面板
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建计算结果面板
        result_frame = ttk.LabelFrame(right_frame, text="计算结果", padding=10)
        result_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        # 创建图表面板
        plot_frame = ttk.LabelFrame(right_frame, text="逸度-压力关系图", padding=10)
        plot_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # ==================== 输入面板内容 ====================
        # 物质选择部分
        substance_frame = ttk.LabelFrame(input_frame, text="物质选择", padding=5)
        substance_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 物质选择模式
        ttk.Label(substance_frame, text="选择模式:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        mode_frame = ttk.Frame(substance_frame)
        mode_frame.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Radiobutton(mode_frame, text="数据库选择", variable=self.substance_selection, 
                        value="database", command=self.toggle_substance_mode).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(mode_frame, text="自定义参数", variable=self.substance_selection, 
                        value="custom", command=self.toggle_substance_mode).pack(side=tk.LEFT, padx=5)
        
        # 物质数据库选择下拉框
        ttk.Label(substance_frame, text="选择物质:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.substance_combobox = ttk.Combobox(substance_frame, textvariable=self.substance_var, 
                                              values=self.converter.list_available_substances(), state="readonly")
        self.substance_combobox.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        self.substance_combobox.bind("<<ComboboxSelected>>", self.on_substance_selected)
        
        # 自定义物质名称输入框
        ttk.Label(substance_frame, text="自定义名称:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.custom_substance_entry = ttk.Entry(substance_frame, textvariable=self.custom_substance_var, width=20)
        self.custom_substance_entry.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 物性参数部分
        properties_frame = ttk.LabelFrame(input_frame, text="物质物性参数", padding=5)
        properties_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 临界温度
        ttk.Label(properties_frame, text="临界温度 (K):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.tc_entry = ttk.Entry(properties_frame, textvariable=self.tc_var, width=15)
        self.tc_entry.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 临界压力及单位
        ttk.Label(properties_frame, text="临界压力:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        pc_frame = ttk.Frame(properties_frame)
        pc_frame.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        
        self.pc_entry = ttk.Entry(pc_frame, textvariable=self.pc_var, width=15)
        self.pc_entry.pack(side=tk.LEFT, padx=0)
        
        self.pc_unit_combobox = ttk.Combobox(pc_frame, values=["Pa", "MPa", "bar", "atm"], 
                                            width=6, state="readonly")
        self.pc_unit_combobox.set("MPa")
        self.pc_unit_combobox.pack(side=tk.LEFT, padx=5)
        self.pc_unit_combobox.bind("<<ComboboxSelected>>", self.on_pc_unit_changed)
        
        # 偏心因子
        ttk.Label(properties_frame, text="偏心因子 (ω):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.omega_entry = ttk.Entry(properties_frame, textvariable=self.omega_var, width=15)
        self.omega_entry.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 计算条件部分
        conditions_frame = ttk.LabelFrame(input_frame, text="计算条件", padding=5)
        conditions_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 温度
        ttk.Label(conditions_frame, text="温度 (K):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(conditions_frame, textvariable=self.t_var, width=15).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 单点压力及单位
        ttk.Label(conditions_frame, text="计算压力:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        p_frame = ttk.Frame(conditions_frame)
        p_frame.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        
        ttk.Entry(p_frame, textvariable=self.p_var, width=15).pack(side=tk.LEFT, padx=0)
        
        p_unit_combobox = ttk.Combobox(p_frame, textvariable=self.p_single_unit_var, 
                                       values=self.converter.get_unit_choices(), width=6, state="readonly")
        p_unit_combobox.pack(side=tk.LEFT, padx=5)
        
        # 压力范围及点数
        range_frame = ttk.LabelFrame(conditions_frame, text="压力范围(用于图表)", padding=5)
        range_frame.grid(row=2, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)
        
        ttk.Label(range_frame, text="最小压力:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(range_frame, textvariable=self.p_min_var, width=10).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        
        ttk.Label(range_frame, text="最大压力:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(range_frame, textvariable=self.p_max_var, width=10).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        
        ttk.Label(range_frame, text="单位:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        range_unit_combobox = ttk.Combobox(range_frame, textvariable=self.p_unit_var, 
                                          values=self.converter.get_unit_choices(), width=6, state="readonly")
        range_unit_combobox.grid(row=0, column=3, rowspan=2, sticky=tk.NS, padx=5, pady=2)
        
        ttk.Label(range_frame, text="点数:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(range_frame, textvariable=self.points_var, width=10).grid(row=2, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 添加临界状态判断复选框
        critical_check_frame = ttk.Frame(input_frame)
        critical_check_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Checkbutton(critical_check_frame, text="启用临界状态判断", variable=self.critical_state_check, 
                       command=self.toggle_critical_state_check).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(critical_check_frame, text="?", width=2, 
                  command=self.show_critical_state_help).pack(side=tk.LEFT, padx=0, pady=5)
        self.critical_text_label = ttk.Label(critical_check_frame, textvariable=self.critical_state_result)
        self.critical_text_label.pack(side=tk.LEFT, padx=5, pady=5, fill=tk.X, expand=True)
        
        # 添加临界状态详细信息区域
        self.critical_details_frame = ttk.LabelFrame(input_frame, text="临界状态详细信息", padding=5)
        self.critical_details_text = tk.Text(self.critical_details_frame, wrap=tk.WORD, height=4, width=40, font=("SimSun", 9))
        self.critical_details_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.critical_details_text.config(state=tk.DISABLED)  # 初始设置为只读
        
        # 默认隐藏临界状态详细信息区域
        if not self.critical_state_check.get():
            self.critical_details_frame.pack_forget()
        else:
            self.critical_details_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 添加物理精确模式复选框
        physical_mode_frame = ttk.Frame(input_frame)
        physical_mode_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Checkbutton(physical_mode_frame, text="物理精确模式", variable=self.physical_accuracy_mode, 
                       command=self.toggle_physical_accuracy_mode).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(physical_mode_frame, text="?", width=2, 
                  command=self.show_physical_mode_help).pack(side=tk.LEFT, padx=0, pady=5)
        ttk.Label(physical_mode_frame, text="(开启后显示相变区域峰值)").pack(side=tk.LEFT, padx=5, pady=5)
        
        # 添加迭代法求解模式复选框
        iterative_mode_frame = ttk.Frame(input_frame)
        iterative_mode_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Checkbutton(iterative_mode_frame, text="使用迭代法求解", variable=self.use_iterative_mode, 
                      command=self.toggle_iterative_mode).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(iterative_mode_frame, text="?", width=2, 
                 command=self.show_iterative_mode_help).pack(side=tk.LEFT, padx=0, pady=5)
        ttk.Label(iterative_mode_frame, text="(提高临界点附近计算稳定性)").pack(side=tk.LEFT, padx=5, pady=5)
        
        # 计算按钮
        ttk.Button(input_frame, text="计算并更新图表", command=self.update_plots).pack(fill=tk.X, padx=5, pady=10)
        
        # 导出数据按钮
        export_frame = ttk.Frame(input_frame)
        export_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(export_frame, text="导出数据", command=lambda: self.export_data("csv")).pack(side=tk.LEFT, padx=5, pady=5, expand=True, fill=tk.X)
        ttk.Button(export_frame, text="导出HTML报告", command=lambda: self.export_data("html")).pack(side=tk.RIGHT, padx=5, pady=5, expand=True, fill=tk.X)
        
        # 帮助和关于按钮
        buttons_frame = ttk.Frame(input_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="使用帮助", command=self.show_help).pack(side=tk.LEFT, padx=5, pady=5, expand=True, fill=tk.X)
        ttk.Button(buttons_frame, text="关于", command=self.show_about).pack(side=tk.RIGHT, padx=5, pady=5, expand=True, fill=tk.X)
        
        # 添加计算原理按钮
        ttk.Button(input_frame, text="计算原理与过程", command=self.show_calculation_theory).pack(fill=tk.X, padx=5, pady=5)
        
        # ==================== 结果面板内容 ====================
        # 创建结果显示网格
        result_grid = ttk.Frame(result_frame)
        result_grid.pack(fill=tk.BOTH, padx=5, pady=5)
        
        # 逸度
        ttk.Label(result_grid, text="逸度:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(result_grid, textvariable=self.fugacity_var, width=30, relief='sunken', anchor=tk.CENTER).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 逸度系数
        ttk.Label(result_grid, text="逸度系数:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(result_grid, textvariable=self.phi_var, width=30, relief='sunken', anchor=tk.CENTER).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 压缩因子
        ttk.Label(result_grid, text="压缩因子:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Label(result_grid, textvariable=self.z_var, width=30, relief='sunken', anchor=tk.CENTER).grid(row=0, column=3, sticky=tk.EW, padx=5, pady=2)
        
        # 理想气体偏差
        ttk.Label(result_grid, text="理想气体偏差:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Label(result_grid, textvariable=self.deviation_var, width=30, relief='sunken', anchor=tk.CENTER).grid(row=1, column=3, sticky=tk.EW, padx=5, pady=2)
        
        # 添加临界状态结果显示行
        ttk.Label(result_grid, text="临界状态:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.critical_label = ttk.Label(result_grid, textvariable=self.critical_state_result, 
                              width=80, relief='sunken', anchor=tk.CENTER)
        self.critical_label.grid(row=2, column=1, columnspan=3, sticky=tk.EW, padx=5, pady=2)
        
        # ==================== 图表面板内容 ====================
        # 创建图表
        self.fig = Figure(figsize=(10, 8), dpi=100)
        self.ax1 = self.fig.add_subplot(211)  # 上半部分：压力-逸度关系
        self.ax2 = self.fig.add_subplot(212)  # 下半部分：压力-逸度系数关系
        
        # 添加画布
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
    def export_data(self, format_type):
        """导出计算数据到CSV或HTML文件"""
        # 检查是否有计算结果
        if not hasattr(self, 'display_results') or not self.display_results:
            messagebox.showerror("导出错误", "没有可导出的数据，请先进行计算。")
            return
        
        try:
            # 获取当前时间作为文件名的一部分
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 获取物质名称，考虑自定义模式
            if self.substance_selection.get() == "database":
                substance = self.substance_var.get().replace(" ", "_").replace("(", "").replace(")", "")
            else:
                substance = self.custom_substance_var.get().replace(" ", "_").replace("(", "").replace(")", "")
            
            if format_type == "csv":
                # 让用户选择保存位置
                file_path = filedialog.asksaveasfilename(
                    defaultextension=".csv",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                    initialfile=f"逸度计算_{substance}_{timestamp}.csv"
                )
                
                if not file_path:  # 用户取消了对话框
                    return
                
                # 导出到CSV
                self.export_to_csv(file_path)
                
            elif format_type == "html":
                # 让用户选择保存位置
                file_path = filedialog.asksaveasfilename(
                    defaultextension=".html",
                    filetypes=[("HTML files", "*.html"), ("All files", "*.*")],
                    initialfile=f"逸度计算_{substance}_{timestamp}.html"
                )
                
                if not file_path:  # 用户取消了对话框
                    return
                
                # 直接导出为HTML
                self.export_to_html(file_path)
            
            # 导出成功后显示消息
            messagebox.showinfo("导出成功", f"数据已成功导出到:\n{file_path}")
            
        except Exception as e:
            messagebox.showerror("导出错误", f"导出数据时出错:\n{str(e)}\n调试信息: {type(e).__name__}")
    
    def export_to_html(self, file_path):
        """导出数据到HTML文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as html_file:
                # 写入HTML头部
                html_file.write('<!DOCTYPE html>\n<html>\n<head>\n')
                html_file.write('<meta charset="utf-8">\n')
                html_file.write('<title>逸度计算数据</title>\n')
                html_file.write('<style>\n')
                html_file.write('body { font-family: SimSun, Arial, sans-serif; margin: 20px; }\n')
                html_file.write('table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }\n')
                html_file.write('th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n')
                html_file.write('th { background-color: #f2f2f2; }\n')
                html_file.write('tr:nth-child(even) { background-color: #f9f9f9; }\n')
                html_file.write('h1 { color: #333; text-align: center; }\n')
                html_file.write('h2 { color: #333; margin-top: 30px; }\n')
                html_file.write('.container { max-width: 1200px; margin: 0 auto; }\n')
                html_file.write('</style>\n')
                html_file.write('</head>\n<body>\n')
                html_file.write('<div class="container">\n')
                
                # 标题和信息
                html_file.write(f'<h1>逸度计算数据报告</h1>\n')
                html_file.write(f'<p>生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>\n')
                
                # 单点计算结果表格
                html_file.write('<h2>单点计算结果</h2>\n')
                html_file.write('<table>\n')
                html_file.write('<tr><th>参数</th><th>数值</th></tr>\n')
                
                for key, value in self.single_point_results.items():
                    html_file.write(f'<tr><td>{key}</td><td>{value}</td></tr>\n')
                
                html_file.write('</table>\n')
                
                # 添加压力范围信息
                html_file.write('<h2>压力范围信息</h2>\n')
                html_file.write('<table>\n')
                html_file.write('<tr><th>参数</th><th>数值</th></tr>\n')
                
                p_min_value = self.p_min_var.get()
                p_max_value = self.p_max_var.get()
                p_unit = self.p_unit_var.get()
                html_file.write(f'<tr><td>用户指定压力范围</td><td>{p_min_value} - {p_max_value} {p_unit}</td></tr>\n')
                
                # 从display_results获取实际计算范围
                if hasattr(self, 'display_results') and self.display_results:
                    pressure_col = next((col for col in self.display_results.keys() if '压力' in col), None)
                    if pressure_col and self.display_results[pressure_col]:
                        min_calculated = min(self.display_results[pressure_col])
                        max_calculated = max(self.display_results[pressure_col])
                        html_file.write(f'<tr><td>实际计算压力范围</td><td>{min_calculated} - {max_calculated} {p_unit}</td></tr>\n')
                
                html_file.write('</table>\n')
                
                # 曲线数据表格
                html_file.write('<h2>曲线数据</h2>\n')
                html_file.write('<table>\n')
                
                # 表头
                headers = list(self.display_results.keys())
                html_file.write('<tr>\n')
                for header in headers:
                    html_file.write(f'<th>{header}</th>\n')
                html_file.write('</tr>\n')
                
                # 获取最大行数
                max_rows = max(len(self.display_results[header]) for header in headers)
                
                # 写入数据行
                for i in range(max_rows):
                    html_file.write('<tr>\n')
                    for header in headers:
                        try:
                            value = self.display_results[header][i]
                            # 格式化数值
                            if isinstance(value, float):
                                value = f"{value:.6f}"
                            html_file.write(f'<td>{value}</td>\n')
                        except IndexError:
                            html_file.write('<td></td>\n')  # 超出范围则写入空单元格
                    html_file.write('</tr>\n')
                
                html_file.write('</table>\n')
                
                # 添加页脚
                html_file.write('<p style="text-align: center; margin-top: 30px; color: #666;">')
                html_file.write('逸度与压强转换工具 - 作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)')
                html_file.write('</p>\n')
                
                html_file.write('</div>\n')
                html_file.write('</body>\n</html>')
            
            # 使用默认浏览器打开HTML文件
            os.startfile(file_path)
            
        except Exception as e:
            messagebox.showerror("导出错误", f"HTML导出失败: {str(e)}")
            # 如果HTML导出失败，尝试导出为CSV作为备份
            csv_path = os.path.splitext(file_path)[0] + '.csv'
            self.export_to_csv(csv_path)
            messagebox.showinfo("已改用CSV格式", 
                               f"由于HTML导出失败，数据已改为CSV格式保存至：\n{csv_path}")
    
    def export_to_csv(self, file_path):
        """导出数据到CSV文件"""
        # 使用UTF-8-SIG编码确保Excel可以正确识别中文
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入单点计算结果
            writer.writerow(["单点计算结果"])
            for key, value in self.single_point_results.items():
                writer.writerow([key, value])
            
            writer.writerow([])  # 空行分隔
            
            # 写入压力范围信息
            writer.writerow(["压力范围信息"])
            p_min_value = self.p_min_var.get()
            p_max_value = self.p_max_var.get()
            p_unit = self.p_unit_var.get()
            writer.writerow(["用户指定压力范围", f"{p_min_value} - {p_max_value} {p_unit}"])
            
            writer.writerow([])  # 空行分隔
            
            # 写入曲线数据
            writer.writerow(["曲线数据"])
            # 获取所有列名
            headers = list(self.display_results.keys())
            writer.writerow(headers)
            
            # 转置数据以便按行写入
            rows = zip(*[self.display_results[header] for header in headers])
            for row in rows:
                writer.writerow(row)
    
    def create_tooltips(self):
        """为各个界面组件添加提示信息"""
        # 这里只实现基本的提示功能，完整实现会需要一个ToolTip类
        # 物质选择提示
        self.substance_combobox.bind("<Enter>", lambda e: self.show_tooltip(e, "从预定义的物质数据库中选择物质"))
        self.substance_combobox.bind("<Leave>", lambda e: self.hide_tooltip())
        
        # 临界参数提示
        self.tc_entry.bind("<Enter>", lambda e: self.show_tooltip(e, "临界温度：物质的临界点温度，单位为K"))
        self.tc_entry.bind("<Leave>", lambda e: self.hide_tooltip())
        
        self.pc_entry.bind("<Enter>", lambda e: self.show_tooltip(e, "临界压力：物质的临界点压力，可选择不同单位"))
        self.pc_entry.bind("<Leave>", lambda e: self.hide_tooltip())
        
        self.omega_entry.bind("<Enter>", lambda e: self.show_tooltip(e, "偏心因子：表征分子非球形程度的参数，无量纲"))
        self.omega_entry.bind("<Leave>", lambda e: self.hide_tooltip())
    
    def show_tooltip(self, event, text):
        """显示工具提示"""
        x, y, _, _ = event.widget.bbox("insert")
        x += event.widget.winfo_rootx() + 25
        y += event.widget.winfo_rooty() + 25
        
        # 创建一个提示窗口
        self.tooltip = tk.Toplevel(event.widget)
        self.tooltip.wm_overrideredirect(True)  # 移除窗口边框
        self.tooltip.wm_geometry(f"+{x}+{y}")
        
        label = ttk.Label(self.tooltip, text=text, background="#ffffe0", relief="solid", borderwidth=1, padding=2)
        label.pack()
    
    def hide_tooltip(self):
        """隐藏工具提示"""
        if hasattr(self, "tooltip"):
            self.tooltip.destroy()
            self.tooltip = None
    
    def initialize_substance_display(self):
        """初始化物质参数显示，确保界面显示正确的物性参数"""
        # 默认选择第一个物质
        if not self.substance_var.get():
            self.substance_var.set(DEFAULT_SUBSTANCE)
        
        # 设置组件状态，确保后续更新物性参数时组件状态正确
        mode = self.substance_selection.get()
        if mode == "database":
            # 启用数据库选择，禁用自定义参数
            self.substance_combobox.config(state="readonly")
            self.custom_substance_entry.config(state="disabled")
            self.tc_entry.config(state="disabled")
            self.pc_entry.config(state="disabled")
            self.pc_unit_combobox.config(state="disabled")
            self.omega_entry.config(state="disabled")
            
            # 在数据库模式下，直接调用更新物性参数
            substance = self.substance_var.get()
            properties = self.converter.get_substance_properties(substance)
            if properties:
                # 更新界面显示
                self.tc_var.set(properties["Tc"])
                
                # 处理临界压力单位
                pc_unit = self.pc_unit_combobox.get()
                pc_pa = properties["Pc"]
                
                if pc_unit == "MPa":
                    self.pc_var.set(pc_pa / 1e6)
                elif pc_unit == "bar":
                    self.pc_var.set(pc_pa / BAR_TO_PA)
                elif pc_unit == "atm":
                    self.pc_var.set(pc_pa / ATM_TO_PA)
                else:  # Pa
                    self.pc_var.set(pc_pa)
                
                self.omega_var.set(properties["omega"])
        else:
            # 禁用数据库选择，启用自定义参数
            self.substance_combobox.config(state="disabled")
            self.custom_substance_entry.config(state="normal")
            self.tc_entry.config(state="normal")
            self.pc_entry.config(state="normal")
            self.pc_unit_combobox.config(state="readonly")
            self.omega_entry.config(state="normal")
        
        # 强制更新界面
        self.update_idletasks()
        
        # 重新检查值是否已正确显示
        print(f"Debug - 初始化后物性参数显示状态:")
        print(f"物质: {self.substance_var.get()}")
        print(f"临界温度: {self.tc_var.get()}")
        print(f"临界压力: {self.pc_var.get()} {self.pc_unit_combobox.get()}")
        print(f"偏心因子: {self.omega_var.get()}")
        print(f"选择模式: {self.substance_selection.get()}")
    
    def toggle_substance_mode(self, update_params=True):
        """
        切换物质选择模式
        
        参数:
        update_params: 是否在切换到数据库模式后自动更新物性参数
        """
        mode = self.substance_selection.get()
        if mode == "database":
            # 启用数据库选择，禁用自定义参数
            self.substance_combobox.config(state="readonly")
            self.custom_substance_entry.config(state="disabled")
            self.tc_entry.config(state="disabled")
            self.pc_entry.config(state="disabled")
            self.pc_unit_combobox.config(state="disabled")
            self.omega_entry.config(state="disabled")
            
            # 更新物性参数，但仅当需要时
            if update_params:
                self.on_substance_selected()
        else:
            # 禁用数据库选择，启用自定义参数
            self.substance_combobox.config(state="disabled")
            self.custom_substance_entry.config(state="normal")
            self.tc_entry.config(state="normal")
            self.pc_entry.config(state="normal")
            self.pc_unit_combobox.config(state="readonly")
            self.omega_entry.config(state="normal")
    
    def on_substance_selected(self, event=None):
        """当从数据库选择物质时更新参数"""
        # 健壮性检查：确保组件已经创建
        if not hasattr(self, 'substance_var') or not hasattr(self, 'tc_var') or not hasattr(self, 'pc_var') or not hasattr(self, 'omega_var'):
            print("警告: 组件尚未完全初始化，跳过更新")
            return
            
        # 获取当前选择的物质
        substance = self.substance_var.get()
        if not substance:  # 防止空值
            return
        
        # 从数据库获取物性参数    
        properties = self.converter.get_substance_properties(substance)
        if properties:
            # 更新临界温度
            self.tc_var.set(properties["Tc"])
            
            # 健壮性检查：确保pc_unit_combobox已创建
            pc_unit = "MPa"  # 默认单位
            if hasattr(self, 'pc_unit_combobox'):
                pc_unit = self.pc_unit_combobox.get()
            
            # 更新临界压力显示，考虑单位
            pc_pa = properties["Pc"]
            
            if pc_unit == "MPa":
                self.pc_var.set(pc_pa / 1e6)
            elif pc_unit == "bar":
                self.pc_var.set(pc_pa / BAR_TO_PA)
            elif pc_unit == "atm":
                self.pc_var.set(pc_pa / ATM_TO_PA)
            else:  # Pa
                self.pc_var.set(pc_pa)
            
            # 更新偏心因子
            self.omega_var.set(properties["omega"])
            
            # 打印调试信息
            print(f"已更新物质 '{substance}' 的物性参数:")
            print(f"临界温度: {properties['Tc']} K")
            print(f"临界压力: {pc_pa} Pa ({self.pc_var.get()} {pc_unit})")
            print(f"偏心因子: {properties['omega']}")
    
    def on_pc_unit_changed(self, event=None):
        """当临界压力单位改变时转换数值"""
        if self.substance_selection.get() == "custom":
            # 获取当前值和单位
            current_value = self.pc_var.get()
            old_unit = event.widget._prev_value if hasattr(event.widget, '_prev_value') else "MPa"
            new_unit = self.pc_unit_combobox.get()
            
            # 先转换为Pa
            if old_unit == "MPa":
                value_pa = current_value * 1e6
            elif old_unit == "bar":
                value_pa = current_value * BAR_TO_PA
            elif old_unit == "atm":
                value_pa = current_value * ATM_TO_PA
            else:  # Pa
                value_pa = current_value
            
            # 再转换为新单位
            if new_unit == "MPa":
                self.pc_var.set(value_pa / 1e6)
            elif new_unit == "bar":
                self.pc_var.set(value_pa / BAR_TO_PA)
            elif new_unit == "atm":
                self.pc_var.set(value_pa / ATM_TO_PA)
            else:  # Pa
                self.pc_var.set(value_pa)
            
            # 保存当前单位供下次使用
            event.widget._prev_value = new_unit
    
    def update_plots(self):
        """更新图表和单点计算结果"""
        try:
            # 获取用户输入的参数
            mode = self.substance_selection.get()
            if mode == "database":
                substance = self.substance_var.get()
            else:
                substance = self.custom_substance_var.get()
                
            Tc = float(self.tc_var.get())
            
            # 处理临界压力单位转换
            pc_value = float(self.pc_var.get())
            pc_unit = self.pc_unit_combobox.get()
            
            if pc_unit == "MPa":
                Pc = pc_value * 1e6
            elif pc_unit == "bar":
                Pc = pc_value * BAR_TO_PA
            elif pc_unit == "atm":
                Pc = pc_value * ATM_TO_PA
            else:  # Pa
                Pc = pc_value
            
            omega = float(self.omega_var.get())
            T = float(self.t_var.get())
            
            # 处理压力单位转换
            p_value = float(self.p_var.get())
            p_unit = self.p_single_unit_var.get()
            P = self.converter.convert_pressure_units(p_value, p_unit, "Pa")
            
            # 处理压力范围单位转换
            p_min_value = float(self.p_min_var.get())
            p_max_value = float(self.p_max_var.get())
            p_range_unit = self.p_unit_var.get()
            p_min = self.converter.convert_pressure_units(p_min_value, p_range_unit, "Pa")
            p_max = self.converter.convert_pressure_units(p_max_value, p_range_unit, "Pa")
            
            num_points = int(self.points_var.get())
            
            # 参数验证
            if Tc <= 0 or Pc <= 0 or T <= 0 or P <= 0 or p_min <= 0 or p_max <= 0 or p_min >= p_max:
                messagebox.showerror("参数错误", "所有温度、压力和临界参数必须为正值，且最小压力必须小于最大压力")
                return
            
            # 输出用户输入的压力范围信息以便调试
            print(f"用户输入压力范围: {p_min_value}-{p_max_value} {p_range_unit}")
            print(f"转换为Pa: {p_min}-{p_max} Pa")
            
            # 显示计算状态
            status_label = ttk.Label(self, text="正在计算图表数据，请稍候...", foreground="blue")
            status_label.pack(fill=tk.X, padx=5, pady=5)
            self.update()  # 更新界面以显示状态标签
            
            try:
                # 计算一系列压力点下的逸度和逸度系数，传入物理精确模式参数和迭代法参数
                pressures, fugacities, phi_values, z_values = self.converter.calculate_pressure_fugacity_points(
                    p_min, p_max, T, Tc, Pc, omega, num_points,
                    is_critical_check=self.critical_state_check.get(),
                    physical_accuracy=self.physical_accuracy_mode.get(),
                    use_iterative=self.use_iterative_mode.get()
                )
                
                # 检查计算结果范围是否符合用户指定范围
                p_min_result = min(pressures)
                p_max_result = max(pressures)
                if abs(p_min_result - p_min) > 0.01 * p_min or abs(p_max_result - p_max) > 0.01 * p_max:
                    print(f"警告：计算结果范围({p_min_result:.6e}-{p_max_result:.6e} Pa)与用户指定范围({p_min:.6e}-{p_max:.6e} Pa)不一致")
                
                # 移除状态标签
                status_label.destroy()
                
                # 检查是否有足够的数据点
                if len(pressures) < 10:
                    messagebox.showwarning("数据不足", 
                                          f"计算得到的有效数据点太少(仅{len(pressures)}个点)，图表可能不连续。\n"
                                          f"建议尝试调整压力范围或切换物理精确模式设置。")
            except Exception as e:
                # 移除状态标签
                status_label.destroy()
                messagebox.showerror("计算错误", f"计算图表数据时出错: {str(e)}\n"
                                     "请尝试调整压力范围或切换物理精确模式设置。")
                return
            
            # 单点计算结果
            try:
                phi = self.converter.pr_equation.calculate_fugacity_coefficient(P, T, Tc, Pc, omega, 
                                                                           self.physical_accuracy_mode.get(),
                                                                           self.use_iterative_mode.get())
                fugacity = phi * P
                Z = self.converter.pr_equation.calculate_Z(P, T, Tc, Pc, omega, 
                                         self.physical_accuracy_mode.get(),
                                         self.use_iterative_mode.get())
                
                # 计算理想气体偏差
                ideal_gas_deviation = abs(Z - 1.0) * 100
                
                # 更新结果标签，考虑显示单位
                self.fugacity_var.set(f"{self.converter.convert_pressure_units(fugacity, 'Pa', p_unit):.4e} {p_unit}")
                self.phi_var.set(f"{phi:.4f}")
                self.z_var.set(f"{Z:.4f}")
                self.deviation_var.set(f"{ideal_gas_deviation:.4f}%")
                
                # 如果启用了临界状态判断，更新临界状态显示并保存到结果中
                critical_state_text = ""
                critical_state_color = "black"
                if self.critical_state_check.get():
                    self.update_critical_state_display()
                    critical_state_text = self.critical_state_result.get()
                    critical_state_color = self.critical_text_label.cget("foreground")
                
                # 更新保存的单点计算结果，添加临界状态信息
                self.single_point_results = {
                    "物质名称": substance,
                    "温度 (K)": f"{T:.2f}",
                    "压力": f"{p_value} {p_unit}",
                    "临界温度 (K)": f"{Tc:.2f}",
                    "临界压力": f"{pc_value} {pc_unit}",
                    "偏心因子": f"{omega:.4f}",
                    "逸度 ({p_unit})": f"{self.converter.convert_pressure_units(fugacity, 'Pa', p_unit):.4e}",
                    "逸度系数": f"{phi:.4f}",
                    "压缩因子": f"{Z:.4f}",
                    "理想气体偏差 (%)": f"{ideal_gas_deviation:.4f}",
                    "临界状态": critical_state_text,
                    "_临界状态颜色_": critical_state_color  # 存储颜色信息，用于导出时着色
                }
                
                # 清除旧图
                self.ax1.clear()
                self.ax2.clear()
                
                # 将Pa转换为用户选择的单位以便更好地显示
                display_unit = p_range_unit
                pressures_converted = [self.converter.convert_pressure_units(p, 'Pa', display_unit) for p in pressures]
                fugacities_converted = [self.converter.convert_pressure_units(f, 'Pa', display_unit) for f in fugacities]
                
                # 保存转换后的数据以便导出
                self.display_results = {
                    f"压力 ({display_unit})": pressures_converted,
                    f"逸度 ({display_unit})": fugacities_converted,
                    "逸度系数": phi_values,
                    "压缩因子": z_values
                }
                
                # 第一个子图：压力-逸度关系
                try:
                    # 确保x轴范围覆盖用户指定的整个压力范围
                    p_min_display = self.converter.convert_pressure_units(p_min, 'Pa', display_unit)
                    p_max_display = self.converter.convert_pressure_units(p_max, 'Pa', display_unit)
                    
                    # 使用更强大的绘图方法，适应数据可能的不连续性
                    # 根据数据点之间的距离判断是否使用线段连接
                    sorted_indices = sorted(range(len(pressures_converted)), key=lambda i: pressures_converted[i])
                    sorted_p = [pressures_converted[i] for i in sorted_indices]
                    sorted_f = [fugacities_converted[i] for i in sorted_indices]
                    
                    # 将数据分组为连续段
                    segments_p = []
                    segments_f = []
                    current_segment_p = [sorted_p[0]]
                    current_segment_f = [sorted_f[0]]
                    
                    # 更宽松的间隙容忍度，以获得更连续的曲线
                    max_gap = max(p_max_value - p_min_value, 1) * 0.05  # 允许的最大间隙为范围的5%
                    
                    for i in range(1, len(sorted_p)):
                        # 如果与前一点的距离过大，开始新段
                        if (sorted_p[i] - sorted_p[i-1]) > max_gap:
                            if len(current_segment_p) > 1:  # 只保存包含多个点的段
                                segments_p.append(current_segment_p)
                                segments_f.append(current_segment_f)
                            current_segment_p = [sorted_p[i]]
                            current_segment_f = [sorted_f[i]]
                        else:
                            current_segment_p.append(sorted_p[i])
                            current_segment_f.append(sorted_f[i])
                    
                    # 添加最后一段
                    if len(current_segment_p) > 1:
                        segments_p.append(current_segment_p)
                        segments_f.append(current_segment_f)
                    
                    # 绘制每个连续段
                    for seg_p, seg_f in zip(segments_p, segments_f):
                        self.ax1.plot(seg_p, seg_f, 'b-', linewidth=2)
                    
                    # 添加散点以增强可视化效果
                    self.ax1.plot(pressures_converted, fugacities_converted, 'b.', markersize=3, alpha=0.3)
                    
                    # 设置图表标题和标签
                    self.ax1.set_title(f"{substance} 的压力-逸度关系 (T = {T} K)")
                    self.ax1.set_xlabel(f"压力 ({display_unit})")
                    self.ax1.set_ylabel(f"逸度 ({display_unit})")
                    self.ax1.grid(True)
                    
                    # 添加理想气体线 (f = P)，确保覆盖整个用户指定的范围
                    ideal_x = np.linspace(p_min_display, p_max_display, 100)
                    ideal_y = ideal_x
                    self.ax1.plot(ideal_x, ideal_y, 'r--', linewidth=1, label="理想气体")
                    
                    # 标记单点计算位置
                    p_point = self.converter.convert_pressure_units(P, 'Pa', display_unit)
                    f_point = self.converter.convert_pressure_units(fugacity, 'Pa', display_unit)
                    self.ax1.plot([p_point], [f_point], 'go', markersize=7, label="计算点")
                    self.ax1.legend()
                    
                    # 设置坐标轴范围 - 严格使用用户指定的范围
                    self.ax1.set_xlim([p_min_display, p_max_display])
                    
                    # y轴范围设置，确保所有数据点和理想气体线都可见
                    f_min = min(min(fugacities_converted), p_min_display) * 0.95
                    f_max = max(max(fugacities_converted), p_max_display) * 1.05
                    self.ax1.set_ylim([f_min, f_max])
                    
                except Exception as e:
                    print(f"绘制第一个图表时出错: {str(e)}")
                    # 使用简化的绘图方式作为备选
                    self.ax1.plot(pressures_converted, fugacities_converted, 'b.-', linewidth=1)
                    self.ax1.set_title(f"{substance} 的压力-逸度关系 (T = {T} K)")
                    self.ax1.set_xlabel(f"压力 ({display_unit})")
                    self.ax1.set_ylabel(f"逸度 ({display_unit})")
                    self.ax1.grid(True)
                    
                    # 确保x轴范围覆盖用户指定的整个压力范围
                    p_min_display = self.converter.convert_pressure_units(p_min, 'Pa', display_unit)
                    p_max_display = self.converter.convert_pressure_units(p_max, 'Pa', display_unit)
                    self.ax1.set_xlim([p_min_display, p_max_display])
                    
                    # 添加理想气体线，确保覆盖用户指定的范围
                    ideal_x = np.linspace(p_min_display, p_max_display, 100)
                    ideal_y = ideal_x
                    self.ax1.plot(ideal_x, ideal_y, 'r--', linewidth=1, label="理想气体")
                
                # 第二个子图：压力-逸度系数关系
                try:
                    # 对逸度系数数据也进行分段处理
                    sorted_phi = [phi_values[i] for i in sorted_indices]
                    
                    # 将数据分组为连续段
                    segments_phi = []
                    current_segment_phi = [sorted_phi[0]]
                    
                    for i in range(1, len(sorted_p)):
                        # 如果与前一点的距离过大，开始新段
                        if (sorted_p[i] - sorted_p[i-1]) > max_gap:
                            if len(current_segment_p) > 1:  # 只保存包含多个点的段
                                segments_phi.append(current_segment_phi)
                            current_segment_phi = [sorted_phi[i]]
                        else:
                            current_segment_phi.append(sorted_phi[i])
                    
                    # 添加最后一段
                    if len(current_segment_phi) > 1:
                        segments_phi.append(current_segment_phi)
                    
                    # 绘制每个连续段
                    for seg_p, seg_phi in zip(segments_p, segments_phi):
                        self.ax2.plot(seg_p, seg_phi, 'g-', linewidth=2)
                    
                    # 添加散点增强可视化效果
                    self.ax2.plot(pressures_converted, phi_values, 'g.', markersize=3, alpha=0.3)
                    
                    # 设置图表标题和标签
                    self.ax2.set_title(f"{substance} 的压力-逸度系数关系 (T = {T} K)")
                    self.ax2.set_xlabel(f"压力 ({display_unit})")
                    self.ax2.set_ylabel("逸度系数 φ")
                    self.ax2.grid(True)
                    
                    # 添加理想气体线 (phi = 1)，确保覆盖整个用户指定的范围
                    ideal_x = np.linspace(p_min_display, p_max_display, 100)
                    ideal_phi = [1.0] * len(ideal_x)
                    self.ax2.plot(ideal_x, ideal_phi, 'r--', linewidth=1, label="理想气体")
                    
                    # 标记单点计算位置
                    self.ax2.plot([p_point], [phi], 'go', markersize=7, label="计算点")
                    self.ax2.legend()
                    
                    # 设置坐标轴范围 - 严格使用用户指定的范围
                    self.ax2.set_xlim([p_min_display, p_max_display])
                    
                    # 设置一个合理的y轴范围，确保理想气体线可见
                    if phi_values:  # 确保有数据
                        phi_min = min(phi_values) * 0.9
                        phi_max = max(phi_values) * 1.1
                    else:
                        phi_min, phi_max = 0.5, 1.5
                    
                    # 确保理想气体线(phi=1)在视图内
                    phi_min = min(phi_min, 0.9)
                    phi_max = max(phi_max, 1.1)
                    self.ax2.set_ylim([phi_min, phi_max])
                    
                except Exception as e:
                    print(f"绘制第二个图表时出错: {str(e)}")
                    # 使用简化的绘图方式作为备选
                    self.ax2.plot(pressures_converted, phi_values, 'g.-', linewidth=1)
                    self.ax2.set_title(f"{substance} 的压力-逸度系数关系 (T = {T} K)")
                    self.ax2.set_xlabel(f"压力 ({display_unit})")
                    self.ax2.set_ylabel("逸度系数 φ")
                    self.ax2.grid(True)
                    
                    # 确保x轴范围覆盖用户指定的整个压力范围
                    self.ax2.set_xlim([p_min_display, p_max_display])
                    
                    # 添加理想气体线，确保覆盖用户指定的范围
                    ideal_x = np.linspace(p_min_display, p_max_display, 100)
                    ideal_phi = [1.0] * len(ideal_x)
                    self.ax2.plot(ideal_x, ideal_phi, 'r--', linewidth=1, label="理想气体")
                
                # 调整布局并重绘
                self.fig.tight_layout()
                self.canvas.draw()
                
                # 设置鼠标悬停显示坐标功能
                self.setup_hover_info(self.ax1, "逸度", pressures_converted, fugacities_converted)
                self.setup_hover_info(self.ax2, "逸度系数", pressures_converted, phi_values)
                
                # 在绘制完两个子图后，如果启用了临界状态判断，在图表上标记临界点和临界温度
                if self.critical_state_check.get():
                    try:
                        # 将临界压力转换为显示单位
                        pc_display = self.converter.convert_pressure_units(Pc, 'Pa', display_unit)
                        
                        # 获取当前温度和临界温度的比例
                        t_ratio = T / Tc
                        
                        # 在第一个图（压力-逸度关系图）中标记临界点
                        if p_min_display <= pc_display <= p_max_display:
                            # 临界压力在显示范围内，添加垂直线
                            self.ax1.axvline(x=pc_display, color='purple', linestyle='--', alpha=0.5, label="临界压力")
                            
                            # 在临界压力处添加标签
                            ymin, ymax = self.ax1.get_ylim()
                            y_pos = ymin + (ymax - ymin) * 0.9  # 位于Y轴90%的位置
                            self.ax1.annotate(f"Pc={pc_display:.2f}", 
                                            xy=(pc_display, y_pos), 
                                            xytext=(pc_display+0.05*(p_max_display-p_min_display), y_pos),
                                            arrowprops=dict(arrowstyle="->", color="purple"),
                                            color="purple", fontsize=9)
                        
                        # 添加临界温度信息到图表标题
                        self.ax1.set_title(f"{substance} 的压力-逸度关系 (T = {T} K, Tc = {Tc} K, T/Tc = {t_ratio:.3f})")
                        
                        # 在第二个图（压力-逸度系数关系图）中标记临界点
                        if p_min_display <= pc_display <= p_max_display:
                            self.ax2.axvline(x=pc_display, color='purple', linestyle='--', alpha=0.5, label="临界压力")
                            
                            # 在临界压力处添加标签
                            ymin, ymax = self.ax2.get_ylim()
                            y_pos = ymin + (ymax - ymin) * 0.9  # 位于Y轴90%的位置
                            self.ax2.annotate(f"Pc={pc_display:.2f}", 
                                            xy=(pc_display, y_pos), 
                                            xytext=(pc_display+0.05*(p_max_display-p_min_display), y_pos),
                                            arrowprops=dict(arrowstyle="->", color="purple"),
                                            color="purple", fontsize=9)
                        
                        # 添加临界温度信息到图表标题
                        self.ax2.set_title(f"{substance} 的压力-逸度系数关系 (T = {T} K, Tc = {Tc} K, T/Tc = {t_ratio:.3f})")
                        
                        # 如果计算范围包含临界点，在图上标记临界区域
                        if abs(t_ratio - 1.0) < 0.1 and p_min_display <= pc_display <= p_max_display:
                            # 创建临界区域
                            crit_width = (p_max_display - p_min_display) * 0.05  # 临界区域宽度
                            crit_x = [pc_display - crit_width, pc_display + crit_width, 
                                    pc_display + crit_width, pc_display - crit_width]
                            
                            # 为第一个图添加临界区域阴影
                            ymin1, ymax1 = self.ax1.get_ylim()
                            crit_y1 = [ymin1, ymin1, ymax1, ymax1]
                            self.ax1.fill(crit_x, crit_y1, color='red', alpha=0.1, label="临界区域")
                            
                            # 为第二个图添加临界区域阴影
                            ymin2, ymax2 = self.ax2.get_ylim()
                            crit_y2 = [ymin2, ymin2, ymax2, ymax2]
                            self.ax2.fill(crit_x, crit_y2, color='red', alpha=0.1, label="临界区域")
                        
                        # 更新图例
                        self.ax1.legend()
                        self.ax2.legend()
                    except Exception as e:
                        print(f"标记临界点时出错：{str(e)}")
                
            except Exception as e:
                messagebox.showerror("计算错误", f"单点计算失败: {str(e)}")
                return
            
        except ValueError as e:
            error_msg = str(e)
            if "could not convert string to float" in error_msg:
                messagebox.showerror("输入错误", "请确保所有输入都是有效的数字。")
            else:
                messagebox.showerror("计算错误", f"计算过程中出现错误：\n{str(e)}")
        except Exception as e:
            messagebox.showerror("程序错误", f"程序运行出错：\n{str(e)}")
            # 打印详细错误信息以便调试
            import traceback
            traceback.print_exc()
    
    def setup_hover_info(self, ax, label, xdata, ydata):
        """设置鼠标悬停时显示坐标信息的功能"""
        # 创建用于显示坐标的annotation
        annot = ax.annotate("", xy=(0,0), xytext=(20,20),
                         textcoords="offset points",
                         bbox=dict(boxstyle="round", fc="yellow", alpha=0.7),
                         arrowprops=dict(arrowstyle="->"))
        annot.set_visible(False)
        
        # 保存数据和注释对象
        ax.plot_data = (xdata, ydata)
        ax.plot_label = label
        ax.plot_annot = annot
        
        # 连接事件处理函数
        self.fig.canvas.mpl_connect("motion_notify_event", lambda event: self.hover(event, ax))

    def hover(self, event, ax):
        """鼠标悬停事件处理函数"""
        # 确保鼠标在坐标轴内部
        if event.inaxes == ax:
            annot = ax.plot_annot
            xdata, ydata = ax.plot_data
            
            # 找到最近的数据点
            x, y = event.xdata, event.ydata
            distances = np.sqrt((xdata - x)**2)
            idx = np.argmin(distances)
            
            # 更新注释内容和位置
            annot.xy = (xdata[idx], ydata[idx])
            text = f"压力: {xdata[idx]:.4f}\n{ax.plot_label}: {ydata[idx]:.4e}"
            annot.set_text(text)
            annot.set_visible(True)
            
            # 重绘图表以显示注释
            self.fig.canvas.draw_idle()
        else:
            # 当鼠标移出坐标轴时隐藏注释
            if hasattr(ax, 'plot_annot'):
                ax.plot_annot.set_visible(False)
                self.fig.canvas.draw_idle()
    
    def show_help(self):
        """显示使用帮助信息"""
        help_text = """
逸度与压强转换工具使用说明:

1. 物质选择:
   - 可以从数据库中选择常见物质，自动加载物性参数
   - 也可以手动输入自定义物质的参数
  
2. 物性参数:
   - 临界温度 (K): 物质的临界温度
   - 临界压力: 物质的临界压力，支持不同单位
   - 偏心因子: 用于表征分子非球形程度的参数
  
3. 计算条件:
   - 温度 (K): 计算条件下的温度
   - 单点压力: 用于计算单点结果的压力
   - 压力范围: 用于绘制曲线的压力范围
   - 曲线点数: 曲线上的点数，增加点数可提高精度
  
4. 计算结果:
   - 逸度: 在给定条件下的逸度，与压力具有相同单位
   - 逸度系数: 逸度与压力之比，无量纲
   - 压缩因子: 描述实际气体与理想气体的偏差
   - 理想气体偏差: 压缩因子偏离1的百分比
  
5. 图表:
   - 上图: 压力-逸度关系曲线
   - 下图: 压力-逸度系数关系曲线
   - 红色虚线: 理想气体行为
   - 绿点: 单点计算位置
  
6. 特殊功能:
   - 计算原理与过程: 点击此按钮可查看详细的计算理论、公式和步骤说明
   - 导出数据: 可将计算结果导出为CSV或Excel格式
   - 悬停显示: 鼠标悬停在图表上可显示坐标值
  
注意:
  - 所有温度必须为正值
  - 所有压力必须为正值
  - PR状态方程适用于大多数气体，但对于强极性分子精度可能降低
        """
        messagebox.showinfo("使用帮助", help_text)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """
逸度与压强转换可视化工具
版本: 1.0.0

作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)

基于Python和Peng-Robinson状态方程实现
用于计算实际气体的逸度、逸度系数和压缩因子

© 2023 材料模拟路漫漫&端木鹏博
        """
        messagebox.showinfo("关于", about_text)

    def check_parameters_display(self):
        """检查并更新物性参数显示，确保界面显示正确的物性参数"""
        # 检查当前模式
        mode = self.substance_selection.get()
        
        # 如果是数据库模式，重新检查物质参数
        if mode == "database":
            substance = self.substance_var.get()
            properties = self.converter.get_substance_properties(substance)
            if properties:
                # 直接强制更新参数，不进行比较，确保显示正确
                self.tc_var.set(properties["Tc"])
                
                pc_unit = self.pc_unit_combobox.get()
                pc_pa = properties["Pc"]
                
                if pc_unit == "MPa":
                    self.pc_var.set(pc_pa / 1e6)
                elif pc_unit == "bar":
                    self.pc_var.set(pc_pa / BAR_TO_PA)
                elif pc_unit == "atm":
                    self.pc_var.set(pc_pa / ATM_TO_PA)
                else:  # Pa
                    self.pc_var.set(pc_pa)
                
                self.omega_var.set(properties["omega"])
                
                print(f"强制更新物质 '{substance}' 的物性参数显示:")
                print(f"临界温度: {properties['Tc']} K")
                print(f"临界压力: {pc_pa} Pa ({self.pc_var.get()} {pc_unit})")
                print(f"偏心因子: {properties['omega']}")
                
                # 强制更新界面显示
                self.update()
                
        # 更新图表，确保显示的是正确的计算结果
        self.update_plots()

    def show_calculation_theory(self):
        """显示计算原理和详细过程"""
        # 创建新窗口
        theory_window = tk.Toplevel(self)
        theory_window.title("逸度计算原理与过程详解")
        theory_window.geometry("800x700")
        theory_window.configure(bg="#f5f5f5")
        
        # 创建可滚动文本区域
        frame = ttk.Frame(theory_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建文本区域
        text = tk.Text(frame, wrap=tk.WORD, font=("SimSun", 11), 
                      yscrollcommand=scrollbar.set, bg="#ffffff", padx=10, pady=10)
        text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=text.yview)
        
        # 定义标签样式
        text.tag_configure("title", font=("SimHei", 14, "bold"), foreground="#003366", 
                           spacing1=10, spacing3=10, justify=tk.CENTER)
        text.tag_configure("subtitle", font=("SimHei", 12, "bold"), foreground="#1a1a1a", 
                           spacing1=6, spacing3=2)
        text.tag_configure("equation", font=("SimSun", 11), foreground="#000066", 
                           spacing1=5, spacing3=5, justify=tk.CENTER)
        text.tag_configure("normal", font=("SimSun", 11))
        text.tag_configure("note", font=("SimSun", 10, "italic"), foreground="#666666")
        
        # 添加内容
        text.insert(tk.END, "逸度计算原理与过程详细说明\n", "title")
        
        # 逸度基本概念
        text.insert(tk.END, "\n1. 逸度与逸度系数基本概念\n", "subtitle")
        text.insert(tk.END, """
逸度(Fugacity)是一个热力学概念，用来描述非理想气体的行为。在理想气体中，化学势由压力决定；而在实际气体中，化学势则由逸度决定。

逸度系数(φ)定义为逸度与压力的比值：
""", "normal")
        text.insert(tk.END, "\nφ = f / P\n", "equation")
        text.insert(tk.END, """
其中：
f = 逸度 (Pa)
P = 压力 (Pa) 
φ = 逸度系数 (无量纲)

对于理想气体，逸度系数等于1（即逸度等于压力）。对于实际气体，逸度系数通常不等于1，这反映了实际气体与理想气体的偏差。
""", "normal")
        
        # PR状态方程
        text.insert(tk.END, "\n2. Peng-Robinson (PR) 状态方程\n", "subtitle")
        text.insert(tk.END, """
Peng-Robinson方程是一种立方型状态方程，用于描述实际气体的行为。PR方程的一般形式为：
""", "normal")
        text.insert(tk.END, """
P = RT/(V-b) - a(T)/(V(V+b)+b(V-b))

或表示为压缩因子Z的形式：

Z³ - (1-B)Z² + (A-3B²-2B)Z - (AB-B²-B³) = 0
""", "equation")
        text.insert(tk.END, """
其中：
Z = 压缩因子 = PV/(RT)
A = a(T)P/(RT)²
B = bP/(RT)

参数a和b与物质的临界性质有关：
a = 0.45724·(R²Tc²)/Pc
b = 0.07780·(RTc)/Pc

α(T)为温度相关函数：
α(T) = [1 + κ(1-√(T/Tc))]²

其中κ与物质的偏心因子ω有关：
κ = 0.37464 + 1.54226ω - 0.26992ω² (对于ω≤0.491)
κ = 0.379642 + 1.48503ω - 0.164423ω² + 0.016666ω³ (对于ω>0.491)
""", "normal")
        
        # 计算压缩因子Z
        text.insert(tk.END, "\n3. 计算压缩因子Z的过程\n", "subtitle")
        text.insert(tk.END, """
计算压缩因子Z是PR方程应用的关键步骤：

① 根据物质的临界温度Tc、临界压力Pc和偏心因子ω，计算参数a和b
② 计算α(T)函数值
③ 计算无量纲参数A和B
④ 求解立方方程以得到Z值
⑤ 如果方程有多个实根，选择最符合热力学稳定性的根

对于本程序，当计算得到多个实根时：
- 对于超临界状态(T>Tc)，通常选择最大的实根
- 对于亚临界状态(T<Tc)，根据压力选择不同的根
- 在某些情况下，通过计算吉布斯自由能最小值来选择最稳定的根
""", "normal")
        
        # 逸度系数计算
        text.insert(tk.END, "\n4. 逸度系数的精确计算\n", "subtitle")
        text.insert(tk.END, """
基于PR状态方程，逸度系数的精确计算公式为：
""", "normal")
        text.insert(tk.END, """
ln(φ) = (Z-1) - ln(Z-B) - A/(2√2·B)·ln[(Z+(1+√2)B)/(Z+(1-√2)B)]
""", "equation")
        text.insert(tk.END, """
这个公式考虑了实际气体与理想气体的偏差，可以在不同温度和压力条件下提供准确的逸度系数。
""", "normal")
        
        # 具体计算步骤
        text.insert(tk.END, "\n5. 本程序的计算步骤详解\n", "subtitle")
        text.insert(tk.END, """
当用户提供物质参数和计算条件后，程序按以下步骤进行计算：

① 首先获取物质的临界参数(Tc, Pc, ω)和计算条件(T, P)
② 计算PR方程的参数a、b和α(T)
③ 计算无量纲参数A和B
④ 求解立方方程得到压缩因子Z
   a. 构建立方方程系数
   b. 使用纯Python数值方法求解方程
   c. 根据热力学稳定性选择适当的根
⑤ 使用选定的Z值计算逸度系数φ
⑥ 计算逸度f = φ·P
⑦ 计算理想气体偏差 = |Z-1|×100%
⑧ 返回并显示计算结果

程序中使用了多种数值优化技术，包括：
- 改进的三次方程求解算法
- 健壮的根选择方法
- 防止数值不稳定的保护措施
- 自适应迭代的逸度求解方法
""", "normal")
        
        # 理想气体偏差
        text.insert(tk.END, "\n6. 理想气体偏差的计算\n", "subtitle")
        text.insert(tk.END, """
理想气体偏差是指实际气体与理想气体行为的差异程度，通过压缩因子Z偏离1的程度来衡量：
""", "normal")
        text.insert(tk.END, """
理想气体偏差(%) = |Z - 1.0| × 100%
""", "equation")
        text.insert(tk.END, """
理想气体的Z值恒等于1，因此理想气体偏差为0%。
实际气体的Z值通常不等于1：
- Z > 1 表示气体分子间的排斥力占主导
- Z < 1 表示气体分子间的引力占主导

偏差越大，表明实际气体行为越偏离理想气体行为，使用理想气体定律的误差就越大。
""", "normal")
        
        
        # 图表说明
        text.insert(tk.END, "\n7. 逸度-压力和逸度系数-压力图表说明\n", "subtitle")
        text.insert(tk.END, """
程序绘制的两个图表展示了逸度和逸度系数与压力的关系：

上图(逸度-压力关系)：
- 蓝色实线表示实际气体的逸度随压力变化曲线
- 红色虚线表示理想气体的逸度(等于压力)
- 两条线的差距表示实际气体与理想气体的偏差
- 绿色点表示当前计算点

下图(逸度系数-压力关系)：
- 绿色实线表示逸度系数随压力变化曲线
- 红色虚线表示理想气体的逸度系数(恒等于1)
- 曲线形状反映了不同压力下实际气体的非理想性
- 绿色点表示当前计算点

通过观察这两个图表，可以直观地了解实际气体行为与理想气体的偏差程度。
""", "normal")
        
        # 参考文献
        text.insert(tk.END, "\n8. 理论基础与参考文献\n", "subtitle")
        text.insert(tk.END, """
① Peng, D. Y.; Robinson, D. B. (1976). "A New Two-Constant Equation of State". Industrial & Engineering Chemistry Fundamentals. 15 (1): 59–64.

② Smith, J. M.; Van Ness, H. C.; Abbott, M. M. "Introduction to Chemical Engineering Thermodynamics", 7th ed.; McGraw-Hill: New York, 2005.

③ Poling, B. E.; Prausnitz, J. M.; O'Connell, J. P. "The Properties of Gases and Liquids", 5th ed.; McGraw-Hill: New York, 2001.

④ Sandler, S. I. "Chemical, Biochemical, and Engineering Thermodynamics", 4th ed.; John Wiley & Sons: New York, 2006.
""", "normal")
        
        # 使文本框只读
        text.config(state=tk.DISABLED)
        
        # 确保窗口置于前台
        theory_window.lift()
        theory_window.focus_force()

    def toggle_critical_state_check(self):
        """响应临界状态判断复选框的状态变化"""
        if self.critical_state_check.get():
            # 如果启用了临界状态判断，显示详细信息区域
            self.critical_details_frame.pack(fill=tk.X, padx=5, pady=5, after=self.critical_text_label.master)
            # 尝试立即更新临界状态结果
            self.update_critical_state_display()
        else:
            # 如果禁用了临界状态判断，隐藏详细信息区域
            self.critical_details_frame.pack_forget()
            # 清空结果显示
            self.critical_state_result.set("")
            self.critical_details_text.config(state=tk.NORMAL)
            self.critical_details_text.delete(1.0, tk.END)
            self.critical_details_text.config(state=tk.DISABLED)
            try:
                self.critical_label.config(foreground="black")
                self.critical_text_label.config(foreground="black")
            except Exception as e:
                print(f"重置颜色失败: {e}")

    def update_critical_state_display(self):
        """更新临界状态判断结果的显示"""
        try:
            # 获取当前温度和压力值
            T = float(self.t_var.get())
            
            # 获取临界温度和压力
            Tc = float(self.tc_var.get())
            
            # 获取单点压力及其单位
            p_value = float(self.p_var.get())
            p_unit = self.p_single_unit_var.get()
            P = self.converter.convert_pressure_units(p_value, p_unit, "Pa")
            
            # 获取临界压力及其单位
            pc_value = float(self.pc_var.get())
            pc_unit = self.pc_unit_combobox.get()
            
            if pc_unit == "MPa":
                Pc = pc_value * 1e6
            elif pc_unit == "bar":
                Pc = pc_value * BAR_TO_PA
            elif pc_unit == "atm":
                Pc = pc_value * ATM_TO_PA
            else:  # Pa
                Pc = pc_value
            
            # 获取压力范围
            p_min_value = float(self.p_min_var.get())
            p_max_value = float(self.p_max_var.get())
            p_range_unit = self.p_unit_var.get()
            p_min = self.converter.convert_pressure_units(p_min_value, p_range_unit, "Pa")
            p_max = self.converter.convert_pressure_units(p_max_value, p_range_unit, "Pa")
            
            # 计算约化温度和约化压力
            Tr = T / Tc
            Pr = P / Pc
            
            # 使用更精确的临界状态判断逻辑
            critical_message = ""
            color = "black"  # 默认颜色
            
            # 检查当前单点是否接近临界点
            is_near_critical_point = abs(Tr - 1.0) < 0.05 and abs(Pr - 1.0) < 0.05
            
            # 检查压力范围内是否包含临界压力
            critical_pressure_in_range = p_min <= Pc <= p_max
            
            # 检查是否在临界温度附近
            is_near_critical_temp = abs(T - Tc) < 0.05 * Tc
            
            # 获取计算方法信息
            calculation_method = "迭代法求解" if self.use_iterative_mode.get() else "三次方程求解"
            physical_mode = "物理精确模式" if self.physical_accuracy_mode.get() else "平滑数值模式"
            
            # 组合临界状态判断结果
            if is_near_critical_point:
                critical_message = "当前计算点接近临界点! (Tr≈1, Pr≈1)"
                color = "red"
            elif is_near_critical_temp and critical_pressure_in_range:
                critical_message = "计算范围内包含临界区域 (T≈Tc, 范围内包含Pc)"
                color = "orange"
            elif critical_pressure_in_range:
                if T > Tc:
                    critical_message = "计算范围包含超临界压力线 (T>Tc, 范围内包含Pc)"
                    color = "purple"
                else:
                    critical_message = "计算范围包含亚临界压力线 (T<Tc, 范围内包含Pc)"
                    color = "blue"
            elif is_near_critical_temp:
                critical_message = "当前温度接近临界温度 (T≈Tc)"
                color = "brown"
            else:
                # 使用相图区域判断
                if T > Tc * 1.1:  # 明显高于临界温度
                    critical_message = "超临界温度区域 (T>>Tc)"
                    color = "green"
                elif T > Tc:  # 略高于临界温度
                    critical_message = "超临界温度区域 (T>Tc)"
                    color = "green"
                else:  # T < Tc
                    if p_max < Pc:  # 压力范围低于临界压力
                        critical_message = "亚临界区域 (T<Tc, P<Pc)"
                        color = "blue"
                    elif p_min > Pc:  # 压力范围高于临界压力
                        critical_message = "亚临界高压区域 (T<Tc, P>Pc)"
                        color = "darkblue"
                    else:
                        critical_message = "亚临界混合区域 (T<Tc, 跨Pc)"
                        color = "teal"
            
            # 添加约化参数信息
            critical_message += f" [Tr={Tr:.3f}, Pr={Pr:.3f}]"
            
            # 更新显示文本
            self.critical_state_result.set(critical_message)
            
            # 更新标签颜色
            try:
                self.critical_label.config(foreground=color)
                self.critical_text_label.config(foreground=color)
            except Exception as e:
                print(f"更新颜色失败: {e}")
            
            # 准备详细信息文本
            details_text = f"临界参数:\n"
            details_text += f"临界温度(Tc): {Tc:.2f} K\n"
            details_text += f"临界压力(Pc): {pc_value:.4f} {pc_unit}\n"
            details_text += f"计算方法: {calculation_method}, {physical_mode}\n\n"
            
            # 根据不同情况添加额外信息
            if critical_pressure_in_range:
                # 计算临界压力在显示单位下的值
                pc_display = self.converter.convert_pressure_units(Pc, 'Pa', p_range_unit)
                details_text += f"临界压力值: {pc_display:.4f} {p_range_unit}\n"
                if p_min < Pc < p_max:
                    # 计算临界压力在计算范围内的相对位置
                    relative_pos = (Pc - p_min) / (p_max - p_min)
                    details_text += f"临界压力在计算范围内的位置: {relative_pos*100:.1f}%\n"
            
            if is_near_critical_temp:
                details_text += f"临界温度差异: {abs(T-Tc):.2f} K ({abs(T-Tc)/Tc*100:.2f}%)\n"
            
            # 更新详细信息显示
            self.critical_details_text.config(state=tk.NORMAL)
            self.critical_details_text.delete(1.0, tk.END)
            self.critical_details_text.insert(tk.END, details_text)
            
            # 应用文本颜色
            self.critical_details_text.tag_configure("colored", foreground=color)
            self.critical_details_text.tag_add("colored", "1.0", "end")
            
            self.critical_details_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.critical_state_result.set(f"计算错误: {str(e)}")
            try:
                self.critical_label.config(foreground="red")
                self.critical_text_label.config(foreground="red")
                
                # 显示错误详情
                self.critical_details_text.config(state=tk.NORMAL)
                self.critical_details_text.delete(1.0, tk.END)
                self.critical_details_text.insert(tk.END, f"临界状态判断出错:\n{str(e)}\n\n请检查输入参数是否正确。")
                self.critical_details_text.config(state=tk.DISABLED)
            except Exception as e:
                print(f"更新错误颜色失败: {e}")

    def show_critical_state_help(self):
        """显示临界状态判断的说明"""
        help_text = """
临界状态判断说明:

1. 临界点：
   - 临界点是指物质的气相和液相无法区分的状态点
   - 由临界温度(Tc)和临界压力(Pc)确定
   - 约化温度 Tr = T/Tc ≈ 1.0，约化压力 Pr = P/Pc ≈ 1.0

2. 相区判断依据：
   - 当前点是否接近临界点
   - 计算范围内是否包含临界压力
   - 当前温度是否接近临界温度
   - 相对于临界温度和压力的位置
   
3. 颜色标识：
   - 红色: 接近临界点
   - 橙色: 计算范围包含临界区域
   - 紫色: 计算范围包含超临界压力线
   - 蓝色: 计算范围包含亚临界压力线
   - 绿色: 超临界温度区域
   - 青色: 亚临界混合区域
   
4. 详细信息：
   - 显示临界温度和压力值
   - 计算方法和物理模式
   - 临界压力在计算范围中的位置
   - 温度与临界温度的差异
   
注意：临界状态判断会使用颜色在图表、导出数据和UI中标识相关信息。
    """
        messagebox.showinfo("临界状态判断说明", help_text)

    def toggle_physical_accuracy_mode(self):
        """响应物理精确模式复选框的状态变化"""
        # 如果已有计算结果，提示用户重新计算
        if hasattr(self, 'display_results') and self.display_results:
            if self.physical_accuracy_mode.get():
                messagebox.showinfo("模式切换", "已切换到物理精确模式。\n此模式下会显示相变区域的所有物理特性，包括峰值。\n请点击'计算并更新图表'按钮重新计算。")
            else:
                messagebox.showinfo("模式切换", "已切换到平滑数值模式。\n此模式下会过滤异常峰值以提供更平滑的曲线。\n请点击'计算并更新图表'按钮重新计算。")

    def show_physical_mode_help(self):
        """显示物理精确模式的说明"""
        help_text = """
物理精确模式说明:

1. 平滑数值模式 (默认):
   - 过滤相变区域的数值异常和峰值
   - 提供更平滑的曲线，便于总体趋势观察
   - 适用于一般热力学分析和可视化

2. 物理精确模式:
   - 严格遵循PR状态方程的数学解
   - 保留相变区域的所有物理特性，包括峰值
   - 适用于相变行为研究和临界现象分析
   
3. 模式区别:
   - 在两相区域，物理精确模式可能显示尖峰
   - 这些峰值虽然在数学上是正确的，但在实际系统中
     通常观察不到，因为实际系统会自发分离为两相
   - 峰值区域对应范德华循环，理论上可用于
     预测相平衡条件
   
注意: 在物理精确模式下，某些条件可能导致曲线出现
不连续点，这反映了状态方程在描述相变时的限制。
    """
        messagebox.showinfo("物理精确模式说明", help_text)

    def toggle_iterative_mode(self):
        """响应迭代法求解模式复选框的状态变化"""
        # 如果已有计算结果，提示用户重新计算
        if hasattr(self, 'display_results') and self.display_results:
            if self.use_iterative_mode.get():
                messagebox.showinfo("模式切换", "已切换到使用迭代法求解模式。\n此模式下会提高临界点附近的计算稳定性。\n请点击'计算并更新图表'按钮重新计算。")
            else:
                messagebox.showinfo("模式切换", "已切换到使用三次方程求解模式。\n此模式下会提高临界点附近的计算稳定性。\n请点击'计算并更新图表'按钮重新计算。")

    def show_iterative_mode_help(self):
        """显示迭代法求解模式的说明"""
        help_text = """
迭代法求解模式说明:

1. 使用迭代法求解模式 (默认):
   - 提高临界点附近的计算稳定性
   - 适用于相变行为研究和临界现象分析
   
2. 使用三次方程求解模式:
   - 提高临界点附近的计算稳定性
   - 适用于一般热力学分析和可视化
   
3. 模式区别:
   - 在两相区域，使用迭代法求解模式可能显示尖峰
   - 这些峰值虽然在数学上是正确的，但在实际系统中
     通常观察不到，因为实际系统会自发分离为两相
   - 峰值区域对应范德华循环，理论上可用于
     预测相平衡条件
   
注意: 在迭代法求解模式下，某些条件可能导致曲线出现
不连续点，这反映了状态方程在描述相变时的限制。
    """
        messagebox.showinfo("迭代法求解模式说明", help_text)


def check_environment():
    """
    检查运行环境，返回可用的界面类型
    
    返回:
    str: "gui" 或 "console"
    """
    # 检查是否有图形界面库
    if not TKINTER_AVAILABLE or not MATPLOTLIB_AVAILABLE:
        return "console"
    
    # 尝试创建一个Tk对象测试GUI是否可用
    root = None
    try:
        # 使用root变量作为显式引用，确保后面可以销毁它
        root = tk.Tk()
        # 隐藏窗口，避免闪烁
        root.withdraw()
        # 如果到这里没有异常，GUI环境可用
        return "gui"
    except Exception:
        return "console"
    finally:
        # 确保在任何情况下都尝试销毁测试窗口
        if root:
            try:
                root.destroy()
                # 尝试刷新事件循环确保销毁完成
                if hasattr(root, 'update'):
                    root.update()
            except:
                pass  # 忽略销毁过程中的任何错误


def main():
    """主函数：选择是使用命令行界面还是图形界面"""
    print("初始化逸度与压强转换工具...")
    print("检查运行环境...")
    
    interface_type = check_environment()
    
    if interface_type == "gui":
        print("启动图形界面...\n")
        try:
            # 防止tkinter在导入时创建隐藏的根窗口
            tk._default_root = None
            
            # 设置样式主题，如果系统支持
            if hasattr(ttk, 'Style'):
                style = ttk.Style()
                try:
                    style.theme_use('clam')  # 使用更现代的主题
                except Exception:
                    pass  # 如果主题不可用，忽略错误
            
            # 创建并运行图形界面
            visualizer = FugacityVisualizer()
            visualizer.mainloop()
        except Exception as e:
            print(f"启动图形界面时出错: {e}")
            print("回退到命令行界面...")
            main_console()
    else:
        print("图形界面不可用，使用命令行界面...\n")
        main_console()


if __name__ == "__main__":
    main()




