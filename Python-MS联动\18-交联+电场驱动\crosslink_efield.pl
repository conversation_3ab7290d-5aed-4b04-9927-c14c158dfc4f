#!perl

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);
use constant TRUE 	=> 1;
use constant FALSE 	=> 0;
use constant DEBUG	=> 2; # larger is more verbose
use constant PICO_TO_FEMTO => 1000;

# 交联+电场驱动简化版脚本
# 基于 "最终版-交付客户.pl" 修改而来
# 功能：使用电场辅助交联过程，通过电场动力学和交联循环的双层嵌套结构实现
# 更新日期：2023

# 用户设置 - 可从GUI获取或在此编辑
my $xsdDocName				= "final";	# 包含交联剂和单体的xsd文件名称
my $conversionTarget 		= 50;			# 单体上反应性原子的目标反应百分比
my $MinRxnRadius 			= 4;			# 初始近距离接触截断值
my $StepRxnRadius 			= 1;			# 近距离接触步长
my $MaxRxnRadius 			= 9;			# 最终近距离接触截断值
my $IterationsPerRadius		= 2;			# 每个半径的最大迭代次数
my $maxCycles               = 2;            # 电场动力学和交联循环的最大次数

# 反应原子和分子名称设置
my $monomerName 			= "monomer";	# 单体分子名称
my $monomerReactiveAtom 	= "R1";			# 单体中反应性原子的名称
my $xlinkerName 			= "xlinker";	# 交联剂分子名称
my $xlinkerReactiveAtom 	= "R2";			# 交联剂中反应性原子的名称

# 通常我们为每个片段定义分子对象以跟踪统计数据
# 对于自交联片段和界面交联等情况，使用此标志关闭分子使用
my $noMol					= TRUE;

# 使用此标志打开环氧类型环
my $openRing				= FALSE;
my $deleteAtom 				= "O";			# 删除R1和此原子之间的键 - 用于环打开

# 缩合反应：从反应原子中移除OH基团？
my $remove_condensation		= FALSE;

# 聚氨酯：将异氰酸酯中的双键转化为单键，假设R1 = 来自O=C=N基团的碳原子
my $polyurethane			= FALSE;

# 允许单个原子上的多重反应？
my $react_multi_monomer		= FALSE;
my $react_multi_xlinker		= FALSE;

# 防止同一两个片段之间的多重交联
my $prevent_intraxlinks		= TRUE;
my %xconnect;  # 连接表，用于防止内部交联

# 模拟设置
my $forcefield		 	= "COMPASSIII";
my $timeStep			= 0.1;			# 动力学时间步长（飞秒）
my $chargeMethod 		= "Atom based";	# 原子基，基团基或Ewald
my $Quality				= "Medium";		# 粗糙/中等/精细/超精细
my $thermostat			= "Andersen";	# Andersen, Nose, Velocity Scale, Berendsen 或 NHL
my $xlinkTemperature	= 300;			# 主要温度
my $xlinkPressure		= 0.0001;		# 主要压力
my $ensemble			= "NVE";		# 通常为NVE，或对液体有真空层的情况使用NVT
										# 如果非周期性，无论如何都将使用NVE

# 电场设置（适用于所有动力学模拟，包括一次性平衡）
my $useElectricField        = TRUE;         # 是否应用电场（所有动力学运行的全局开关）
my $electricFieldStrength   = 1;            # 电场强度，单位V/Å
my $electricFieldX          = 0;            # 场方向的X分量
my $electricFieldY          = 0;            # 场方向的Y分量
my $electricFieldZ          = 1;            # 场方向的Z分量
my $counterElectricField    = "Yes";        # 是否对带净电荷的系统施加相反的力

# 动力学时间设置
my $fieldDynamicsTime       = 1;            # 每次电场动力学运行的皮秒数
my $one_time_equilibration	= 0;			# 初始平衡的动力学皮秒数
my $straightDynamicsTime	= 10;			# 每次新距离前的动力学时间（皮秒）
my $analyzeDuration			= 10;			# 用于采样热力学数据的动力学皮秒数

# 温度循环设置
my $UseTempCycle			= FALSE;
my $startTemp				= $xlinkTemperature;		# 起始温度
my $lowTemp					= $xlinkTemperature;		# 结束温度
my $highTemp				= $xlinkTemperature + 200;	# 高温
my $tempStep				= 50;						# 温度步长
my $tempDuration			= 20;						# 每个温度的时间（皮秒）

# 用于将交联键平滑结合在一起的临时约束键参数
my $UseRestraintBonds			= TRUE;		# 开/关平滑算法
my $RestraintBondingTargetLength = 1.47;		# 使用来自CVFF的C-N键参数
my $RestraintForceConstant 		= 356.5988;
my $nRestraintBondingStages		= 3;			# 平滑增加约束键的增量
my $relax_length 				= 1;			# 键合前放松结构的时间（皮秒）

# 计算最大键能量以帮助评估网络应变和可能的环刺穿
my $UseMaxBondEnergy			= TRUE;

######################################################
# GUI? - 尝试打开活动文档
######################################################
my $xsdDoc;
eval{ $xsdDoc = Documents->ActiveDocument; };

if ($@) # 无活动文档 - 使用上面定义的参数
{
	$xsdDoc	= $Documents{"$xsdDocName.xsd"};
}
else
{
	# 获取用户提供的选项
	my %Arg;
	GetOptions(\%Arg,
		"Forcefield=s", 
		"Conversion=i", 
		"MinRxnRadius=f", 
		"MaxRxnRadius=f", 
		"StepRxnRadius=f",
		"Iterations_per_Radius=i", 
		"ChargeMethod=s", 
		"Temperature=f",
		"TemperatureCycle=s",
		"Temperature_Step=f",
		"High_Temperature=f",
		"ps_per_Temperature=f",
		"Initial_Equil_ps=f",
		"Equil_ps=f",
		"OpenRings=s",
		"Condensation=s",
		"Polyurethane=s",
		"React_Multi_Monomer=s",
		"React_Multi_Xlinker=s",
		"Use_Molecules=s",
		"Ensemble=s",
		"UseElectricField=s",
		"ElectricFieldStrength=f",
		"ElectricFieldX=f",
		"ElectricFieldY=f",
		"ElectricFieldZ=f",
		"CounterElectricField=s",
		"MaxCycles=i",
		"FieldDynamicsTime=f"
	);

	$conversionTarget 	= $Arg{Conversion}	if (exists $Arg{Conversion});
	$MinRxnRadius 		= $Arg{MinRxnRadius}	if (exists $Arg{MinRxnRadius});
	$StepRxnRadius 		= $Arg{StepRxnRadius}	if (exists $Arg{StepRxnRadius});
	$MaxRxnRadius 		= $Arg{MaxRxnRadius}	if (exists $Arg{MaxRxnRadius});
	$IterationsPerRadius	= $Arg{Iterations_per_Radius} if (exists $Arg{Iterations_per_Radius});
	$openRing		= TRUE  if (exists $Arg{OpenRings} and $Arg{OpenRings} eq "Yes");
	$openRing		= FALSE if (exists $Arg{OpenRings} and $Arg{OpenRings} eq "No");
	$remove_condensation	= TRUE  if (exists $Arg{Condensation} and $Arg{Condensation} eq "Yes");
	$remove_condensation	= FALSE if (exists $Arg{Condensation} and $Arg{Condensation} eq "No");
	$polyurethane		= TRUE  if (exists $Arg{Polyurethane} and $Arg{Polyurethane} eq "Yes");
	$polyurethane		= FALSE if (exists $Arg{Polyurethane} and $Arg{Polyurethane} eq "No");
	$react_multi_monomer	= TRUE  if ($Arg{React_Multi_Monomer} eq "Yes");
	$react_multi_monomer	= FALSE if ($Arg{React_Multi_Monomer} eq "No");
	$react_multi_xlinker	= TRUE  if ($Arg{React_Multi_Xlinker} eq "Yes");
	$react_multi_xlinker	= FALSE if ($Arg{React_Multi_Xlinker} eq "No");
	$forcefield	 	= $Arg{Forcefield}	if (exists $Arg{Forcefield});
	$chargeMethod 		= $Arg{ChargeMethod}	if (exists $Arg{ChargeMethod});
	$xlinkTemperature	= $Arg{Temperature}	if (exists $Arg{Temperature});
	$one_time_equilibration	= $Arg{Initial_Equil_ps} if (exists $Arg{Initial_Equil_ps});
	$straightDynamicsTime   = $Arg{Equil_ps}	if (exists $Arg{Equil_ps});
	$UseTempCycle		= TRUE  if (exists $Arg{TemperatureCycle} and $Arg{TemperatureCycle} eq "Yes");
	$UseTempCycle		= FALSE if (exists $Arg{TemperatureCycle} and $Arg{TemperatureCycle} eq "No");
	$highTemp		= $Arg{High_Temperature}	if (exists $Arg{High_Temperature});
	$tempStep		= $Arg{Temperature_Step}	if (exists $Arg{Temperature_Step});
	$tempDuration		= $Arg{ps_per_Temperature}	if (exists $Arg{ps_per_Temperature});
	$noMol			= TRUE  if (exists $Arg{Use_Molecules} and $Arg{Use_Molecules} eq "No");
	$noMol			= FALSE if (exists $Arg{Use_Molecules} and $Arg{Use_Molecules} eq "Yes");
	$ensemble		= $Arg{Ensemble}	if (exists $Arg{Ensemble} and $Arg{Ensemble} ne "");
	
	# 电场参数
	$useElectricField       = TRUE  if (exists $Arg{UseElectricField} and $Arg{UseElectricField} eq "Yes");
	$useElectricField       = FALSE if (exists $Arg{UseElectricField} and $Arg{UseElectricField} eq "No");
	$electricFieldStrength  = $Arg{ElectricFieldStrength} if (exists $Arg{ElectricFieldStrength});
	$electricFieldX         = $Arg{ElectricFieldX} if (exists $Arg{ElectricFieldX});
	$electricFieldY         = $Arg{ElectricFieldY} if (exists $Arg{ElectricFieldY});
	$electricFieldZ         = $Arg{ElectricFieldZ} if (exists $Arg{ElectricFieldZ});
	$counterElectricField   = "Yes" if (exists $Arg{CounterElectricField} and $Arg{CounterElectricField} eq "Yes");
	$counterElectricField   = "No"  if (exists $Arg{CounterElectricField} and $Arg{CounterElectricField} eq "No");
	$maxCycles              = $Arg{MaxCycles} if (exists $Arg{MaxCycles});
	$fieldDynamicsTime      = $Arg{FieldDynamicsTime} if (exists $Arg{FieldDynamicsTime});

	# 将用户在GUI中指定的选项写入文本文件
	my $textDoc = Documents->New("GUI_inputs.txt");
	$textDoc->Append("USER OPTIONS\n============================\n");
	while ( my ($key, $value) = each(%Arg) ) 
	{
		$textDoc->Append(sprintf "$key => $value\n");
	}
	$textDoc->Append("============================\n\n");
	$textDoc->Close;
}

###########################################################################################
#
# 结束用户设置
#
###########################################################################################

# 导入一些具有主机名、mpi和参数信息的服务器文件
eval 
{
	Documents->Import("fromdsd.txt");
	Documents->Import("mpd.hosts");
};

# 用于报告运行进度的文件
my $textDoc = Documents->New("Progress.txt");

# 用于报告每个循环所花时间的文件
my $timeDoc = Documents->New("Timings.txt");
$timeDoc->Append("Distance Iteration Elapsted_Time(hr) Segment_Time(hr) Conversion(%)\n");
my $segtime = time; # 运行段的时间（秒）

# 复制第一个文档以保存起始结构
my $rootName = "xlink";
my $doc = Documents->New("$rootName.xsd");
$doc->CopyFrom($xsdDoc);

# 使用全局设置初始化Forcite
my $Forcite = Modules->Forcite;
$Forcite->ChangeSettings([
	CurrentForcefield	=> $forcefield,
	Quality			=> $Quality,
	Temperature		=> $xlinkTemperature,
	Pressure		=> $xlinkPressure,
	Thermostat		=> $thermostat,
	Barostat		=> "Andersen",
	TimeStep		=> $timeStep,
	TrajectoryFrequency	=> 1000,            # 轨迹帧保存频率
	AppendTrajectory	=> "Yes",           # 追加轨迹而不是覆盖
	WriteVelocities		=> "Yes",
	EnergyDeviation		=> 500000000,
	WriteLevel		=> "Silent"
]);

# 周期性：支持0和3。2可能有效
my $Periodicity;
if (($doc->SymmetrySystems->Count == 0) || ($doc->SymmetrySystem->SymmetryDefinition->Periodicity == 0))
{
	$Periodicity = 0;
	die "非周期性系统不能使用Ewald\n" if ($chargeMethod eq "Ewald");
	$Forcite->ChangeSettings([
		NonPeriodicElectrostaticSummationMethod		=> $chargeMethod,
		NonPeriodicvdWSummationMethod			=> "Atom based"
	]);
}
else
{
	$Periodicity = $doc->SymmetrySystem->SymmetryDefinition->Periodicity;
	$Forcite->ChangeSettings([
	    	'3DPeriodicElectrostaticSummationMethod'	=> $chargeMethod,
		'****************************' 			=> "Atom based",
		'2DPeriodicElectrostaticSummationMethod'	=> $chargeMethod,
		'2DPeriodicvdWSummationMethod'			=> "Atom based",
	]);
} 
warn "2D周期性尚未经过测试\n" if ($Periodicity == 2);

# 防止重启运行的文件名冲突
$xsdDoc->Name = "initial" if ($xsdDoc->Name =~ /^xlink_/);
$xsdDoc->Close;

# 创建用于保存统计数据的研究表
my $statTable = Documents->New($rootName."_statistics.std");
my $nstatsheets = 1;

# 创建用于保存每个距离循环结束时的中间结构的研究表
# 可用于进一步分析系统的演变
my $structureTable = Documents->New($rootName."_structures.std");
$structureTable->ColumnHeading(1) = "Distance (A)";
$structureTable->ColumnHeading(2) = "Iteration";
$structureTable->ColumnHeading(3) = "Percent Conversion";
$structureTable->ColumnHeading(4) = "Cycle";

###########################################################################################
# 初始化交联数据

# 计数反应和已反应原子
my $reactiveMonomerAtoms = 0;
my $reactiveXLinkerAtoms = 0;
my $reactedMonomerAtoms  = 0;
my $reactedXLinkerAtoms  = 0;
my $totalMonomerAtoms    = 0;
foreach my $atom (@{$doc->UnitCell->Atoms})
{
	$reactiveMonomerAtoms++ if ( isReactiveR1($atom) );
	$reactiveXLinkerAtoms++  if ( isReactiveR2($atom) );
	$reactedMonomerAtoms++  if ($atom->Name =~ /^$monomerReactiveAtom-\d/);
	$reactedXLinkerAtoms++   if ($atom->Name =~ /^$xlinkerReactiveAtom-\d/);
	$totalMonomerAtoms++    if ($atom->Name =~ /^$monomerReactiveAtom/);
}

# xlinkPotential是交联剂可形成的可能交联数
my $xlinkPotential = $reactiveXLinkerAtoms;
if ($react_multi_xlinker)
{
	$xlinkPotential = 0;
	foreach my $atom (@{$doc->UnitCell->Atoms})
	{
		if ($atom->Name =~ /^$xlinkerReactiveAtom/)
		{
			$xlinkPotential += HCount($atom);
			if ($remove_condensation)
			{
				my ($nOH) = OHCount($atom);
				$xlinkPotential += $nOH;
			}
		}
	}
}

# 计算之前运行中形成的交联
my $xlinkCounter = 0;
foreach my $bond (@{$doc->UnitCell->Bonds})
{
	$xlinkCounter++ if ($bond->Name =~ /^xlink/);
}

my $conversion = calculateConversion($doc);
my $rowCounter		= 0;	# 研究表中的当前行

# 检查单体和交联剂原子所需数量与转化率的一致性
my $targetMonomerAtoms = $totalMonomerAtoms * ($conversionTarget / 100) - $reactedMonomerAtoms;
$textDoc->Append( "转化率 = 已反应单体原子占反应性单体原子的百分比\n");
$textDoc->Append( "转化率目标 = $conversionTarget, 当前转化率 = $conversion\n");
$textDoc->Append( "需要反应的单体原子总数: $targetMonomerAtoms \n");
$textDoc->Append( "反应物原子: 单体 $reactiveMonomerAtoms, 交联剂 $reactiveXLinkerAtoms\n");
$textDoc->Save;
die "需要更多交联剂才能达到指定转化率\n" 
	if ($xlinkPotential < $targetMonomerAtoms);

# 优化和动力学步骤的计数器
my $mdcounter = 0;
my $geomoptcounter = 0;

# 准备分子对象以跟踪交联统计
Initialize_xlinkStatistics($doc) unless ($noMol);

###########################################################################################
# 一次性平衡
# 注意：如果$useElectricField为TRUE，则在此处应用电场

if ($one_time_equilibration > 0)
{
	my $steps = ($one_time_equilibration * PICO_TO_FEMTO / $timeStep);
	$textDoc->Append("\n一次性平衡\n");
	my $results = ForciteDynamics($doc, $steps, "NVE");
	my $results = ForciteDynamics($doc, $steps, $ensemble);
}

###########################################################################################
# 电场动力学和交联双层循环
# 初始化循环计数器
my $cycleCounter = 0;
my $targetReached = FALSE;

$textDoc->Append("\n开始电场动力学和交联双层循环\n");
$textDoc->Save;

# 执行最多$maxCycles次电场动力学与交联循环
while ($cycleCounter < $maxCycles && !$targetReached) {
    # 递增循环计数器
    $cycleCounter++;
    
    $textDoc->Append("\n\n====================================================\n");
    $textDoc->Append("======== 电场动力学和交联循环 #$cycleCounter ========\n");
    $textDoc->Append("====================================================\n\n");
    
    # 重命名文档
    $doc->Name = "xlink_cycle_".$cycleCounter;
    
    # 执行电场动力学
    $textDoc->Append("执行电场动力学 - 循环 #$cycleCounter\n");
    my $steps = ($fieldDynamicsTime * PICO_TO_FEMTO / $timeStep);
    my $results = ForciteDynamics($doc, $steps, $ensemble);
    
    # 对每个反应半径执行交联
    for (my $RxnRadius = $MinRxnRadius; $RxnRadius <= $MaxRxnRadius; $RxnRadius += $StepRxnRadius) {
        # 设置文档命名格式
        my $xsdNameDist = sprintf("xlink_cycle_%d_R%.2f", $cycleCounter, $RxnRadius);
        
        # 为每个新半径平衡（除第一个外）
        if ($RxnRadius > $MinRxnRadius) {
            $textDoc->Append("在新半径下平衡\n");
            $doc->Name = $xsdNameDist . "_init";
            ForciteGeomOpt($doc, 2000);
            my $steps = ($straightDynamicsTime * PICO_TO_FEMTO / $timeStep);
            my $results = ForciteDynamics($doc, $steps, $ensemble);
        }
        
        # 在每个半径下迭代交联，直到：
        # A) 达到最大迭代次数
        # B) 无新交联形成
        # C) 达到转化率目标
        for (my $iteration = 1; $iteration <= $IterationsPerRadius; $iteration++) {
            $textDoc->Append("\n\n##########################################################\n");
            $textDoc->Append("###### 循环 $cycleCounter, 半径 $RxnRadius, 迭代 $iteration\n");
            $textDoc->Append("##########################################################\n\n");
            $textDoc->Save;
            
            $doc->Name = $xsdNameDist."_".$iteration;
            
            # 在结构中创建新键
            my $numBonds = createNewXlinks($doc, $RxnRadius);
            
            # 更新转化率
            $conversion = calculateConversion($doc);
            $textDoc->Append(sprintf "交联= %d \n转化率= %.01F %%\n", $xlinkCounter, $conversion);
            $textDoc->Save;
            
            # 如果没有新键，跳出循环并转到下一个半径
            if ($numBonds == 0) {
                $textDoc->Append("没有创建新键，增加反应距离\n");
                last;
            }
            
            # 扰动以消除可能生成的长键
            optimizeAndPerturb($doc);
            
            # 生成一些交联统计
            xlinkStatistics($doc, $RxnRadius, $rowCounter) unless ($noMol);
            maxBondEnergy($doc, $RxnRadius, $rowCounter) if ($UseMaxBondEnergy);
            AnalyzeFragments($doc, $RxnRadius, $rowCounter);
            
            # 将结构保存到研究表
            $textDoc->Append("将中间结构保存到研究表\n");
            eval {
                # 检查工作表是否有足够的行
                my $rowCount = $structureTable->RowCount || 0;
                if ($rowCount <= $rowCounter) {
                    $structureTable->InsertRows($rowCount + 1, $rowCounter - $rowCount + 1);
                }
                
                $doc->InsertInto($structureTable);
                $structureTable->Cell($rowCounter,1) = $RxnRadius;
                $structureTable->Cell($rowCounter,2) = $iteration;
                $structureTable->Cell($rowCounter,3) = $conversion;
                $structureTable->Cell($rowCounter,4) = $cycleCounter;
            };
            if ($@) {
                $textDoc->Append("无法将结构保存到研究表: $@\n");
            }
            
            # 短动力学运行以记录一些热力学性质
            $textDoc->Append("\n\n运行额外动力学进行分析\n");
            my $steps = ($analyzeDuration * PICO_TO_FEMTO / $timeStep);
            my $freq = int($steps/20);
            my $results = ForciteDynamics($doc, $steps, $ensemble, (TrajectoryFrequency => $freq));
            getTrajTempAndPressure($results, $rowCounter, $RxnRadius);
            getEnergies($results,$rowCounter);
            $rowCounter++;
            
            # 报告此迭代所用时间
            $timeDoc->Append(sprintf "%-8.2f %-9d %-17.1f %-16.2f %-8.1f\n", 
                $RxnRadius, $iteration, (time-$^T)/3600, 
                (time-$segtime)/3600, $conversion);
            $segtime = time;
            
            # 保存所有文档，以防中止脚本而不丢失数据
            Documents->SaveAll;
            
            # 如果达到目标转化率，设置标志并跳出迭代循环
            if ($conversion >= $conversionTarget) {
                $targetReached = TRUE;
                last;
            }
        } # 下一次迭代
        
        # 如果达到目标转化率，跳出半径循环
        last if ($targetReached);
    } # 下一个半径
    
    # 检查是否达到目标转化率或完成所有循环
    if ($targetReached || $cycleCounter >= $maxCycles) {
        $textDoc->Append("\n达到目标转化率或完成所有循环，退出主循环\n");
        last;
    }
} # 下一个循环

# 重命名最终xsd为唯一名称
$doc->Name = $rootName."_final";

# 计算键分布
analyzeBonds($doc);

# 创建交联原子集合
XlinkSet($doc);

$textDoc->Append("\n##############################################################\n\n");
$textDoc->Append("计算完成\n");
$textDoc->Append("系统中有 $xlinkCounter 个交联\n");
$textDoc->Append(sprintf "最终转化率 %.1f%%\n", $conversion);
$textDoc->Append("完成的电场动力学和交联循环数: $cycleCounter\n");
$textDoc->Append("几何优化总步数: $geomoptcounter\n");
$textDoc->Append("分子动力学总步数: $mdcounter\n");

# 报告总时间
my $time_hr = (time-$^T)/3600;
$textDoc->Append(sprintf "\n总时间 %.2f 小时\n", $time_hr);
$textDoc->Append("\n##############################################################\n");
$textDoc->Save;
Documents->SaveAll;

##########################################################################################################
#
#		主程序结束
#
##########################################################################################################

# 转化率定义为至少反应一次的单体（R1）反应性原子的百分比
sub calculateConversion
{
	my $doc1 = shift;
	# 计数已反应原子
	my $reactedMonomerAtoms  = 0;
	my $totalMonomerAtoms    = 0;
	foreach my $atom (@{$doc1->UnitCell->Atoms})
	{
		$reactedMonomerAtoms++  if ($atom->Name =~ /^$monomerReactiveAtom-\d/);
		$totalMonomerAtoms++    if ($atom->Name =~ /^$monomerReactiveAtom/);
	}

	my $conversion = 100.0 * $reactedMonomerAtoms / $totalMonomerAtoms;
	return $conversion;
}

#########################################################################################################

# 创建'not-R1'和'not-R2'集合
# 这些用于近距离接触计算，在集合排除模式下完成。
# 查找以下接触：
# (A) 单元格中的R1和R2原子
# (B)（不幸的是）单元格中的R1或R2原子与相邻单元格中的图像原子。

sub createReactiveAtomSets 
{
	my $doc = shift;

	$textDoc->Append("  createReactiveAtomSets\n");

	# 初始化反应性原子计数器	
	my $R1Counter = 0;
	my $R2Counter = 0;
	
	# 为原子创建两个Perl数组	
	my @notR1;
	my @notR2;

	# 基于原子名称创建两个集合
	# 一个包含除R1外的所有原子，另一个包含除R2外的所有原子
	my $atoms = $doc->UnitCell->Atoms;
	foreach my $atom (@$atoms) 
	{
		# 检查原子是否为反应性原子	
		if (isReactiveR1($atom)) 
		{					
			push (@notR2, $atom);
			$R1Counter++;		
		} 
		elsif (isReactiveR2($atom)) 
		{					
			push (@notR1, $atom);
			$R2Counter++;		
		} 
		else 
		{		
			push (@notR1, $atom);
			push (@notR2, $atom);
		}	
	}

	# 基于原子数组创建集合并隐藏它们
	my $notR1Set = $doc->CreateSet("notR1", \@notR1);
	my $notR2Set = $doc->CreateSet("notR2", \@notR2);
	$notR1Set->IsVisible = 0;
	$notR2Set->IsVisible = 0;

	$textDoc->Append("    $R1Counter 个反应性单体原子\n");
	$textDoc->Append("    $R2Counter 个反应性交联原子\n\n");
	$textDoc->Save;
		
	return ($doc, $R1Counter, $R2Counter);
}

#########################################################################################################

# 确定这是否是单体反应性原子
# 取决于是否使用react_multi。如果是，我们检查附着的氢。

sub isReactiveR1
{
	my $atom = shift;
	my $name = $atom->Name;
	if ($name =~ /^$monomerReactiveAtom/)
	{
		return TRUE if (not $react_multi_monomer and $name eq "$monomerReactiveAtom");
		if ($remove_condensation and $react_multi_monomer)
		{
			my ($nOH) = OHCount($atom);
			return TRUE if ($nOH > 0);
		}
		return TRUE if ($react_multi_monomer and HCount($atom) > 0);
	}
	return FALSE;
}

#########################################################################################################

# 确定这是否是交联剂反应性原子
# 取决于是否使用react_multi。如果是，我们检查附着的氢。

sub isReactiveR2
{
	my $atom = shift;
	my $name = $atom->Name;
	if ($name =~ /^$xlinkerReactiveAtom/)
	{
		return TRUE if (not $react_multi_xlinker and $name eq "$xlinkerReactiveAtom");
		if ($remove_condensation and $react_multi_xlinker)
		{
			my ($nOH) = OHCount($atom);
			return TRUE if ($nOH > 0);
		}
		return TRUE if ($react_multi_xlinker and HCount($atom) > 0);
	}
	return FALSE;
}

#########################################################################################################

# 这是为了更优雅地处理重启
# 返回名称如R1-38和R2-7的原子数

sub countReactedAtoms
{
	my ($doc1) = @_;
	my $nO = 0; my $nXL = 0;
	my $atoms = $doc1->UnitCell->Atoms;
	foreach my $atom (@$atoms) 
	{
		$nO++ if ($atom->Name =~ /^$monomerReactiveAtom-\d+$/);
		$nXL++ if ($atom->Name =~ /^$xlinkerReactiveAtom-\d+$/);
	}
	return ($nO, $nXL);
}

#########################################################################################################

# 交联R1-R2近距离接触原子，通过约束或真实键
# 重命名原子以防止进一步交联（除非multi选项开启）

sub createNewXlinks 
{
	my $doc1 = shift;
	my $distance = shift;
	
	my $t0 = time;
	$textDoc->Append("createNewXlinks\n");	

	# 更新反应性集合
	($doc1, my $R1Count, my $R2Count) = createReactiveAtomSets($doc1);
			
	# 使用集合排除法重新计算近距离接触，基于当前反应
	# 截断距离和更新集合
	Tools->BondCalculation->ChangeSettings([
		DistanceCriterionMode	=> "Absolute",
		ExclusionMode		=> "Set", 
		MaxAbsoluteDistance	=> $distance
	]);
	my $closeContacts = $doc1->CalculateCloseContacts;		
		
	# 删除与相邻单元格中非R原子的接触
	$textDoc->Append(sprintf "找到 %d 个近距离接触\n", $closeContacts->Count) if (DEBUG);
	foreach my $closeContact (@$closeContacts) 
	{
		my $atom1 = $closeContact->Atom1;
		my $atom2 = $closeContact->Atom2;	
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}
		# 删除除非两个原子都是反应性的且类型相反
		$closeContact->Delete unless ( isReactiveR1($atom1) and isReactiveR2($atom2) );
	}
	$textDoc->Append(sprintf "非R排除后: %d\n", $closeContacts->Count) if (DEBUG > 1);


	# 不允许"内部"交联，即同一单元之间的多个键
	$closeContacts = excludeIntra($doc1, $closeContacts) if ($closeContacts->Count);


	# 排除由列表中较早的交联使原子反应性无效的交联
	$closeContacts = excludeConcurrentIsreactive($doc1, $closeContacts) if ($closeContacts->Count);
	

	# 每轮仅允许原子有一个新交联（以保守为主）
	$closeContacts = excludeSameAtom($doc1, $closeContacts) if ($closeContacts->Count);

	
	# 排除超过转化率目标的任何交联
	$closeContacts = excludeConversionLimit($doc1, $closeContacts) if ($closeContacts->Count);


	# 将存活的近距离接触转换为约束或键
	my $newBondCounter = 0;
	
	foreach my $closeContact (@$closeContacts) 
	{
		my $atom1 = $closeContact->Atom1;
		my $atom2 = $closeContact->Atom2;
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}

		# 创建交联（真实或虚拟）				
		if ($UseRestraintBonds)
		{
			createNewBondRestraint($doc1, $atom1, $atom2);
		} 
		else 
		{
			createNewBond($doc1, $atom1, $atom2);
		}
		$newBondCounter++;
		
	}
	
	$textDoc->Append("  形成了 $newBondCounter 个链接\n");	
	
	EquilibrateRestraintXlinks($doc1) if ($UseRestraintBonds and $newBondCounter > 0);

	$textDoc->Append("  $newBondCounter 个新交联键\n\n");
	$textDoc->Save;
	
	# 调整氢原子
	$doc1->AdjustHydrogen;
	
	# 确保所有R原子有正确的配位
	testCoordination($doc1);
	
	# 重新生成电荷组
	if ($chargeMethod eq "Group based")
	{
		$doc1 = DivideAndConquer($doc1, $Forcite);
	}
	
	# 删除下一个循环的近距离接触和集合	
	$doc1->UnitCell->CloseContacts->Delete;
	$doc1->UnitCell->Sets->Delete;
	
	# 几何优化
	$textDoc->Append("  ");
	ForciteGeomOpt($doc1, 2000);
	
	# 报告时间
	$textDoc->Append(sprintf "\ncreateNewXlinks花费了 %d 秒\n\n", time-$t0); 
	$textDoc->Save;

	return ($newBondCounter);
}

##############################################################################################################
# 测试所有R原子是否有正确数量的键

sub testCoordination
{
	my $doc2 = shift;
	my %Coord = (
		"C" => 4,
		"N" => 3,
		"O" => 2,
		"Si" => 4
		);
	my $nmatch = 0; my $nmismatch = 0;
	foreach (@{$doc2->UnitCell->Atoms})
	{
		next unless ($_->Name =~ /^$monomerReactiveAtom/ or $_->Name =~ /^$xlinkerReactiveAtom/);
		next unless (exists $Coord{$_->ElementSymbol});
		my $expectedCoordination = $Coord{$_->ElementSymbol};
		my $actualCoordination = calcCoord($_);
		if ($expectedCoordination == $actualCoordination)
		{
			$nmatch++;
		}
		else
		{
			$nmismatch++;
			$textDoc->Append(sprintf "配位不匹配: 名称 %s, 元素 %s, 预期 %.1f, 实际 %.1f\n", 
				$_->Name, $_->ElementSymbol, $expectedCoordination, $actualCoordination)
				if (DEBUG > 1);
		}
	}
	if ($nmismatch)
	{
		$textDoc->Append(sprintf "警告：%d 个（共 %d 个）反应性原子存在配位问题\n", 
			$nmismatch, $nmatch+$nmismatch);
	}
	else
	{
		$textDoc->Append("$nmatch 个原子的配位测试通过\n") if (DEBUG);
	}
}

##############################################################################################################

sub calcCoord
{
	my $atom = shift;
	my $coord = 0;
	foreach (@{$atom->Bonds})
	{
		$coord++ if ($_->BondType eq "Single");
		$coord+2 if ($_->BondType eq "Double");
		$coord+3 if ($_->BondType eq "Triple");
		$coord+1.5 if ($_->BondType eq "Aromatic");
		$coord+1.5 if ($_->BondType eq "Partial double");
	}
	return $coord;
}

##############################################################################################################
# 删除内部交联
# 内部交联定义为同两个原子或一元片段之间的第二次交联

sub excludeIntra
{
	my $doc2 = shift;
	my $closeContacts = shift;
	
	if ($prevent_intraxlinks and $noMol) 
	{
		my @tmpbonds = ();
		# 这里我们只防止已经通过3个或更少键路径长度链接在一起的原子之间的交联
		foreach my $closeContact (@$closeContacts) 
		{
			my $atom1 = $closeContact->Atom1;
			my $atom2 = $closeContact->Atom2;
			if ( alreadyLinked($atom1, $atom2) )
			{
				$closeContact->Delete;
			}
			else
			{
				# 创建临时键以防止并发内部交联
				my $bond = $doc2->CreateBond($atom1, $atom2, "Single", [Name=>"tmpbond",Color=>RGB(128,255,0)]);
				push @tmpbonds, $bond;
			}
		}
		foreach (@tmpbonds)
		{
			$_->Delete;
		}
	} # 基于路径的内部交联结束
	
	elsif ($prevent_intraxlinks and not $noMol)
	{
		my $intra1Count=0;
		foreach my $closeContact (@$closeContacts) 
		{
			my $atom1 = $closeContact->Atom1;
			my $atom2 = $closeContact->Atom2;
			my $mol1; my $mol2;
			eval{ 
				$mol1 = $atom1->Ancestors->Molecule->Name; 
				$mol2 = $atom2->Ancestors->Molecule->Name;
				foreach my $mol (@{$xconnect{$mol1}})
				{
					if ($mol eq $mol2)
					{
						$closeContact->Delete;
						$intra1Count++;
						last;
					}
				}
			};
		}
		

		# 排除并发内部交联
		my $intra2Count=0;
		my %xconnect_tmp;
		foreach my $closeContact (@$closeContacts) 
		{
			# 测试这个近距离接触是否与任何先前的冲突
			my $isIntra = FALSE;
			my $atom1 = $closeContact->Atom1;
			my $atom2 = $closeContact->Atom2;
			my $mol1; my $mol2;
			eval{ 
				$mol1 = $atom1->Ancestors->Molecule->Name; 
				$mol2 = $atom2->Ancestors->Molecule->Name;
				foreach my $mol (@{$xconnect_tmp{$mol1}})
				{
					if ($mol eq $mol2)
					{
						$closeContact->Delete;
						$intra2Count++;
						$isIntra = TRUE;
						last;
					}
				}
				# 将此近距离接触添加到临时xconnect表
				if (not $isIntra)
				{
					push @{$xconnect_tmp{$mol1}}, $mol2;
					push @{$xconnect_tmp{$mol2}}, $mol1;
				}
			};
		}

		$textDoc->Append(sprintf "删除了 %d 个先前和 %d 个并发内部近距离接触\n", $intra1Count, $intra2Count) 
			if (DEBUG > 2);
		
	} # 基于分子的内部交联结束
	
	$textDoc->Append(sprintf "内部排除后: %d 个近距离接触\n", $closeContacts->Count) if (DEBUG > 1);
	return $closeContacts;
}


##############################################################################################################
# 删除并发-isreactive交联
# 交联可能影响其组成原子的isreactive状态
# 以下排除了"先前"并发交联会使原子的反应性关闭的情况

sub excludeConcurrentIsreactive
{
	my $doc2 = shift;
	my $closeContacts = shift;
	
	
	# 创建临时文档以测试交联方案
	my $tmpdoc = $doc2->SaveAs("CrosslinkTest.xsd");
	my $tmpCC = $tmpdoc->UnitCell->CloseContacts;
	$textDoc->Append("警告：近距离接触数量不匹配\n") if ($tmpCC->Count != $closeContacts->Count);
	
	# 同步每个文档中的近距离接触
	my $nAsync=0;
	my @cclist = ();
	for (my $i=0; $i < $closeContacts->Count; $i++)
	{
		my $name = sprintf("cc%d", $i+1);
		push @cclist, $name;
		my $ccA = $closeContacts->[$i];
		my $ccB = $tmpCC->[$i];
		$ccA->Name = $name;
		$ccB->Name = $name;
		# 为每个近距离接触创建唯一ID
		my $ccAtest = sprintf("X1%f_X2%f", $ccA->Atom1->X, $ccA->Atom2->X);
		my $ccBtest = sprintf("X1%f_X2%f", $ccB->Atom1->X, $ccB->Atom2->X);
		$nAsync++ if ($ccAtest ne $ccBtest);
	}
	$textDoc->Append("警告：$nAsync 个近距离接触不匹配\n") if ($nAsync);
	
	# 循环遍历近距离接触，优先处理列表中较早的
	my $nExcluded=0; my $nImplicitExcluded=0;
	foreach my $ccname (@cclist) 
	{
		$textDoc->Append("CC $ccname") if (DEBUG > 3);
		my $cc = $closeContacts->Item($ccname);
		
		# 近距离接触中的原子可能已被删除
		my $closeContact;
		eval { $closeContact = $tmpCC->Item($ccname); };
		if ($@) 
		{
			# 如果原子被删除（例如因为缩聚反应移除了OH）
			# 那么这个近距离接触被隐式删除。所以我们需要在真实结构中排除它。
			$cc->Delete;
			$nImplicitExcluded++;
			$textDoc->Append(" 隐式排除\n") if (DEBUG > 3);
			next;
		}
		
		# 获取原子，单体原子在前
		my $atom1; my $atom2;
		$atom1 = $closeContact->Atom1;
		$atom2 = $closeContact->Atom2;
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}
		

		# 它们是否仍然具有反应性？
		if (isReactiveR1($atom1) and isReactiveR2($atom2))
		{
			$textDoc->Append(" 创建测试键。") if (DEBUG > 3);
			# 创建临时键以在下一次迭代中返回正确的isReactive状态
			createNewBond($tmpdoc, $atom1, $atom2, TRUE);
			$tmpdoc->AdjustHydrogen;
		}
		else
		{
			$textDoc->Append(" 不具反应性，删除 $ccname。") if (DEBUG > 3);
			$cc->Delete;
			$closeContact->Delete;
			$nExcluded++;
		}
		$textDoc->Append(sprintf "CC %d testCC %d\n", $closeContacts->Count, $tmpCC->Count) if (DEBUG > 3);
	}
	$textDoc->Append(sprintf " 由于并发-isreact规则删除了 %d 个（%d 个显式和 %d 个隐式）接触\n", 
		$nExcluded+$nImplicitExcluded, $nExcluded, $nImplicitExcluded) if (DEBUG > 2);
	$textDoc->Append(sprintf "并发-isreactive排除后: %d 个近距离接触\n", $closeContacts->Count) 
		if (DEBUG > 1);
	$tmpdoc->Delete;
	return $closeContacts;
}


##############################################################################################################
# 每轮只允许原子有一个新交联（以保守为主）

sub excludeSameAtom
{
	my $doc2 = shift;
	my $closeContacts = shift;
	
	my $lockedAtoms = $doc2->UnitCell->Beads->Atoms; # 空集合
	foreach my $closeContact (@$closeContacts) 
	{
		my $atom1 = $closeContact->Atom1;
		my $atom2 = $closeContact->Atom2;	
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}
		if ($atom1->Name =~ /_LOCKED$/ or $atom2->Name =~ /_LOCKED$/)
		{
			$closeContact->Delete;
			next;
		}
		# 锁定这些原子，使它们不再反应
		$atom1->Name .= "_LOCKED";
		$atom2->Name .= "_LOCKED";
		$lockedAtoms->Add($atom1);
		$lockedAtoms->Add($atom2);
	}
	# 解锁原子
	foreach my $atom (@$lockedAtoms)
	{
		$atom->Name =~ s/_LOCKED$//;
	}
	$textDoc->Append(sprintf "同一原子排除后: %d 个近距离接触\n", $closeContacts->Count) 
		if (DEBUG > 1);
	return $closeContacts;
}

##############################################################################################################
# 删除任何会超过转化率目标的交联
# 我们在单独的文档中模拟键合，因为例如缩聚可能意味着在每次交联后必须重新计算转化率（例如R1原子可能被删除）

sub excludeConversionLimit
{
	my $doc2 = shift;
	my $closeContacts = shift;
	
	# 创建临时文档以测试交联方案
	my $tmpdoc = $doc2->SaveAs("CrosslinkTest2.xsd");
	my $tmpCC = $tmpdoc->UnitCell->CloseContacts;
	$textDoc->Append("警告：近距离接触数量不匹配\n") if ($tmpCC->Count != $closeContacts->Count);
	
	# 同步每个文档中的近距离接触
	my $nAsync=0;
	my @cclist = ();
	for (my $i=0; $i < $closeContacts->Count; $i++)
	{
		my $name = sprintf("cc%d", $i+1);
		push @cclist, $name;
		my $ccA = $closeContacts->[$i];
		my $ccB = $tmpCC->[$i];
		$ccA->Name = $name;
		$ccB->Name = $name;
		# 为每个近距离接触创建唯一ID
		my $ccAtest = sprintf("X1%f_X2%f", $ccA->Atom1->X, $ccA->Atom2->X);
		my $ccBtest = sprintf("X1%f_X2%f", $ccB->Atom1->X, $ccB->Atom2->X);
		$nAsync++ if ($ccAtest ne $ccBtest);
	}
	$textDoc->Append("警告：$nAsync 个近距离接触不匹配\n") if ($nAsync);
	
	# 循环遍历近距离接触，优先处理列表中较早的
	my $nLimit = -1;
	for (my $i=0; $i<@cclist; $i++)
	{
		my $ccname = $cclist[$i];
		my $cc = $closeContacts->Item($ccname);
		my $closeContact = $tmpCC->Item($ccname);
		
		# 获取原子，单体原子在前
		my $atom1; my $atom2;
		$atom1 = $closeContact->Atom1;
		$atom2 = $closeContact->Atom2;
		if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
		{
			$atom1 = $closeContact->Atom2;
			$atom2 = $closeContact->Atom1;
		}
		
		# 更新临时文档中的键合
		createNewBond($tmpdoc, $atom1, $atom2, TRUE);
		$tmpdoc->AdjustHydrogen;
		
		my $tmpConversion = calculateConversion($tmpdoc);
		if ($tmpConversion > $conversionTarget)
		{
			$nLimit = $i;
			last;
		}
	}
	$tmpdoc->Delete;
	
	# 现在删除限制以上的所有接触
	# 这里我们允许超过限制一个，以触发主循环中脚本的结束
	if ($nLimit >= 0)
	{
		for (my $i=$nLimit+1; $i<@cclist; $i++)
		{
			my $ccname = $cclist[$i];
			my $cc = $closeContacts->Item($ccname);
			$cc->Delete;
		}
	}
	$textDoc->Append(sprintf "转化率限制后: %d 个近距离接触\n", $closeContacts->Count) 
		if (DEBUG > 1);
	return $closeContacts;
}


#########################################################################################################
# 检查两个原子是否由3个或更少的键分隔
sub alreadyLinked
{
	my ($atom1, $atom2) = @_;
	my $atom2name_orig = $atom2->Name;
	my $atom2name = "atom2_alreadyLinked";
	$atom2->Name = $atom2name;
	my $atom1name = "atom1_alreadyLinked";
	my $atom1name_orig = $atom1->Name;
	$atom1->Name = $atom1name;
	my $onebondconnection = FALSE;
	my $twobondconnection = FALSE;
	my $threebondconnection = FALSE;
	foreach my $atomA (@{$atom1->AttachedAtoms})
	{
		my $atomAname = "atomA_alreadyLinked";
		my $atomAname_orig = $atomA->Name;
		$atomA->Name = $atomAname;
		$onebondconnection = TRUE if ($atomA->Name eq $atom2name);
		foreach my $atomB (@{$atomA->AttachedAtoms})
		{
			next if ($atomB->Name eq $atom1name);
			$twobondconnection = TRUE if ($atomB->Name eq $atom2name);
			foreach my $atomC (@{$atomB->AttachedAtoms})
			{
				next if ($atomC->Name eq $atomAname);
				$threebondconnection = TRUE if ($atomC->Name eq $atom2name);
			}
		}
		$atomA->Name = $atomAname_orig;
	}
	$atom1->Name = $atom1name_orig;
	$atom2->Name = $atom2name_orig;
	return TRUE if ($onebondconnection or $twobondconnection or $threebondconnection);
	return FALSE;
}

#########################################################################################################

# 在两个原子之间施加谐波距离约束

sub createNewBondRestraint
{	
	my ($doc2, $atom1, $atom2) = @_;	
	
	# 测量反应原子之间的距离			
	my $distance = $doc2->CreateDistance([$atom1, $atom2]);
	
	# 创建约束并设置初始平衡键长和力常数
	# （这些在平衡例程中适当更新）					
			
	my $restraint = $distance->CreateRestraint("Harmonic");
	$restraint->HarmonicForceConstant = 0;
	$restraint->HarmonicMinimum = $distance->Distance;
}

#########################################################################################################

# 创建新的交联键并更改链接的显示样式和名称
# 还调整化学：环氧环和缩聚物
# 期望$atom1来自单体，$atom2来自交联剂

sub createNewBond
{	
	my $doc1 = shift;
	my $atom1 = shift;
	my $atom2 = shift;
	my $test = shift;

	# 打开环氧环	
	deleteEpoxyBond($atom1) if ($openRing);

	# 移除缩聚OH基团	
	if ($remove_condensation) 
	{
		my $oh1 = CondenseLink($atom1, $test);
		my $oh2 = CondenseLink($atom2, $test);
		if (DEBUG > 0)
		{
			$textDoc->Append("    警告：CondenseLink从单体和交联剂中移除了OH基团\n") 
				if ($oh1 and $oh2);
			$textDoc->Append("    警告：CondenseLink没有从单体或交联剂中移除OH基团\n") 
				if (not $oh1 and not $oh2);
		}
	}

	# 将聚氨酯中的双键转换为单键	
	Polyurethane($atom1) if ($polyurethane);

	# 创建新键
    $xlinkCounter++ unless ($test);
	my $newBond = $doc1->CreateBond($atom1, $atom2, "Single", ([Name => "xlink-".$xlinkCounter]));        
    
    # 设置创建的键的显示样式为球棍式       
    $atom1->Style = "Ball and stick";
    $atom2->Style = "Ball and stick";
    
    # 在每个原子名称后附加交联索引（不会影响未来的交联）
    $atom1->Name .= "-".$xlinkCounter;
    $atom2->Name .= "-".$xlinkCounter;

	# 更新连接表
	if ($prevent_intraxlinks and not $test)
	{
		my $mol1; my $mol2;
		eval{ 
			$mol1 = $atom1->Ancestors->Molecule->Name;
			$mol2 = $atom2->Ancestors->Molecule->Name;
			push @{$xconnect{$mol1}}, $mol2;
			push @{$xconnect{$mol2}}, $mol1;
		};
	}
    
    $textDoc->Append( sprintf "    在 %s 和 %s 之间创建键 \n\n", $atom1->Name, $atom2->Name)
    	unless ($test);
}

#########################################################################

# 计数附着的氢

sub HCount
{
	my $atom = shift;
	my $n = 0;
	foreach (@{$atom->AttachedAtoms})
	{
		$n++ if ($_->ElementSymbol eq "H");
	}
	return $n;
}

#########################################################################

# 计数附着的OH基团并返回第一个
# 逻辑规则：
# (A) 第一个附着的原子必须是氧并有两个键
# (B) 第二个附着的原子必须是氢
# 这两个规则应确保只找到适当的OH

sub OHCount
{
	my $atom = shift;
	my $n = 0;
	my $Oatom;
	my $Hatom;
	foreach my $atom1 (@{$atom->AttachedAtoms})
	{
		if ($atom1->ElementSymbol eq "O" and $atom1->Bonds->Count == 2)
		{
			foreach my $atom2 (@{$atom1->AttachedAtoms})
			{
				if ($atom2->ElementSymbol eq "H")
				{
					$n++;
					if ($n == 1)
					{
						# 对于第一个OH，返回原子以便我们可以删除它们
						$Oatom = $atom1;
						$Hatom = $atom2;
					}
				}
			}
		}
	}
	return ($n, $Oatom, $Hatom);
}

#########################################################################
# 识别并移除附着到给定原子的OH基团
# 如果附着有多个OH基团，只删除第一个

sub CondenseLink
{
	my $xlinkAtom = shift;
	my $test = shift;
	my $status = 0;
	
	my ($nOH, $Oatom, $Hatom) = OHCount($xlinkAtom);
	if ($nOH > 0)
	{
		$Oatom->Delete;
		$Hatom->Delete;
		$status = 1;
		$textDoc->Append( sprintf "  CondenseLink: 从 %s 删除OH基团\n", $xlinkAtom->Name)
			if (DEBUG > 0 and not $test);
	}
	
	return $status;
}

#######################################################################################################################

# 删除指定原子和$deleteAtom之间的键

sub deleteEpoxyBond 
{
	my $epoxyatom = shift;
	
	my $n = 0;
	foreach my $atom (@{$epoxyatom->AttachedAtoms}) 
	{
		if ($atom->ElementSymbol eq "$deleteAtom")
		{
			foreach my $bond (@{$atom->Bonds}) 
			{
				if ($bond->Atom1->Name eq $epoxyatom->Name or $bond->Atom2->Name eq $epoxyatom->Name)
				{
					$textDoc->Append(
						sprintf "    删除环氧键，介于 %s 和 %s\n",
						$bond->Atom1->Name, $bond->Atom2->Name ) 
					if (DEBUG);
					$bond->Delete;
					$n++;
				} 
			}
		}
	}
	warn "deleteEpoxyBond: 删除了 $n 个键" unless ($n == 1);
}

#######################################################################################################################

# 聚氨酯转换：在异氰酸酯（O=C=N）基团中将双键改为单键

sub Polyurethane 
{
	my $atom0 = shift;
	
	# 首先验证它是异氰酸酯
	my $isIsocyanate = FALSE;
	
	# ~C~ ??
	unless ($atom0->ElementSymbol eq 'C' and $atom0->Bonds->Count == 2)
	{
		warn "警告 无效的聚氨酯基团，代码 1\n";
		return $isIsocyanate;
	}
	
	
	# N~x~O
	if ($atom0->AttachedAtoms->[0]->ElementSymbol eq 'O' and $atom0->AttachedAtoms->[1]->ElementSymbol eq 'N')
	{
	}
	elsif ($atom0->AttachedAtoms->[0]->ElementSymbol eq 'N' and $atom0->AttachedAtoms->[1]->ElementSymbol eq 'O')
	{
	}
	else
	{
		warn "警告 无效的聚氨酯基团，代码 2\n";
		return $isIsocyanate;
	}
		
	# =x=
	my $bonds = $atom0->Bonds;
	my $bond1 = $bonds->[0];
	my $bond2 = $bonds->[1];
	if ($bond1->BondType ne 'Double' or $bond2->BondType ne 'Double')
	{
		warn "警告 无效的聚氨酯基团，代码 3\n";
		return $isIsocyanate;
	}

	# 好的，我们知道它是异氰酸酯，因为我们有原子C、N和O，以及一对双键
	# 现在找到我们需要更改的键
	$isIsocyanate = TRUE;
	my $Nbond = $bond1;
	$Nbond = $bond2 if ($bond2->Atom1->ElementSymbol eq 'N' or $bond2->Atom2->ElementSymbol eq 'N');
	
	# 将其更改为单键
	$Nbond->BondType = 'Single';
		
	$textDoc->Append(
		sprintf "    转换尿烷键，介于 %s 和 %s\n",
		$Nbond->Atom1->Name, $Nbond->Atom2->Name ) 
		if (DEBUG);

	return $isIsocyanate;
}

#########################################################################
# 通过交替几何优化和分子动力学步骤让结构保持健康
# 通过扰动当前结构，使原子轻微分开

sub optimizeAndPerturb
{
    my $doc4 = shift;
    my $haveCharge = shift || 0;
    $textDoc->Append("optimizeAndPerturb\n");
    
    ForciteGeomOpt($doc4, 500);

    # 短暂的高温，以重新分配原子位置
    # 注意：应用电场（如果开启）
    my $steps = (2 * PICO_TO_FEMTO / $timeStep);
    my $results = ForciteDynamics($doc4, $steps, "NVE", (Temperature => $xlinkTemperature + 200));

    # 使用电场进行一次短暂动力学来重新调整原子位置
    if ($useElectricField) {
        $textDoc->Append("  应用电场进行动力学重排\n");
        my $steps = (0.5 * PICO_TO_FEMTO / $timeStep);
        my $results = ForciteDynamics($doc4, $steps, $ensemble);
    }
    
    # 最终几何优化使键能合理
    ForciteGeomOpt($doc4, 1000);
    
    $textDoc->Save;
}

#########################################################################
# 这会将键Harmonic约束的力常数向与目标键长相匹配的方向增加
# 在几个阶段中这样做，同时运行动力学不断推拉原子

sub EquilibrateRestraintXlinks 
{
	my $doc = shift;
	
	$textDoc->Append("EquilibrateRestraintXlinks\n");
	
	# 如果激活，则根据元素设置目标XlinkLength
	# 这里通过向主要原子添加后缀，R1-12 -> R1_C-12	
	if (0)
	{
		# 此版本未使用，因为它会改变原子名称
		my %PairingTable = (
			"C_N" => 1.47, # 标准C-N键长度	
			"C_C" => 1.53, # 标准C-C键长度
			"C_O" => 1.42, # 标准C-O键长度
			"N_N" => 1.3,	  # 标准N-N键长度
			);

		my $nrm = 0;
		foreach my $atom (@{$doc->UnitCell->Atoms})
		{
			if ($atom->Name =~ /$monomerReactiveAtom/ or $atom->Name =~ /$xlinkerReactiveAtom/)
			{
				my $element = $atom->ElementSymbol;
				my $name = $atom->Name;
				$atom->Name =~ s/$monomerReactiveAtom/$monomerReactiveAtom\_$element/;
				$atom->Name =~ s/$xlinkerReactiveAtom/$xlinkerReactiveAtom\_$element/;
				$nrm++ if ($atom->Name ne $name);
			}
		}
		$textDoc->Append("重命名了 $nrm 个原子以包含元素符号\n");
	}
	
	# 计数约束数量
	my $nrestraints = 0;
	foreach my $dist (@{$doc->UnitCell->Distances}) 
	{
		foreach my $r (@{$dist->Restraints})
		{
			$nrestraints++ if (RestraintType($r) eq "distance");
		}
	}
	$textDoc->Append("  系统中有 $nrestraints 个约束\n");

	# 对于没有键的系统，此例程为空操作
	return if ($nrestraints == 0);

	my $startDist = 1.0;  	# MD皮秒
	my $stepDist = 1.0;		# MD皮秒
	my $targetForceConstant = $RestraintForceConstant;
	
	# 多个阶段：
	# 1) 增加键合势
	# 2) 允许动态进行，使原子移动到新的距离
	# 3) 锁定在适当的键长
	# 4) 移除距离约束，替换为键
	
	for (my $i=0; $i<$nRestraintBondingStages; $i++)
	{
		# 计算当前阶段的力常数
		my $percent = (1.0 * $i) / ($nRestraintBondingStages-1.0);
		my $forceConstant = $targetForceConstant * $percent;
		
		$textDoc->Append(sprintf("  **阶段%d, 目标%f, 当前约束%f\n", $i+1, $targetForceConstant, $forceConstant));
		
		# 更新所有约束
		foreach my $dist (@{$doc->UnitCell->Distances}) 
		{
			foreach my $r (@{$dist->Restraints})
			{
				if (RestraintType($r) eq "distance")
				{
					$r->HarmonicForceConstant = $forceConstant;
					
					# 使用单一键长，加速键合过程
					my $d = $dist->Distance;
					if (abs($d - $RestraintBondingTargetLength) > 0.01)
					{
						$textDoc->Append(sprintf "    距离 %.3f -> %.3f \n", $d, $RestraintBondingTargetLength);
					}
					$r->HarmonicMinimum = $RestraintBondingTargetLength;
				}
			}
		}
		
		# 放松结构，使原子能够移动到合适的键长（MD）
		my $relax_time = $relax_length; # 皮秒
		if ($relax_time > 0)
		{
			$textDoc->Append(sprintf "  放松结构 %.1f ps, 力常数 %.2f\n", 
				$relax_time, $forceConstant);
			
			# 使用几何优化和一点MD来优化结构
			ForciteGeomOpt($doc, 2000);
			
			# 使用控制动力学，注意电场（如果启用）
			my $steps = ($relax_time * PICO_TO_FEMTO / $timeStep);
			my $results = ForciteDynamics($doc, $steps, "NVE", (Temperature => $xlinkTemperature));
			
		}
	} # 下一阶段
	
	# 键位于适当位置，现在替换约束键
	foreach my $dist (@{$doc->UnitCell->Distances}) 
	{
		# 获取连接的原子
		eval {
			my $atom1 = $dist->Endpoints->[0];
			my $atom2 = $dist->Endpoints->[1];
			
			foreach my $r (@{$dist->Restraints})
			{
				if (RestraintType($r) eq "distance")
				{
					$r->Delete;
					
					# 创建真实的约束键并设置其长度
					my $bond = $doc->CreateBond($atom1, $atom2, "Single", ([Name => "xlink"]));
					my $bondName = sprintf("xlink-%d", $xlinkCounter);
					$bond->Name = $bondName;
					
					# 增加计数器，在具有多个键的系统中保持同步
					$xlinkCounter++;
				}	
			}
		};
		# 在eval中重命名原子
	}
	
	# 清理距离对象，因为约束已删除
	$doc->UnitCell->Distances->Delete;
	
	$textDoc->Append("  **约束键已替换为真实键的名称xlink-*\n");
	$textDoc->Save;
	
	# 几何优化以重新组织键
	ForciteGeomOpt($doc, 2000);

}

#########################################################################################################
# 确定约束类型的辅助函数

sub RestraintType
{
    my ($restraint) = @_;
    my $type = "unknown";
    eval {
        my $ancestors = $restraint->Ancestors;
        if ( $ancestors->Distance->Count ) {
            $type = "distance";
        }
        elsif ( $ancestors->Angle->Count ) {
            $type = "angle";
        }
        elsif ( $ancestors->Torsion->Count ) {
            $type = "torsion";
        }
    };
    return $type;
}

#######################################################################################################################
# 使用电场应用分子动力学，如果useElectricField为true

sub ForciteDynamics
{
    my $doc1 = shift;
    my $steps = shift;
    my $dynEnsemble = shift;
    my %args = @_;
    
    # 使用传入的参数修改默认动力学设置
    my @options = (
        NumberOfSteps => $steps,
        Ensemble => $dynEnsemble,
    );
    
    foreach my $key (keys %args) {
        push @options, $key, $args{$key};
    }
    
    # 电场设置
    if ($useElectricField) {
        push @options, (
            UseExternalElectricField => "Yes",
            ExternalElectricFieldStrength => $electricFieldStrength,
            ExternalElectricFieldDirectionX => $electricFieldX,
            ExternalElectricFieldDirectionY => $electricFieldY,
            ExternalElectricFieldDirectionZ => $electricFieldZ,
            ExternalElectricFieldEnvelopeType => "Step",
            ExternalElectricFieldAlternatingFrequency => 0,
            UseCounterElectricFieldForNetCharge => $counterElectricField
        );
        $textDoc->Append(sprintf("  应用电场 %.3f V/Å 在方向 [%d, %d, %d]\n", 
            $electricFieldStrength, $electricFieldX, $electricFieldY, $electricFieldZ));
    } else {
        push @options, UseExternalElectricField => "No";
    }
    
    $mdcounter += $steps;
    $textDoc->Append(sprintf "运行动力学: %d 步, 温度 = %.1f K\n", 
        $steps, $args{Temperature} || $xlinkTemperature);
    $textDoc->Save;
    
    return $Forcite->Dynamics($doc1, @options);
}

#######################################################################################################################
# 对文档应用几何优化

sub ForciteGeomOpt
{
    my $doc1 = shift;
    my $steps = shift;
    my $optMethod = shift || "Smart";
    
    $geomoptcounter += $steps;
    $textDoc->Append(sprintf "执行几何优化: %d 步, 方法 = %s\n", $steps, $optMethod);
    $textDoc->Save;
    
    my @options = (
        MaxIterations => $steps,
        OptimizationMethod => $optMethod,
        EnergyDriftPerAtom => 0.0001,
        MaxForce => 0.001,
        MaxStress => 0.001,
        MaxDisplacement => 0.01,
        MaxStressTolerance => 0.1
    );
    
    return $Forcite->GeometryOptimization($doc1, @options);
}

#######################################################################################################################
# 从动力学结果中获取热力学性质数据

sub getTrajTempAndPressure
{
    my $results = shift;
    my $row = shift;
    my $dist = shift;
    
    # 创建表格
    my $sheet;
    eval {
        $sheet = $statTable->Sheets("Thermo");
    };
    if ($@) {
        # 如果找不到，创建新工作表
        eval {
            $sheet = $statTable->InsertSheet($nstatsheets, "Thermo");
            $nstatsheets++;
            $sheet->ColumnHeading(1) = "Distance (Å)";
            $sheet->ColumnHeading(2) = "Temperature (K)";
            $sheet->ColumnHeading(3) = "Pressure (GPa)";
            $sheet->ColumnHeading(4) = "Volume (Å³)";
        };
        if ($@) {
            $textDoc->Append("无法创建Thermo工作表: $@\n");
            return;
        }
    }
    
    # 直接从$results获取能量数据，避免使用Trajectory属性
    my $energy;
    eval {
        # 尝试方法1：直接从results获取
        $energy = $results->Energy;
    };
    if ($@) {
        eval {
            # 尝试方法2：从最后一帧获取
            $energy = $results->EndFrame->Energy;
        };
        if ($@) {
            $textDoc->Append("无法获取热力学数据: $@\n");
            return;
        }
    }
    
    if (defined $energy) {
        # 确保工作表有足够的行
        eval {
            # 尝试获取当前行数
            my $rowCount = $sheet->RowCount || 0;
            # 如果当前行数小于要写入的行，添加足够的行
            if ($rowCount <= $row) {
                # 添加足够的行
                $sheet->InsertRows($rowCount + 1, $row - $rowCount + 1);
            }
            
            # 写入数据
            $sheet->Cell($row,1) = $dist;
            $sheet->Cell($row,2) = $energy->Temperature;
            $sheet->Cell($row,3) = $energy->Pressure / 10000.0; # 将压力从 atm 转换为 GPa
            $sheet->Cell($row,4) = $energy->Volume;
        };
        if ($@) {
            $textDoc->Append("写入Thermo数据时出错: $@\n");
        }
    }
}

#######################################################################################################################
# 从动力学结果中获取能量数据

sub getEnergies
{
    my $results = shift;
    my $row = shift;
    
    # 创建表格
    my $sheet;
    eval {
        $sheet = $statTable->Sheets("Energy");
    };
    if ($@) {
        # 如果找不到，创建新工作表
        eval {
            $sheet = $statTable->InsertSheet($nstatsheets, "Energy");
            $nstatsheets++;
            $sheet->ColumnHeading(1) = "Kinetic";
            $sheet->ColumnHeading(2) = "Potential";
            $sheet->ColumnHeading(3) = "NonBond";
            $sheet->ColumnHeading(4) = "vdW";
            $sheet->ColumnHeading(5) = "Electrostatic";
            $sheet->ColumnHeading(6) = "Valence";
            $sheet->ColumnHeading(7) = "Total";
        };
        if ($@) {
            $textDoc->Append("无法创建Energy工作表: $@\n");
            return;
        }
    }
    
    # 直接从$results获取能量数据，避免使用Trajectory属性
    my $energy;
    eval {
        # 尝试方法1：直接从results获取
        $energy = $results->Energy;
    };
    if ($@) {
        eval {
            # 尝试方法2：从最后一帧获取
            $energy = $results->EndFrame->Energy;
        };
        if ($@) {
            $textDoc->Append("无法获取能量数据: $@\n");
            return;
        }
    }
    
    if (defined $energy) {
        # 确保工作表有足够的行
        eval {
            # 尝试获取当前行数
            my $rowCount = $sheet->RowCount || 0;
            # 如果当前行数小于要写入的行，添加足够的行
            if ($rowCount <= $row) {
                # 添加足够的行
                $sheet->InsertRows($rowCount + 1, $row - $rowCount + 1);
            }
            
            # 写入数据
            $sheet->Cell($row,1) = $energy->KineticEnergy;
            $sheet->Cell($row,2) = $energy->PotentialEnergy;
            $sheet->Cell($row,3) = $energy->NonBondEnergy;
            $sheet->Cell($row,4) = $energy->VDWEnergy;
            $sheet->Cell($row,5) = $energy->ElectrostaticEnergy;
            $sheet->Cell($row,6) = $energy->ValenceEnergy;
            $sheet->Cell($row,7) = $energy->TotalEnergy;
        };
        if ($@) {
            $textDoc->Append("写入Energy数据时出错: $@\n");
        }
    }
}

#######################################################################################################################
# 分析键分布，并将结果存储在研究表中

sub analyzeBonds
{
    my $doc1 = shift;
    
    # 创建键长分布表
    my $bondLengthDoc = Documents->New("bond_length_distribution.std");
    $bondLengthDoc->ColumnHeading(1) = "Bond Type";
    $bondLengthDoc->ColumnHeading(2) = "Average Length (Å)";
    $bondLengthDoc->ColumnHeading(3) = "Std Dev (Å)";
    $bondLengthDoc->ColumnHeading(4) = "Min Length (Å)";
    $bondLengthDoc->ColumnHeading(5) = "Max Length (Å)";
    $bondLengthDoc->ColumnHeading(6) = "Count";
    
    # 收集不同键类型的总计
    my %bondTypes;
    my %bondLengths;
    
    foreach my $bond (@{$doc1->UnitCell->Bonds}) {
        my $type = $bond->BondType;
        my $length = $bond->Length;
        $bondTypes{$type}++;
        push @{$bondLengths{$type}}, $length;
    }
    
    # 统计并填充表格
    my $row = 0;
    foreach my $type (sort keys %bondTypes) {
        my @lengths = @{$bondLengths{$type}};
        my $count = scalar @lengths;
        
        # 计算统计数据
        my $sum = 0;
        my $min = $lengths[0];
        my $max = $lengths[0];
        
        foreach my $len (@lengths) {
            $sum += $len;
            $min = $len if $len < $min;
            $max = $len if $len > $max;
        }
        
        my $avg = $sum / $count;
        
        # 计算标准差
        my $sumSqDiff = 0;
        foreach my $len (@lengths) {
            $sumSqDiff += ($len - $avg) ** 2;
        }
        my $stdDev = sqrt($sumSqDiff / $count);
        
        # 填充表格
        $bondLengthDoc->Cell($row, 1) = $type;
        $bondLengthDoc->Cell($row, 2) = $avg;
        $bondLengthDoc->Cell($row, 3) = $stdDev;
        $bondLengthDoc->Cell($row, 4) = $min;
        $bondLengthDoc->Cell($row, 5) = $max;
        $bondLengthDoc->Cell($row, 6) = $count;
        
        $row++;
    }
    
    # 添加交联键信息
    my @xlinkLengths;
    foreach my $bond (@{$doc1->UnitCell->Bonds}) {
        if ($bond->Name =~ /^xlink/) {
            push @xlinkLengths, $bond->Length;
        }
    }
    
    if (@xlinkLengths) {
        my $count = scalar @xlinkLengths;
        my $sum = 0;
        my $min = $xlinkLengths[0];
        my $max = $xlinkLengths[0];
        
        foreach my $len (@xlinkLengths) {
            $sum += $len;
            $min = $len if $len < $min;
            $max = $len if $len > $max;
        }
        
        my $avg = $sum / $count;
        
        # 计算标准差
        my $sumSqDiff = 0;
        foreach my $len (@xlinkLengths) {
            $sumSqDiff += ($len - $avg) ** 2;
        }
        my $stdDev = sqrt($sumSqDiff / $count);
        
        # 填充表格
        $bondLengthDoc->Cell($row, 1) = "Crosslink";
        $bondLengthDoc->Cell($row, 2) = $avg;
        $bondLengthDoc->Cell($row, 3) = $stdDev;
        $bondLengthDoc->Cell($row, 4) = $min;
        $bondLengthDoc->Cell($row, 5) = $max;
        $bondLengthDoc->Cell($row, 6) = $count;
    }
    
    $bondLengthDoc->Save;
}


#######################################################################################################################
# 为交联分子创建一个集合

sub XlinkSet
{
    my $doc1 = shift;
    
    # 查找所有交联的原子
    my @atomList;
    
    foreach my $atom (@{$doc1->UnitCell->Atoms}) {
        if ($atom->Name =~ /($monomerReactiveAtom|$xlinkerReactiveAtom)-\d+/) {
            push @atomList, $atom;
        }
    }
    
    # 为这些原子创建集合
    if (@atomList) {
        my $set = $doc1->CreateSet("Crosslinked Atoms", \@atomList);
        $set->Color = RGB(255, 0, 0);  # 红色
    }
    
    # 查找所有交联键
    my @bondList;
    
    foreach my $bond (@{$doc1->UnitCell->Bonds}) {
        if ($bond->Name =~ /^xlink/) {
            push @bondList, $bond;
        }
    }
    
    # 为这些键创建集合
    if (@bondList) {
        my $set = $doc1->CreateSet("Crosslink Bonds", \@bondList);
        $set->Color = RGB(0, 0, 255);  # 蓝色
    }
}

#######################################################################################################################
# 分析片段分布

sub AnalyzeFragments
{
    my $doc1 = shift;
    my $dist = shift;
    my $row = shift;
    
    # 创建表格（如果尚不存在）
    my $sheet;
    eval {
        $sheet = $statTable->Sheets("Fragments");
    };
    if ($@) {
        # 如果找不到，创建新工作表
        eval {
            $sheet = $statTable->InsertSheet($nstatsheets, "Fragments");
            $nstatsheets++;
            $sheet->ColumnHeading(1) = "Distance (Å)";
            $sheet->ColumnHeading(2) = "Total Fragments";
            $sheet->ColumnHeading(3) = "Average Fragment Size (atoms)";
            $sheet->ColumnHeading(4) = "Largest Fragment (atoms)";
            $sheet->ColumnHeading(5) = "Crosslinks";
        };
        if ($@) {
            $textDoc->Append("无法创建Fragments工作表: $@\n");
            return;
        }
    }
    
    # 分析片段 - 使用UnitCell->Molecules获取分子列表
    my $fragments = $doc1->UnitCell->Molecules;
    my $totalFragments = $fragments->Count;
    my $xlinkCount = 0;
    
    foreach my $bond (@{$doc1->UnitCell->Bonds}) {
        $xlinkCount++ if ($bond->Name =~ /^xlink/);
    }
    
    # 计算片段大小统计
    my $totalAtoms = 0;
    my $largestFragment = 0;
    
    foreach my $fragment (@$fragments) {
        my $atomCount = $fragment->Atoms->Count;
        $totalAtoms += $atomCount;
        $largestFragment = $atomCount if $atomCount > $largestFragment;
    }
    
    my $avgFragmentSize = $totalFragments > 0 ? $totalAtoms / $totalFragments : 0;
    
    # 填充表格
    eval {
        # 尝试获取当前行数
        my $rowCount = $sheet->RowCount || 0;
        # 如果当前行数小于要写入的行，添加足够的行
        if ($rowCount <= $row) {
            # 添加足够的行
            $sheet->InsertRows($rowCount + 1, $row - $rowCount + 1);
        }
        
        # 写入数据
        $sheet->Cell($row, 1) = $dist;
        $sheet->Cell($row, 2) = $totalFragments;
        $sheet->Cell($row, 3) = $avgFragmentSize;
        $sheet->Cell($row, 4) = $largestFragment;
        $sheet->Cell($row, 5) = $xlinkCount;
    };
    if ($@) {
        $textDoc->Append("写入Fragments数据时出错: $@\n");
    }
}

#######################################################################################################################
# 初始化交联统计

sub Initialize_xlinkStatistics
{
    my $doc1 = shift;
    return if $noMol;
    
    # 需要实现这个函数，但对于简化版脚本可以留空
    $textDoc->Append("Molecule统计已禁用（noMol = TRUE）\n");
}

#######################################################################################################################
# 计算交联统计

sub xlinkStatistics
{
    my $doc1 = shift;
    my $dist = shift;
    my $row = shift;
    return if $noMol;
    
    # 如果启用了分子统计功能，这里需要实现完整的xlinkStatistics函数
    # 以下是基本框架
    
    # 初始化xlinker工作表
    my $sheet;
    eval {
        # 尝试获取现有工作表
        $sheet = $statTable->Sheets("Xlinkers");
    };
    if ($@) {
        # 创建新工作表
        eval {
            $statTable->ActiveSheetIndex = 0;
            $sheet = $statTable->Sheets(1); # 使用第一个工作表
            $sheet->Title = "Xlinkers";
            $sheet->ColumnHeading(0) = "Reaction Radius (A)";
            $sheet->ColumnHeading(1) = "xlinkers reacted";
            $sheet->ColumnHeading(2) = "Percent xlinkers reacted";
            $sheet->ColumnHeading(3) = "Intramolecular xlinks";
            $sheet->ColumnHeading(4) = "Reacted xlinker atoms per xlinker";
        };
        if ($@) {
            $textDoc->Append("无法初始化Xlinkers工作表: $@\n");
            return;
        }
    }
    
    # 分析与写入数据代码将根据需要实现
    # 示例：写入一些基本信息
    eval {
        # 确保行数充足
        my $rowCount = $sheet->RowCount || 0;
        if ($rowCount <= $row) {
            $sheet->InsertRows($rowCount + 1, $row - $rowCount + 1);
        }
        
        # 写入基本数据
        $sheet->Cell($row, 0) = $dist;
        $sheet->Cell($row, 1) = 0; # 示例：已反应的交联剂数
        $sheet->Cell($row, 2) = 0; # 示例：已反应交联剂百分比
        $sheet->Cell($row, 3) = 0; # 示例：分子内交联数
        $sheet->Cell($row, 4) = 0; # 示例：每个交联剂的已反应交联剂原子
    };
    if ($@) {
        $textDoc->Append("写入Xlinkers数据时出错: $@\n");
    }
    
    $textDoc->Append("分子统计功能已禁用（noMol = TRUE）或未完全实现\n");
}

#######################################################################################################################
# 分析最大键能量

sub maxBondEnergy
{
    my $doc1 = shift;
    my $dist = shift;
    my $row = shift;
    return unless $UseMaxBondEnergy;
    
    # 创建表格（如果尚不存在）
    my $sheet;
    eval {
        $sheet = $statTable->Sheets("MaxBondEnergy");
    };
    if ($@) {
        # 如果找不到，创建新工作表
        eval {
            $sheet = $statTable->InsertSheet($nstatsheets, "MaxBondEnergy");
            $nstatsheets++;
            $sheet->ColumnHeading(1) = "Distance (Å)";
            $sheet->ColumnHeading(2) = "Max Bond Length";
            $sheet->ColumnHeading(3) = "Bond Type";
            $sheet->ColumnHeading(4) = "Bond Atom 1";
            $sheet->ColumnHeading(5) = "Bond Atom 2";
        };
        if ($@) {
            $textDoc->Append("无法创建MaxBondEnergy工作表: $@\n");
            return;
        }
    }
    
    # 简化为仅分析键长
    my $maxLength = 0;
    my $maxBond;
    
    # 找到最长的键（通常与高能量相关）
    foreach my $bond (@{$doc1->UnitCell->Bonds}) {
        if ($bond->Length > $maxLength) {
            $maxLength = $bond->Length;
            $maxBond = $bond;
        }
    }
    
    # 填充表格
    if (defined $maxBond) {
        eval {
            # 尝试获取当前行数
            my $rowCount = $sheet->RowCount || 0;
            # 如果当前行数小于要写入的行，添加足够的行
            if ($rowCount <= $row) {
                # 添加足够的行
                $sheet->InsertRows($rowCount + 1, $row - $rowCount + 1);
            }
            
            # 写入数据
            $sheet->Cell($row, 1) = $dist;
            $sheet->Cell($row, 2) = $maxLength;
            $sheet->Cell($row, 3) = $maxBond->BondType;
            $sheet->Cell($row, 4) = $maxBond->Atom1->Name;
            $sheet->Cell($row, 5) = $maxBond->Atom2->Name;
        };
        if ($@) {
            $textDoc->Append("写入MaxBondEnergy数据时出错: $@\n");
        }
    }
}
