# MS软件完全卸载工具

这是一个用于彻底卸载BIOVIA Materials Studio及其组件的工具，通过自动化Geek Uninstaller软件，并清理注册表和残留文件，确保软件被完全卸载。

## 功能特点

- 自动搜索并找到所有BIOVIA相关程序
- 支持多个版本识别（2020、2021、2022等）
- 强制卸载程序，包括注册表清理
- 自动搜索并删除C盘中的残留文件
- 验证卸载结果，确保完全卸载

## 使用前准备

1. **管理员权限**：必须使用管理员权限运行本程序
2. **备份数据**：建议在卸载前备份重要数据
3. **关闭相关程序**：确保所有BIOVIA相关程序已关闭

## 使用方法

### 方法一：使用批处理文件（推荐）

1. 右键点击`运行卸载工具.bat`文件
2. 选择"以管理员身份运行"
3. 按照程序提示进行操作

### 方法二：直接运行Python脚本

1. 确保已安装Python及必要的库
2. 以管理员身份打开命令提示符
3. 导航到程序所在目录
4. 运行命令：`python Other+MS_Until.py`

### 方法三：使用打包的可执行文件

1. 运行`make_exe.py`生成可执行文件
2. 右键点击生成的`MS卸载工具.exe`
3. 选择"以管理员身份运行"

## 使用步骤详解

1. **启动程序**：程序将自动请求管理员权限
2. **捕获界面元素**：首次使用时，程序会询问是否需要捕获界面元素
   - 按照提示将鼠标放在相应位置，然后按Enter
   - 这些捕获的图像将用于自动识别界面元素
3. **搜索BIOVIA程序**：
   - 程序会自动打开Geek软件并搜索BIOVIA程序
   - 会保存搜索结果的截图，供用户确认
4. **卸载程序**：
   - 对每个找到的程序，按照提示逐一卸载
   - 程序会尝试自动点击相应按钮，如果失败会提示手动操作
5. **清理注册表**：自动清理注册表中的BIOVIA和Accelrys相关条目
6. **清理残留文件**：搜索并删除C盘中的所有相关文件
7. **验证结果**：再次打开Geek软件，确认所有程序已卸载

## 问题排查

- **如果程序无法启动**：确保以管理员权限运行
- **如果无法识别界面元素**：
  - 在"resources"目录下检查是否有模板图像
  - 重新运行程序并选择重新捕获界面元素
- **如果文件无法删除**：
  - 记下路径，稍后手动删除
  - 确认文件未被其他程序占用

## 系统要求

- Windows 10/11
- Python 3.6或更高版本（如果直接运行Python脚本）
- 依赖库：pyautogui, opencv-python, pillow

## 文件说明

- `Other+MS_Until.py`：主程序
- `运行卸载工具.bat`：启动批处理文件
- `make_exe.py`：打包程序
- `resources/`：存放界面元素模板和调试截图
- `geek.exe`：Geek Uninstaller软件

## 注意事项

- 本程序会删除所有BIOVIA相关文件，请确保这是您想要的操作
- 部分文件可能需要管理员权限才能删除
- 程序在操作过程中会保存截图，用于调试和验证 