import os
import sys
import subprocess

def ensure_installed(package):
    """确保指定的包已安装"""
    try:
        __import__(package)
    except ImportError:
        print(f"正在安装依赖包: {package}")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"{package} 安装完成")

def main():
    """打包程序为可执行文件"""
    # 确保必要的依赖包已安装
    dependencies = ["PyInstaller", "pyautogui", "opencv-python", "pillow"]
    for dep in dependencies:
        ensure_installed(dep)
    
    # 导入PyInstaller
    import PyInstaller.__main__
    
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 要打包的主脚本
    main_script = os.path.join(script_dir, "MS_Uninstaller_GUI.py")
    
    # 图标文件路径（如果有）
    icon_path = os.path.join(script_dir, "resources", "icon.ico")
    
    # 资源目录路径
    resources_dir = os.path.join(script_dir, "resources")
    
    # 创建资源目录（如果不存在）
    if not os.path.exists(resources_dir):
        os.makedirs(resources_dir)
    
    # 创建默认图标（如果不存在）
    if not os.path.exists(icon_path):
        try:
            # 创建一个简单的图标
            from PIL import Image, ImageDraw
            img = Image.new('RGBA', (256, 256), color=(255, 255, 255, 0))
            d = ImageDraw.Draw(img)
            
            # 绘制一个简单的圆形图标
            d.ellipse((20, 20, 236, 236), fill=(30, 144, 255, 255))
            d.ellipse((60, 60, 196, 196), fill=(255, 255, 255, 255))
            d.ellipse((80, 80, 176, 176), fill=(220, 20, 60, 255))
            
            # 保存为ICO格式
            img.save(icon_path, format='ICO')
            print(f"已创建默认图标: {icon_path}")
        except Exception as e:
            print(f"创建默认图标时出错: {e}")
            icon_path = ""
    
    # 确保geek.exe在打包目录中
    geek_path = os.path.join(script_dir, "geek.exe")
    if not os.path.exists(geek_path):
        print("警告: 在打包目录中未找到geek.exe")
        print(f"请确保将geek.exe复制到: {script_dir}")
        response = input("是否继续打包？(y/n): ")
        if response.lower() != 'y':
            print("打包已取消")
            return
    
    # 设置PyInstaller参数
    args = [
        main_script,
        "--name=MS卸载工具_GUI",
        "--onefile",
        "--windowed",  # 不显示控制台窗口
        "--add-data", f"{resources_dir};resources",
    ]
    
    # 添加图标（如果存在）
    if os.path.exists(icon_path):
        args.append(f"--icon={icon_path}")
    
    # 添加geek.exe（如果存在）
    if os.path.exists(geek_path):
        args.append("--add-data")
        args.append(f"{geek_path};.")
    
    # 添加隐藏导入
    hidden_imports = [
        "--hidden-import=cv2",
        "--hidden-import=PIL",
        "--hidden-import=PIL._imagingtk",
        "--hidden-import=PIL._tkinter_finder",
        "--hidden-import=tkinter",
        "--hidden-import=automation_utils"
    ]
    args.extend(hidden_imports)
    
    print("开始打包...")
    print(f"打包参数: {' '.join(args)}")
    
    try:
        PyInstaller.__main__.run(args)
        print("\n打包完成!")
        print(f"可执行文件位置: {os.path.join(script_dir, 'dist', 'MS卸载工具_GUI.exe')}")
    except Exception as e:
        print(f"打包过程中出错: {e}")

if __name__ == "__main__":
    print("=== MS软件卸载工具(GUI版)打包程序 ===")
    main() 