##################################################################################################################
# perl                                                                                                           #
#                                                                                                                #
# Author: DMPB                                                                                                   #
# Version: 1.1                                                                                                   #
# Tested on: Materials Studio 2020                                                                               #
#                                                                                                                #
# Required modules: Materials Visualizer, Forcite                                                                #
# This script aims to apply incremental strain to molecular systems and analyze the resulting stress response.   #
# Through simulation, it can obtain the stress-strain curve and elastic modulus of materials. The script provides#
# a convenient and efficient way to perform such analyses, allowing for the exploration of material mechanical   #
# properties under strain without the need for complex manual calculations.                                      #
#                                                                                                                #
# Date: 2023-03-28                                                                                               #
# Updated: 2024-06-25 - Added support for fixed atom set movement during strain                                  #
#                                                                                                                #
##################################################################################################################
#!perl
# 材料应力应变分析脚本
# 该脚本对分子系统施加递增应变并分析产生的应力响应
# 通过模拟可以获得材料的应力-应变曲线和弹性模量

use strict;
use warnings;  
use Getopt::Long;
use MaterialsScript qw(:all);

###########################################
# 用户可配置参数
###########################################

# 输入/输出设置
my $docName = "0";                       # 输入文档的基本名称（不带扩展名）
my $outputPrefix = "strain_analysis";    # 输出文件的前缀
my $keep_intermediate_files = "Yes";      # 是否保留中间文件 "Yes"或"No"

# 固定原子设置
my $handle_fixed_atoms = "Yes";          # 是否处理固定原子集 "Yes"或"No"
my $fixed_set_name = "Top";      # 固定原子集的名称

# 应变设置
my $numberStrains = 30;                  # 应变步骤的执行次数
my $strain_factor = 0.02;                # 每步的应变因子（百分比）
my $strain_Axis = "LengthC";             # 被应变的轴：LengthA (x轴), LengthB (y轴), LengthC (z轴)
my $strain_direction = "tensile";        # 应变类型："tensile"(拉伸)或"compressive"(压缩)

# 力场设置
my $ffName = "COMPASSIII";               # 所用的力场名称
my $quality = "Medium";                  # 整体质量设置
my $vdwSummationMethod = "Atom based";   # 设置非键合方法
my $coulombicSummationMethod = "Ewald";  # 静电相互作用求和方法

# 分子动力学设置
my $timeStep = 1;                        # MD运行的时间步长，单位为飞秒(fs)
my $numberSteps = 50000;                 # 每步的动力学步数
my $trajFrequency = 1000;                # 轨迹写入帧的频率

# 键设置
my $bond_length_limit = 1.76;            # 长度超过此值的键将被删除（模拟断键）
my $final_bond_length = 1.15;            # 最终重新计算的最大键长

# 热力学设置
my $temperature = 298;                   # 模拟温度(K)
my $thermostat = "NHL";             # 恒温器类型(Velocity Scale,Nose,Andersen,Berendsen,NHL)
my $ensemble = "NVT";                    # 系综类型

# 分析设置
my $calculate_elastic_modulus = 1;       # 是否计算弹性模量(1=是, 0=否)

###########################################
# 主脚本
###########################################

# 打印脚本信息
print "\n=============================================================\n";
print "Materials Studio应力-应变分析脚本\n";
print "=============================================================\n\n";
print "输入文档: $docName.xsd\n";
print "应变轴: $strain_Axis\n";
print "应变步数: $numberStrains\n";
print "每步应变因子: $strain_factor\n";
print "力场: $ffName\n\n";

# 打开输入文档
my $doc = $Documents{"$docName.xsd"};
if (!$doc) {
    die "错误: 无法打开文档 $docName.xsd。请检查文件是否存在。\n";
}

# 创建研究表格
my $newStudyTable = Documents->New("$outputPrefix\_stress_strain_summary.std");
my $st = $newStudyTable->ActiveSheet;
$st->ColumnHeading(0) = "Frame document";
$st->ColumnHeading(1) = "Strain";
$st->ColumnHeading(2) = "Stress XX (GPa)";
$st->ColumnHeading(3) = "Stress YY (GPa)";
$st->ColumnHeading(4) = "Stress ZZ (GPa)";

# 创建一个包含每个周期最后一帧的总结轨迹文档
my $summaryTrj = Documents->New("$outputPrefix\_finalFrames.xtd");

# 初始化Forcite模块
my $module = Modules->Forcite;
# 更改模块设置
$module->ChangeSettings([
    CurrentForcefield => "$ffName",
    TimeStep => $timeStep,
    '3DPeriodicElectrostaticSummationMethod' => "$coulombicSummationMethod",
    '****************************' => "$vdwSummationMethod",
    Quality => "$quality",
    Thermostat => "$thermostat",
    Ensemble3D => "$ensemble",
    Temperature => $temperature,
    WriteLevel => "Silent"
]);

# 运行初始平衡
print "运行初始平衡...\n";
my $results = $module->Dynamics->Run($doc, ([
    AssignForcefieldTypes => "No",
    ChargeAssignment => "Use current",
    InitialVelocities => "Random",
    NumberOfSteps => $numberSteps,
    TrajectoryFrequency => $trajFrequency
]));

my $trj = $results->Trajectory;
# 重命名平衡运行轨迹
$trj->Name = ("$outputPrefix\_equilibration");
print "完成初始平衡\n";

# 复制最后一帧，作为应变前的起始结构
my $newDoc = Documents->New("$outputPrefix\_0.xsd");
$trj->Trajectory->CurrentFrame = $trj->Trajectory->EndFrame;
$newDoc->CopyFrom($trj);

# 将初始结构添加到总结轨迹
$summaryTrj->Trajectory->AppendFramesFrom($newDoc);

# 读取要应变的轴的初始长度
my $initial_length = $newDoc->UnitCell->Lattice3D->$strain_Axis;
print "初始 $strain_Axis: $initial_length Å\n\n";

# 为第0行(初始状态)添加文档和应变值
$st->Cell(0, 0) = $newDoc;
$st->Cell(0, 1) = 0.0;  # 初始应变为0

# 获取初始状态的应力
my $initial_stress = $newDoc->SymmetrySystem->Stress;
my $initial_stressXX = $initial_stress->Eij(1, 1);
my $initial_stressYY = $initial_stress->Eij(2, 2);
my $initial_stressZZ = $initial_stress->Eij(3, 3);

# 将初始应力保存到研究表格
$st->Cell(0, 2) = $initial_stressXX;
$st->Cell(0, 3) = $initial_stressYY;
$st->Cell(0, 4) = $initial_stressZZ;

# 用于存储应力-应变数据的数组
my @strains;
my @stresses;

# 开始应变循环
print "开始应变分析...\n";
for (my $strain = 1; $strain <= $numberStrains; ++$strain) {
    print "步骤 $strain / $numberStrains: ";
    
    # 根据应变计算新长度
    my $strain_value;
    my $newstrainLength;
    
    if ($strain_direction eq "tensile") {
        $newstrainLength = $initial_length * (1 + $strain * $strain_factor);
        $strain_value = $strain * $strain_factor;
    } else {
        $newstrainLength = $initial_length * (1 - $strain * $strain_factor);
        $strain_value = -$strain * $strain_factor;
    }
    
    print "应用应变 $strain_value\n";
    $newDoc->UnitCell->Lattice3D->$strain_Axis = $newstrainLength;
    
    # 处理固定原子集，使其随应变同步移动
    if (lc($handle_fixed_atoms) eq "yes") {
        eval {
            if ($newDoc->UnitCell->Sets($fixed_set_name)) {
                my $fixedAtoms = $newDoc->UnitCell->Sets($fixed_set_name)->Atoms;
                my $fixedCount = @$fixedAtoms;
                print "  调整固定原子位置，总共 $fixedCount 个原子...\n";
                
                # 计算应变比例因子（与单元格相同）
                my $strain_value;
                if ($strain_direction eq "tensile") {
                    $strain_value = $strain * $strain_factor;
                } else {
                    $strain_value = -$strain * $strain_factor;
                }
                
                # 打印当前应变步骤信息
                print "  应用绝对应变值: $strain_value，当前单元格长度: $newstrainLength\n";
                
                # 对每个固定原子应用位移 - 仅设置分数坐标，让MS自动转换为实际坐标
                foreach my $atom (@$fixedAtoms) {
                    # 保持原子的分数坐标不变，MS会自动处理转换
                    # 这样可以确保原子与晶胞变形同步移动
                    my $fracXYZ = $atom->FractionalXYZ;
                    
                    # 由于已经更改了晶胞尺寸，保持分数坐标不变即可让原子随晶胞变化
                    # 不需要任何额外计算，MS会在内部正确转换为笛卡尔坐标
                    $atom->FractionalXYZ = $fracXYZ;
                }
                
                # 确保原子仍然保持固定状态
                $fixedAtoms->Fix("XYZ");
                print "  固定原子位置已调整完成\n";
            } else {
                print "  未找到固定原子集 '$fixed_set_name'，跳过位置调整\n";
            }
        };
        if ($@) {
            print "  处理固定原子时出错: $@\n";
        }
    }
    
    # 在此应变下运行动力学
    print "  运行分子动力学...\n";
    my $strainResults = $module->Dynamics->Run($newDoc, ([
        AssignForcefieldTypes => "Yes",
        ChargeAssignment => "Forcefield assigned",
        InitialVelocities => "Current",
        NumberOfSteps => $numberSteps,
        TrajectoryFrequency => $trajFrequency
    ]));
    
    my $straintrj = $strainResults->Trajectory;
    
    # 复制最后一帧到新文档
    my $newerDoc = Documents->New("$outputPrefix\_$strain.xsd");
    $straintrj->Trajectory->CurrentFrame = $straintrj->Trajectory->EndFrame;
    $newerDoc->CopyFrom($straintrj);
    
    # 删除轨迹以节省内存
    print "  清理临时文件...\n";
    $straintrj->Delete;
    
    # 删除超出键长限制的键（模拟断键）
    my $bonds = $newerDoc->UnitCell->Bonds;
    my $deleted_count = 0;
    foreach my $bond (@$bonds) {
        if ($bond->Length > $bond_length_limit) {
            $bond->Delete;
            $deleted_count++;
        }
    }
    print "  删除了 $deleted_count 个超过长度限制的键\n";
    
    # 将最后一帧添加到总结轨迹
    $summaryTrj->Trajectory->AppendFramesFrom($newerDoc);
    
    # 保存文档
    $summaryTrj->Save;
    $st->Cell($strain, 0) = $newerDoc;
    $st->Cell($strain, 1) = $strain_value;
    
    # 获取应力张量并计算应力（单位：GPa）
    my $stress = $newerDoc->SymmetrySystem->Stress;
    my $stressXX = $stress->Eij(1, 1);
    my $stressYY = $stress->Eij(2, 2);
    my $stressZZ = $stress->Eij(3, 3);
    
    # 存储用于弹性模量计算的数据
    push @strains, $strain_value;
    push @stresses, $stressZZ;  # 使用与应变轴对应的应力
    
    # 将应力数据保存到研究表格
    $st->Cell($strain, 2) = $stressXX;
    $st->Cell($strain, 3) = $stressYY;
    $st->Cell($strain, 4) = $stressZZ;
    
    print "  当前轴向应力: $stressZZ GPa\n\n";
    
    # 如果用户选择不保留中间文件，则删除它们
    if (lc($keep_intermediate_files) eq "No") {
        print "  根据设置，删除中间文件 $outputPrefix\_$strain.xsd\n";
        $newerDoc->Delete();
    }
    
    # 清理内存
    if ($strain < $numberStrains) {
        $newDoc = $newerDoc;
    }
}

print "应变计算完成。\n\n";

# 如果需要，计算弹性模量
if ($calculate_elastic_modulus) {
    print "计算弹性模量...\n";
    
    # 线性回归计算斜率（弹性模量）
    my $n = scalar @strains;
    
    # 增加一个更智能的方法来识别弹性区域
    # 方法1：通过计算最大R²来自动确定最佳线性区域
    
    my $max_r_squared = 0;
    my $best_elastic_range = 5; # 至少使用5个点
    my $best_modulus = 0;
    
    # 尝试不同的数据点范围，找出R²最高的范围
    for (my $range = 5; $range <= int($n * 0.5) && $range <= 15; $range++) {
        my $sum_x = 0;
        my $sum_y = 0;
        my $sum_xy = 0;
        my $sum_xx = 0;
        my $sum_yy = 0;
        
        for (my $i = 0; $i < $range; $i++) {
            $sum_x += $strains[$i];
            $sum_y += $stresses[$i];
            $sum_xy += $strains[$i] * $stresses[$i];
            $sum_xx += $strains[$i] * $strains[$i];
            $sum_yy += $stresses[$i] * $stresses[$i];
        }
        
        my $slope = ($range * $sum_xy - $sum_x * $sum_y) / ($range * $sum_xx - $sum_x * $sum_x);
        my $intercept = ($sum_y - $slope * $sum_x) / $range;
        
        # 计算R²
        my $ss_tot = 0;
        my $ss_res = 0;
        my $y_mean = $sum_y / $range;
        
        for (my $i = 0; $i < $range; $i++) {
            $ss_tot += ($stresses[$i] - $y_mean) ** 2;
            $ss_res += ($stresses[$i] - ($slope * $strains[$i] + $intercept)) ** 2;
        }
        
        my $r_squared = 1 - ($ss_res / $ss_tot);
        
        # 如果找到更好的拟合，则更新值
        if ($r_squared > $max_r_squared) {
            $max_r_squared = $r_squared;
            $best_elastic_range = $range;
            $best_modulus = $slope;
        }
    }
    
    my $elastic_range = $best_elastic_range;
    my $elastic_modulus = $best_modulus;
    
    print "自动确定的弹性区域： 前 $elastic_range 个点\n";
    print "拟合优度 R²: $max_r_squared\n";
    print "沿 $strain_Axis 轴的弹性模量: $elastic_modulus GPa\n\n";
    
    # 将弹性模量添加到研究表格
    $st->ColumnHeading(6) = "Elastic Modulus (GPa)";
    $st->Cell(1, 6) = $elastic_modulus;
    $st->ColumnHeading(7) = "R-squared";
    $st->Cell(1, 7) = $max_r_squared;
    $st->ColumnHeading(8) = "Elastic Points Used";
    $st->Cell(1, 8) = $elastic_range;
}

# 从轨迹文档中提取应力数据（保持与原始脚本的兼容性）
print "从轨迹中提取应力数据...\n";
my $numFrames = $summaryTrj->Trajectory->NumFrames;
print "分析的帧数: $numFrames\n";

for (my $counter = 1; $counter <= $numFrames; ++$counter) {
    $summaryTrj->Trajectory->CurrentFrame = $counter;
    
    my $stressdoc = Documents->New("stressdoc.xsd");
    $stressdoc->CopyFrom($summaryTrj);
    # 使用后立即处理
    my $stress = $stressdoc->SymmetrySystem->Stress;
    my $stressXX = $stress->Eij(1, 1);
    my $stressYY = $stress->Eij(2, 2);
    my $stressZZ = $stress->Eij(3, 3);

    # 验证并更新之前计算的值
    $st->Cell($counter-1, 2) = $stressXX;
    $st->Cell($counter-1, 3) = $stressYY;
    $st->Cell($counter-1, 4) = $stressZZ;

    # 立即清理这个临时文档
    $stressdoc->Discard;
    $stressdoc = undef;  # 确保引用被释放


}

print "应力数据提取完成。\n";

# 使用指定的最大键长重新计算键
print "使用最大键长 $final_bond_length Å 重新计算键...\n";
$summaryTrj->CalculateBonds(Settings(
    MaxBondLength => $final_bond_length));

# 简化最终清理（保留一个简单的最终检查）
print "\n执行最终清理检查...\n";
# 只有当用户选择不保留中间文件时才执行最终清理
if (lc($keep_intermediate_files) eq "no") {
    for (my $i = 0; $i <= $numberStrains; $i++) {
        my $temp_file = $Documents{"$outputPrefix\_$i.xsd"};
        if ($temp_file) {
            print "  清理遗漏文件: $outputPrefix\_$i.xsd\n";
            $temp_file->Close if $temp_file->can('Close');
            $temp_file->Delete();
        }
    }
} else {
    print "  根据设置，保留所有中间文件\n";
}

# 保存最终文档
$newStudyTable->Save;
$summaryTrj->Save;

print "\n分析完成。结果保存至 $outputPrefix\_stress_strain_summary.std\n";
print "总结轨迹保存至 $outputPrefix\_finalFrames.xtd\n";
print "=============================================================\n";