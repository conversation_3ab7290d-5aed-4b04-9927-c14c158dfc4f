#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CIF晶格转换工具 - 用于批量将CIF文件中的坐标轴变换，使垂直于001的面变为原晶胞的特定晶面指数
同时保持坐标轴正交。

支持的晶面指数：100、010、101、-101、110、-110、011、0-11
"""

import os
import sys
import argparse
import numpy as np
from collections import OrderedDict
import re
import glob
import shutil
from pathlib import Path
import math

# 检查是否安装了PyCifRW库
try:
    from CifFile import ReadCif, CifFile, CifBlock
except ImportError:
    print("错误: 未找到PyCifRW库。请使用命令安装:")
    print("pip install pycifrw")
    sys.exit(1)

# 定义晶面转换配置
PLANE_CONFIGS = {
    # 晶面: [A轴, B轴, C轴]
    "010": [[0, 0, 1], [1, 0, 0], [0, 1, 0]],
    "100": [[0, 1, 0], [0, 0, 1], [1, 0, 0]],
    "101": [[1, 0, -1], [0, 1, 0], [1, 0, 1]],
    "-101": [[1, 0, 1], [0, 1, 0], [1, 0, -1]],
    "110": [[-1, 1, 0], [0, 0, 1], [1, 1, 0]],
    "-110": [[1, 1, 0], [0, 0, 1], [-1, 1, 0]],
    "011": [[1, 0, 0], [0, -1, 1], [0, 1, 1]],
    "0-11": [[1, 0, 0], [0, 1, 1], [0, -1, 1]]
}

class CifTransformer:
    """CIF文件晶格转换类，用于处理晶体结构的坐标变换"""
    
    def __init__(self, input_path, output_dir, planes=None, prefix="", suffix=""):
        """
        初始化转换器
        
        参数:
            input_path (str): 输入CIF文件路径或包含CIF文件的目录
            output_dir (str): 输出目录路径
            planes (list): 要转换的晶面指数列表，如果为None则处理所有支持的晶面
            prefix (str): 输出文件名前缀
            suffix (str): 输出文件名后缀
        """
        self.input_path = input_path
        self.output_dir = output_dir
        self.prefix = prefix
        self.suffix = suffix
        
        # 如果未指定晶面，使用所有支持的晶面
        self.planes = planes if planes else list(PLANE_CONFIGS.keys())
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 找到要处理的所有CIF文件
        if os.path.isdir(input_path):
            self.cif_files = glob.glob(os.path.join(input_path, "*.cif"))
        else:
            self.cif_files = [input_path] if input_path.lower().endswith('.cif') else []
            
        if not self.cif_files:
            print(f"警告: 在 {input_path} 中未找到CIF文件")
    
    def process_all_files(self):
        """处理所有CIF文件"""
        print(f"找到 {len(self.cif_files)} 个CIF文件，开始处理...")
        
        for cif_file in self.cif_files:
            try:
                print(f"处理文件: {os.path.basename(cif_file)}")
                self.process_cif_file(cif_file)
            except Exception as e:
                print(f"处理文件 {cif_file} 时出错: {str(e)}")
        
        print("处理完成!")
    
    def process_cif_file(self, cif_file):
        """处理单个CIF文件"""
        basename = os.path.basename(cif_file)
        name_without_ext = os.path.splitext(basename)[0]
        
        # 读取CIF文件
        try:
            cf = ReadCif(cif_file)
            # 获取CIF块名称，通常为第一个块
            blockname = list(cf.keys())[0]
            block = cf[blockname]
        except Exception as e:
            print(f"读取CIF文件 {basename} 失败: {str(e)}")
            return
            
        # 获取晶胞参数
        try:
            cell_params = self._get_cell_parameters(block)
        except Exception as e:
            print(f"无法获取晶胞参数: {str(e)}")
            return
        
        # 对每个目标晶面执行转换
        for plane in self.planes:
            if plane not in PLANE_CONFIGS:
                print(f"警告: 不支持的晶面指数 {plane}，跳过")
                continue
                
            try:
                output_filename = f"{self.prefix}{name_without_ext}_{plane}{self.suffix}.cif"
                output_path = os.path.join(self.output_dir, output_filename)
                
                # 执行晶格转换
                self._transform_to_plane(cif_file, blockname, block, plane, cell_params, output_path)
                print(f"  转换为晶面 {plane} 完成: {output_filename}")
            except Exception as e:
                print(f"  转换为晶面 {plane} 失败: {str(e)}")
    
    def _get_cell_parameters(self, block):
        """从CIF块中提取晶胞参数"""
        # 提取晶胞长度和角度
        a = float(block['_cell_length_a'])
        b = float(block['_cell_length_b'])
        c = float(block['_cell_length_c'])
        alpha = float(block['_cell_angle_alpha'])
        beta = float(block['_cell_angle_beta'])
        gamma = float(block['_cell_angle_gamma'])
        
        # 将角度从度转换为弧度
        alpha_rad = math.radians(alpha)
        beta_rad = math.radians(beta)
        gamma_rad = math.radians(gamma)
        
        return {'a': a, 'b': b, 'c': c, 
                'alpha': alpha, 'beta': beta, 'gamma': gamma,
                'alpha_rad': alpha_rad, 'beta_rad': beta_rad, 'gamma_rad': gamma_rad}
    
    def _build_cell_matrix(self, cell_params):
        """构建晶胞矩阵，将分数坐标转换为笛卡尔坐标"""
        a, b, c = cell_params['a'], cell_params['b'], cell_params['c']
        alpha_rad, beta_rad, gamma_rad = cell_params['alpha_rad'], cell_params['beta_rad'], cell_params['gamma_rad']
        
        # 计算晶胞体积的中间值
        v = math.sqrt(1.0 - math.cos(alpha_rad)**2 - math.cos(beta_rad)**2 - math.cos(gamma_rad)**2 
                      + 2.0 * math.cos(alpha_rad) * math.cos(beta_rad) * math.cos(gamma_rad))
        
        # 构建转换矩阵
        cell_matrix = np.zeros((3, 3))
        cell_matrix[0, 0] = a
        cell_matrix[0, 1] = b * math.cos(gamma_rad)
        cell_matrix[0, 2] = c * math.cos(beta_rad)
        cell_matrix[1, 1] = b * math.sin(gamma_rad)
        cell_matrix[1, 2] = c * (math.cos(alpha_rad) - math.cos(beta_rad) * math.cos(gamma_rad)) / math.sin(gamma_rad)
        cell_matrix[2, 2] = c * v / math.sin(gamma_rad)
        
        return cell_matrix
    
    def _transform_to_plane(self, cif_file, blockname, block, plane, cell_params, output_path):
        """执行晶格转换到指定晶面"""
        # 读取原始文件以保留所有内容
        with open(cif_file, 'r') as f:
            original_content = f.read()
        
        # 获取变换矩阵
        axes = PLANE_CONFIGS[plane]
        transform_matrix = np.array(axes, dtype=float)
        
        # 构建原始晶胞矩阵
        cell_matrix = self._build_cell_matrix(cell_params)
        
        # 计算新的晶胞矩阵
        new_cell_matrix = np.dot(transform_matrix, cell_matrix)
        
        # 提取新的晶胞参数
        new_a = np.linalg.norm(new_cell_matrix[0])
        new_b = np.linalg.norm(new_cell_matrix[1])
        new_c = np.linalg.norm(new_cell_matrix[2])
        
        # 计算新的晶胞角度
        alpha_new = math.degrees(self._angle_between(new_cell_matrix[1], new_cell_matrix[2]))
        beta_new = math.degrees(self._angle_between(new_cell_matrix[0], new_cell_matrix[2]))
        gamma_new = math.degrees(self._angle_between(new_cell_matrix[0], new_cell_matrix[1]))
        
        # 检查是否正交
        is_orthogonal = self._check_orthogonal([alpha_new, beta_new, gamma_new])
        
        # 如果不是正交的，进行正交化处理
        if not is_orthogonal:
            print(f"  注意: 转换后的晶胞不正交 (alpha={alpha_new:.2f}°, beta={beta_new:.2f}°, gamma={gamma_new:.2f}°)")
            new_cell_matrix = self._orthogonalize_cell(new_cell_matrix)
            
            # 重新计算正交化后的参数
            new_a = np.linalg.norm(new_cell_matrix[0])
            new_b = np.linalg.norm(new_cell_matrix[1])
            new_c = np.linalg.norm(new_cell_matrix[2])
            alpha_new = 90.0
            beta_new = 90.0
            gamma_new = 90.0
        
        # 创建一个新的CIF文件
        new_cif = CifFile()
        new_block = CifBlock()
        new_cif[blockname] = new_block
        
        # 复制原始块的内容到新块
        for key in block.keys():
            if not key.startswith('_cell_') and not key.startswith('_atom_site_'):
                new_block[key] = block[key]
        
        # 更新晶胞参数
        new_block['_cell_length_a'] = str(new_a)
        new_block['_cell_length_b'] = str(new_b)
        new_block['_cell_length_c'] = str(new_c)
        new_block['_cell_angle_alpha'] = str(alpha_new)
        new_block['_cell_angle_beta'] = str(beta_new)
        new_block['_cell_angle_gamma'] = str(gamma_new)
        
        # 转换原子位置
        self._transform_atom_positions(block, new_block, transform_matrix, cell_matrix, new_cell_matrix)
        
        # 保存转换后的CIF文件
        new_cif.WriteOut(output_path)
        
        # 添加注释，说明转换信息
        with open(output_path, 'r+') as f:
            content = f.read()
            f.seek(0, 0)
            comment = f"# 此文件由CIF晶格转换工具生成\n"
            comment += f"# 原始文件: {os.path.basename(cif_file)}\n"
            comment += f"# 转换晶面: {plane}\n"
            comment += f"# 新坐标轴: A={axes[0]}, B={axes[1]}, C={axes[2]}\n"
            comment += f"# 是否进行正交化: {'是' if not is_orthogonal else '否'}\n\n"
            f.write(comment + content)
    
    def _angle_between(self, v1, v2):
        """计算两个向量之间的角度（弧度）"""
        v1_u = v1 / np.linalg.norm(v1)
        v2_u = v2 / np.linalg.norm(v2)
        return np.arccos(np.clip(np.dot(v1_u, v2_u), -1.0, 1.0))
    
    def _check_orthogonal(self, angles, tolerance=0.5):
        """检查晶胞是否正交（角度接近90度）"""
        return all(abs(angle - 90.0) < tolerance for angle in angles)
    
    def _orthogonalize_cell(self, cell_matrix):
        """对晶胞进行正交化处理，使轴相互垂直"""
        # 使用Graham-Schmidt正交化过程
        a = cell_matrix[0]
        b = cell_matrix[1]
        c = cell_matrix[2]
        
        # 保持a方向不变
        a_ortho = a.copy()
        
        # 使b垂直于a
        b_ortho = b - np.dot(b, a_ortho) / np.dot(a_ortho, a_ortho) * a_ortho
        
        # 使c垂直于a和b
        c_ortho = c - np.dot(c, a_ortho) / np.dot(a_ortho, a_ortho) * a_ortho
        c_ortho = c_ortho - np.dot(c_ortho, b_ortho) / np.dot(b_ortho, b_ortho) * b_ortho
        
        # 组合成新的正交矩阵
        ortho_matrix = np.vstack([a_ortho, b_ortho, c_ortho])
        
        return ortho_matrix
    
    def _transform_atom_positions(self, old_block, new_block, transform_matrix, old_cell_matrix, new_cell_matrix):
        """转换原子位置坐标"""
        # 获取原子位置的列名
        fract_cols = []
        for key in old_block.keys():
            if key.startswith('_atom_site_fract_'):
                fract_cols.append(key)
        
        # 获取所有原子位置数据
        atom_loop = old_block.GetLoop('_atom_site_label')
        loop_keys = atom_loop.keys()
        
        # 创建新的循环数据
        new_loop = CifBlock.LoopBlock()
        for key in loop_keys:
            new_loop.AddColumn(key)
        
        # 转换每个原子的位置
        for i in range(len(atom_loop['_atom_site_label'])):
            # 获取原子的分数坐标
            fract_coords = np.array([
                float(atom_loop['_atom_site_fract_x'][i]),
                float(atom_loop['_atom_site_fract_y'][i]),
                float(atom_loop['_atom_site_fract_z'][i])
            ])
            
            # 转换为笛卡尔坐标
            cart_coords = np.dot(old_cell_matrix.T, fract_coords)
            
            # 应用变换矩阵
            new_cart_coords = np.dot(transform_matrix, cart_coords)
            
            # 转换回分数坐标
            new_fract_coords = np.dot(np.linalg.inv(new_cell_matrix.T), new_cart_coords)
            
            # 将坐标限制在单位晶胞内 (0 <= x < 1)
            new_fract_coords = new_fract_coords % 1.0
            
            # 添加到新循环中
            loop_row = {}
            for key in loop_keys:
                if key == '_atom_site_fract_x':
                    loop_row[key] = f"{new_fract_coords[0]:.6f}"
                elif key == '_atom_site_fract_y':
                    loop_row[key] = f"{new_fract_coords[1]:.6f}"
                elif key == '_atom_site_fract_z':
                    loop_row[key] = f"{new_fract_coords[2]:.6f}"
                else:
                    loop_row[key] = atom_loop[key][i]
            
            new_loop.AddPacket(loop_row)
        
        # 将循环添加到新块
        new_block.AddLoop(new_loop)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='CIF晶格转换工具')
    parser.add_argument('--input', required=True, help='输入CIF文件或包含CIF文件的目录路径')
    parser.add_argument('--output', required=True, help='输出目录路径')
    parser.add_argument('--planes', help='要转换的晶面指数列表，以逗号分隔，如 "100,010,110"')
    parser.add_argument('--prefix', default='', help='输出文件名前缀')
    parser.add_argument('--suffix', default='', help='输出文件名后缀')
    
    args = parser.parse_args()
    
    # 处理晶面指数列表
    planes = None
    if args.planes:
        planes = [p.strip() for p in args.planes.split(',')]
        # 验证晶面指数是否有效
        for p in planes:
            if p not in PLANE_CONFIGS:
                print(f"错误: 不支持的晶面指数 '{p}'")
                print(f"支持的晶面指数: {', '.join(PLANE_CONFIGS.keys())}")
                sys.exit(1)
    
    return args


def main():
    """主函数"""
    args = parse_args()
    
    # 解析晶面参数
    planes = None
    if args.planes:
        planes = [p.strip() for p in args.planes.split(',')]
    
    # 创建转换器实例
    transformer = CifTransformer(
        input_path=args.input,
        output_dir=args.output,
        planes=planes,
        prefix=args.prefix,
        suffix=args.suffix
    )
    
    # 执行处理
    transformer.process_all_files()


if __name__ == "__main__":
    main() 