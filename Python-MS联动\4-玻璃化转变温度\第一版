import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.ticker import MaxNLocator
from matplotlib import style
from scipy.signal import savgol_filter
from scipy.optimize import curve_fit
import pandas as pd
import os
import seaborn as sns
import tkinter as tk
from tkinter import filedialog, ttk, messagebox, scrolledtext
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import sys

# 设置全局绘图样式
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_context("notebook", font_scale=1.1)
plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题

# 修复字体问题 - 替换上标³
def format_volume_unit(with_superscript=False):
    """根据字体支持情况返回体积单位"""
    if with_superscript:
        return "cm³/g"
    else:
        return "cm3/g"

# 设置是否使用上标
USE_SUPERSCRIPT = False
VOLUME_UNIT = format_volume_unit(USE_SUPERSCRIPT)
DENSITY_UNIT = "g/cm3" if not USE_SUPERSCRIPT else "g/cm³"

def read_data_from_file(file_path):
    """
    从文件中读取温度和密度数据
    支持CSV和Excel格式
    
    支持的列名格式：
    温度列: 'Temperature', 'Temperature(K)', 'Temperature (K)' 等
    密度列: 'Density', 'Density(g/cm³)', 'Density g/cm3' 等
    
    返回：temperatures列表和densities列表
    """
    file_ext = os.path.splitext(file_path)[1].lower()
    
    if file_ext == '.csv':
        df = pd.read_csv(file_path)
    elif file_ext in ['.xls', '.xlsx']:
        df = pd.read_excel(file_path)
    else:
        raise ValueError(f"不支持的文件格式: {file_ext}，请使用CSV或Excel文件")
    
    # 尝试匹配温度列和密度列
    temp_column = None
    density_column = None
    
    # 常见的温度列名
    temp_patterns = ['Temperature', 'Temp', 'T', '温度']
    # 常见的密度列名
    density_patterns = ['Density', 'ρ', '密度']
    
    # 查找温度列
    for col in df.columns:
        # 检查该列是否匹配任一温度模式
        if any(pattern.lower() in col.lower() for pattern in temp_patterns):
            temp_column = col
            break
    
    # 查找密度列
    for col in df.columns:
        # 检查该列是否匹配任一密度模式
        if any(pattern.lower() in col.lower() for pattern in density_patterns):
            density_column = col
            break
    
    # 如果未找到列，尝试直接使用第一列作为温度，第二列作为密度
    if temp_column is None and density_column is None and len(df.columns) >= 2:
        print("警告：未能识别温度和密度列，使用前两列代替。")
        temp_column = df.columns[0]
        density_column = df.columns[1]
    else:
        # 如果仍未找到列，报错
        if temp_column is None:
            raise ValueError(f"文件缺少必要的温度列。支持的列名包括: {', '.join(temp_patterns)}或类似名称")
        if density_column is None:
            raise ValueError(f"文件缺少必要的密度列。支持的列名包括: {', '.join(density_patterns)}或类似名称")
    
    print(f"使用列: '{temp_column}' 作为温度, '{density_column}' 作为密度")
    temperatures = df[temp_column].tolist()
    densities = df[density_column].tolist()
    
    return temperatures, densities

def find_glass_transition_temperature(temperatures, densities, output_path=None, sample_name=None):
    """
    使用多种方法寻找玻璃化转变温度
    
    1. 计算密度随温度变化的速率
    2. 找出该速率显著变化的位置
    3. 使用曲线拟合识别两个不同的区域
    4. 绘制结果进行可视化
    
    参数:
    temperatures - 温度数据列表
    densities - 密度数据列表
    output_path - 可视化结果保存路径，默认为None(当前目录)
    sample_name - 样品名称，用于图表标题
    
    返回:
    final_tg - 最终估计的玻璃化转变温度
    """
    temperatures = np.array(temperatures)
    densities = np.array(densities)
    
    # 按温度排序(降序)
    sort_idx = np.argsort(-temperatures)
    temperatures = temperatures[sort_idx]
    densities = densities[sort_idx]
    
    # 方法2：拟合两条线到不同区域并找出交点
    # 尝试不同的分割点并最小化误差
    best_error = float('inf')
    best_split = 0
    best_params = None
    best_r2_high = 0
    best_r2_low = 0
    
    for split_idx in range(3, len(temperatures)-3):
        # 高温区域的线性拟合
        try:
            # 拟合高温区域
            high_temps = temperatures[:split_idx]
            high_dens = densities[:split_idx]
            high_temp_params, high_residuals, _, _, _ = np.polyfit(high_temps, high_dens, 1, full=True)
            
            # 计算高温区域R²值
            high_fit = np.polyval(high_temp_params, high_temps)
            ss_tot_high = np.sum((high_dens - np.mean(high_dens))**2)
            ss_res_high = np.sum((high_dens - high_fit)**2)
            r2_high = 1 - (ss_res_high / ss_tot_high) if ss_tot_high != 0 else 0
            
            # 拟合低温区域
            low_temps = temperatures[split_idx:]
            low_dens = densities[split_idx:]
            low_temp_params, low_residuals, _, _, _ = np.polyfit(low_temps, low_dens, 1, full=True)
            
            # 计算低温区域R²值
            low_fit = np.polyval(low_temp_params, low_temps)
            ss_tot_low = np.sum((low_dens - np.mean(low_dens))**2)
            ss_res_low = np.sum((low_dens - low_fit)**2)
            r2_low = 1 - (ss_res_low / ss_tot_low) if ss_tot_low != 0 else 0
            
            # 计算拟合误差
            high_error = np.sum((high_fit - high_dens)**2)
            low_error = np.sum((low_fit - low_dens)**2)
            total_error = high_error + low_error
            
            if total_error < best_error:
                best_error = total_error
                best_split = split_idx
                best_params = (high_temp_params, low_temp_params)
                best_r2_high = r2_high
                best_r2_low = r2_low
        except:
            continue
    
    if best_params:
        # 找出两条线的交点
        high_params, low_params = best_params
        # m1*x + b1 = m2*x + b2
        # (m1-m2)*x = b2-b1
        # x = (b2-b1)/(m1-m2)
        m1, b1 = high_params
        m2, b2 = low_params
        tg_method2 = (b2-b1)/(m1-m2)
        final_tg = tg_method2
    else:
        # 如果无法找到交点，使用其他方法
        # 计算斜率(一阶导数)
        slopes = []
        mid_temps = []
        
        for i in range(len(temperatures)-1):
            temp_diff = temperatures[i] - temperatures[i+1]
            density_diff = densities[i+1] - densities[i]
            slope = density_diff / temp_diff
            slopes.append(slope)
            mid_temps.append((temperatures[i] + temperatures[i+1]) / 2)
        
        slopes = np.array(slopes)
        mid_temps = np.array(mid_temps)
        
        # 使用Savitzky-Golay滤波器平滑斜率以减少噪声
        if len(slopes) > 5:  # 该滤波器至少需要5个点
            window_length = min(5, len(slopes) - 2 if len(slopes) % 2 == 0 else len(slopes) - 1)
            smooth_slopes = savgol_filter(slopes, window_length, 2)
        else:
            smooth_slopes = slopes
        
        # 计算二阶导数(斜率的变化率)
        second_deriv = []
        second_mid_temps = []
        
        for i in range(len(smooth_slopes)-1):
            temp_diff = mid_temps[i] - mid_temps[i+1]
            slope_diff = smooth_slopes[i+1] - smooth_slopes[i]
            second_d = slope_diff / temp_diff
            second_deriv.append(second_d)
            second_mid_temps.append((mid_temps[i] + mid_temps[i+1]) / 2)
        
        second_deriv = np.array(second_deriv)
        second_mid_temps = np.array(second_mid_temps)
        
        # 方法1：找出二阶导数绝对值最大的温度点
        tg_method1 = second_mid_temps[np.argmax(np.abs(second_deriv))]
        final_tg = tg_method1
    
    # 创建简洁的玻璃化转变温度分析图
    plt.figure(figsize=(8, 6), dpi=100)
    
    # 设置样品名称
    if sample_name is None:
        sample_name = "样品"
    
    # 主图 - 温度vs密度
    plt.scatter(temperatures, densities, color='#3182bd', s=40, alpha=0.8)
    
    # 如果有最佳拟合参数，绘制拟合线
    if best_params:
        high_params, low_params = best_params
        
        # 温度范围
        t_min, t_max = min(temperatures), max(temperatures)
        temp_range = np.linspace(t_min, t_max, 1000)
        
        # 高温区域拟合线
        high_fit = np.polyval(high_params, temp_range)
        plt.plot(temp_range, high_fit, '-', color='#e41a1c', 
                linewidth=2, label=f'液体区拟合 (R²={best_r2_high:.2f})')
        
        # 低温区域拟合线
        low_fit = np.polyval(low_params, temp_range)
        plt.plot(temp_range, low_fit, '-', color='#4daf4a', 
                linewidth=2, label=f'玻璃区拟合 (R²={best_r2_low:.2f})')
        
        # 标记玻璃化转变温度
        plt.axvline(x=final_tg, color='black', linestyle='--', alpha=0.7)
        plt.text(final_tg+5, min(densities) + 0.9*(max(densities)-min(densities)), 
                f'Tg = {final_tg:.0f}K', fontsize=14, color='darkred', 
                bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5'))
    
    # 格式设置
    plt.xlabel('温度 (K)', fontsize=12)
    plt.ylabel(f'密度 ({DENSITY_UNIT})', fontsize=12)
    plt.title(f'{sample_name}的密度随温度的变化', fontsize=14)
    plt.legend(loc='best')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 调整布局并保存
    plt.tight_layout()
    
    # 确定保存路径
    if output_path is None:
        base_dir = os.path.dirname(os.path.abspath(__file__))
        output_file = os.path.join(base_dir, f'glass_transition_{sample_name}.png')
    else:
        output_file = output_path
    
    # 保存高分辨率图像
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"可视化结果已保存至 {output_file}")
    
    # 显示图表
    plt.show()
    
    # 生成详细分析图 (可选)
    if best_params:
        create_detailed_analysis(temperatures, densities, best_params, final_tg, sample_name)
    
    # 输出结果
    print(f"玻璃化转变温度 (Tg): {final_tg:.1f}K")
    
    # 返回最终结果
    return final_tg

def create_detailed_analysis(temperatures, densities, best_params, tg, sample_name=None):
    """
    创建详细的分析图表
    """
    # 创建高级可视化图表
    fig = plt.figure(figsize=(12, 10), dpi=100)
    gs = gridspec.GridSpec(2, 2, height_ratios=[2, 1.5])
    
    # 设置全局标题
    plt.suptitle(f"玻璃化转变温度 (Tg) 详细分析 - {sample_name}", fontsize=16, y=0.98)
    
    # 绘图1：温度vs密度
    ax1 = plt.subplot(gs[0, 0])
    ax1.scatter(temperatures, densities, color='#3182bd', s=40, alpha=0.7)
    
    # 如果有最佳拟合参数，绘制拟合线
    if best_params:
        high_params, low_params = best_params
        
        # 温度范围
        temp_range = np.linspace(min(temperatures), max(temperatures), 1000)
        
        # 高温区域拟合线
        high_mask = temp_range >= tg
        if np.any(high_mask):
            high_fit = np.polyval(high_params, temp_range[high_mask])
            ax1.plot(temp_range[high_mask], high_fit, '-', color='#e41a1c', 
                    linewidth=2, label='液体区域拟合')
        
        # 低温区域拟合线
        low_mask = temp_range <= tg
        if np.any(low_mask):
            low_fit = np.polyval(low_params, temp_range[low_mask])
            ax1.plot(temp_range[low_mask], low_fit, '-', color='#4daf4a', 
                    linewidth=2, label='玻璃区域拟合')
        
        # 标记Tg
        ax1.axvline(x=tg, color='black', linestyle='--', alpha=0.7)
        ax1.text(tg+5, min(densities) + 0.8*(max(densities)-min(densities)), 
                f'Tg = {tg:.0f}K', fontsize=12, color='black')
    
    ax1.set_xlabel('温度 (K)', fontsize=12)
    ax1.set_ylabel(f'密度 ({DENSITY_UNIT})', fontsize=12)
    ax1.set_title('温度与密度关系', fontsize=14)
    ax1.legend(loc='best')
    ax1.grid(True, linestyle='--', alpha=0.7)
    
    # 绘图2：温度vs密度一阶导数
    ax2 = plt.subplot(gs[0, 1])
    
    # 计算一阶导数（密度随温度的变化率）
    slopes = []
    mid_temps = []
    
    for i in range(len(temperatures)-1):
        temp_diff = temperatures[i] - temperatures[i+1]
        density_diff = densities[i+1] - densities[i]
        slope = density_diff / temp_diff
        slopes.append(slope)
        mid_temps.append((temperatures[i] + temperatures[i+1]) / 2)
    
    slopes = np.array(slopes)
    mid_temps = np.array(mid_temps)
    
    # 平滑曲线
    if len(slopes) > 5:
        window_length = min(5, len(slopes) - 2 if len(slopes) % 2 == 0 else len(slopes) - 1)
        smooth_slopes = savgol_filter(slopes, window_length, 2)
    else:
        smooth_slopes = slopes
    
    ax2.scatter(mid_temps, slopes, color='#3182bd', s=30, alpha=0.5, label="变化率")
    ax2.plot(mid_temps, smooth_slopes, color='red', linewidth=2, label="平滑曲线")
    ax2.axvline(x=tg, color='black', linestyle='--', alpha=0.7)
    
    ax2.set_xlabel('温度 (K)', fontsize=12)
    ax2.set_ylabel('密度变化率 (d密度/dT)', fontsize=12)
    ax2.set_title('密度变化率曲线', fontsize=14)
    ax2.legend(loc='best')
    ax2.grid(True, linestyle='--', alpha=0.7)
    
    # 绘图3：区域分段视图
    ax4 = plt.subplot(gs[1, :])
    
    # 分区域绘制数据点
    high_temp_mask = temperatures > tg
    low_temp_mask = temperatures <= tg
    
    ax4.scatter(temperatures[high_temp_mask], densities[high_temp_mask], 
                s=60, color='#e41a1c', alpha=0.8, label='液体区域')
    ax4.scatter(temperatures[low_temp_mask], densities[low_temp_mask], 
                s=60, color='#4daf4a', alpha=0.8, label='玻璃区域')
    
    # 拟合线
    if best_params:
        high_params, low_params = best_params
        temp_range = np.linspace(min(temperatures), max(temperatures), 1000)
        
        high_mask = temp_range >= tg
        if np.any(high_mask):
            high_fit = np.polyval(high_params, temp_range[high_mask])
            ax4.plot(temp_range[high_mask], high_fit, '-', color='#e41a1c', 
                    linewidth=2, alpha=0.7)
        
        low_mask = temp_range <= tg
        if np.any(low_mask):
            low_fit = np.polyval(low_params, temp_range[low_mask])
            ax4.plot(temp_range[low_mask], low_fit, '-', color='#4daf4a', 
                    linewidth=2, alpha=0.7)
    
    # 标记玻璃化转变温度
    ax4.axvline(x=tg, color='black', linestyle='--', linewidth=2, alpha=0.8,
               label=f'玻璃化转变温度：{tg:.1f}K')
    
    ax4.set_xlabel('温度 (K)', fontsize=12)
    ax4.set_ylabel(f'密度 ({DENSITY_UNIT})', fontsize=12)
    ax4.set_title('区域分段视图 - 玻璃与液体状态', fontsize=14)
    ax4.legend(loc='best')
    ax4.grid(True, linestyle='--', alpha=0.7)
    
    # 调整布局并保存
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    # 保存详细分析图
    base_dir = os.path.dirname(os.path.abspath(__file__))
    output_file = os.path.join(base_dir, f'detailed_analysis_{sample_name}.png')
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"详细分析已保存至 {output_file}")
    
    plt.show()

def save_results_to_file(temperatures, densities, tg, output_path=None):
    """
    将分析结果保存到文件中
    
    参数:
    temperatures - 温度数据列表
    densities - 密度数据列表
    tg - 计算得到的玻璃化转变温度
    output_path - 输出文件路径，默认为当前目录下的 tg_results.csv
    """
    if output_path is None:
        base_dir = os.path.dirname(os.path.abspath(__file__))
        output_path = os.path.join(base_dir, 'tg_results.csv')
    
    # 创建结果DataFrame
    results_df = pd.DataFrame({
        'Temperature(K)': temperatures,
        'Density(g/cm3)': densities
    })
    
    # 添加额外的结果信息
    high_temp_mask = temperatures > tg
    low_temp_mask = temperatures <= tg
    
    # 标记区域
    results_df['Region'] = ['液体' if temp > tg else '玻璃' for temp in temperatures]
    
    # 保存结果
    results_df.to_csv(output_path, index=False, encoding='utf-8-sig')
    print(f"分析结果已保存至 {output_path}")

class TgAnalysisApp:
    """
    玻璃化转变温度分析GUI应用
    """
    def __init__(self, root):
        self.root = root
        self.root.title("玻璃化转变温度分析工具")
        self.root.geometry("1000x750")  # 更大的默认窗口尺寸
        self.root.minsize(900, 700)     # 增加最小窗口尺寸
        
        # 设置应用风格
        style = ttk.Style()
        style.theme_use('clam')  # 使用较现代的主题
        
        # 创建主框架 - 使用Canvas和Scrollbar支持滚动
        self.main_canvas = tk.Canvas(self.root)
        self.scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=self.main_canvas.yview)
        self.scrollable_frame = ttk.Frame(self.main_canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.main_canvas.configure(
                scrollregion=self.main_canvas.bbox("all")
            )
        )
        
        self.main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.main_canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.main_canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # 创建标题
        title_label = ttk.Label(self.scrollable_frame, text="玻璃化转变温度(Tg)分析程序", font=("SimHei", 16, "bold"))
        title_label.pack(pady=10)
        
        # 创建上半部分框架 - 数据输入和控制
        self.top_frame = ttk.Frame(self.scrollable_frame)
        self.top_frame.pack(fill=tk.X, expand=False, pady=5)
        
        # 文件选择区域
        self.file_frame = ttk.LabelFrame(self.top_frame, text="数据文件选择", padding=5)
        self.file_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(self.file_frame, textvariable=self.file_path_var, width=50)
        self.file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        self.browse_button = ttk.Button(self.file_frame, text="浏览...", command=self.browse_file)
        self.browse_button.pack(side=tk.LEFT, padx=5)
        
        self.load_button = ttk.Button(self.file_frame, text="加载数据", command=self.load_data)
        self.load_button.pack(side=tk.LEFT, padx=5)
        
        # 样品信息区域
        self.sample_frame = ttk.LabelFrame(self.top_frame, text="样品信息", padding=5)
        self.sample_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        ttk.Label(self.sample_frame, text="样品名称:").pack(side=tk.LEFT, padx=5)
        self.sample_name_var = tk.StringVar(value="样品")
        self.sample_entry = ttk.Entry(self.sample_frame, textvariable=self.sample_name_var, width=20)
        self.sample_entry.pack(side=tk.LEFT, padx=5)
        
        # 数据预览区域 - 减小高度
        self.data_frame = ttk.LabelFrame(self.scrollable_frame, text="数据预览", padding=5)
        self.data_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        # 创建表格
        self.tree_frame = ttk.Frame(self.data_frame)
        self.tree_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        self.tree_scroll = ttk.Scrollbar(self.tree_frame)
        self.tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置较小高度的Treeview
        self.tree = ttk.Treeview(self.tree_frame, yscrollcommand=self.tree_scroll.set, height=5)
        self.tree.pack(fill=tk.X, expand=False)
        
        self.tree_scroll.config(command=self.tree.yview)
        
        # 设置列
        self.tree["columns"] = ("index", "temperature", "density")
        self.tree.column("#0", width=0, stretch=tk.NO)
        self.tree.column("index", anchor=tk.CENTER, width=50)
        self.tree.column("temperature", anchor=tk.CENTER, width=120)
        self.tree.column("density", anchor=tk.CENTER, width=120)
        
        self.tree.heading("#0", text="", anchor=tk.CENTER)
        self.tree.heading("index", text="序号", anchor=tk.CENTER)
        self.tree.heading("temperature", text="温度 (K)", anchor=tk.CENTER)
        self.tree.heading("density", text=f"密度 ({DENSITY_UNIT})", anchor=tk.CENTER)
        
        # 创建数据可视化预览 - 减小高度
        self.preview_frame = ttk.LabelFrame(self.scrollable_frame, text="数据可视化预览", padding=5)
        self.preview_frame.pack(fill=tk.X, expand=False, padx=5, pady=5, ipady=100)  # 设置内部高度
        
        # 分析控制区域
        self.control_frame = ttk.LabelFrame(self.scrollable_frame, text="操作控制", padding=10)
        self.control_frame.pack(fill=tk.X, expand=False, padx=5, pady=10)
        
        # 使用更大的按钮，便于看到
        self.analyze_button = ttk.Button(self.control_frame, text="分析数据", command=self.analyze_data, 
                                         style='Big.TButton')
        self.analyze_button.pack(side=tk.LEFT, padx=10, pady=10)
        
        self.save_button = ttk.Button(self.control_frame, text="保存结果", command=self.save_results,
                                     style='Big.TButton')
        self.save_button.pack(side=tk.LEFT, padx=10, pady=10)
        
        self.detailed_var = tk.BooleanVar(value=False)
        self.detailed_check = ttk.Checkbutton(self.control_frame, text="生成详细分析", 
                                              variable=self.detailed_var)
        self.detailed_check.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 添加大字体的状态显示
        self.status_frame = ttk.Frame(self.scrollable_frame)
        self.status_frame.pack(fill=tk.X, expand=False, padx=5, pady=10)
        
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪 - 请选择数据文件")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var, 
                                     font=("SimHei", 12, "bold"))
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 创建大按钮样式
        style.configure('Big.TButton', font=('SimHei', 12))
        
        # 初始化数据
        self.temperatures = []
        self.densities = []
        self.tg_result = None
        self.data_loaded = False
        self.preview_canvas = None
        
        # 绑定鼠标滚轮事件
        self.root.bind("<MouseWheel>", self._on_mousewheel)
        
    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        self.main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
    def browse_file(self):
        """浏览并选择数据文件"""
        filetypes = [
            ('CSV文件', '*.csv'),
            ('Excel文件', '*.xlsx *.xls'),
            ('所有文件', '*.*')
        ]
        filepath = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=filetypes
        )
        if filepath:
            self.file_path_var.set(filepath)
            # 从文件名提取样品名称
            base_name = os.path.basename(filepath)
            sample_name = os.path.splitext(base_name)[0]
            self.sample_name_var.set(sample_name)
    
    def load_data(self):
        """加载数据文件"""
        filepath = self.file_path_var.get().strip()
        if not filepath:
            messagebox.showerror("错误", "请先选择数据文件")
            return
            
        try:
            self.status_var.set("正在加载数据...")
            self.root.update()
            
            self.temperatures, self.densities = read_data_from_file(filepath)
            self.data_loaded = True
            
            # 清空现有表格
            for item in self.tree.get_children():
                self.tree.delete(item)
                
            # 填充表格
            for i, (temp, dens) in enumerate(zip(self.temperatures, self.densities)):
                self.tree.insert("", tk.END, values=(i+1, f"{temp:.1f}", f"{dens:.4f}"))
                
            # 更新状态
            self.status_var.set("已加载 {} 个数据点 - 点击'分析数据'继续".format(len(self.temperatures)))
            
            # 生成预览图
            self.create_preview_plot()
            
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败: {str(e)}")
            self.status_var.set("数据加载失败")
    
    def create_preview_plot(self):
        """创建数据预览图"""
        # 清除现有图表
        for widget in self.preview_frame.winfo_children():
            widget.destroy()
            
        if not self.data_loaded or len(self.temperatures) == 0:
            return
            
        # 创建简单预览图
        fig, ax = plt.subplots(figsize=(8, 4), dpi=100)
        ax.scatter(self.temperatures, self.densities, color='#3182bd', s=30, alpha=0.7)
        ax.set_xlabel('温度 (K)')
        ax.set_ylabel(f'密度 ({DENSITY_UNIT})')
        ax.set_title('温度-密度数据预览')
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 将图表嵌入到tkinter窗口
        canvas = FigureCanvasTkAgg(fig, master=self.preview_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        self.preview_canvas = canvas
    
    def analyze_data(self):
        """分析数据并显示结果"""
        if not self.data_loaded or len(self.temperatures) == 0:
            messagebox.showerror("错误", "请先加载有效数据")
            return
            
        try:
            self.status_var.set("正在分析数据...")
            self.root.update()
            
            # 获取样品名称
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "样品"
            
            # 计算玻璃化转变温度
            self.tg_result = find_glass_transition_temperature(
                self.temperatures, self.densities, 
                sample_name=sample_name
            )
            
            # 更新状态
            self.status_var.set("分析完成，玻璃化转变温度: {:.1f}K".format(self.tg_result))
            
        except Exception as e:
            messagebox.showerror("错误", f"分析数据失败: {str(e)}")
            self.status_var.set("数据分析失败")
    
    def save_results(self):
        """保存分析结果"""
        if not self.data_loaded or self.tg_result is None:
            messagebox.showerror("错误", "请先加载数据并完成分析")
            return
            
        try:
            # 选择保存路径
            filetypes = [('CSV文件', '*.csv')]
            save_path = filedialog.asksaveasfilename(
                title="保存分析结果",
                defaultextension=".csv",
                filetypes=filetypes
            )
            
            if save_path:
                save_results_to_file(self.temperatures, self.densities, self.tg_result, save_path)
                messagebox.showinfo("成功", f"结果已保存至: {save_path}")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存结果失败: {str(e)}")

# 主程序入口
if __name__ == "__main__":
    # 检查运行模式
    if len(sys.argv) > 1 and sys.argv[1] == "--cli":
        # 命令行模式
        if len(sys.argv) > 2:
            file_path = sys.argv[2]
        else:
            file_path = input("请输入数据文件路径（CSV或Excel格式）: ")
            
        # 可选参数: 样品名称
        sample_name = None
        if len(sys.argv) > 3:
            sample_name = sys.argv[3]
        
        # 读取数据
        temperatures, densities = read_data_from_file(file_path)
        
        # 打印读取到的数据
        print(f"读取到 {len(temperatures)} 个数据点")
        print("温度(K) | 密度(g/cm3)")
        print("-" * 30)
        for t, d in zip(temperatures, densities):
            print(f"{t:7.1f} | {d:7.4f}")
        
        # 计算玻璃化转变温度并生成可视化
        tg = find_glass_transition_temperature(temperatures, densities, sample_name=sample_name)
        
        # 保存处理结果
        save_results_to_file(temperatures, densities, tg)
    else:
        # GUI模式
        root = tk.Tk()
        app = TgAnalysisApp(root)
        root.mainloop()