# 线性模型机器学习应用 - 使用指南

这个指南将帮助您使用线性模型机器学习应用程序进行数据分析和预测。本应用提供了简单直观的界面，即使没有编程经验的用户也能轻松使用。

## 应用界面概述

应用程序界面分为两个主要部分：
- **左侧面板**：用于数据加载、特征选择、算法选择和参数设置
- **右侧面板**：用于显示结果、性能指标和可视化图表
- **底部区域**：包含一个明显的橙色"运行模型"按钮，是开始模型训练和预测的主要方式

## 详细使用步骤

### 1. 加载数据

1. 点击左上角"**数据加载**"部分的"**加载数据文件**"按钮
2. 在弹出的文件选择对话框中，选择您的CSV或Excel数据文件
3. 数据加载成功后，文件名将显示在按钮下方，并会自动填充目标变量下拉列表和特征列表

> **注意**：您的数据必须是表格格式，包含数值型列，至少有一个可以作为目标变量（预测目标）

### 2. 选择目标变量

1. 在"**数据处理**"区域，从下拉列表中选择您需要预测的目标变量
2. 默认情况下，系统会选择数据表的最后一列

### 3. 选择特征

1. 在"**数据处理**"区域的列表框中，选择用于预测的特征列
2. 您可以通过点击并拖动或按住Ctrl键点击来选择多个特征
3. 也可以使用"**选择所有特征**"和"**清除所有特征**"按钮快速管理特征选择

> **注意**：目标变量会被自动从特征列表中排除

### 4. 设置测试集比例

1. 使用"**测试集比例**"滑块调整训练集和测试集的分割比例
2. 数值范围从0.1（10%用于测试）到0.5（50%用于测试）
3. 默认值为0.2（20%用于测试）

### 5. 数据标准化

在"**数据处理**"区域，可以选择是否标准化数据：
1. 勾选"**标准化数据**"选项可以将所有特征缩放到均值为0，标准差为1
2. 这对于不同尺度的特征特别有用，如房价（十万级）和房间数（个位数）
3. 大多数情况下建议启用此选项

### 6. 选择算法

在"**算法选择**"区域，选择您想要使用的线性模型算法：

- **线性回归**：基本的线性回归模型，适用于简单的线性关系
- **岭回归**：添加L2正则化的线性回归，适用于特征间有相关性的情况
- **Lasso回归**：添加L1正则化的线性回归，适用于需要特征选择的情况
- **弹性网络**：结合L1和L2正则化，综合了岭回归和Lasso回归的优点

### 7. 调整算法参数

根据所选算法，在"**参数设置**"区域调整相应参数：

- **线性回归**：无需额外参数
- **岭回归**：
  - 正则化系数(alpha)：控制正则化强度，值越大，模型越简单
- **Lasso回归**：
  - 正则化系数(alpha)：控制正则化强度，值越大，更多特征被置为零
  - 最大迭代次数：算法的最大迭代次数
- **弹性网络**：
  - 正则化系数(alpha)：控制整体正则化强度
  - L1比率：L1正则化的比例，1.0表示纯L1正则化，0.0表示纯L2正则化
  - 最大迭代次数：算法的最大迭代次数

### 8. 运行模型 - 重要！

完成上述所有设置后，您有**两种方式**可以运行模型：

1. 点击左侧面板底部的"**运行模型**"按钮
2. 点击窗口底部橙色的"**运行模型**"按钮（最推荐）

系统将使用您的设置训练模型并生成预测结果。如果成功，将显示"模型训练和预测完成"的提示。

### 9. 查看结果

在右侧面板，您可以查看模型的性能和预测结果：

- **模型结果**区域显示性能指标：
  - MSE (均方误差)：误差的平方的平均值，越小越好
  - R² (决定系数)：模型解释数据变异的比例，接近1表示拟合良好

- **可视化**区域提供三种可视化类型：
  - **预测vs实际值**：散点图显示预测值与实际值的对比，点越接近对角线，预测越准确
  - **特征重要性**：条形图显示各特征的系数，正值（蓝色）表示正相关，负值（红色）表示负相关
  - **残差图**：显示预测误差的分布，理想情况下残差应随机分布在零线附近

### 10. 导出结果

1. 如果您对结果满意，点击右侧面板底部的"**导出结果**"按钮
2. 选择保存路径和格式（CSV或Excel）
3. 结果文件将包含训练集和测试集的实际值和预测值

## 示例场景

### 房价预测

1. 加载包含房价数据的CSV文件（如house_price_data.csv）
2. 选择"房价"作为目标变量
3. 选择相关特征如"面积"、"房间数"、"房龄"等
4. 启用"标准化数据"选项
5. 选择"岭回归"算法并调整alpha参数
6. 点击"运行模型"按钮
7. 查看预测结果和性能指标
8. 导出预测结果以进行进一步分析

### 销售预测

1. 加载销售数据CSV文件（如sales_data.csv）
2. 选择"销售量"作为目标变量
3. 选择特征如"广告支出"、"促销折扣"等
4. 启用"标准化数据"选项
5. 选择"弹性网络"算法并调整参数
6. 点击"运行模型"按钮
7. 分析模型性能和特征重要性
8. 导出结果用于销售规划

## 常见问题解答

**Q: 为什么运行模型时出现错误？**
A: 请确保选择了数值型的目标变量和至少一个数值型特征。非数值列无法用于线性模型。

**Q: 如何判断我应该使用哪种算法？**
A: 可以尝试多种算法并比较它们的性能指标。一般来说：
- 数据简单，特征少 → 线性回归
- 特征间有相关性 → 岭回归
- 需要特征选择 → Lasso回归
- 两种效果都需要 → 弹性网络

**Q: 模型性能不佳，如何改进？**
A: 尝试调整算法参数，特别是正则化系数。也可以尝试不同的特征组合或添加新特征。

**Q: 如何解读特征重要性图？**
A: 条形图显示每个特征的系数大小和方向。蓝色条表示该特征与目标变量正相关，红色条表示负相关。长度表示影响强度。

**Q: 为什么要标准化数据？**
A: 标准化可以使不同尺度的特征处于相同的范围内，改善模型训练的稳定性和准确性，尤其是对于岭回归和弹性网络等正则化模型。 