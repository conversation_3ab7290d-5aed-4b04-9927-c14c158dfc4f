#!/usr/bin/env python3
"""
数据格式转换脚本
将介电数据.md文件转换为标准的.epsilon格式
"""

import pandas as pd
import numpy as np
import os

def convert_md_to_epsilon():
    """将介电数据.md转换为.epsilon格式"""
    input_file = "介电数据.md"
    output_file = "介电数据.epsilon"
    
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return False
    
    try:
        # 读取MD文件，跳过第一行标题
        print(f"正在读取文件: {input_file}")
        data = pd.read_csv(input_file, sep='\t', skiprows=1, header=None)
        
        # 检查数据格式
        if data.shape[1] < 3:
            print(f"错误: 数据列数不足，需要至少3列，实际有{data.shape[1]}列")
            return False
        
        # 提取数据列
        energy = data.iloc[:, 0].values
        epsilon_1 = data.iloc[:, 1].values
        epsilon_2 = data.iloc[:, 2].values
        
        # 检查数据有效性
        valid_mask = (
            np.isfinite(energy) & 
            np.isfinite(epsilon_1) & 
            np.isfinite(epsilon_2) &
            (energy > 0)
        )
        
        energy = energy[valid_mask]
        epsilon_1 = epsilon_1[valid_mask]
        epsilon_2 = epsilon_2[valid_mask]
        
        print(f"有效数据点数: {len(energy)}")
        print(f"能量范围: {energy[0]:.6f} - {energy[-1]:.3f} eV")
        print(f"ε₁范围: {np.min(epsilon_1):.6f} - {np.max(epsilon_1):.6f}")
        print(f"ε₂范围: {np.min(epsilon_2):.6e} - {np.max(epsilon_2):.6e}")
        
        # 写入.epsilon格式文件
        with open(output_file, 'w') as f:
            f.write("# CASTEP epsilon file converted from 介电数据.md\n")
            f.write("# Energy(eV)    Epsilon_1    Epsilon_2\n")
            
            for i in range(len(energy)):
                f.write(f"{energy[i]:.8f}  {epsilon_1[i]:.8f}  {epsilon_2[i]:.8e}\n")
        
        print(f"✅ 转换完成，输出文件: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        return False

def test_converted_file():
    """测试转换后的文件"""
    import sys
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
    
    from castep_conductivity_calculator import CastepConductivityApp
    
    epsilon_file = "介电数据.epsilon"
    if not os.path.exists(epsilon_file):
        print("错误: 转换后的文件不存在")
        return False
    
    print("\n测试转换后的文件...")
    print("=" * 40)
    
    app = CastepConductivityApp()
    
    try:
        # 测试加载
        if not app.load_data(epsilon_file):
            return False
        
        # 测试计算
        if not app.calculate_conductivity():
            return False
        
        if not app.calculate_dc_conductivity():
            return False
        
        # 导出结果
        if not app.export_results("介电数据_results.csv", export_all=True):
            return False
        
        # 打印摘要
        app.print_summary()
        
        print("\n🎉 转换后的文件测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("CASTEP介电数据格式转换工具")
    print("=" * 50)
    
    # 转换文件
    if convert_md_to_epsilon():
        # 测试转换后的文件
        test_converted_file()
    else:
        print("转换失败，无法进行测试")

if __name__ == "__main__":
    main()
