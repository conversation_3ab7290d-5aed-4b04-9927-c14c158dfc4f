##################################################################################################################
# perl                                                                                                            #
#                                                                                                                 #
# Author: IMATSOFT.DM                                                                                             #
# Version: 2.2                                                                                                    #
# Tested on: Materials Studio 2020                                                                                #
#                                                                                                                 #
# Required modules: Materials Visualizer, Forcite                                                                 #
#                                                                                                                 #
# Description:                                                                                                    #
# This script calculates the interaction energy between three defined sets of atoms (SetA, SetB, and SetC)        #
# using the COMPASSIII force field. For each frame in the trajectory, the script computes the total potential,    #
# van der Waals, and electrostatic energies for both the full system and each set of atoms separately. It then    #
# calculates the interaction energy (including VDW and electrostatic components). The results are saved in an STD #
# file.                                                                                                           #
#                                                                                                                 #
# Date: 2023-03-27                                                                                                #
##################################################################################################################

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);

#################################################################################################################
#                                         BEGIN USER INPUT                                                      #
#################################################################################################################

# Load the structure document
my $doc = Documents->ActiveDocument;
my %Args;
GetOptions(\%Args, "SetA=s", "SetB=s", "SetC=s", "Forcefield=s",
"Quilty=s","Strat_trj=i","End_trj=i","VDW=s","Elec=s","SaveStructures=s");
# Define the atom sets for energy calculations
my $setA = $Args{SetA};
my $setB = $Args{SetB};
my $setC = $Args{SetC};
my $Forcefield = $Args{Forcefield};
my $Quilty = $Args{Quilty};
my $Strat_trj = $Args{Strat_trj};
my $end_trj = $Args{End_trj};
my $VDW = $Args{VDW};
my $Elec = $Args{Elec};
my $SaveStructures = $Args{SaveStructures} || "No"; # Default to "No" if not provided
#################################################################################################################
#                                         END USER INPUT                                                        #
#################################################################################################################

# Create an output table to store the calculated energies
my $table = Documents->New("Eint_ThreePart.std");
my $activesheet = $table->ActiveSheet;

# Set the column headings for the output table
$activesheet->ColumnHeading(0) = "Time_ps";
$activesheet->ColumnHeading(1) = "Total_Structure";
$activesheet->ColumnHeading(2) = "E_Total_Kcal/mol";
$activesheet->ColumnHeading(3) = "E_Total_VDW_Kcal/mol";
$activesheet->ColumnHeading(4) = "E_Total_Elec_Kcal/mol";
$activesheet->ColumnHeading(5) = "SetA_Structure";
$activesheet->ColumnHeading(6) = "E_SetA_Kcal/mol";
$activesheet->ColumnHeading(7) = "E_SetA_VDW_Kcal/mol";
$activesheet->ColumnHeading(8) = "E_SetA_Elec_Kcal/mol";
$activesheet->ColumnHeading(9) = "SetB_Structure";
$activesheet->ColumnHeading(10) = "E_SetB_Kcal/mol";
$activesheet->ColumnHeading(11) = "E_SetB_VDW_Kcal/mol";
$activesheet->ColumnHeading(12) = "E_SetB_Elec_Kcal/mol";
$activesheet->ColumnHeading(13) = "SetC_Structure";
$activesheet->ColumnHeading(14) = "E_SetC_Kcal/mol";
$activesheet->ColumnHeading(15) = "E_SetC_VDW_Kcal/mol";
$activesheet->ColumnHeading(16) = "E_SetC_Elec_Kcal/mol";
$activesheet->ColumnHeading(17) = "SetAB_Structure";
$activesheet->ColumnHeading(18) = "E_SetAB_Kcal/mol";
$activesheet->ColumnHeading(19) = "E_SetAB_VDW_Kcal/mol";
$activesheet->ColumnHeading(20) = "E_SetAB_Elec_Kcal/mol";
$activesheet->ColumnHeading(21) = "SetAC_Structure";
$activesheet->ColumnHeading(22) = "E_SetAC_Kcal/mol";
$activesheet->ColumnHeading(23) = "E_SetAC_VDW_Kcal/mol";
$activesheet->ColumnHeading(24) = "E_SetAC_Elec_Kcal/mol";
$activesheet->ColumnHeading(25) = "SetBC_Structure";
$activesheet->ColumnHeading(26) = "E_SetBC_Kcal/mol";
$activesheet->ColumnHeading(27) = "E_SetBC_VDW_Kcal/mol";
$activesheet->ColumnHeading(28) = "E_SetBC_Elec_Kcal/mol";
$activesheet->ColumnHeading(29) = "Eint_A_Potential_Kcal/mol";
$activesheet->ColumnHeading(30) = "Eint_B_Potential_Kcal/mol";
$activesheet->ColumnHeading(31) = "Eint_C_Potential_Kcal/mol";
$activesheet->ColumnHeading(32) = "Eint_A_Vdw_Kcal/mol";
$activesheet->ColumnHeading(33) = "Eint_B_Vdw_Kcal/mol";
$activesheet->ColumnHeading(34) = "Eint_C_Vdw_Kcal/mol";
$activesheet->ColumnHeading(35) = "Eint_A_Elec_Kcal/mol";
$activesheet->ColumnHeading(36) = "Eint_B_Elec_Kcal/mol";
$activesheet->ColumnHeading(37) = "Eint_C_Elec_Kcal/mol";

# Retrieve the trajectory object from the document
my $trajectory = $doc->Trajectory;

# Loop through each frame in the trajectory
for (my $i = $Strat_trj; $i <= $end_trj; ++$i) {
    $trajectory->CurrentFrame = $i;
    
    # Calculate the potential energy for the entire system
    my $temp_All = Documents->New("temp_all.xsd");
    $temp_All->CopyFrom($doc);
    
    my $results_All = Modules->Forcite->Energy->Run($temp_All, Settings(
        Quality => $Quilty,
        '****************************' => $VDW,
        '3DPeriodicElectrostaticSummationMethod' => $Elec,
        CurrentForcefield => $Forcefield,
        AssignForcefieldTypes => 'No',
        ChargeAssignment => 'Use current'
    ));

    my $EPot_All = $temp_All->PotentialEnergy;
    my $EVdw_All = $temp_All->VanDerWaalsEnergy;
    my $Elec_All = $temp_All->ElectrostaticEnergy;
    
    # Calculate the potential energy for SetA (remove SetB and SetC)
    my $temp_SetA = Documents->New("temp_SetA.xsd");
    $temp_SetA->CopyFrom($doc);
    $temp_SetA->UnitCell->Sets($setB)->Atoms->Delete;
    $temp_SetA->UnitCell->Sets($setC)->Atoms->Delete;
    
    my $results_SetA = Modules->Forcite->Energy->Run($temp_SetA, Settings(
        Quality => $Quilty,
        '****************************' => $VDW,
        '3DPeriodicElectrostaticSummationMethod' => $Elec,
        CurrentForcefield => $Forcefield,
        AssignForcefieldTypes => 'No',
        ChargeAssignment => 'Use current'
    ));

    my $EPot_SetA = $temp_SetA->PotentialEnergy;
    my $EVdw_SetA = $temp_SetA->VanDerWaalsEnergy;
    my $EElec_SetA = $temp_SetA->ElectrostaticEnergy;
    
    # Calculate the potential energy for SetB (remove SetA and SetC)
    my $temp_SetB = Documents->New("temp_SetB.xsd");
    $temp_SetB->CopyFrom($doc);
    $temp_SetB->UnitCell->Sets($setA)->Atoms->Delete;
    $temp_SetB->UnitCell->Sets($setC)->Atoms->Delete;
    
    my $results_SetB = Modules->Forcite->Energy->Run($temp_SetB, Settings(
        Quality => $Quilty,
        '****************************' => $VDW,
        '3DPeriodicElectrostaticSummationMethod' => $Elec,
        CurrentForcefield => $Forcefield,
        AssignForcefieldTypes => 'No',
        ChargeAssignment => 'Use current'
    ));

    my $EPot_SetB = $temp_SetB->PotentialEnergy;
    my $EVdw_SetB = $temp_SetB->VanDerWaalsEnergy;
    my $EElec_SetB = $temp_SetB->ElectrostaticEnergy;
    
    # Calculate the potential energy for SetC (remove SetA and SetB)
    my $temp_SetC = Documents->New("temp_SetC.xsd");
    $temp_SetC->CopyFrom($doc);
    $temp_SetC->UnitCell->Sets($setA)->Atoms->Delete;
    $temp_SetC->UnitCell->Sets($setB)->Atoms->Delete;
    
    my $results_SetC = Modules->Forcite->Energy->Run($temp_SetC, Settings(
        Quality => $Quilty,
        '****************************' => $VDW,
        '3DPeriodicElectrostaticSummationMethod' => $Elec,
        CurrentForcefield => $Forcefield,
        AssignForcefieldTypes => 'No',
        ChargeAssignment => 'Use current'
    ));

    my $EPot_SetC = $temp_SetC->PotentialEnergy;
    my $EVdw_SetC = $temp_SetC->VanDerWaalsEnergy;
    my $EElec_SetC = $temp_SetC->ElectrostaticEnergy;
    
    # Calculate the potential energy for SetA+SetB (remove SetC)
    my $temp_SetAB = Documents->New("temp_SetAB.xsd");
    $temp_SetAB->CopyFrom($doc);
    $temp_SetAB->UnitCell->Sets($setC)->Atoms->Delete;
    
    my $results_SetAB = Modules->Forcite->Energy->Run($temp_SetAB, Settings(
        Quality => $Quilty,
        '****************************' => $VDW,
        '3DPeriodicElectrostaticSummationMethod' => $Elec,
        CurrentForcefield => $Forcefield,
        AssignForcefieldTypes => 'No',
        ChargeAssignment => 'Use current'
    ));

    my $EPot_SetAB = $temp_SetAB->PotentialEnergy;
    my $EVdw_SetAB = $temp_SetAB->VanDerWaalsEnergy;
    my $EElec_SetAB = $temp_SetAB->ElectrostaticEnergy;
    
    # Calculate the potential energy for SetA+SetC (remove SetB)
    my $temp_SetAC = Documents->New("temp_SetAC.xsd");
    $temp_SetAC->CopyFrom($doc);
    $temp_SetAC->UnitCell->Sets($setB)->Atoms->Delete;
    
    my $results_SetAC = Modules->Forcite->Energy->Run($temp_SetAC, Settings(
        Quality => $Quilty,
        '****************************' => $VDW,
        '3DPeriodicElectrostaticSummationMethod' =>$Elec,
        CurrentForcefield => $Forcefield,
        AssignForcefieldTypes => 'No',
        ChargeAssignment => 'Use current'
    ));

    my $EPot_SetAC = $temp_SetAC->PotentialEnergy;
    my $EVdw_SetAC = $temp_SetAC->VanDerWaalsEnergy;
    my $EElec_SetAC = $temp_SetAC->ElectrostaticEnergy;
    
    # Calculate the potential energy for SetB+SetC (remove SetA)
    my $temp_SetBC = Documents->New("temp_SetBC.xsd");
    $temp_SetBC->CopyFrom($doc);
    $temp_SetBC->UnitCell->Sets($setA)->Atoms->Delete;
    
    my $results_SetBC = Modules->Forcite->Energy->Run($temp_SetBC, Settings(
        Quality => $Quilty,
        '****************************' => $VDW,
        '3DPeriodicElectrostaticSummationMethod' => $Elec,
        CurrentForcefield => $Forcefield,
        AssignForcefieldTypes => 'No',
        ChargeAssignment => 'Use current'
    ));

    my $EPot_SetBC = $temp_SetBC->PotentialEnergy;
    my $EVdw_SetBC = $temp_SetBC->VanDerWaalsEnergy;
    my $EElec_SetBC = $temp_SetBC->ElectrostaticEnergy;
    
    # Calculate the three-part interaction energy
    my $Eint_Potential_A = $EPot_All - ($EPot_SetAB + $EPot_SetAC) + $EPot_SetA;
    my $Eint_Potential_B = $EPot_All - ($EPot_SetAB + $EPot_SetBC) + $EPot_SetB;
    my $Eint_Potential_C = $EPot_All - ($EPot_SetAC + $EPot_SetBC) + $EPot_SetC;
    # Calculate van der Waals interaction energy
    my $Eint_Vdw_A = $EVdw_All - ($EVdw_SetAB + $EVdw_SetAC) + $EVdw_SetA;
    my $Eint_Vdw_B = $EVdw_All - ($EVdw_SetAB + $EVdw_SetBC) + $EVdw_SetB;
    my $Eint_Vdw_C = $EVdw_All - ($EVdw_SetAC + $EVdw_SetBC) + $EVdw_SetC;

    # Calculate electrostatic interaction energy
    my $Eint_Elec_A = $Elec_All - ($EElec_SetAB + $EElec_SetAC) + $EElec_SetA;
    my $Eint_Elec_B = $Elec_All - ($EElec_SetAB + $EElec_SetBC) + $EElec_SetB;
    my $Eint_Elec_C = $Elec_All - ($EElec_SetAC + $EElec_SetBC) + $EElec_SetC;
    
    # Output the results to the table
    $activesheet->Cell($i - 1, 0) = $trajectory->FrameTime;
    
    # Save structures to table only if SaveStructures is set to "Yes"
    if ($SaveStructures eq "Yes") {
        $activesheet->Cell($i - 1, 1) = $temp_All;
        $activesheet->Cell($i - 1, 5) = $temp_SetA;
        $activesheet->Cell($i - 1, 9) = $temp_SetB;
        $activesheet->Cell($i - 1, 13) = $temp_SetC;
        $activesheet->Cell($i - 1, 17) = $temp_SetAB;
        $activesheet->Cell($i - 1, 21) = $temp_SetAC;
        $activesheet->Cell($i - 1, 25) = $temp_SetBC;
    }
    
    # Always save energy values
    $activesheet->Cell($i - 1, 2) = $EPot_All;
    $activesheet->Cell($i - 1, 3) = $EVdw_All;
    $activesheet->Cell($i - 1, 4) = $Elec_All;
    $activesheet->Cell($i - 1, 6) = $EPot_SetA;
    $activesheet->Cell($i - 1, 7) = $EVdw_SetA;
    $activesheet->Cell($i - 1, 8) = $EElec_SetA;
    $activesheet->Cell($i - 1, 10) = $EPot_SetB;
    $activesheet->Cell($i - 1, 11) = $EVdw_SetB;
    $activesheet->Cell($i - 1, 12) = $EElec_SetB;
    $activesheet->Cell($i - 1, 14) = $EPot_SetC;
    $activesheet->Cell($i - 1, 15) = $EVdw_SetC;
    $activesheet->Cell($i - 1, 16) = $EElec_SetC;
    $activesheet->Cell($i - 1, 18) = $EPot_SetAB;
    $activesheet->Cell($i - 1, 19) = $EVdw_SetAB;
    $activesheet->Cell($i - 1, 20) = $EElec_SetAB;
    $activesheet->Cell($i - 1, 22) = $EPot_SetAC;
    $activesheet->Cell($i - 1, 23) = $EVdw_SetAC;
    $activesheet->Cell($i - 1, 24) = $EElec_SetAC;
    $activesheet->Cell($i - 1, 26) = $EPot_SetBC;
    $activesheet->Cell($i - 1, 27) = $EVdw_SetBC;
    $activesheet->Cell($i - 1, 28) = $EElec_SetBC;
    $activesheet->Cell($i - 1, 29) = $Eint_Potential_A;
    $activesheet->Cell($i - 1, 30) = $Eint_Potential_B;
    $activesheet->Cell($i - 1, 31) = $Eint_Potential_C;
    $activesheet->Cell($i - 1, 32) = $Eint_Vdw_A;
    $activesheet->Cell($i - 1, 33) = $Eint_Vdw_B;
    $activesheet->Cell($i - 1, 34) = $Eint_Vdw_C;
    $activesheet->Cell($i - 1, 35) = $Eint_Elec_A;
    $activesheet->Cell($i - 1, 36) = $Eint_Elec_B;
    $activesheet->Cell($i - 1, 37) = $Eint_Elec_C;
    
    # Delete the temporary structures
    $temp_All->Delete;
    $temp_SetA->Delete;
    $temp_SetB->Delete;
    $temp_SetC->Delete;
    $temp_SetAB->Delete;
    $temp_SetAC->Delete;
    $temp_SetBC->Delete;
}

# Save the output table
$table->Save();