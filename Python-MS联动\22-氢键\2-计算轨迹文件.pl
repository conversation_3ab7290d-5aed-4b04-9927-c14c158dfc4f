##################################################################################################################
# perl                                                                                                           #
#                                                                                                                #
# Author: IMATSOFT.DM                                                                                            #
# Version: 1.1                                                                                                   #
# Tested on: Materials Studio 2020                                                                               #
#                                                                                                                #
# Required modules: Materials Visualizer, BondCalculator                                                         #
# This script automates the process of hydrogen bond analysis in a given molecular structure over all trajectory #
# frames. The script calculates the number of hydrogen bonds and their lengths for each frame and outputs the    #
# data into a new table file. This approach ensures consistent and efficient extraction of hydrogen bond data.   #
#                                                                                                                #
# Date: 2023-03-27                                                                                               #
#                                                                                                                #
##################################################################################################################

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);
use Cwd;


#################################################################################################################
#                                         BEGIN USER INPUT                                                      #
#################################################################################################################

# 定义参数哈希表用于接收GUI传入的参数
my %Args;
GetOptions(\%Args, "Output_Bond_Lengths=s", "Start_Frame=i", "End_Frame=i");

# 获取当前工作目录
my $cwd = getcwd;

# 创建一个输出日志文件
open my $out, '>', "Hbond_process.txt";

# Load the input document with molecular structure information
my $doc = Documents->ActiveDocument;

# 控制是否输出每帧氢键键长的开关 ("Yes" = 输出, "No" = 不输出)
my $output_bond_lengths = $Args{Output_Bond_Lengths};

# 轨迹的起始与终止帧数
my $start_frame = $Args{Start_Frame};      # 默认起始帧为1
my $end_frame = $Args{End_Frame};     # 默认终止帧为1000

# 固定输出文件名，确保有.std扩展名
my $output_filename = "DM-Hbond_Analysis.std";

# 设置默认值（如果GUI没有传入参数）
if (!defined $output_bond_lengths) {
    $output_bond_lengths = "Yes";
}
if (!defined $start_frame) {
    $start_frame = 1;
}
if (!defined $end_frame) {
    $end_frame = 1000;
}

# 输出初始信息到日志文件
print {$out} "开始执行氢键分析脚本...\n";
print {$out} "==================================================\n";
print {$out} "分析文档: " . $doc->Name . "\n";
print {$out} "起始帧: $start_frame\n";
print {$out} "终止帧: $end_frame\n";
print {$out} "输出键长数据: $output_bond_lengths\n";
print {$out} "数据输出文件: $output_filename\n";
print {$out} "==================================================\n";

#################################################################################################################
#                                         END USER INPUT                                                        #
#################################################################################################################	

# Create a new output document to store the hydrogen bond data
my $table = Documents->New($output_filename);
my $sheet = $table->ActiveSheet;
print {$out} "已创建新的数据表: $output_filename\n";

# Set up the output table with appropriate column headers
$sheet->ColumnHeading(0) = "Frame";
$sheet->ColumnHeading(1) = "Number_Hbond";
print {$out} "已设置数据表列标题\n";

# Retrieve the trajectory data from the loaded document
my $trajectory = $doc->Trajectory;
print {$out} "轨迹总帧数: " . $trajectory->NumFrames . "\n";

# 调整终止帧数，如果默认值超过了实际轨迹帧数，则使用实际帧数
if ($end_frame > $trajectory->NumFrames) {
    $end_frame = $trajectory->NumFrames;
    print {$out} "终止帧调整为实际轨迹最大帧数: $end_frame\n";
}

# Create a new sheet to store hydrogen bond lengths if output_bond_lengths is enabled
my $new_sheet;
if ($output_bond_lengths eq "Yes") {
    $new_sheet = $table->Sheets->InsertSheet;
    $new_sheet->Title = "Hbond_Length";
    print {$out} "已创建氢键键长数据表\n";
}

# 预先为主表添加足够的行
for (my $i = $start_frame; $i <= $end_frame; $i++) {
    $sheet->InsertRow($i - $start_frame);
}
print {$out} "已预先添加数据表行\n";
print {$out} "==================================================\n";
print {$out} "开始逐帧分析...\n";

# Loop through each frame in the trajectory
for (my $i = $start_frame; $i <= $end_frame; ++$i) {
    print {$out} "正在处理帧: $i\n";
    $trajectory->CurrentFrame = $i;
    
    $doc->UnitCell->HydrogenBonds->Delete;
    print {$out} "  - 已删除旧的氢键信息\n";
    
    # Perform hydrogen bond calculation for the document
    Tools->BondCalculation->HBonds->Calculate($doc);
    print {$out} "  - 已计算氢键\n";
    
    # Count the number of hydrogen bonds in the current frame
    my $Count_Num = $doc->UnitCell->HydrogenBonds->Count;
    print {$out} "  - 该帧氢键数量: $Count_Num\n";
    
    # Store the frame number and corresponding hydrogen bond count in the main table
    $sheet->Cell($i - $start_frame, 0) = $i;
    $sheet->Cell($i - $start_frame, 1) = $Count_Num;
    
    # 如果启用了输出键长选项，则处理键长数据
    if ($output_bond_lengths eq "Yes") {
        print {$out} "  - 正在处理氢键键长数据\n";
        # Assign column headings dynamically for each frame
        $new_sheet->ColumnHeading($i-$start_frame) = "Frame_$i";
        
        # Retrieve hydrogen bond data for the current frame
        my $Length_Hbond = $doc->UnitCell->HydrogenBonds;
        
        # Initialize a counter for hydrogen bond entries
        my $j = 0;
        
        # 预先添加足够的行来存储当前帧的氢键数据
        if ($Count_Num > 0) {
            for (my $k = 0; $k < $Count_Num; $k++) {
                if ($k >= $new_sheet->RowCount) {
                    $new_sheet->InsertRow($k);
                }
            }
            print {$out} "  - 已添加 $Count_Num 行用于存储键长数据\n";
        }
        
        # Iterate over all hydrogen bonds to extract their lengths
        foreach my $Hbond_Length (@$Length_Hbond) {
            my $length = $Hbond_Length->Length;
            $new_sheet->Cell($j, $i-$start_frame) = $length;
            ++$j;
        }
        print {$out} "  - 已写入 $j 个氢键键长数据\n";
    }
}

print {$out} "==================================================\n";
print {$out} "氢键分析完成！\n";
print {$out} "分析帧数: " . ($end_frame - $start_frame + 1) . "\n";
print {$out} "结果已保存到文件: $output_filename\n";
print {$out} "==================================================\n";

# 关闭输出文件
close($out);

# 导入日志文件到 Materials Studio
Documents->Import("$cwd/Hbond_process.txt");