
# 交联+电场驱动脚本运行步骤详解

该脚本实现了在Materials Studio中进行分子交联的自动化模拟，并支持电场驱动功能。以下是其详细运行步骤：

## 1. 初始化设置
- 设置基本参数：交联目标值、反应半径范围、反应原子名称等
- 配置电场设置（强度、方向等）
- 创建进度文档（Progress.txt）和时间记录文档（Timings.txt）

## 2. 文件准备
- 复制初始结构文件
- 初始化Forcite模块，设置力场、温度、压力等参数
- 检查周期性条件并相应配置电荷计算方法
- 创建用于统计的Study表格（统计表和结构表）

## 3. 初始平衡化
- 执行一次性电场平衡动力学模拟
- 根据设置，以NVE和用户指定的系综进行动态模拟

## 4. 主交联循环
对每个反应距离执行以下步骤：
   - 如果不是第一个半径,则进行下面一步
 

## 5. 每次交联迭代的步骤
   - 创建反应原子集合
   - 计算原子间的距离，满足距离条件则
   - 创建新交联键（直接成键）
   - 平衡新生成的交联结构（几何优化优化）
   - 收集并记录交联统计信息

## 7. 后处理步骤

   - 调整氢原子
   

## 8. 结果分析与输出
 
   - 创建交联原子集合
   - 输出最终交联统计信息
   - 分析网络片段结构
   - 保存轨迹文件（每次动力学模拟生成唯一轨迹）

## 电场功能特点
- 在所有动力学模拟中应用电场（包括初始平衡和主交联过程）
- 用户可设置电场强度、方向和是否应用反电场
- 为每次动力学模拟创建唯一的轨迹文件，以确保数据不会覆盖
- 使用临时重命名方式解决轨迹文件冲突

脚本通过多次迭代形成交联结构，同时监控转化率，直到达到目标转化率或最大反应距离。
