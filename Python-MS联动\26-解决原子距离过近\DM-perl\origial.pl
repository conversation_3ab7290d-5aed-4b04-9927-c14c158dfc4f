#!perl

##################################################################################################################
# perl                                                                                                           #
#                                                                                                                #
# Author: <PERSON><PERSON>                                                                                            #
# Version: 1.0                                                                                                   #
# Tested on: Materials Studio 2020                                                                               #
#                                                                                                                #
# Required modules: Materials Visualizer                                                                         #
# This script detects and fixes atoms that are too close in Materials Studio models, merging atoms that are      #
# closer than a critical distance by placing a new atom at their average position.                               #
# The script automatically calculates distances between atoms, and for atom pairs closer than the threshold,     #
# creates a new atom at the average position and rebuilds the bonding relationships.                             #
# Useful for fixing structural issues caused by atoms being too close after importing external structures or     #
# modifying atom positions.                                                                                      #
#                                                                                                                #
# Date: 2023-07-01                                                                                               #
#                                                                                                                #
#                                                                                                                #
##################################################################################################################

use strict;
use warnings;
use MaterialsScript qw(:all);



# ========== User-Defined Parameters (modify values directly) ==========
my $criticalDist = 0.5;         # Critical distance (Å), atoms closer than this will be merged
my @atomsOfInterest = ();       # Atom types to check, empty array means check all atoms
my $periodicBoundary = 1;       # Consider periodic boundary conditions, 1=yes, 0=no
my $documentName = "test.xsd";  # Document name to process

# ========== Helper Functions ==========

# Calculate distance between two atoms
sub calculateDistance {
    my ($atom1, $atom2) = @_;
    
    my $dx = $atom1->X - $atom2->X;
    my $dy = $atom1->Y - $atom2->Y;
    my $dz = $atom1->Z - $atom2->Z;
    
    return sqrt($dx*$dx + $dy*$dy + $dz*$dz);
}

# Calculate minimum distance considering periodic boundary conditions
sub calculateMinimumDistance {
    my ($atom1, $atom2, $doc) = @_;
    
    # If not considering periodicity, return Euclidean distance
    if (!$periodicBoundary) {
        return calculateDistance($atom1, $atom2);
    }
    
    # Get unit cell vectors
    my $unitCell = $doc->UnitCell;
    
    # Safely check if lattice parameters can be obtained
    eval {
        my $test = $unitCell->Lattice3D->LengthA;
    };
    if ($@) {
        # Cannot get lattice parameters, possibly a non-periodic structure
        return calculateDistance($atom1, $atom2);
    }
    
    # Get cell lengths
    my $a = $unitCell->Lattice3D->LengthA;
    my $b = $unitCell->Lattice3D->LengthB;
    my $c = $unitCell->Lattice3D->LengthC;
    
    # Get cell angles (radians)
    my $alpha = $unitCell->Lattice3D->AngleAlpha * 3.14159 / 180.0;
    my $beta = $unitCell->Lattice3D->AngleBeta * 3.14159 / 180.0;
    my $gamma = $unitCell->Lattice3D->AngleGamma * 3.14159 / 180.0;
    
    # Calculate cell vector components
    # a vector is always along x-axis
    my @a_vec = ($a, 0, 0);
    
    # b vector is in xy-plane
    my @b_vec = ($b * cos($gamma), $b * sin($gamma), 0);
    
    # c vector calculation is more complex
    my $cx = $c * cos($beta);
    my $cy = $c * (cos($alpha) - cos($beta) * cos($gamma)) / sin($gamma);
    my $cz = sqrt($c * $c - $cx * $cx - $cy * $cy);
    my @c_vec = ($cx, $cy, $cz);
    
    # Get atom coordinates
    my ($x1, $y1, $z1) = ($atom1->X, $atom1->Y, $atom1->Z);
    my ($x2, $y2, $z2) = ($atom2->X, $atom2->Y, $atom2->Z);
    
    # Calculate minimum distance across all possible images
    my $minDist = 1e10;  # Set a very large initial value
    
    for (my $nx = -1; $nx <= 1; $nx++) {
        for (my $ny = -1; $ny <= 1; $ny++) {
            for (my $nz = -1; $nz <= 1; $nz++) {
                # Calculate coordinates in periodic image
                my $tx = $x2 + $nx * $a_vec[0] + $ny * $b_vec[0] + $nz * $c_vec[0];
                my $ty = $y2 + $nx * $a_vec[1] + $ny * $b_vec[1] + $nz * $c_vec[1];
                my $tz = $z2 + $nx * $a_vec[2] + $ny * $b_vec[2] + $nz * $c_vec[2];
                
                # Calculate distance
                my $dx = $x1 - $tx;
                my $dy = $y1 - $ty;
                my $dz = $z1 - $tz;
                my $dist = sqrt($dx*$dx + $dy*$dy + $dz*$dz);
                
                # Update minimum distance
                if ($dist < $minDist) {
                    $minDist = $dist;
                }
            }
        }
    }
    
    return $minDist;
}

# Calculate average position of two atoms
sub calculateAveragePosition {
    my ($atom1, $atom2) = @_;
    
    my $avgX = ($atom1->X + $atom2->X) / 2;
    my $avgY = ($atom1->Y + $atom2->Y) / 2;
    my $avgZ = ($atom1->Z + $atom2->Z) / 2;
    
    return ($avgX, $avgY, $avgZ);
}

# Check if atom exists (without using Exists method)
sub atomExists {
    my ($atom) = @_;
    
    # Use eval to wrap access to atom properties, return 0 if it fails
    eval {
        my $x = $atom->X;
        return 1;
    };
    
    # If eval fails (throws exception), return 0 indicating atom doesn't exist
    if ($@) {
        return 0;
    }
    
    return 1;
}

# Check if structure is periodic
sub isPeriodic {
    my ($doc) = @_;
    
    # Use eval to try to get lattice parameters, if successful, consider it periodic
    eval {
        my $a = $doc->UnitCell->Lattice3D->LengthA;
        my $b = $doc->UnitCell->Lattice3D->LengthB;
        my $c = $doc->UnitCell->Lattice3D->LengthC;
        return 1;
    };
    
    if ($@) {
        return 0;
    }
    
    return 1;
}

# Create bond between atoms
sub createBondBetweenAtoms {
    my ($doc, $atom1, $atom2, $bondOrder) = @_;
    
    # No longer using bond type parameters, create default bond
    
    # Try multiple methods for creating bonds
    eval {
        # Method 1: Use user-provided example format, but without specifying type
        $doc->CreateBond($atom1, $atom2);
        return 1;
    };
    
    if ($@) {
        # Method 1 failed, try second method
        eval {
            # Method 2: Use UnitCell's CreateBond method, but without specifying type
            $doc->UnitCell->CreateBond($atom1, $atom2);
            return 1;
        };
    }
    
    if ($@) {
        # Method 2 failed, try third method
        eval {
            # Method 3: Use Bonds collection's Add method
            my $bonds = $doc->UnitCell->Bonds("All");
            my $newBond = $bonds->Add($atom1, $atom2);
            return 1;
        };
    }
    
    if ($@) {
        # Method 3 failed, try fourth method
        eval {
            # Method 4: Use auto-calculation feature to create bonds
            $doc->CalculateBonds();
            return 1;
        };
    }
    
    # If still failed, return 0
    if ($@) {
        return 0;
    }
    
    return 1;
}

# ========== Main Program ==========

# Get current document
my $doc = $Documents{$documentName};
if (!$doc) {
    print "Error: Document '$documentName' not found! Script terminated.\n";
    exit;
}

# Check if UnitCell exists
if (!defined $doc->UnitCell) {
    print "Error: Document '$documentName' does not contain a UnitCell! Script terminated.\n";
    exit;
}

# Create log document
my $logDoc = Documents->New("TooCloseAtoms_Log.txt");
$logDoc->Append("Too Close Atoms Detection and Fixing Tool v1.0\n");
$logDoc->Append("-------------------------------------------\n");
$logDoc->Append("Document name: " . $doc->Name . "\n");
$logDoc->Append("Critical distance: " . $criticalDist . "Å\n");
if (@atomsOfInterest) {
    $logDoc->Append("Atom types to check: " . join(", ", @atomsOfInterest) . "\n");
}
else {
    $logDoc->Append("Atom types to check: All\n");
}
$logDoc->Append("Consider periodic boundary conditions: " . ($periodicBoundary ? "Yes" : "No") . "\n");

# Check if structure is periodic
my $is_periodic = isPeriodic($doc);
if ($periodicBoundary && !$is_periodic) {
    $logDoc->Append("Warning: Periodic boundary requested but structure has no periodicity information. Using Euclidean distance calculation.\n");
    $periodicBoundary = 0;
}

# Record cell information
if ($periodicBoundary && $is_periodic) {
    $logDoc->Append("\nCell information:\n");
    eval {
        $logDoc->Append(sprintf("a = %.4f, b = %.4f, c = %.4f\n", 
            $doc->UnitCell->Lattice3D->LengthA,
            $doc->UnitCell->Lattice3D->LengthB,
            $doc->UnitCell->Lattice3D->LengthC));
        $logDoc->Append(sprintf("alpha = %.2f, beta = %.2f, gamma = %.2f\n", 
            $doc->UnitCell->Lattice3D->AngleAlpha,
            $doc->UnitCell->Lattice3D->AngleBeta,
            $doc->UnitCell->Lattice3D->AngleGamma));
    };
    if ($@) {
        $logDoc->Append("Error getting cell parameters: $@\n");
    }
}

$logDoc->Append("-------------------------------------------\n\n");

# Create a new document to save the processed structure
my $newDoc = Documents->New($doc->Name . "_Fixed.xsd");
$newDoc->CopyFrom($doc);

# Get all atoms
my $atoms = $newDoc->UnitCell->Atoms;
my @atomList = @$atoms;

# Filter atoms of interest
if (@atomsOfInterest) {
    my @filteredAtoms;
    foreach my $atom (@atomList) {
        if (grep { $_ eq $atom->ElementSymbol } @atomsOfInterest) {
            push @filteredAtoms, $atom;
        }
    }
    @atomList = @filteredAtoms;
}

$logDoc->Append("Original atom count: " . scalar(@$atoms) . "\n");
$logDoc->Append("Atoms to check: " . scalar(@atomList) . "\n\n");

my $mergeCount = 0;
my $didMerge = 1;  # Initialize to 1 to ensure entering the loop

# Loop until no atom pairs need merging
while ($didMerge) {
    $didMerge = 0;
    
    # Iterate through all possible atom pairs
    for (my $i = 0; $i < @atomList; $i++) {
        # Skip if current atom is marked for deletion
        next if (!defined $atomList[$i] || !atomExists($atomList[$i]));
        
        for (my $j = $i + 1; $j < @atomList; $j++) {
            # Skip if compared atom is marked for deletion
            next if (!defined $atomList[$j] || !atomExists($atomList[$j]));
            
            # Calculate minimum distance between atoms
            my $distance = calculateMinimumDistance($atomList[$i], $atomList[$j], $newDoc);
            
            # If distance is less than critical value, merge these atoms
            if ($distance < $criticalDist) {
                # Calculate average position
                my ($avgX, $avgY, $avgZ) = calculateAveragePosition($atomList[$i], $atomList[$j]);
                
                # Record atoms to be merged
                $logDoc->Append(sprintf("Merging atoms: %s(%.4f,%.4f,%.4f) and %s(%.4f,%.4f,%.4f) Distance: %.4f\n", 
                    $atomList[$i]->ElementSymbol, $atomList[$i]->X, $atomList[$i]->Y, $atomList[$i]->Z,
                    $atomList[$j]->ElementSymbol, $atomList[$j]->X, $atomList[$j]->Y, $atomList[$j]->Z,
                    $distance));
                
                # Create a new atom at average position
                my $newAtom;
                
                # Use Material Studio official recommended Point function to create atom
                eval {
                    # Use Point function to create coordinates, conforming to MS API standard
                    $newAtom = $newDoc->UnitCell->CreateAtom($atomList[$i]->ElementSymbol, 
                                                           Point(X => $avgX, Y => $avgY, Z => $avgZ));
                    $logDoc->Append("Successfully created atom using method 1\n");
                };
                
                if ($@ || !defined $newAtom) {
                    $logDoc->Append("Method 1 failed to create atom, trying method 2...\n");
                    eval {
                        # Try passing a single coordinate value
                        $newAtom = $newDoc->UnitCell->CreateAtom($atomList[$i]->ElementSymbol, 
                                                               Point(XYZ => [$avgX, $avgY, $avgZ]));
                        $logDoc->Append("Successfully created atom using method 2\n");
                    };
                }
                
                if ($@ || !defined $newAtom) {
                    $logDoc->Append("Method 2 failed to create atom, trying method 3...\n");
                    eval {
                        # Try cloning atom and modifying position
                        $newAtom = $atomList[$i]->Clone();
                        $newAtom->X = $avgX;
                        $newAtom->Y = $avgY;
                        $newAtom->Z = $avgZ;
                        $logDoc->Append("Successfully created atom using method 3\n");
                    };
                }
                
                if ($@ || !defined $newAtom) {
                    # All methods failed, record error
                    $logDoc->Append("Error: Cannot create new atom: $@\n");
                    print "Error: Cannot create new atom: $@\n"; 
                    next;
                } else {
                    $logDoc->Append("Successfully created new atom\n");
                }
                
                # After creating atom, calculate bonds
                my $bondCreated = 0;
                
                # Save atom bond information
                my @atom1Bonds = @{$atomList[$i]->Bonds};
                my @atom2Bonds = @{$atomList[$j]->Bonds};
                
                # Rebuild bonds for new atom
                foreach my $bond (@atom1Bonds) {
                    my $atom1 = $bond->Atom1;
                    my $atom2 = $bond->Atom2;
                    my $otherAtom;
                    
                    if ($atom1->Name eq $atomList[$i]->Name) {
                        $otherAtom = $atom2;
                    } else {
                        $otherAtom = $atom1;
                    }
                    
                    # Avoid creating bonds with deleted atoms
                    if (defined $otherAtom && atomExists($otherAtom) && $otherAtom->Name ne $atomList[$j]->Name) {
                        # Get atom names (prevent undefined)
                        my $newAtomName = "new atom";
                        my $otherAtomName = "other atom";
                        
                        eval {
                            $newAtomName = $newAtom->Name if defined $newAtom->Name;
                            $otherAtomName = $otherAtom->Name if defined $otherAtom->Name;
                        };
                        
                        # Use fault-tolerant bond creation function
                        if (createBondBetweenAtoms($newDoc, $newAtom, $otherAtom)) {
                            $bondCreated++;
                            $logDoc->Append("Successfully created bond: $newAtomName <-> $otherAtomName\n");
                        } else {
                            $logDoc->Append("Warning: All bond creation methods failed: $newAtomName <-> $otherAtomName\n");
                        }
                    }
                }
                
                foreach my $bond (@atom2Bonds) {
                    my $atom1 = $bond->Atom1;
                    my $atom2 = $bond->Atom2;
                    my $otherAtom;
                    
                    if ($atom1->Name eq $atomList[$j]->Name) {
                        $otherAtom = $atom2;
                    } else {
                        $otherAtom = $atom1;
                    }
                    
                    # Avoid creating bonds with deleted atoms or duplicate bonds
                    if (defined $otherAtom && atomExists($otherAtom) && $otherAtom->Name ne $atomList[$i]->Name) {
                        my $createBond = 1;
                        
                        # Check if bond already exists
                        foreach my $existingBond (@{$newAtom->Bonds}) {
                            if (atomExists($existingBond->Atom1) && atomExists($existingBond->Atom2)) {
                                if (($existingBond->Atom1->Name eq $otherAtom->Name) || 
                                    ($existingBond->Atom2->Name eq $otherAtom->Name)) {
                                    $createBond = 0;
                                    last;
                                }
                            }
                        }
                        
                        if ($createBond) {
                            # Get atom names (prevent undefined)
                            my $newAtomName = "new atom";
                            my $otherAtomName = "other atom";
                            
                            eval {
                                $newAtomName = $newAtom->Name if defined $newAtom->Name;
                                $otherAtomName = $otherAtom->Name if defined $otherAtom->Name;
                            };
                            
                            # Use fault-tolerant bond creation function
                            if (createBondBetweenAtoms($newDoc, $newAtom, $otherAtom)) {
                                $bondCreated++;
                                $logDoc->Append("Successfully created bond: $newAtomName <-> $otherAtomName\n");
                            } else {
                                $logDoc->Append("Warning: All bond creation methods failed: $newAtomName <-> $otherAtomName\n");
                            }
                        }
                    }
                }
                
                # Record number of bonds created
                $logDoc->Append("Created $bondCreated bonds for the new atom\n");
                
                # If no bonds were created, try automatic bond calculation
                if ($bondCreated == 0) {
                    eval {
                        $newDoc->CalculateBonds();
                        $logDoc->Append("Used automatic bond calculation feature\n");
                    };
                }
                
                # Try to delete original atoms
                eval {
                    $atomList[$i]->Delete;
                    $atomList[$j]->Delete;
                };
                if ($@) {
                    $logDoc->Append("Warning: Error deleting atoms: $@\n");
                }
                
                # Update array, add new atom to the end of the list
                $atomList[$i] = undef;
                $atomList[$j] = undef;
                push @atomList, $newAtom;
                
                $mergeCount++;
                $didMerge = 1;
                
                # Once a pair is found and merged, break out of inner loop
                last;
            }
        }
        
        # If a pair of atoms was merged, restart outer loop
        last if $didMerge;
    }
    
    # If completed a round of merging, clean up undefined elements in array
    if ($didMerge) {
        @atomList = grep { defined $_ && atomExists($_) } @atomList;
    }
}

# Update log
$logDoc->Append("\nTotal merged $mergeCount pairs of atoms\n");
$logDoc->Append("Final atom count: " . scalar(@{$newDoc->UnitCell->Atoms}) . "\n");
$logDoc->Append("\nProcessing complete!\n");

# Print results to console
print "Processing complete! Merged $mergeCount pairs of atoms. Results saved as \"" . $newDoc->Name . "\"\n";
