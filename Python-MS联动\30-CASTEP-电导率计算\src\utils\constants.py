"""
物理常数定义模块
包含电导率计算所需的所有物理常数
"""

import math

# 基本物理常数
VACUUM_PERMITTIVITY = 8.854187817e-12  # 真空介电常数 ε₀ (F/m)
ELEMENTARY_CHARGE = 1.602176634e-19    # 元电荷 e (C)
PLANCK_CONSTANT = 6.62607015e-34       # 普朗克常数 h (J·s)
HBAR = PLANCK_CONSTANT / (2 * math.pi) # 约化普朗克常数 ℏ (J·s)
SPEED_OF_LIGHT = 299792458             # 光速 c (m/s)

# 单位转换常数
EV_TO_JOULE = ELEMENTARY_CHARGE        # eV 转 J
EV_TO_RAD_PER_S = ELEMENTARY_CHARGE / HBAR  # eV 转 rad/s

# 计算相关常数
EPSILON_0 = VACUUM_PERMITTIVITY        # 真空介电常数别名

class PhysicalConstants:
    """物理常数类，提供便捷的常数访问和单位转换"""
    
    # 基本常数
    epsilon_0 = VACUUM_PERMITTIVITY
    e = ELEMENTARY_CHARGE
    h = PLANCK_CONSTANT
    hbar = HBAR
    c = SPEED_OF_LIGHT
    
    @staticmethod
    def eV_to_rad_per_s(energy_eV):
        """
        将能量从eV转换为角频率(rad/s)
        
        Args:
            energy_eV (float or array): 能量值(eV)
            
        Returns:
            float or array: 角频率(rad/s)
        """
        return energy_eV * EV_TO_RAD_PER_S
    
    @staticmethod
    def eV_to_Hz(energy_eV):
        """
        将能量从eV转换为频率(Hz)
        
        Args:
            energy_eV (float or array): 能量值(eV)
            
        Returns:
            float or array: 频率(Hz)
        """
        return energy_eV * EV_TO_RAD_PER_S / (2 * math.pi)
    
    @staticmethod
    def conductivity_formula(omega, epsilon_2):
        """
        计算光学导率的核心公式: σ(ω) = ω·ε₂(ω)·ε₀
        
        Args:
            omega (float or array): 角频率(rad/s)
            epsilon_2 (float or array): 介电函数虚部
            
        Returns:
            float or array: 光学导率(S/m)
        """
        return omega * epsilon_2 * EPSILON_0

# 默认计算参数
DEFAULT_FIT_POINTS = 5      # 默认外推点数
DEFAULT_ENERGY_THRESHOLD = 0.1  # 默认低能阈值(eV)
MIN_FIT_POINTS = 2          # 最小外推点数

# 文件格式相关
SUPPORTED_EXTENSIONS = ['.epsilon', '.csv', '.txt', '.xlsx', '.xls']
DEFAULT_OUTPUT_FILENAME = 'conductivity_results.csv'

# 数值计算参数
NUMERICAL_TOLERANCE = 1e-12  # 数值计算容差
MAX_ENERGY_RANGE = 100.0     # 最大能量范围(eV)
MIN_ENERGY_STEP = 1e-6       # 最小能量步长(eV)
