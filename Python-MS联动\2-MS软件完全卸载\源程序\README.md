# Materials Studio 一键卸载工具

这是一个专门用于完全卸载 Materials Studio 及其相关组件的工具。工具具有精美的图形界面，操作简单，并确保只能使用一次。

## 功能特点

- 自动查找并卸载 Materials Studio 主程序
- 自动卸载 BIOVIA License Pack
- 清理系统中残留的 Biovia 和 Accelrys 文件夹
- 清理相关注册表项
- 一次性使用设计，需要序列码激活
- 精美的图形界面和卸载进度显示

## 使用方法

1. 将程序复制到 Materials Studio 的安装目录
2. 双击运行 `MS卸载助手.exe` 
3. 输入有效的激活序列码
4. 点击"开始卸载"按钮
5. 等待卸载完成

## 注意事项

- 卸载前请确保关闭所有相关程序
- 程序需要管理员权限才能运行
- 卸载过程不可逆，请确保需要卸载后再操作
- 此程序只能使用一次，若需再次使用，请联系开发者获取新的序列码

## 开发信息

此程序使用 Python 开发，主要依赖项：

- tkinter (GUI界面)
- PIL/Pillow (图像处理)
- PyInstaller (打包)

## 如何构建

如需自行构建程序：

1. 安装所需依赖: `pip install -r requirements.txt`
2. 运行构建脚本: `python build.py`
3. 构建后的可执行文件将生成在 `dist` 目录中

## 许可证

专用软件，未经授权不得分发或修改。 