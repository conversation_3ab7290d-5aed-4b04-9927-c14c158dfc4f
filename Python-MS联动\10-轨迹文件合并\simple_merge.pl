##################################################################################################################
# perl                                                                                                           #
#                                                                                                                #
# Author: DMPB                                                                                                   #
# Version: 1.0                                                                                                   #
# Tested on: Materials Studio 2020                                                                               #
#                                                                                                                #
# Required modules: Materials Visualizer                                                                         #
# This script provides a simplified tool for merging multiple trajectory files (.xtd) into a single trajectory.  #
# It sequentially combines frames from source trajectory files, maintaining their original order. This tool is   #
# particularly useful for analyzing simulation results spread across multiple trajectory files or for combining  #
# trajectories from different simulation stages. The merged trajectory preserves all structural information      #
# and facilitates comprehensive analysis of molecular dynamics over extended timeframes.                         #
#                                                                                                                #
# Date: 2023-07-15                                                                                               #
#                                                                                                                #
#                                                                                                                #
##################################################################################################################

#!perl

use strict;
use warnings;
use MaterialsScript qw(:all);

#################################################################################################################
#                                         BEGIN USER INPUT                                                      #
#################################################################################################################

# 设置源轨迹文件和目标文件
my @sourceTrajectoryFiles = (
    "trajectory1.xtd",
    "trajectory2.xtd",
    # 可继续添加更多文件...
);
#################################################################################################################
#                                         END USER INPUT                                                        #
#################################################################################################################	
my $mergedTrajectoryFile = "merged_trajectory.xtd";

# 用第一个文件创建输出文档
my $firstDoc = $Documents{$sourceTrajectoryFiles[0]};
my $outputDoc = Documents->New($mergedTrajectoryFile);
#$outputDoc->CopyFrom($firstDoc);

# 合并所有轨迹文件
foreach my $srcFile (@sourceTrajectoryFiles) {
    my $srcDoc = $Documents{$srcFile};
    my $numFrames = $srcDoc->Trajectory->NumFrames;
    
    # 合并轨迹帧
    if ($srcFile eq $sourceTrajectoryFiles[0]) {
        # 第一个文件的第2帧到最后一帧(因为第一帧已通过CopyFrom复制)
        if ($numFrames > 0) {
            $outputDoc->Trajectory->AppendFramesFrom($srcDoc, Frames(Start => 1, End => $numFrames));
        }
    } else {
        # 其他文件的所有帧
        $outputDoc->Trajectory->AppendFramesFrom($srcDoc, Frames(Start => 1, End => $numFrames));
    }
}

# 保存结果
$outputDoc->Save();

print "合并完成! 文件已保存为 $mergedTrajectoryFile\n";