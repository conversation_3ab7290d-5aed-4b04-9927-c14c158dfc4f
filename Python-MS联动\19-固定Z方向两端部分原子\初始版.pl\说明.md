# Z方向边界原子固定工具使用说明

## 工具介绍

此脚本用于在周期性结构中固定Z方向上下边界一定距离范围内的所有原子。特别适用于模拟材料界面、表面吸附、剪切模拟以及其他需要固定晶体边界同时允许内部原子自由移动的场景。

## 技术规格

- **开发者**：DMPB
- **版本**：1.1
- **测试环境**：Materials Studio 2020
- **依赖模块**：Materials Visualizer

## 计算流程

脚本的执行流程如下：

1. **参数初始化**
   - 获取当前打开的文档（默认为`test.xsd`）
   - 设置默认参数：
     - 边界距离：3.0Å
     - 固定方向：X、Y、Z三个方向（XYZ）
     - 是否创建原子集合：是
     - 固定原子集合名称：
       - 全部固定原子：`Fixed_Atoms`
       - 上边界固定原子：`Top`
       - 下边界固定原子：`Bottom`
     - 日志文件名：`fix_atoms_log.txt`

2. **文档验证**
   - 创建输出日志文件
   - 验证当前文档是否包含周期性单元格信息
   - 如无周期结构，脚本终止并提示错误

3. **参数计算**
   - 获取单元格Z方向长度参数
   - 计算需要固定原子的区域范围：
     - 下边界区域(Bottom)：0 到 `边界距离`(Å)
     - 上边界区域(Top)：`(最大Z值 - 边界距离)` 到 `最大Z值`(Å)

4. **原子筛选**
   - 获取单元格中的所有原子
   - 遍历原子，检查其Z坐标是否在需要固定的边界范围内
   - 将满足条件的原子添加到待固定列表中，同时按上边界(Top)和下边界(Bottom)进行分类
   - 如果没有找到满足条件的原子，脚本终止并记录警告

5. **原子集合创建（可选）**
   - 如果设置了创建原子集合，检查同名集合是否已存在
   - 如存在同名集合，则删除旧集合
   - 创建三个新的原子集合：
     - `Fixed_Atoms`：包含所有固定原子
     - `Top`：包含上边界固定原子
     - `Bottom`：包含下边界固定原子

6. **原子固定操作**
   - 根据用户指定的方向（X、Y、Z或其组合）固定选定的原子
   - 通过集合来固定原子，可以分别操作`Top`和`Bottom`集合进行不同设置
   - 或直接对筛选出的原子进行固定操作

7. **结果保存与输出**
   - 保存修改后的文档
   - 记录执行结果摘要：
     - 总原子数
     - 已固定原子数（包括Top和Bottom区域的原子数量）
     - 固定比例（百分比）
   - 保存详细的日志文件
   - 向控制台输出完成信息

## 应用场景

此工具适用于以下场景：

- 材料界面模拟
- 表面吸附模拟
- 剪切模拟
- 需要固定晶体边界的计算
- 拉伸测试模拟
- 边界约束下的分子动力学模拟
- 上下边界不同固定约束条件的模拟

## 使用注意事项

1. 确保当前已打开目标文档
2. 确保目标文档包含周期性单元格信息
3. 根据实际需要调整边界距离参数
4. 固定方向可根据模拟需求调整（X、Y、Z或其组合）
5. 可以为上边界(Top)和下边界(Bottom)分别设置不同的固定条件
6. 固定原子后，建议查看日志文件以确认操作成功
7. 检查固定原子数量与总原子数的比例是否合理

## 参数自定义

如需调整默认参数，请修改脚本中"USER INPUT SECTION"部分：

```perl
# 基础配置参数
my $doc = $Documents{"test.xsd"};       # 当前打开的文档
my $boundaryDistance = 3.0;             # 从晶格边界固定原子的距离范围（默认3.0Å）
my $fixDirections = "XYZ";              # 固定方向："X", "Y", "Z", "XY", "XZ", "YZ", 或 "XYZ"
my $createSets = "yes";                 # 是否创建包含固定原子的集合："yes" 或 "no"
my $fixedSetName = "Fixed_Atoms";       # 总固定原子集合名称
my $topSetName = "Top";                 # 上边界固定原子集合名称
my $bottomSetName = "Bottom";           # 下边界固定原子集合名称
my $logFileName = "fix_atoms_log.txt";  # 日志文件名
```

## 结果验证

执行脚本后，建议：
1. 查看创建的日志文件，确认固定的原子数量
2. 在Materials Studio中查看固定的原子：
   - 通过`Fixed_Atoms`集合查看所有固定原子
   - 通过`Top`集合查看上边界固定原子
   - 通过`Bottom`集合查看下边界固定原子
3. 确认固定的原子位置是否符合预期

## 更新日志

- **版本1.1**（2024-06-25）
  - 初始版本发布，实现基本的边界原子固定功能
  
- **版本1.2**（待发布）
  - 增加上下边界原子分类功能
  - 新增`Top`和`Bottom`集合，分别代表上边界和下边界固定原子
  - 支持对上下边界原子应用不同的固定条件
