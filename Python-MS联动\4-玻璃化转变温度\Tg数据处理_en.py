import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.ticker import MaxNLocator
from matplotlib import style
from scipy.signal import savgol_filter
from scipy.optimize import curve_fit
import pandas as pd
import os
import seaborn as sns
import tkinter as tk
from tkinter import filedialog, ttk, messagebox, scrolledtext
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.patches import Circle
import sys

# Set global plot style
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_context("notebook", font_scale=1.1)
plt.rcParams['font.sans-serif'] = ['SimHei']  # Set Chinese font
plt.rcParams['axes.unicode_minus'] = False    # Fix minus sign display issue

# Fix font issue - replace superscript
def format_volume_unit(with_superscript=False):
    """Return volume unit based on font support"""
    if with_superscript:
        return "cm³/g"
    else:
        return "cm3/g"

# Set whether to use superscript
USE_SUPERSCRIPT = False
VOLUME_UNIT = format_volume_unit(USE_SUPERSCRIPT)
DENSITY_UNIT = "g/cm3" if not USE_SUPERSCRIPT else "g/cm³"

# Draggable Tg point class
class DraggableTg:
    def __init__(self, ax, x, y, temperatures, densities, on_update_callback=None, y_min=None, y_max=None):
        self.ax = ax
        self.temperatures = temperatures
        self.densities = densities
        self.x = x
        self.y = y
        self.y_min = y_min
        self.y_max = y_max
        
        # No longer create red Tg point, only use vertical dashed line
        # Use virtual line to mark Tg position
        min_temp, max_temp = min(temperatures), max(temperatures)
        self.tg_line = self.ax.axvline(x=x, color='black', linestyle='--', linewidth=1.0, alpha=0.7, zorder=4)
        
        # Text label
        self.text = ax.text(x+5, min(densities) + 0.9*(max(densities)-min(densities)),
                            f'Tg = {x:.0f}K', fontsize=12, color='darkred',
                            bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5'))
        
        self.press = None
        self.background = None
        self.connect()
        self.on_update_callback = on_update_callback
        
        # Save references to high and low temperature fitting lines
        self.high_line = None
        self.low_line = None
        
        # Add variables for fit line control points dragging
        self.fit_line_points = []  # Store fit line control points
        self.active_point = None   # Currently dragging point
        self.high_temp_range = None  # High temperature fitting range
        self.low_temp_range = None   # Low temperature fitting range
        self.show_control_points = True  # Whether to show control points
        self.calc_intercept = None  # Calculated intersection position
        
        # Cache fitting parameters to reduce calculation frequency
        self.cached_high_params = None
        self.cached_low_params = None
        self.last_fit_position = None
        self.drag_threshold = 0.5  # Drag threshold (temperature units)
        
        # Pre-calculate fitting parameters to reduce initial drag lag
        high_params, low_params = self.fit_two_regions(x)
        self.cached_high_params = high_params
        self.cached_low_params = low_params
        self.last_fit_position = x
    
    def connect(self):
        """Connect all events"""
        # Connect to the entire canvas instead of point object
        self.cidpress = self.ax.figure.canvas.mpl_connect('button_press_event', self.on_press)
        self.cidrelease = self.ax.figure.canvas.mpl_connect('button_release_event', self.on_release)
        self.cidmotion = self.ax.figure.canvas.mpl_connect('motion_notify_event', self.on_motion)
    
    def on_press(self, event):
        """When mouse is pressed"""
        if event.inaxes != self.ax:
            return
        
        # Check if mouse is near the virtual line (±5 pixels)
        # Convert event coordinates to data coordinates
        mouse_x = event.xdata
        mouse_y = event.ydata
        
        # Check if clicked on a fit line control point
        for i, point in enumerate(self.fit_line_points):
            if point and abs(mouse_x - point[0]) < 5 and abs(mouse_y - point[1]) < 0.05 * (max(self.densities) - min(self.densities)):
                self.active_point = i
                self.press = (point[0], point[1], event.xdata, event.ydata)
                
                # Increase point size as feedback when mouse is pressed
                for artist in self.ax.lines[:]:
                    if hasattr(artist, 'is_control_point') and artist.is_control_point:
                        idx = self.ax.lines.index(artist)
                        if idx == i:
                            artist.set_markersize(10)  # Increase current control point size
                            self.ax.figure.canvas.draw_idle()
                return
        
        # If mouse is near Tg line, capture drag event
        if abs(mouse_x - self.x) < 5:  # 5 is pixel-level error, can be adjusted
            self.press = (self.x, event.xdata, event.ydata)
            self.active_point = None
            
            # Change Tg line style
            self.tg_line.set_linewidth(2.0)
            self.tg_line.set_alpha(1.0)
            self.ax.figure.canvas.draw_idle()
    
    def on_motion(self, event):
        """When mouse is moving"""
        if self.press is None:
            return
        if event.inaxes != self.ax:
            return
        
        # If dragging a fit line control point
        if self.active_point is not None:
            x0, y0, xpress, ypress = self.press
            
            # Calculate movement increment
            dx = event.xdata - xpress
            dy = event.ydata - ypress
            
            # Update fitting line parameters based on dragged point
            if self.high_line and self.low_line:
                min_temp, max_temp = min(self.temperatures), max(self.temperatures)
                
                if self.active_point == 0:  # High temperature line left endpoint
                    # Update high temperature line left endpoint position
                    new_x = min(max(x0 + dx, min_temp), self.x)  # Limit within data range and not exceed Tg point
                    new_y = y0 + dy
                    self.fit_line_points[0] = (new_x, new_y)
                    self.update_fit_params_from_points()
                
                elif self.active_point == 1:  # High temperature line right endpoint
                    # Update high temperature line right endpoint position
                    new_x = min(max(x0 + dx, self.x), max_temp)  # Limit within data range and not less than Tg point
                    new_y = y0 + dy
                    self.fit_line_points[1] = (new_x, new_y)
                    self.update_fit_params_from_points()
                
                elif self.active_point == 2:  # Low temperature line left endpoint
                    # Update low temperature line left endpoint position
                    new_x = min(max(x0 + dx, min_temp), self.x)  # Limit within data range and not exceed Tg point
                    new_y = y0 + dy
                    self.fit_line_points[2] = (new_x, new_y)
                    self.update_fit_params_from_points()
                
                elif self.active_point == 3:  # Low temperature line right endpoint
                    # Update low temperature line right endpoint position
                    new_x = min(max(x0 + dx, self.x), max_temp)  # Limit within data range and not less than Tg point
                    new_y = y0 + dy
                    self.fit_line_points[3] = (new_x, new_y)
                    self.update_fit_params_from_points()
                
                # Update fitting lines
                self.update_fit_lines_from_points()
                
                # Redraw the figure
                self.ax.figure.canvas.draw_idle()
                
                # Callback function to notify external parameters have been updated
                if self.on_update_callback:
                    self.on_update_callback(self.x, self.cached_high_params, self.cached_low_params)
            
            return
        
        # If dragging Tg line
        # Only allow horizontal movement (X axis direction)
        x0, xpress, ypress = self.press
        dx = event.xdata - xpress
        
        # Update x coordinate (limit within temperature range)
        min_temp = min(self.temperatures)
        max_temp = max(self.temperatures)
        drag_x = min(max(x0 + dx, min_temp), max_temp)
        
        try:
            # Optimization: only recalculate fitting parameters when movement exceeds threshold
            if (self.last_fit_position is None or 
                abs(drag_x - self.last_fit_position) > self.drag_threshold):
                
                # Update fitting parameters
                high_params, low_params = self.fit_two_regions(drag_x)
                self.cached_high_params = high_params
                self.cached_low_params = low_params
                self.last_fit_position = drag_x
            else:
                # Use cached fitting parameters
                high_params = self.cached_high_params
                low_params = self.cached_low_params
            
            # Calculate the actual intersection of two fitting lines
            m1, b1 = high_params
            m2, b2 = low_params
            
            # When dragging, move directly to mouse position for better responsiveness
            # Only calculate actual intersection point when mouse is released
            new_x = drag_x
            
            # Calculate y value at mouse position
            high_y = np.polyval(high_params, new_x)
            low_y = np.polyval(low_params, new_x)
            new_y = (high_y + low_y) / 2  # Take average of two lines at this position
            
            # Update position
            self.x = new_x
            self.y = new_y
            
            # Update Tg line position
            self.tg_line.set_xdata([new_x, new_x])
            
            # Update text label position and content
            self.text.set_position((new_x+5, min(self.densities) + 0.9*(max(self.densities)-min(self.densities))))
            self.text.set_text(f'Tg = {new_x:.0f}K')
            
            # Update fitting curves display
            if self.high_line and self.low_line:
                # Generate x data points for the lines
                min_temp, max_temp = min(self.temperatures), max(self.temperatures)
                
                # Update high temperature fitting line
                high_temps = np.linspace(min_temp, max_temp, 100)
                high_densities = np.polyval(high_params, high_temps)
                
                # High temperature fitting range
                if self.high_temp_range:
                    high_t_start, high_t_end = self.high_temp_range
                    
                    # Filter points within range
                    high_mask = (high_temps >= high_t_start) & (high_temps <= high_t_end)
                    high_temps_filtered = high_temps[high_mask]
                    high_densities_filtered = high_densities[high_mask]
                    
                    # Update line
                    self.high_line.set_data(high_temps_filtered, high_densities_filtered)
                else:
                    # Default: Show high temperature fit line from Tg to max temperature
                    high_mask = high_temps >= self.x
                    high_temps_filtered = high_temps[high_mask]
                    high_densities_filtered = high_densities[high_mask]
                    self.high_line.set_data(high_temps_filtered, high_densities_filtered)
                
                # Update low temperature fitting line
                low_temps = np.linspace(min_temp, max_temp, 100)
                low_densities = np.polyval(low_params, low_temps)
                
                # Low temperature fitting range
                if self.low_temp_range:
                    low_t_start, low_t_end = self.low_temp_range
                    
                    # Filter points within range
                    low_mask = (low_temps >= low_t_start) & (low_temps <= low_t_end)
                    low_temps_filtered = low_temps[low_mask]
                    low_densities_filtered = low_densities[low_mask]
                    
                    # Update line
                    self.low_line.set_data(low_temps_filtered, low_densities_filtered)
                else:
                    # Default: Show low temperature fit line from min temperature to Tg
                    low_mask = low_temps <= self.x
                    low_temps_filtered = low_temps[low_mask]
                    low_densities_filtered = low_densities[low_mask]
                    self.low_line.set_data(low_temps_filtered, low_densities_filtered)
                
                # Update fitting line control points display
                if self.show_control_points:
                    self.update_control_points_display()
            
            # Redraw the figure
            self.ax.figure.canvas.draw_idle()
            
            # Call the update callback if provided
            if self.on_update_callback:
                self.on_update_callback(self.x, high_params, low_params)
        
        except Exception as e:
            print(f"Error in on_motion: {e}")
    
    def on_release(self, event):
        """When mouse is released"""
        if self.press is None:
            return
        
        # Update to the final Tg position using the intersection of two lines
        if self.active_point is None and self.cached_high_params is not None and self.cached_low_params is not None:
            try:
                # Calculate the intersection point of two fitting lines
                m1, b1 = self.cached_high_params
                m2, b2 = self.cached_low_params
                
                # Find intersection by solving: m1*x + b1 = m2*x + b2
                if m1 != m2:  # Avoid division by zero
                    intersection_x = (b2 - b1) / (m1 - m2)
                    intersection_y = m1 * intersection_x + b1
                    
                    # Check if intersection is within temperature range
                    min_temp, max_temp = min(self.temperatures), max(self.temperatures)
                    if min_temp <= intersection_x <= max_temp:
                        # Update to intersection position
                        self.x = intersection_x
                        self.y = intersection_y
                        self.calc_intercept = (intersection_x, intersection_y)
                        
                        # Update Tg line position
                        self.tg_line.set_xdata([intersection_x, intersection_x])
                        
                        # Update text label
                        self.text.set_position((intersection_x+5, min(self.densities) + 0.9*(max(self.densities)-min(self.densities))))
                        self.text.set_text(f'Tg = {intersection_x:.0f}K')
                        
                        # Update fitting display
                        if self.high_line and self.low_line:
                            # Generate x data points for the lines
                            min_temp, max_temp = min(self.temperatures), max(self.temperatures)
                            
                            # Update high temperature fitting line
                            high_temps = np.linspace(min_temp, max_temp, 100)
                            high_densities = np.polyval(self.cached_high_params, high_temps)
                            
                            # High temperature fitting range
                            if self.high_temp_range:
                                high_t_start, high_t_end = self.high_temp_range
                                
                                # Filter points within range
                                high_mask = (high_temps >= high_t_start) & (high_temps <= high_t_end)
                                high_temps_filtered = high_temps[high_mask]
                                high_densities_filtered = high_densities[high_mask]
                                
                                # Update line
                                self.high_line.set_data(high_temps_filtered, high_densities_filtered)
                            else:
                                # Default: Show high temperature fit line from Tg to max temperature
                                high_mask = high_temps >= self.x
                                high_temps_filtered = high_temps[high_mask]
                                high_densities_filtered = high_densities[high_mask]
                                self.high_line.set_data(high_temps_filtered, high_densities_filtered)
                            
                            # Update low temperature fitting line
                            low_temps = np.linspace(min_temp, max_temp, 100)
                            low_densities = np.polyval(self.cached_low_params, low_temps)
                            
                            # Low temperature fitting range
                            if self.low_temp_range:
                                low_t_start, low_t_end = self.low_temp_range
                                
                                # Filter points within range
                                low_mask = (low_temps >= low_t_start) & (low_temps <= low_t_end)
                                low_temps_filtered = low_temps[low_mask]
                                low_densities_filtered = low_densities[low_mask]
                                
                                # Update line
                                self.low_line.set_data(low_temps_filtered, low_densities_filtered)
                            else:
                                # Default: Show low temperature fit line from min temperature to Tg
                                low_mask = low_temps <= self.x
                                low_temps_filtered = low_temps[low_mask]
                                low_densities_filtered = low_densities[low_mask]
                                self.low_line.set_data(low_temps_filtered, low_densities_filtered)
                        
                        # Call the update callback with final position
                        if self.on_update_callback:
                            self.on_update_callback(intersection_x, self.cached_high_params, self.cached_low_params)
            
            except Exception as e:
                print(f"Error calculating intersection: {e}")
        
        # Reset Tg line style
        self.tg_line.set_linewidth(1.0)
        self.tg_line.set_alpha(0.7)
        
        # Reset control point size if it was active
        if self.active_point is not None:
            for artist in self.ax.lines[:]:
                if hasattr(artist, 'is_control_point') and artist.is_control_point:
                    artist.set_markersize(8)  # Reset to normal size
            
            # Update display
            self.update_control_points_display()
            
            # Reset active point
            self.active_point = None
        
        # Reset drag state
        self.press = None
        
        # Redraw canvas
        self.ax.figure.canvas.draw_idle()
    
    def disconnect(self):
        """Disconnect all callbacks"""
        self.ax.figure.canvas.mpl_disconnect(self.cidpress)
        self.ax.figure.canvas.mpl_disconnect(self.cidrelease)
        self.ax.figure.canvas.mpl_disconnect(self.cidmotion)
    
    def set_fit_lines(self, high_line, low_line):
        """Set the high and low temperature fitting lines"""
        self.high_line = high_line
        self.low_line = low_line
    
    def set_high_temp_range(self, start, end):
        """Set high temperature fitting range"""
        self.high_temp_range = (start, end)
    
    def set_low_temp_range(self, start, end):
        """Set low temperature fitting range"""
        self.low_temp_range = (start, end)
    
    def fit_two_regions(self, tg_x):
        """Fit two straight lines to data divided at Tg"""
        try:
            # Split data at current Tg position
            high_temp_mask = self.temperatures >= tg_x
            low_temp_mask = self.temperatures <= tg_x
            
            # Check if enough data points in each region
            if sum(high_temp_mask) < 2 or sum(low_temp_mask) < 2:
                # Not enough points for fitting, return default values
                # Default: straight line with small slope
                return [0.0001, 1.0], [0.0002, 1.0]
            
            # High temperature region fitting
            high_temp_x = self.temperatures[high_temp_mask]
            high_temp_y = self.densities[high_temp_mask]
            high_params = np.polyfit(high_temp_x, high_temp_y, 1)
            
            # Low temperature region fitting
            low_temp_x = self.temperatures[low_temp_mask]
            low_temp_y = self.densities[low_temp_mask]
            low_params = np.polyfit(low_temp_x, low_temp_y, 1)
            
            # If data already has control points, use them to update fitting ranges
            if self.fit_line_points and len(self.fit_line_points) >= 4:
                # Use control points to limit fitting ranges
                # This allows for excluding outliers from fitting process
                pass
            
            # Update control points based on fitting results
            self.update_control_points(high_params, low_params, tg_x)
            
            return high_params, low_params
        
        except Exception as e:
            print(f"Error in fit_two_regions: {e}")
            # Return default values on error
            return [0.0001, 1.0], [0.0002, 1.0]
    
    def update_control_points(self, high_params, low_params, tg_x):
        """Update control points for fitting line adjustment"""
        min_temp, max_temp = min(self.temperatures), max(self.temperatures)
        
        # Initialize if not already created
        if not self.fit_line_points or len(self.fit_line_points) < 4:
            # Create default control points
            # High temp line start point (at Tg)
            high_start_x = tg_x
            high_start_y = np.polyval(high_params, high_start_x)
            
            # High temp line end point
            high_end_x = max_temp
            high_end_y = np.polyval(high_params, high_end_x)
            
            # Low temp line start point
            low_start_x = min_temp
            low_start_y = np.polyval(low_params, low_start_x)
            
            # Low temp line end point (at Tg)
            low_end_x = tg_x
            low_end_y = np.polyval(low_params, low_end_x)
            
            # Store control points
            self.fit_line_points = [
                (high_start_x, high_start_y),
                (high_end_x, high_end_y),
                (low_start_x, low_start_y),
                (low_end_x, low_end_y)
            ]
        else:
            # Update only positions based on new fitting parameters
            # We keep the x positions unchanged as they are user-defined
            # but update y values according to new fits
            
            # High temperature line control points
            high_start_x = self.fit_line_points[0][0]
            high_start_y = np.polyval(high_params, high_start_x)
            
            high_end_x = self.fit_line_points[1][0]
            high_end_y = np.polyval(high_params, high_end_x)
            
            # Low temperature line control points
            low_start_x = self.fit_line_points[2][0]
            low_start_y = np.polyval(low_params, low_start_x)
            
            low_end_x = self.fit_line_points[3][0]
            low_end_y = np.polyval(low_params, low_end_x)
            
            # Update control points
            self.fit_line_points = [
                (high_start_x, high_start_y),
                (high_end_x, high_end_y),
                (low_start_x, low_start_y),
                (low_end_x, low_end_y)
            ]
    
    def update_control_points_display(self):
        """Update display of control points for fitting lines"""
        # Remove old control points
        for artist in self.ax.lines[:]:
            if hasattr(artist, 'is_control_point') and artist.is_control_point:
                artist.remove()
        
        # Skip if points not available
        if not self.fit_line_points or len(self.fit_line_points) < 4:
            return
        
        # Add new points with updated positions
        # High temperature line control points
        point1 = self.ax.plot(self.fit_line_points[0][0], self.fit_line_points[0][1], 
                             'o', markersize=8, color='red', alpha=0.8, zorder=5)[0]
        point1.is_control_point = True  # Add custom attribute
        
        point2 = self.ax.plot(self.fit_line_points[1][0], self.fit_line_points[1][1], 
                             'o', markersize=8, color='red', alpha=0.8, zorder=5)[0]
        point2.is_control_point = True  # Add custom attribute
        
        # Low temperature line control points
        point3 = self.ax.plot(self.fit_line_points[2][0], self.fit_line_points[2][1], 
                             'o', markersize=8, color='blue', alpha=0.8, zorder=5)[0]
        point3.is_control_point = True  # Add custom attribute
        
        point4 = self.ax.plot(self.fit_line_points[3][0], self.fit_line_points[3][1], 
                             'o', markersize=8, color='blue', alpha=0.8, zorder=5)[0]
        point4.is_control_point = True  # Add custom attribute
    
    def update_fit_params_from_points(self):
        """Update fitting parameters based on control points"""
        # Ensure we have valid control points
        if not self.fit_line_points or len(self.fit_line_points) < 4:
            return
        
        # Extract control points
        high_start = self.fit_line_points[0]
        high_end = self.fit_line_points[1]
        low_start = self.fit_line_points[2]
        low_end = self.fit_line_points[3]
        
        # Calculate new high temperature line parameters (slope and intercept)
        # Using point-slope form: y - y1 = m(x - x1)
        high_x = np.array([high_start[0], high_end[0]])
        high_y = np.array([high_start[1], high_end[1]])
        
        # Calculate fitting parameters: y = mx + b
        high_params = np.polyfit(high_x, high_y, 1)
        
        # Calculate new low temperature line parameters
        low_x = np.array([low_start[0], low_end[0]])
        low_y = np.array([low_start[1], low_end[1]])
        
        # Calculate fitting parameters: y = mx + b
        low_params = np.polyfit(low_x, low_y, 1)
        
        # Update cached parameters
        self.cached_high_params = high_params
        self.cached_low_params = low_params
        
        # Update high temperature fitting range
        self.high_temp_range = (min(high_x), max(high_x))
        
        # Update low temperature fitting range
        self.low_temp_range = (min(low_x), max(low_x))
        
        # Calculate intersection of the two lines
        try:
            # Extract slope (m) and intercept (b) from params
            m1, b1 = high_params
            m2, b2 = low_params
            
            # Find intersection by solving: m1*x + b1 = m2*x + b2
            if m1 != m2:  # Avoid division by zero
                intersection_x = (b2 - b1) / (m1 - m2)
                intersection_y = m1 * intersection_x + b1
                
                # Check if intersection is within temperature range
                min_temp, max_temp = min(self.temperatures), max(self.temperatures)
                if min_temp <= intersection_x <= max_temp:
                    # Store the calculated intersection
                    self.calc_intercept = (intersection_x, intersection_y)
        except:
            # In case of error, don't update intersection
            pass
    
    def update_fit_lines_from_points(self):
        """Update fitting lines using control points"""
        # Ensure we have high and low temperature lines
        if not self.high_line or not self.low_line:
            return
        
        # Generate x data points for the lines
        min_temp, max_temp = min(self.temperatures), max(self.temperatures)
        
        # Calculate new high temperature fitting line
        if self.cached_high_params is not None:
            high_temps = np.linspace(min_temp, max_temp, 100)
            high_densities = np.polyval(self.cached_high_params, high_temps)
            
            # High temperature fitting range
            if self.high_temp_range:
                high_t_start, high_t_end = self.high_temp_range
                
                # Filter points within range
                high_mask = (high_temps >= high_t_start) & (high_temps <= high_t_end)
                high_temps_filtered = high_temps[high_mask]
                high_densities_filtered = high_densities[high_mask]
                
                # Update line
                self.high_line.set_data(high_temps_filtered, high_densities_filtered)
        
        # Calculate new low temperature fitting line
        if self.cached_low_params is not None:
            low_temps = np.linspace(min_temp, max_temp, 100)
            low_densities = np.polyval(self.cached_low_params, low_temps)
            
            # Low temperature fitting range
            if self.low_temp_range:
                low_t_start, low_t_end = self.low_temp_range
                
                # Filter points within range
                low_mask = (low_temps >= low_t_start) & (low_temps <= low_t_end)
                low_temps_filtered = low_temps[low_mask]
                low_densities_filtered = low_densities[low_mask]
                
                # Update line
                self.low_line.set_data(low_temps_filtered, low_densities_filtered)
    
    def set_showable(self, show):
        """Show or hide Tg point and line"""
        self.tg_line.set_visible(show)
        self.text.set_visible(show)
        
        # Also hide control points
        for artist in self.ax.lines[:]:
            if hasattr(artist, 'is_control_point') and artist.is_control_point:
                artist.set_visible(show)
    
    def get_tg(self):
        """Get current Tg value"""
        return self.x
    
    def toggle_control_points(self, show=None):
        """Show/hide control points"""
        if show is not None:
            self.show_control_points = show
        else:
            self.show_control_points = not self.show_control_points
        
        # Show/hide control points based on state
        for artist in self.ax.lines[:]:
            if hasattr(artist, 'is_control_point') and artist.is_control_point:
                artist.set_visible(self.show_control_points)
        
        # Redraw the figure
        self.ax.figure.canvas.draw_idle()
        
        return self.show_control_points

class DraggablePoint:
    """Draggable point for scatter plot data points"""
    
    def __init__(self, ax, x, y, scatter=None, index=None, callback=None):
        self.ax = ax
        self.x = x
        self.y = y
        self.scatter = scatter  # Reference to the scatter plot
        self.index = index      # Index of this point in the scatter data
        self.callback = callback
        self.press = None
        self.background = None
        
        # Connect to mouse events
        self.cidpress = ax.figure.canvas.mpl_connect('button_press_event', self.on_press)
        self.cidrelease = ax.figure.canvas.mpl_connect('button_release_event', self.on_release)
        self.cidmotion = ax.figure.canvas.mpl_connect('motion_notify_event', self.on_motion)
    
    def on_press(self, event):
        """Handle mouse button press"""
        if event.inaxes != self.ax:
            return
        
        # Check if the click is near this point
        contains, attrd = self.scatter.contains(event)
        if not contains:
            return
        
        # Check if this specific point was clicked (using index of contained points)
        if self.index not in attrd['ind']:
            return
        
        # Set the scatter point selection
        fc = self.scatter.get_facecolors()
        if len(fc) == 1:  # If all points have the same color
            fc = np.tile(fc, (len(self.scatter.get_offsets()), 1))
        
        # Highlight the selected point
        fc[self.index] = (1, 0, 0, 1)  # Red highlight
        self.scatter.set_facecolors(fc)
        
        # Store press data
        self.press = event.xdata, event.ydata
        
        # Redraw
        self.ax.figure.canvas.draw_idle()
    
    def on_motion(self, event):
        """Handle mouse motion"""
        if self.press is None or event.inaxes != self.ax:
            return
        
        # Calculate movement
        dx = event.xdata - self.press[0]
        dy = event.ydata - self.press[1]
        
        # Update point position
        self.x += dx
        self.y += dy
        
        # Update scatter data
        offsets = self.scatter.get_offsets()
        offsets[self.index] = [self.x, self.y]
        self.scatter.set_offsets(offsets)
        
        # Update press reference point
        self.press = event.xdata, event.ydata
        
        # Call callback if provided
        if self.callback:
            self.callback(self.index, self.x, self.y)
        
        # Redraw
        self.ax.figure.canvas.draw_idle()
    
    def on_release(self, event):
        """Handle mouse button release"""
        if self.press is None:
            return
        
        # Reset press data
        self.press = None
        
        # Reset point color
        fc = self.scatter.get_facecolors()
        if len(fc) > 1:  # Only reset if we have individual colors
            fc[self.index] = (0, 0, 1, 0.7)  # Back to default blue
            self.scatter.set_facecolors(fc)
        
        # Redraw
        self.ax.figure.canvas.draw_idle()
    
    def disconnect(self):
        """Disconnect all callbacks"""
        self.ax.figure.canvas.mpl_disconnect(self.cidpress)
        self.ax.figure.canvas.mpl_disconnect(self.cidrelease)
        self.ax.figure.canvas.mpl_disconnect(self.cidmotion)

import os
import sys
import numpy as np
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QPushButton, QFileDialog, QMessageBox, QRadioButton,
                            QButtonGroup, QComboBox, QTabWidget, QSpinBox, QDoubleSpinBox,
                            QCheckBox, QGroupBox, QGridLayout, QSplitter, QProgressBar,
                            QLineEdit, QSlider, QMenuBar, QStatusBar, QAction, QDialog,
                            QTextEdit, QFormLayout, QTableWidget, QTableWidgetItem)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize, QSettings, QStandardPaths
from PyQt5.QtGui import QIcon, QFont, QPixmap, QCursor, QColor

import matplotlib
matplotlib.use('Qt5Agg')
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
from matplotlib.figure import Figure
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.widgets import Slider

import re
import time
import glob
import traceback
import subprocess
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
from scipy.optimize import curve_fit
from scipy.signal import savgol_filter

class AnalysisThread(QThread):
    """Thread for running Tg analysis to keep UI responsive"""
    # Signal for progress updates (current step, max steps, message)
    progress_update = pyqtSignal(int, int, str)
    
    # Signal for completion with results
    analysis_complete = pyqtSignal(dict)
    
    # Signal for errors
    error_occurred = pyqtSignal(str)
    
    def __init__(self, file_paths, settings):
        super().__init__()
        self.file_paths = file_paths
        self.settings = settings
        self.results = {}
        self.stop_requested = False
    
    def run(self):
        """Run the analysis"""
        try:
            total_files = len(self.file_paths)
            
            for i, file_path in enumerate(self.file_paths):
                # Check if stop was requested
                if self.stop_requested:
                    break
                
                # Update progress
                self.progress_update.emit(i+1, total_files, f"Processing file {i+1}/{total_files}: {os.path.basename(file_path)}")
                
                # Process the file
                try:
                    result = self.process_single_file(file_path)
                    
                    # Store result
                    if result:
                        self.results[file_path] = result
                
                except Exception as e:
                    error_msg = f"Error processing {os.path.basename(file_path)}: {str(e)}"
                    self.error_occurred.emit(error_msg)
                    print(error_msg)
                    traceback.print_exc()
            
            # Analysis completed
            self.progress_update.emit(total_files, total_files, "Analysis completed")
            self.analysis_complete.emit(self.results)
        
        except Exception as e:
            error_msg = f"Error in analysis thread: {str(e)}"
            self.error_occurred.emit(error_msg)
            print(error_msg)
            traceback.print_exc()
    
    def process_single_file(self, file_path):
        """Process a single data file for Tg analysis"""
        # Get file extension
        ext = os.path.splitext(file_path)[1].lower()
        
        # Read data based on file type
        if ext == '.dat':
            # Parse the .dat file format
            temps, densities, data_info = self.parse_dat_file(file_path)
        elif ext == '.csv':
            # Parse CSV with pandas
            temps, densities, data_info = self.parse_csv_file(file_path)
        elif ext == '.txt':
            # Parse simple text file
            temps, densities, data_info = self.parse_txt_file(file_path)
        elif ext == '.xls' or ext == '.xlsx':
            # Parse Excel file
            temps, densities, data_info = self.parse_excel_file(file_path)
        else:
            raise ValueError(f"Unsupported file format: {ext}")
        
        # Sort data by temperature
        sort_indices = np.argsort(temps)
        temps = np.array(temps)[sort_indices]
        densities = np.array(densities)[sort_indices]
        
        # Preprocessing if enabled
        if self.settings.get('apply_smoothing', False):
            window_size = self.settings.get('smoothing_window', 7)
            poly_order = self.settings.get('smoothing_order', 3)
            
            # Ensure window size is odd and not larger than data
            if window_size % 2 == 0:
                window_size += 1
            window_size = min(window_size, len(temps) - 2)
            
            # Apply Savitzky-Golay filter for smoothing
            if window_size > poly_order and window_size > 2:
                try:
                    densities = savgol_filter(densities, window_size, poly_order)
                except Exception as e:
                    print(f"Smoothing failed: {e}")
        
        # Perform initial glass transition temperature analysis
        tg, high_params, low_params = self.calculate_tg(temps, densities)
        
        # Calculate actual density at Tg
        density_at_tg = np.interp(tg, temps, densities)
        
        # Calculate additional metrics
        volume_expansion_coefficient_high = -high_params[0] / density_at_tg  # α = -(1/ρ)(dρ/dT)
        volume_expansion_coefficient_low = -low_params[0] / density_at_tg
        
        # Format results
        result = {
            'temperatures': temps,
            'densities': densities,
            'tg': tg,
            'high_temp_fit': high_params,
            'low_temp_fit': low_params,
            'volume_expansion_coef_high': volume_expansion_coefficient_high,
            'volume_expansion_coef_low': volume_expansion_coefficient_low,
            'density_at_tg': density_at_tg,
            'data_info': data_info
        }
        
        return result
    
    def parse_dat_file(self, file_path):
        """Parse .dat file format (MS Analysis output)"""
        data_info = {}
        temps = []
        densities = []
        
        with open(file_path, 'r') as f:
            lines = f.readlines()
            
            # Extract header information
            header_lines = [line for line in lines if line.strip() and line.strip()[0] in '#!;']
            for line in header_lines:
                # Try to extract key-value pairs from header comments
                if ':' in line:
                    key, value = line.strip('#!; ').split(':', 1)
                    data_info[key.strip()] = value.strip()
            
            # Extract data
            for line in lines:
                line = line.strip()
                if line and not line.startswith(('#', '!', ';')):
                    try:
                        parts = line.split()
                        if len(parts) >= 2:
                            temp = float(parts[0])
                            density = float(parts[1])
                            temps.append(temp)
                            densities.append(density)
                    except (ValueError, IndexError):
                        continue
        
        # Check if we found any data
        if not temps:
            raise ValueError("No valid data found in file")
        
        return np.array(temps), np.array(densities), data_info
    
    def parse_csv_file(self, file_path):
        """Parse CSV file"""
        data_info = {}
        
        try:
            # Try to read with pandas
            df = pd.read_csv(file_path)
            
            # Try to identify temperature and density columns
            temp_col = None
            density_col = None
            
            # Check if we have columns with recognizable names
            for col in df.columns:
                col_lower = col.lower()
                if 'temp' in col_lower or 't' == col_lower:
                    temp_col = col
                elif 'density' in col_lower or 'dens' in col_lower or 'rho' in col_lower:
                    density_col = col
            
            # If columns weren't found, use first and second columns
            if temp_col is None or density_col is None:
                temp_col = df.columns[0]
                density_col = df.columns[1]
            
            # Extract data
            temps = df[temp_col].values
            densities = df[density_col].values
            
            # Extract metadata from header if present
            if df.shape[1] > 2:
                for col in df.columns[2:]:
                    if df[col].nunique() == 1:  # If column has only one value, treat as metadata
                        data_info[col] = df[col].iloc[0]
            
            return temps, densities, data_info
        
        except Exception as e:
            # Fall back to simple parsing
            temps = []
            densities = []
            
            with open(file_path, 'r') as f:
                lines = f.readlines()
                
                # Skip header
                data_lines = []
                for line in lines:
                    line = line.strip()
                    if ',' in line and all(c.isdigit() or c in ',.+-eE' for c in line.replace(',', '') if c.strip()):
                        data_lines.append(line)
                    elif line and line[0] not in '#!;' and line.count(',') >= 1:
                        data_lines.append(line)
                
                # Extract data
                for line in data_lines:
                    try:
                        parts = line.split(',')
                        temp = float(parts[0])
                        density = float(parts[1])
                        temps.append(temp)
                        densities.append(density)
                    except (ValueError, IndexError):
                        continue
            
            # Check if we found any data
            if not temps:
                raise ValueError("No valid data found in CSV file")
            
            return np.array(temps), np.array(densities), data_info
    
    def parse_txt_file(self, file_path):
        """Parse plain text file"""
        data_info = {}
        temps = []
        densities = []
        
        with open(file_path, 'r') as f:
            lines = f.readlines()
            
            # Extract data
            for line in lines:
                line = line.strip()
                if line and not line.startswith(('#', '!', ';')):
                    try:
                        parts = line.split()
                        if len(parts) >= 2:
                            temp = float(parts[0])
                            density = float(parts[1])
                            temps.append(temp)
                            densities.append(density)
                    except (ValueError, IndexError):
                        continue
        
        # Check if we found any data
        if not temps:
            raise ValueError("No valid data found in text file")
        
        return np.array(temps), np.array(densities), data_info
    
    def parse_excel_file(self, file_path):
        """Parse Excel file"""
        data_info = {}
        
        try:
            # Try to read with pandas
            df = pd.read_excel(file_path)
            
            # Try to identify temperature and density columns
            temp_col = None
            density_col = None
            
            # Check if we have columns with recognizable names
            for col in df.columns:
                col_lower = str(col).lower()
                if 'temp' in col_lower or 't' == col_lower:
                    temp_col = col
                elif 'density' in col_lower or 'dens' in col_lower or 'rho' in col_lower:
                    density_col = col
            
            # If columns weren't found, use first and second columns
            if temp_col is None or density_col is None:
                temp_col = df.columns[0]
                density_col = df.columns[1]
            
            # Extract data
            temps = df[temp_col].values
            densities = df[density_col].values
            
            # Extract metadata from header if present
            if df.shape[1] > 2:
                for col in df.columns[2:]:
                    if df[col].nunique() == 1:  # If column has only one value, treat as metadata
                        data_info[col] = df[col].iloc[0]
            
            return temps, densities, data_info
        
        except Exception as e:
            raise ValueError(f"Failed to parse Excel file: {str(e)}")
    
    def calculate_tg(self, temps, densities):
        """Calculate glass transition temperature by fitting two lines"""
        # Initial guess for Tg is around median temperature
        median_temp_index = len(temps) // 2
        initial_tg_guess = temps[median_temp_index]
        
        # Systematic search for Tg by trying different split points
        best_tg = initial_tg_guess
        best_fit_error = float('inf')
        best_high_params = None
        best_low_params = None
        
        # Try different temperature points as potential Tg
        candidate_indices = np.linspace(len(temps)//4, 3*len(temps)//4, 20, dtype=int)
        for idx in candidate_indices:
            candidate_tg = temps[idx]
            
            # Split data at candidate Tg
            high_temps = temps[temps >= candidate_tg]
            high_densities = densities[temps >= candidate_tg]
            
            low_temps = temps[temps <= candidate_tg]
            low_densities = densities[temps <= candidate_tg]
            
            # Skip if not enough points in either region
            if len(high_temps) < 3 or len(low_temps) < 3:
                continue
            
            # Fit lines to high and low temperature regions
            high_params = np.polyfit(high_temps, high_densities, 1)
            low_params = np.polyfit(low_temps, low_densities, 1)
            
            # Calculate fit error (sum of squared residuals)
            high_fit = np.polyval(high_params, high_temps)
            low_fit = np.polyval(low_params, low_temps)
            
            high_error = np.sum((high_densities - high_fit)**2)
            low_error = np.sum((low_densities - low_fit)**2)
            total_error = high_error + low_error
            
            # Update best fit if this is better
            if total_error < best_fit_error:
                best_fit_error = total_error
                best_tg = candidate_tg
                best_high_params = high_params
                best_low_params = low_params
        
        # Calculate intersection of the best fit lines
        # Using m1*x + b1 = m2*x + b2 => x = (b2 - b1) / (m1 - m2)
        m1, b1 = best_high_params
        m2, b2 = best_low_params
        
        # Avoid division by zero for parallel lines
        if m1 != m2:
            intersection_x = (b2 - b1) / (m1 - m2)
            
            # Check if intersection is within temperature range
            if min(temps) <= intersection_x <= max(temps):
                best_tg = intersection_x
        
        return best_tg, best_high_params, best_low_params
    
    def stop(self):
        """Stop the analysis thread"""
        self.stop_requested = True 

class TgAnalyzer(QMainWindow):
    """Main application window for Tg analysis"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize variables
        self.data = {}  # Store all loaded data
        self.current_file = None  # Currently selected file
        self.analysis_thread = None  # Background processing thread
        self.tg_line = None  # Reference to Tg line in plot
        self.tg_text = None  # Reference to Tg text label in plot
        self.high_fit_line = None  # Reference to high temperature fit line
        self.low_fit_line = None  # Reference to low temperature fit line
        self.plot_points = None  # Reference to data points
        self.control_points = []  # List of draggable control points
        
        # Setup UI
        self.init_ui()
        
        # Load settings
        self.load_settings()
    
    def init_ui(self):
        """Initialize the user interface"""
        # Set window properties
        self.setWindowTitle("Glass Transition Temperature Analyzer")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget and main layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        main_layout = QVBoxLayout(self.central_widget)
        
        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel for controls
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # File selection group
        file_group = QGroupBox("Data Files")
        file_layout = QVBoxLayout()
        
        # Buttons for file operations
        file_buttons_layout = QHBoxLayout()
        self.load_button = QPushButton("Load Files")
        self.load_button.clicked.connect(self.load_files)
        file_buttons_layout.addWidget(self.load_button)
        
        self.clear_button = QPushButton("Clear All")
        self.clear_button.clicked.connect(self.clear_data)
        file_buttons_layout.addWidget(self.clear_button)
        file_layout.addLayout(file_buttons_layout)
        
        # File list
        self.file_list = QComboBox()
        self.file_list.currentIndexChanged.connect(self.on_file_selected)
        file_layout.addWidget(self.file_list)
        
        file_group.setLayout(file_layout)
        left_layout.addWidget(file_group)
        
        # Analysis settings group
        settings_group = QGroupBox("Analysis Settings")
        settings_layout = QVBoxLayout()
        
        # Smoothing options
        smoothing_layout = QHBoxLayout()
        self.smoothing_check = QCheckBox("Apply Smoothing")
        smoothing_layout.addWidget(self.smoothing_check)
        
        window_layout = QHBoxLayout()
        window_layout.addWidget(QLabel("Window:"))
        self.window_spin = QSpinBox()
        self.window_spin.setRange(5, 51)
        self.window_spin.setSingleStep(2)
        self.window_spin.setValue(11)
        window_layout.addWidget(self.window_spin)
        
        order_layout = QHBoxLayout()
        order_layout.addWidget(QLabel("Poly Order:"))
        self.order_spin = QSpinBox()
        self.order_spin.setRange(1, 5)
        self.order_spin.setValue(3)
        order_layout.addWidget(self.order_spin)
        
        smoothing_controls = QHBoxLayout()
        smoothing_controls.addLayout(window_layout)
        smoothing_controls.addLayout(order_layout)
        
        smoothing_widget = QWidget()
        smoothing_widget.setLayout(smoothing_controls)
        
        smoothing_layout.addWidget(smoothing_widget)
        settings_layout.addLayout(smoothing_layout)
        
        # Connect smoothing checkbox
        self.smoothing_check.stateChanged.connect(self.update_smoothing_controls)
        self.update_smoothing_controls(False)
        
        # Temperature range selection
        range_layout = QGridLayout()
        range_layout.addWidget(QLabel("Temperature Range:"), 0, 0)
        
        self.temp_min = QDoubleSpinBox()
        self.temp_min.setRange(-273, 10000)
        self.temp_min.setValue(0)
        self.temp_min.setSuffix(" K")
        range_layout.addWidget(QLabel("Min:"), 1, 0)
        range_layout.addWidget(self.temp_min, 1, 1)
        
        self.temp_max = QDoubleSpinBox()
        self.temp_max.setRange(-273, 10000)
        self.temp_max.setValue(1000)
        self.temp_max.setSuffix(" K")
        range_layout.addWidget(QLabel("Max:"), 2, 0)
        range_layout.addWidget(self.temp_max, 2, 1)
        
        self.auto_range = QCheckBox("Auto Range")
        self.auto_range.setChecked(True)
        range_layout.addWidget(self.auto_range, 3, 0, 1, 2)
        
        # Connect auto range checkbox
        self.auto_range.stateChanged.connect(self.update_range_controls)
        
        settings_layout.addLayout(range_layout)
        self.update_range_controls(True)
        
        # Manual control points option
        self.manual_control = QCheckBox("Manual Control Points")
        self.manual_control.stateChanged.connect(self.toggle_manual_control)
        settings_layout.addWidget(self.manual_control)
        
        # Run analysis button
        self.analyze_button = QPushButton("Analyze")
        self.analyze_button.clicked.connect(self.run_analysis)
        settings_layout.addWidget(self.analyze_button)
        
        settings_group.setLayout(settings_layout)
        left_layout.addWidget(settings_group)
        
        # Results group
        results_group = QGroupBox("Results")
        results_layout = QFormLayout()
        
        self.tg_result = QLabel("-")
        results_layout.addRow("Tg:", self.tg_result)
        
        self.density_result = QLabel("-")
        results_layout.addRow("Density at Tg:", self.density_result)
        
        self.expansion_high = QLabel("-")
        results_layout.addRow("α (High Temp):", self.expansion_high)
        
        self.expansion_low = QLabel("-")
        results_layout.addRow("α (Low Temp):", self.expansion_low)
        
        results_group.setLayout(results_layout)
        left_layout.addWidget(results_group)
        
        # Export section
        export_group = QGroupBox("Export")
        export_layout = QVBoxLayout()
        
        self.export_button = QPushButton("Export Results")
        self.export_button.clicked.connect(self.export_results)
        export_layout.addWidget(self.export_button)
        
        self.save_plot_button = QPushButton("Save Plot")
        self.save_plot_button.clicked.connect(self.save_plot)
        export_layout.addWidget(self.save_plot_button)
        
        export_group.setLayout(export_layout)
        left_layout.addWidget(export_group)
        
        # Add a stretch at the bottom
        left_layout.addStretch()
        
        # Right panel for plot
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Create matplotlib canvas
        self.figure = Figure(figsize=(8, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setMinimumWidth(600)
        
        # Add toolbar
        self.toolbar = NavigationToolbar(self.canvas, self)
        right_layout.addWidget(self.toolbar)
        right_layout.addWidget(self.canvas)
        
        # Add plot axis
        self.ax = self.figure.add_subplot(111)
        self.ax.set_xlabel('Temperature (K)')
        self.ax.set_ylabel('Density (g/cm³)')
        self.ax.grid(True)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        right_layout.addWidget(self.progress_bar)
        
        # Status bar for messages
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        
        # Set initial splitter sizes
        splitter.setSizes([300, 900])
        
        # Create menu
        self.create_menu()
        
        # Show the window
        self.show()
    
    def create_menu(self):
        """Create the application menu"""
        menu_bar = self.menuBar()
        
        # File menu
        file_menu = menu_bar.addMenu("File")
        
        open_action = QAction("Open Files", self)
        open_action.triggered.connect(self.load_files)
        file_menu.addAction(open_action)
        
        save_action = QAction("Save Results", self)
        save_action.triggered.connect(self.export_results)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("Exit", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Edit menu
        edit_menu = menu_bar.addMenu("Edit")
        
        preferences_action = QAction("Preferences", self)
        preferences_action.triggered.connect(self.show_preferences)
        edit_menu.addAction(preferences_action)
        
        # Help menu
        help_menu = menu_bar.addMenu("Help")
        
        about_action = QAction("About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def update_smoothing_controls(self, state):
        """Update visibility of smoothing controls based on checkbox state"""
        enabled = self.smoothing_check.isChecked()
        self.window_spin.setEnabled(enabled)
        self.order_spin.setEnabled(enabled)
    
    def update_range_controls(self, state):
        """Update visibility of temperature range controls based on checkbox state"""
        disabled = self.auto_range.isChecked()
        self.temp_min.setDisabled(disabled)
        self.temp_max.setDisabled(disabled)
    
    def load_settings(self):
        """Load application settings"""
        settings = QSettings("TgAnalyzer", "GlassTransitionApp")
        
        # Load window geometry
        geometry = settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)
        
        # Load analysis preferences
        self.smoothing_check.setChecked(settings.value("smoothing_enabled", False, type=bool))
        self.window_spin.setValue(settings.value("smoothing_window", 11, type=int))
        self.order_spin.setValue(settings.value("smoothing_order", 3, type=int))
        self.auto_range.setChecked(settings.value("auto_range", True, type=bool))
        
        # Update controls based on loaded settings
        self.update_smoothing_controls(self.smoothing_check.isChecked())
        self.update_range_controls(self.auto_range.isChecked())
    
    def save_settings(self):
        """Save application settings"""
        settings = QSettings("TgAnalyzer", "GlassTransitionApp")
        
        # Save window geometry
        settings.setValue("geometry", self.saveGeometry())
        
        # Save analysis preferences
        settings.setValue("smoothing_enabled", self.smoothing_check.isChecked())
        settings.setValue("smoothing_window", self.window_spin.value())
        settings.setValue("smoothing_order", self.order_spin.value())
        settings.setValue("auto_range", self.auto_range.isChecked())
    
    def closeEvent(self, event):
        """Handle window close event"""
        # Save settings
        self.save_settings()
        
        # Accept the close event
        event.accept()
    
    def load_files(self):
        """Load data files"""
        file_dialog = QFileDialog()
        file_paths, _ = file_dialog.getOpenFileNames(
            self,
            "Select Data Files",
            "",
            "Data Files (*.dat *.txt *.csv *.xls *.xlsx);;All Files (*.*)"
        )
        
        if not file_paths:
            return
        
        # Clear existing data if requested
        if self.data and QMessageBox.question(
            self,
            "Clear Existing Data",
            "Do you want to clear existing data before loading new files?",
            QMessageBox.Yes | QMessageBox.No
        ) == QMessageBox.Yes:
            self.clear_data()
        
        # Create analysis thread
        self.progress_bar.setVisible(True)
        self.status_bar.showMessage("Loading files...")
        
        # Get analysis settings
        settings = {
            'apply_smoothing': self.smoothing_check.isChecked(),
            'smoothing_window': self.window_spin.value(),
            'smoothing_order': self.order_spin.value()
        }
        
        # Create and start analysis thread
        self.analysis_thread = AnalysisThread(file_paths, settings)
        self.analysis_thread.progress_update.connect(self.update_progress)
        self.analysis_thread.analysis_complete.connect(self.analysis_completed)
        self.analysis_thread.error_occurred.connect(self.analysis_error)
        self.analysis_thread.start()
    
    def update_progress(self, current, total, message):
        """Update progress bar and status message"""
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)
        self.status_bar.showMessage(message)
    
    def analysis_completed(self, results):
        """Handle completion of analysis thread"""
        # Update data dictionary with new results
        self.data.update(results)
        
        # Update file list
        self.update_file_list()
        
        # Hide progress bar
        self.progress_bar.setVisible(False)
        self.status_bar.showMessage("Analysis completed", 3000)
        
        # If this is the first file, select it
        if self.file_list.count() > 0 and self.current_file is None:
            self.file_list.setCurrentIndex(0)
    
    def analysis_error(self, error_message):
        """Handle errors from analysis thread"""
        QMessageBox.warning(self, "Analysis Error", error_message)
        self.status_bar.showMessage("Error during analysis", 3000)
    
    def update_file_list(self):
        """Update the file list combo box"""
        # Store current selection
        current_index = self.file_list.currentIndex()
        
        # Clear and rebuild list
        self.file_list.clear()
        
        # Add files
        for file_path in sorted(self.data.keys()):
            self.file_list.addItem(os.path.basename(file_path), file_path)
        
        # Restore selection if possible
        if current_index >= 0 and current_index < self.file_list.count():
            self.file_list.setCurrentIndex(current_index)
    
    def on_file_selected(self, index):
        """Handle selection of a file from the list"""
        if index < 0:
            return
        
        # Get file path from combo box data
        file_path = self.file_list.itemData(index)
        if file_path in self.data:
            self.current_file = file_path
            self.display_results(file_path)
    
    def clear_data(self):
        """Clear all loaded data"""
        self.data = {}
        self.current_file = None
        self.file_list.clear()
        
        # Clear results
        self.tg_result.setText("-")
        self.density_result.setText("-")
        self.expansion_high.setText("-")
        self.expansion_low.setText("-")
        
        # Clear plot
        self.ax.clear()
        self.ax.set_xlabel('Temperature (K)')
        self.ax.set_ylabel('Density (g/cm³)')
        self.ax.grid(True)
        self.canvas.draw()
        
        # Clear control points
        self.control_points = []
        
        self.status_bar.showMessage("All data cleared", 3000)
    
    def display_results(self, file_path):
        """Display results for the selected file"""
        if file_path not in self.data:
            return
        
        result = self.data[file_path]
        
        # Display numeric results
        self.tg_result.setText(f"{result['tg']:.2f} K")
        self.density_result.setText(f"{result['density_at_tg']:.6f} g/cm³")
        self.expansion_high.setText(f"{result['volume_expansion_coef_high']:.6e} K⁻¹")
        self.expansion_low.setText(f"{result['volume_expansion_coef_low']:.6e} K⁻¹")
        
        # Update plot
        self.update_plot(result)
    
    def update_plot(self, result):
        """Update the plot with current results"""
        # Clear previous plot
        self.ax.clear()
        
        # Get data
        temps = result['temperatures']
        densities = result['densities']
        tg = result['tg']
        high_params = result['high_temp_fit']
        low_params = result['low_temp_fit']
        
        # Plot data points
        self.plot_points = self.ax.scatter(temps, densities, color='blue', alpha=0.7, label='Data')
        
        # Get temperature range for fit lines
        t_min = min(temps)
        t_max = max(temps)
        
        # Create fit lines
        t_high = np.linspace(tg, t_max, 100)
        density_high = np.polyval(high_params, t_high)
        high_line, = self.ax.plot(t_high, density_high, 'r-', label='High Temp Fit')
        self.high_fit_line = high_line
        
        t_low = np.linspace(t_min, tg, 100)
        density_low = np.polyval(low_params, t_low)
        low_line, = self.ax.plot(t_low, density_low, 'g-', label='Low Temp Fit')
        self.low_fit_line = low_line
        
        # Add Tg marker and text
        density_at_tg = np.interp(tg, temps, densities)
        self.tg_line = self.ax.axvline(x=tg, color='k', linestyle='--', alpha=0.7)
        self.tg_text = self.ax.text(
            tg, min(densities), f'Tg = {tg:.2f} K',
            ha='center', va='bottom', bbox=dict(facecolor='white', alpha=0.7)
        )
        
        # Add legend and grid
        self.ax.legend()
        self.ax.grid(True)
        
        # Set labels
        self.ax.set_xlabel('Temperature (K)')
        self.ax.set_ylabel('Density (g/cm³)')
        
        # Set title
        if self.current_file:
            self.ax.set_title(f"Glass Transition Analysis - {os.path.basename(self.current_file)}")
        
        # Update canvas
        self.canvas.draw()
        
        # Create control points if manual control is enabled
        if self.manual_control.isChecked():
            self.create_control_points(temps, densities, high_params, low_params, tg)
    
    def toggle_manual_control(self, state):
        """Toggle manual control points mode"""
        if state and self.current_file is not None:
            # Create control points for current data
            result = self.data[self.current_file]
            self.create_control_points(
                result['temperatures'],
                result['densities'],
                result['high_temp_fit'],
                result['low_temp_fit'],
                result['tg']
            )
        else:
            # Remove existing control points
            for point in self.control_points:
                point.disconnect()
            self.control_points = []
    
    def create_control_points(self, temps, densities, high_params, low_params, tg):
        """Create draggable control points for manual adjustment"""
        # Clear any existing control points
        for point in self.control_points:
            point.disconnect()
        self.control_points = []
        
        # Create control point for Tg position
        density_at_tg = np.interp(tg, temps, densities)
        tg_point = self.ax.scatter([tg], [density_at_tg], color='red', s=100, zorder=5)
        
        # Create draggable point object
        tg_drag = DraggablePoint(
            self.ax, tg, density_at_tg, scatter=tg_point, index=0,
            callback=self.on_tg_point_moved
        )
        self.control_points.append(tg_drag)
        
        # Create control points for fit lines (endpoints)
        t_min = min(temps)
        t_max = max(temps)
        
        # High temp line endpoints
        high_start_y = np.polyval(high_params, tg)
        high_end_y = np.polyval(high_params, t_max)
        high_points = self.ax.scatter([tg, t_max], [high_start_y, high_end_y], color='red', s=80, zorder=5)
        
        high_start_drag = DraggablePoint(
            self.ax, tg, high_start_y, scatter=high_points, index=0,
            callback=lambda idx, x, y: self.on_fit_point_moved('high', idx, x, y)
        )
        high_end_drag = DraggablePoint(
            self.ax, t_max, high_end_y, scatter=high_points, index=1,
            callback=lambda idx, x, y: self.on_fit_point_moved('high', idx, x, y)
        )
        
        self.control_points.extend([high_start_drag, high_end_drag])
        
        # Low temp line endpoints
        low_start_y = np.polyval(low_params, t_min)
        low_end_y = np.polyval(low_params, tg)
        low_points = self.ax.scatter([t_min, tg], [low_start_y, low_end_y], color='green', s=80, zorder=5)
        
        low_start_drag = DraggablePoint(
            self.ax, t_min, low_start_y, scatter=low_points, index=0,
            callback=lambda idx, x, y: self.on_fit_point_moved('low', idx, x, y)
        )
        low_end_drag = DraggablePoint(
            self.ax, tg, low_end_y, scatter=low_points, index=1,
            callback=lambda idx, x, y: self.on_fit_point_moved('low', idx, x, y)
        )
        
        self.control_points.extend([low_start_drag, low_end_drag])
        
        # Redraw canvas
        self.canvas.draw()
    
    def on_tg_point_moved(self, index, x, y):
        """Handle movement of the Tg control point"""
        if self.current_file is None:
            return
        
        # Update Tg line position
        self.tg_line.set_xdata([x, x])
        
        # Update Tg text
        self.tg_text.set_position((x, y))
        self.tg_text.set_text(f'Tg = {x:.2f} K')
        
        # Update result display
        self.tg_result.setText(f"{x:.2f} K")
        self.density_result.setText(f"{y:.6f} g/cm³")
        
        # Update the stored result
        result = self.data[self.current_file]
        result['tg'] = x
        result['density_at_tg'] = y
        
        # Update fit lines
        self.update_fit_lines()
        
        # Redraw canvas
        self.canvas.draw()
    
    def on_fit_point_moved(self, line_type, index, x, y):
        """Handle movement of fit line control points"""
        # Update fit parameters based on the new point positions
        self.update_fit_lines()
        
        # Redraw canvas
        self.canvas.draw()
    
    def update_fit_lines(self):
        """Update the fit lines based on control points"""
        if self.current_file is None or not self.control_points:
            return
        
        result = self.data[self.current_file]
        temps = result['temperatures']
        
        # Get Tg point position
        tg_point = self.control_points[0]
        tg_x = tg_point.x
        
        # Get high temp line points
        high_start = self.control_points[1]
        high_end = self.control_points[2]
        
        # Get low temp line points
        low_start = self.control_points[3]
        low_end = self.control_points[4]
        
        # Calculate high temp line parameters
        high_params = np.polyfit(
            [high_start.x, high_end.x],
            [high_start.y, high_end.y],
            1
        )
        
        # Calculate low temp line parameters
        low_params = np.polyfit(
            [low_start.x, low_end.x],
            [low_start.y, low_end.y],
            1
        )
        
        # Update result with new parameters
        result['high_temp_fit'] = high_params
        result['low_temp_fit'] = low_params
        
        # Calculate and update expansion coefficients
        density_at_tg = tg_point.y
        result['density_at_tg'] = density_at_tg
        
        volume_expansion_high = -high_params[0] / density_at_tg
        volume_expansion_low = -low_params[0] / density_at_tg
        
        result['volume_expansion_coef_high'] = volume_expansion_high
        result['volume_expansion_coef_low'] = volume_expansion_low
        
        # Update display
        self.expansion_high.setText(f"{volume_expansion_high:.6e} K⁻¹")
        self.expansion_low.setText(f"{volume_expansion_low:.6e} K⁻¹")
        
        # Get temperature range
        t_min = min(temps)
        t_max = max(temps)
        
        # Update high temp line
        t_high = np.linspace(tg_x, t_max, 100)
        density_high = np.polyval(high_params, t_high)
        self.high_fit_line.set_data(t_high, density_high)
        
        # Update low temp line
        t_low = np.linspace(t_min, tg_x, 100)
        density_low = np.polyval(low_params, t_low)
        self.low_fit_line.set_data(t_low, density_low)
    
    def run_analysis(self):
        """Run or re-run analysis on current file"""
        if not self.current_file or self.current_file not in self.data:
            QMessageBox.warning(self, "No Data", "Please load a data file first.")
            return
        
        # Get settings
        settings = {
            'apply_smoothing': self.smoothing_check.isChecked(),
            'smoothing_window': self.window_spin.value(),
            'smoothing_order': self.order_spin.value()
        }
        
        # Create thread for single file analysis
        self.progress_bar.setVisible(True)
        self.status_bar.showMessage("Analyzing data...")
        
        self.analysis_thread = AnalysisThread([self.current_file], settings)
        self.analysis_thread.progress_update.connect(self.update_progress)
        self.analysis_thread.analysis_complete.connect(self.reanalysis_completed)
        self.analysis_thread.error_occurred.connect(self.analysis_error)
        self.analysis_thread.start()
    
    def reanalysis_completed(self, results):
        """Handle completion of re-analysis"""
        # Update data with new results
        self.data.update(results)
        
        # Update display
        if self.current_file in results:
            self.display_results(self.current_file)
        
        # Hide progress bar
        self.progress_bar.setVisible(False)
        self.status_bar.showMessage("Analysis completed", 3000)
    
    def export_results(self):
        """Export analysis results to file"""
        if not self.data:
            QMessageBox.warning(self, "No Data", "No data to export.")
            return
        
        # Ask for save location
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getSaveFileName(
            self,
            "Save Results",
            "",
            "CSV Files (*.csv);;Excel Files (*.xlsx);;Text Files (*.txt)"
        )
        
        if not file_path:
            return
        
        try:
            # Prepare data
            results = []
            for file_path, result in self.data.items():
                results.append({
                    'File': os.path.basename(file_path),
                    'Tg (K)': result['tg'],
                    'Density at Tg (g/cm³)': result['density_at_tg'],
                    'Volume Expansion Coef (High) (K⁻¹)': result['volume_expansion_coef_high'],
                    'Volume Expansion Coef (Low) (K⁻¹)': result['volume_expansion_coef_low'],
                    'High Temp Slope': result['high_temp_fit'][0],
                    'High Temp Intercept': result['high_temp_fit'][1],
                    'Low Temp Slope': result['low_temp_fit'][0],
                    'Low Temp Intercept': result['low_temp_fit'][1]
                })
            
            # Create DataFrame
            df = pd.DataFrame(results)
            
            # Save based on extension
            ext = os.path.splitext(file_path)[1].lower()
            if ext == '.csv':
                df.to_csv(file_path, index=False)
            elif ext == '.xlsx':
                df.to_excel(file_path, index=False)
            elif ext == '.txt':
                df.to_csv(file_path, index=False, sep='\t')
            else:
                # Default to CSV
                df.to_csv(file_path + '.csv', index=False)
            
            self.status_bar.showMessage(f"Results exported to {file_path}", 3000)
            
        except Exception as e:
            QMessageBox.critical(self, "Export Error", f"Error exporting results: {str(e)}")
    
    def save_plot(self):
        """Save the current plot to an image file"""
        if self.ax is None or not self.current_file:
            QMessageBox.warning(self, "No Plot", "No plot to save.")
            return
        
        # Ask for save location
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getSaveFileName(
            self,
            "Save Plot",
            "",
            "PNG Files (*.png);;PDF Files (*.pdf);;SVG Files (*.svg);;JPEG Files (*.jpg)"
        )
        
        if not file_path:
            return
        
        try:
            # Save the figure
            self.figure.savefig(file_path, dpi=300, bbox_inches='tight')
            self.status_bar.showMessage(f"Plot saved to {file_path}", 3000)
            
        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Error saving plot: {str(e)}")
    
    def show_preferences(self):
        """Show preferences dialog"""
        # Simple implementation - could be expanded
        QMessageBox.information(self, "Preferences", "Preferences dialog not implemented.")
    
    def show_about(self):
        """Show about dialog"""
        about_text = """
        <h1>Glass Transition Temperature Analyzer</h1>
        <p>Version 1.0</p>
        <p>A tool for analyzing glass transition temperatures from MD simulations.</p>
        <p>© 2023</p>
        """
        
        QMessageBox.about(self, "About", about_text)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle("Fusion")
    
    # Create main window
    window = TgAnalyzer()
    
    # Start event loop
    sys.exit(app.exec_()) 