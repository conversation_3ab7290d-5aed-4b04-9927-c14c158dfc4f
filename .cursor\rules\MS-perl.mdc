---
description: 
globs: 
alwaysApply: false
---
{
  "version": 1,
  "name": "Materials Studio Perl Rules",
  "description": "用于Materials Studio和Python集成的Perl脚本编码规则",
  "language": "perl",
  "rules": [
    {
      "name": "基本设置",
      "scope": "source.perl",
      "settings": {
        "tabSize": 4,
        "insertSpaces": true,
        "bracketSpacing": true,
        "insertFinalNewline": true
      }
    },
    {
      "name": "代码质量检查",
      "priority": "high",
      "linter": {
        "rules": [
          {
            "id": "use-strict",
            "level": "warning",
            "message": "Perl脚本应当使用'use strict;'启用严格模式"
          },
          {
            "id": "use-warnings",
            "level": "info",
            "message": "建议使用'use warnings;'启用警告"
          },
          {
            "id": "use-materials-script",
            "level": "error",
            "message": "Materials Studio Perl脚本必须导入MaterialsScript模块"
          }
        ],
        "disallowed": [
          {
            "function": "Exists",
            "message": "Perl语言不允许使用Exists函数，请使用'defined'或'exists'（小写）代替",
            "replacement": "defined"
          },
          {
            "pattern": "\\$\\w+\\s*=\\s*undef\\s*;",
            "message": "不建议直接将变量设为undef，请考虑使用my声明或赋值为空字符串/0",
            "replacement": "my $1;"
          }
        ]
      }
    },
    {
      "name": "文件结构与组织",
      "description": "Materials Studio Perl脚本推荐文件组织结构",
      "priority": "medium",
      "pattern": {
        "header": [
          "#!perl",
          "",
          "use strict;",
          "use warnings;",
          "use MaterialsScript qw(:all);"
        ],
        "sections": [
          "# 脚本信息与描述",
          "# 配置参数",
          "# 初始化",
          "# 主执行流程",
          "# 子程序定义"
        ]
      }
    },
    {
      "name": "项目文件命名规范",
      "description": "项目文件命名规范",
      "priority": "low",
      "fileNaming": {
        "perl": "^[\\w\\-]+\\.pl$",
        "studyTable": "^[\\w\\-]+\\.std$",
        "trajectory": "^[\\w\\-]+\\.xtd$",
        "structure": "^[\\w\\-]+\\.xsd$"
      }
    },
    {
      "name": "Materials Studio API函数",
      "scope": "source.perl",
      "priority": "high",
      "completions": [
        {
          "trigger": "Documents->",
          "items": [
            "New", "Current", "Import", "Load", "Find", "Save", 
            "SaveAs", "Close", "Delete", "Item", "Items"
          ]
        },
        {
          "trigger": "Modules->",
          "items": [
            "Forcite", "CASTEP", "DMol3", "GULP", "Amorphous Cell", 
            "Reflex", "Sorption", "QMERA", "DFTB+", "Mesodyn"
          ]
        },
        {
          "trigger": "$forcite->",
          "items": [
            "ChangeSettings", "Dynamics", "Optimization", "Analysis",
            "Energy", "Charges", "Torsions", "ForceField", "Run"
          ]
        },
        {
          "trigger": "Tools->",
          "items": [
            "BondCalculation", "Symmetry", "Morphology", "Blends",
            "PolymerBuilding", "CrystalBuilding", "Protocols"
          ]
        },
        {
          "trigger": "$doc->UnitCell->",
          "items": [
            "Atoms", "Bonds", "HydrogenBonds", "Torsions", "Lattice",
            "LatticeParameters", "Density", "Volume", "Mass", "Fragments"
          ]
        }
      ]
    },
    {
      "name": "脚本信息注释",
      "priority": "high",
      "snippets": [
        {
          "trigger": "ms-header-bilingual",
          "description": "中英双语脚本头注释",
          "body": "#!perl\n\nuse strict;\nuse warnings;\nuse MaterialsScript qw(:all);\n\n# Title/标题: ${1:脚本标题}\n# Author/作者: ${2:作者}\n# Version/版本: ${3:1.0}\n# Materials Studio Version/版本: ${4:2020+}\n# Modules/模块: ${5:Materials Visualizer, Forcite}\n#\n# Description/描述:\n# ${6:Script description in English}\n# ${7:脚本的中文描述}\n#\n# Date/日期: ${8:YYYY-MM-DD}"
        },
        {
          "trigger": "ms-header-en",
          "description": "英文脚本头注释",
          "body": "#!perl\n\nuse strict;\nuse warnings;\nuse MaterialsScript qw(:all);\n\n# Title: ${1:Script Title}\n# Author: ${2:Author}\n# Version: ${3:1.0}\n# Materials Studio Version: ${4:2020+}\n# Modules: ${5:Materials Visualizer, Forcite}\n#\n# Description:\n# ${6:Detailed description of the script functionality}\n#\n# Date: ${7:YYYY-MM-DD}"
        },
        {
          "trigger": "ms-header-cn",
          "description": "中文脚本头注释",
          "body": "#!perl\n\nuse strict;\nuse warnings;\nuse MaterialsScript qw(:all);\n\n# 标题: ${1:脚本标题}\n# 作者: ${2:作者}\n# 版本: ${3:1.0}\n# Materials Studio版本: ${4:2020+}\n# 所需模块: ${5:Materials Visualizer, Forcite}\n#\n# 描述:\n# ${6:脚本功能的详细描述}\n#\n# 日期: ${7:YYYY-MM-DD}"
        },
        {
          "trigger": "section-header-bilingual",
          "description": "中英双语部分标题",
          "body": "#################################################################################\n# ${1:SECTION NAME} / ${2:部分名称}\n#################################################################################\n\n${3:# 代码内容}"
        }
      ]
    },
    {
      "name": "错误处理与日志",
      "priority": "medium",
      "snippets": [
        {
          "trigger": "try-eval-bilingual",
          "description": "中英双语错误处理",
          "body": "eval {\n    ${1:# 可能出错的代码}\n};\nif (\\$@) {\n    \\$logFile->Append(\"ERROR/错误: ${2:错误描述}: \\$@\\n\");\n    \\$logFile->Save;\n    ${3:# 错误处理}\n}"
        },
        {
          "trigger": "log-status",
          "description": "记录当前状态",
          "body": "\\$logFile->Append(\"${1:[INFO]} ${2:信息内容} (\\$${3:variable})\\n\");\n\\$logFile->Save;"
        },
        {
          "trigger": "die-gracefully-bilingual",
          "description": "优雅终止(中英双语)",
          "body": "\\$logFile->Append(\"[CRITICAL/严重错误] ${1:错误描述}\\n\");\n\\$logFile->Save;\ndie \"ERROR/错误: ${1:错误描述}\";"
        }
      ]
    },
    {
      "name": "性能优化规则",
      "priority": "low",
      "description": "提高脚本性能的规则和建议",
      "hints": [
        {
          "pattern": "for\\s*\\(.*\\$[^>]+->[^>]+->\\w+.*\\)",
          "message": "性能提示：避免在循环中重复获取对象属性，建议先将值存储在变量中"
        },
        {
          "pattern": "@\\$[\\w\\d\\(\\)]+",
          "message": "性能提示：使用标量上下文获取数组长度比使用@符号更高效：scalar(@array)"
        },
        {
          "pattern": "\\$[\\w\\d]+\\s*=\\s*\\(\\);",
          "message": "性能提示：清空数组使用@array = ()而不是$array = ();"
        },
        {
          "pattern": "\\$\\w+\\s*=\\s*split\\(/\\s*\/\\s*/,\\s*\\$\\w+\\);",
          "message": "性能提示：对于简单分隔符，split ' '比split /\\s+/更高效"
        }
      ]
    },
    {
      "name": "特定领域术语与代码模板",
      "priority": "medium",
      "description": "材料科学特定领域的术语和代码模板",
      "completions": [
        {
          "trigger": "ms-term-",
          "items": [
            "力场", "分子动力学", "蒙特卡洛", "玻璃化转变温度", "交联", "扩散", 
            "氢键", "分子间相互作用", "热力学性质", "动力学性质", "结构优化",
            "能量最小化", "密度泛函理论", "晶格常数", "介电常数", "导通率"
          ]
        }
      ],
      "snippets": [
        {
          "trigger": "group-atoms-by-element",
          "description": "按元素对原子分组",
          "body": "# 按元素对原子分组\nmy %atomsByElement;\nmy \\$atoms = \\$doc->UnitCell->Atoms;\n\nforeach my \\$atom (@\\$atoms) {\n    my \\$element = \\$atom->ElementSymbol;\n    push @{\\$atomsByElement{\\$element}}, \\$atom;\n}\n\n# 处理每种元素的原子\nforeach my \\$element (keys %atomsByElement) {\n    my \\$count = scalar(@{\\$atomsByElement{\\$element}});\n    \\$logFile->Append(\"元素 \\$element: \\$count 个原子\\n\");\n    \n    # 处理特定元素的原子\n    if (\\$element eq \"${1:C}\") {\n        ${2:# 对碳原子执行操作}\n    }\n}"
        }
      ]
    }
  ]
} 