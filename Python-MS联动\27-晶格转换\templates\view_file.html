<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ filename }} - CIF文件查看</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .file-info {
            background-color: #e9f7fe;
            border-left: 4px solid #3498db;
            padding: 10px 15px;
            margin-bottom: 20px;
        }
        .file-content {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: Consolas, monospace;
            white-space: pre-wrap;
            overflow-x: auto;
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #ddd;
        }
        .comment {
            color: #2ecc71;
        }
        .keyword {
            color: #3498db;
            font-weight: bold;
        }
        .value {
            color: #e74c3c;
        }
        .atom {
            color: #9b59b6;
        }
        .actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .btn {
            display: inline-block;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            color: white;
        }
        .btn-back {
            background-color: #95a5a6;
        }
        .btn-back:hover {
            background-color: #7f8c8d;
        }
        .btn-list {
            background-color: #3498db;
        }
        .btn-list:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>文件内容: {{ filename }}</h1>
        
        <div class="file-info">
            <p>此文件是转换后生成的CIF文件，包含晶体结构信息。</p>
            <p>文件开头的注释显示了转换信息，包括原始文件名、转换晶面和转换后的坐标轴。</p>
        </div>
        
        <div class="file-content">{{ content }}</div>
        
        <div class="actions">
            <a href="/list_files/{{ session_id }}" class="btn btn-list">返回文件列表</a>
            <a href="/" class="btn btn-back">返回首页</a>
        </div>
    </div>

    <script>
        // 简单的语法高亮
        document.addEventListener('DOMContentLoaded', function() {
            const content = document.querySelector('.file-content');
            let html = content.innerHTML;
            
            // 高亮注释
            html = html.replace(/(#.+)$/mg, '<span class="comment">$1</span>');
            
            // 高亮关键字和值
            html = html.replace(/(_[a-zA-Z0-9_.]+)/g, '<span class="keyword">$1</span>');
            html = html.replace(/([-+]?[0-9]*\.?[0-9]+)/g, '<span class="value">$1</span>');
            
            // 高亮原子标签
            html = html.replace(/\b([A-Z][a-z]?)\b/g, '<span class="atom">$1</span>');
            
            content.innerHTML = html;
        });
    </script>
</body>
</html> 