#!/usr/bin/env python3
"""
CASTEP电导率计算器使用示例
演示如何使用Python API进行电导率计算
"""

import sys
import os
import numpy as np

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.io.file_reader import CastepFileReader
from src.core.conductivity_calculator import ConductivityCalculator
from src.visualization.plotter import ConductivityPlotter
from src.io.result_exporter import ResultExporter

def example_basic_usage():
    """基本使用示例"""
    print("=" * 60)
    print("示例1: 基本使用流程")
    print("=" * 60)
    
    # 1. 创建组件实例
    reader = CastepFileReader()
    calculator = ConductivityCalculator()
    plotter = ConductivityPlotter()
    exporter = ResultExporter()
    
    # 2. 读取数据
    input_file = "介电数据.epsilon"
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        print("请先运行 convert_data.py 转换数据文件")
        return
    
    print(f"读取文件: {input_file}")
    energy, epsilon_1, epsilon_2 = reader.read_epsilon_file(input_file)
    print(f"数据点数: {len(energy)}")
    
    # 3. 计算光学导率
    print("计算光学导率...")
    omega, sigma_omega = calculator.calculate_optical_conductivity(energy, epsilon_2)
    print(f"光学导率范围: {np.min(sigma_omega):.2e} - {np.max(sigma_omega):.2e} S/m")
    
    # 4. 计算直流电导率
    print("计算直流电导率...")
    fit_result = calculator.extrapolate_dc_conductivity(
        energy, sigma_omega, 
        fit_points=5, 
        method='linear'
    )
    print(f"直流电导率: {fit_result['sigma_dc']:.6e} S/m")
    print(f"拟合质量 R²: {fit_result['r_squared']:.6f}")
    
    # 5. 导出结果
    print("导出结果...")
    exporter.export_to_csv(energy, omega, sigma_omega, epsilon_1, epsilon_2, "example_results.csv")
    
    # 6. 生成图表（可选）
    print("生成图表...")
    try:
        plotter.plot_conductivity_spectrum(energy, sigma_omega, log_scale=True, 
                                         save_path="example_conductivity.png")
    except:
        print("图表生成跳过（可能缺少显示环境）")
    
    print("✅ 基本使用示例完成")

def example_advanced_usage():
    """高级使用示例"""
    print("\n" + "=" * 60)
    print("示例2: 高级功能演示")
    print("=" * 60)
    
    # 创建计算器
    calculator = ConductivityCalculator()
    
    # 生成示例数据
    print("生成示例数据...")
    energy = np.linspace(0.1, 5.0, 100)
    epsilon_2 = 0.5 * energy * np.exp(-energy/2.0) + 0.01
    
    # 计算光学导率
    omega, sigma_omega = calculator.calculate_optical_conductivity(energy, epsilon_2)
    
    # 比较不同拟合方法
    methods = ['linear', 'polynomial', 'exponential']
    print("\n比较不同拟合方法:")
    print("-" * 40)
    
    for method in methods:
        try:
            fit_result = calculator.extrapolate_dc_conductivity(
                energy, sigma_omega, 
                fit_points=8, 
                method=method
            )
            
            sigma_dc = fit_result['sigma_dc']
            r_squared = fit_result['r_squared']
            
            print(f"{method:12s}: σ_dc = {sigma_dc:10.3e} S/m, R² = {r_squared:.6f}")
            
        except Exception as e:
            print(f"{method:12s}: 计算失败 - {str(e)}")
    
    # 获取计算摘要
    summary = calculator.get_calculation_summary()
    print(f"\n计算摘要:")
    print(f"数据点数: {summary['data_points']}")
    print(f"能量范围: {summary['energy_range'][0]:.3f} - {summary['energy_range'][1]:.3f} eV")
    
    print("✅ 高级功能示例完成")

def example_batch_processing():
    """批量处理示例"""
    print("\n" + "=" * 60)
    print("示例3: 批量处理演示")
    print("=" * 60)
    
    # 模拟多个数据集
    datasets = []
    for i in range(3):
        energy = np.linspace(0.1, 3.0, 50)
        # 不同的介电函数参数
        epsilon_2 = (0.2 + 0.1*i) * energy * np.exp(-energy/(1.5+0.5*i)) + 0.005*(i+1)
        datasets.append((f"dataset_{i+1}", energy, epsilon_2))
    
    # 批量计算
    calculator = ConductivityCalculator()
    exporter = ResultExporter()
    
    results = []
    
    for name, energy, epsilon_2 in datasets:
        print(f"处理数据集: {name}")
        
        try:
            # 计算
            omega, sigma_omega = calculator.calculate_optical_conductivity(energy, epsilon_2)
            fit_result = calculator.extrapolate_dc_conductivity(energy, sigma_omega)
            
            # 记录结果
            result = {
                'name': name,
                'data_points': len(energy),
                'energy_range': (energy[0], energy[-1]),
                'sigma_dc': fit_result['sigma_dc'],
                'r_squared': fit_result['r_squared']
            }
            results.append(result)
            
            # 导出单个结果
            exporter.export_to_csv(energy, omega, sigma_omega, 
                                 output_path=f"batch_{name}_results.csv")
            
        except Exception as e:
            print(f"  ❌ 处理失败: {str(e)}")
            continue
    
    # 汇总结果
    print("\n批量处理结果汇总:")
    print("-" * 60)
    print(f"{'数据集':<12} {'点数':<6} {'能量范围(eV)':<15} {'σ_dc(S/m)':<12} {'R²':<8}")
    print("-" * 60)
    
    for result in results:
        name = result['name']
        points = result['data_points']
        e_range = f"{result['energy_range'][0]:.1f}-{result['energy_range'][1]:.1f}"
        sigma_dc = f"{result['sigma_dc']:.2e}"
        r_squared = f"{result['r_squared']:.4f}"
        
        print(f"{name:<12} {points:<6} {e_range:<15} {sigma_dc:<12} {r_squared:<8}")
    
    print("✅ 批量处理示例完成")

def example_error_handling():
    """错误处理示例"""
    print("\n" + "=" * 60)
    print("示例4: 错误处理演示")
    print("=" * 60)
    
    calculator = ConductivityCalculator()
    
    # 测试各种错误情况
    test_cases = [
        ("空数据", np.array([]), np.array([])),
        ("负能量", np.array([-1, 0, 1]), np.array([0.1, 0.2, 0.3])),
        ("NaN数据", np.array([1, 2, np.nan]), np.array([0.1, 0.2, 0.3])),
        ("长度不匹配", np.array([1, 2, 3]), np.array([0.1, 0.2])),
        ("全零ε₂", np.array([1, 2, 3]), np.array([0, 0, 0])),
    ]
    
    for test_name, energy, epsilon_2 in test_cases:
        print(f"测试: {test_name}")
        try:
            omega, sigma_omega = calculator.calculate_optical_conductivity(energy, epsilon_2)
            print(f"  ✅ 意外成功")
        except Exception as e:
            print(f"  ❌ 预期错误: {str(e)[:50]}...")
    
    print("✅ 错误处理示例完成")

def main():
    """主函数"""
    print("CASTEP电导率计算器使用示例")
    print("本脚本演示如何使用Python API进行电导率计算")
    
    # 运行各个示例
    example_basic_usage()
    example_advanced_usage()
    example_batch_processing()
    example_error_handling()
    
    print("\n" + "=" * 60)
    print("所有示例运行完成！")
    print("=" * 60)
    
    print("\n更多使用方法:")
    print("1. 命令行版本: python castep_conductivity_calculator.py -h")
    print("2. Web界面版本: streamlit run conductivity_gui.py")
    print("3. 查看README.md了解详细文档")

if __name__ == "__main__":
    main()
