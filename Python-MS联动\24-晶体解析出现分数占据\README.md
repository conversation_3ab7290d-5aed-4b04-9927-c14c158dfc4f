# CIF文件占据率修改工具

## 简介

本工具用于修复晶体结构CIF文件中的分数占据问题，根据"保大去小"原则，将所有非1.0的占据率替换为1.0。

## 版权声明

本程序免费使用，不可售卖。
编译者：端木鹏博 (<EMAIL>)

## 使用方法

1. 直接双击运行可执行文件
2. 点击"添加文件"按钮选择需要处理的CIF文件
3. 选择或确认输出文件夹
4. 点击"处理文件"按钮开始处理
5. 处理完成后将在输出文件夹中生成修改后的文件

## 打包说明（开发者用）

要将此Python程序打包成可执行文件，请按照以下步骤操作：

### 安装必要的工具

```bash
pip install pyinstaller
```

### 打包命令

#### 基本打包（生成单个exe文件）

```bash
pyinstaller --onefile --windowed --icon=app_icon.ico cif_occupancy_fixer.py
```

> 注意：如果你有图标文件，可以使用`--icon`参数指定。

#### 高级打包（带更多选项）

```bash
pyinstaller --name="CIF文件占据率修改工具" --onefile --windowed --icon=app_icon.ico --add-data="README.md;." --clean cif_occupancy_fixer.py
```

打包完成后，可执行文件将位于`dist`目录中。

### 在不同系统上打包

- **Windows**: 直接在Windows上运行上述命令
- **macOS**: 在macOS上运行类似命令，但可能需要额外的签名步骤
- **Linux**: 在Linux上打包，但需要注意用户的桌面环境兼容性

## 注意事项

1. 打包后的程序可能被杀毒软件误报，这是PyInstaller打包程序常见的情况。
2. 确保在目标用户的操作系统上测试程序。
3. 如果程序依赖于其他文件（如图标、配置文件等），请确保它们也被正确打包。

## 故障排除

如果打包后的程序无法运行，可以尝试以下方法：

1. 在命令行中运行可执行文件，查看错误信息
2. 使用`--debug=all`选项重新打包，生成更详细的日志
3. 检查依赖项是否完整，可能需要手动添加某些隐式依赖

## 支持与联系

如有问题，请联系: <EMAIL> 