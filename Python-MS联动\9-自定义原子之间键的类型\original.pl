##################################################################################################################
# perl                                                                                                           #
#                                                                                                                #
# Author: DMPB                                                                                                   #
# Version: 1.3                                                                                                   #
# Tested on: Materials Studio 2020                                                                               #
#                                                                                                                #
# Required modules: Materials Visualizer                                                                         #
# This script modifies the bond types between atoms in two specified sets. It supports element type restrictions #
# to target specific bonds (e.g., C-C, C-O) and can change bonds to Single, Double, Triple, or Aromatic types.  #
# The script provides detailed reporting of found bonds, skipped bonds, and modification results. Modified atoms #
# are highlighted in the visualization for easy identification. This tool is useful for customizing chemical     #
# models and creating specialized bonding patterns in molecular structures.                                      #
#                                                                                                                #
# Date: 2023-12                                                                                                  #
#                                                                                                                #
#                                                                                                                #
##################################################################################################################

#!perl

use strict;
use warnings;
use MaterialsScript qw(:all);

# 功能：将指定的两个Set原子之间的键类型进行变换


#################################################################################################################
#                                         BEGIN USER INPUT                                                      #
#################################################################################################################
# 定义元素类型限制
my $elementType1 = "C";          # 要修改的键第一个原子的元素类型
my $elementType2 = "C";          # 要修改的键第二个原子的元素类型
# 若要修改C-O键，设置elementType1="C"，elementType2="O"
# 若要修改所有键，不限制元素类型，设置elementType1=""，elementType2=""

# 当前打开的文档
my $doc = $Documents{"test-bond.xsd"};
die "错误：没有活动的文档，请先打开一个分子结构文件。\n" unless $doc;

# 定义程序参数
my $selectedSetName1 = "1";      # 第一个选择的原子集合名称
my $selectedSetName2 = "2";      # 第二个选择的原子集合名称
my $newBondType = "Double";      # 要改变成的键类型（Single, Double, Triple, Aromatic）

#################################################################################################################
#                                         END USER INPUT                                                        #
#################################################################################################################

# 程序开始
print "\n=============================================\n";
print "       Materials Studio 键类型修改工具      \n";
print "=============================================\n\n";

print "程序参数：\n";
print "集合1: \"$selectedSetName1\"\n";
print "集合2: \"$selectedSetName2\"\n";
print "新键类型: \"$newBondType\"\n";
if ($elementType1 ne "" && $elementType2 ne "") {
    if ($elementType1 eq $elementType2) {
        print "元素限制: 只修改 $elementType1-$elementType1 键\n\n";
    } else {
        print "元素限制: 只修改 $elementType1-$elementType2 键\n\n";
    }
} else {
    print "元素限制: 无限制\n\n";
}

# 检查是否存在指定的Set
my $sets = $doc->UnitCell->Sets;

my $set1Exists = 0;
my $set2Exists = 0;
foreach my $set (@$sets) {
    if ($set->Name eq $selectedSetName1) {
        $set1Exists = 1;
    }
    if ($set->Name eq $selectedSetName2) {
        $set2Exists = 1;
    }
}

# 如果集合不存在，询问用户是否创建
if (!$set1Exists) {
    print "集合\"$selectedSetName1\"不存在，请先选择原子然后执行以下操作：\n";
    print "1. 右键点击选中的原子\n";
    print "2. 选择\"New Set\"\n";
    print "3. 将集合命名为\"$selectedSetName1\"\n";
    die "请先创建原子集合\"$selectedSetName1\"后再运行此脚本\n";
}

if (!$set2Exists) {
    print "集合\"$selectedSetName2\"不存在，请先选择原子然后执行以下操作：\n";
    print "1. 右键点击选中的原子\n";
    print "2. 选择\"New Set\"\n";
    print "3. 将集合命名为\"$selectedSetName2\"\n";
    die "请先创建原子集合\"$selectedSetName2\"后再运行此脚本\n";
}

# 获取集合中的原子
my $atoms1 = $doc->UnitCell->Sets($selectedSetName1)->Atoms;
my $atoms2 = $doc->UnitCell->Sets($selectedSetName2)->Atoms;

print "集合\"$selectedSetName1\"中有".scalar(@$atoms1)."个原子\n";
print "集合\"$selectedSetName2\"中有".scalar(@$atoms2)."个原子\n";

# 计数器
my $modified_bonds = 0;
my $found_bonds = 0;
my $skipped_element_bonds = 0;

# 保存所有处理过的原子对，用于高亮显示
my @processed_atom_pairs = ();

# 创建两个哈希表，快速查找原子是否属于集合1或集合2
my %set1_atoms;
my %set2_atoms;

# 获取文档中的所有原子，用于确定一个原子是否同时属于两个集合
my %all_atoms_info;
my $all_atoms = $doc->UnitCell->Atoms;
foreach my $atom (@$all_atoms) {
    $all_atoms_info{$atom->ID} = { 
        "atom" => $atom, 
        "in_set1" => 0, 
        "in_set2" => 0 
    };
}

print "\n检查集合中的原子...\n";
print "集合\"$selectedSetName1\"中的原子:\n";
foreach my $atom (@$atoms1) {
    $set1_atoms{$atom->ID} = $atom;
    $all_atoms_info{$atom->ID}{"in_set1"} = 1;
    print "  - ".$atom->Name." (ID: ".$atom->ID.", ".$atom->ElementSymbol.")\n";
}

print "\n集合\"$selectedSetName2\"中的原子:\n";
foreach my $atom (@$atoms2) {
    $set2_atoms{$atom->ID} = $atom;
    $all_atoms_info{$atom->ID}{"in_set2"} = 1;
    print "  - ".$atom->Name." (ID: ".$atom->ID.", ".$atom->ElementSymbol.")\n";
}

# 检查重叠的原子（同时属于两个集合的原子）
my @overlap_atoms;
foreach my $id (keys %all_atoms_info) {
    if ($all_atoms_info{$id}{"in_set1"} && $all_atoms_info{$id}{"in_set2"}) {
        push @overlap_atoms, $all_atoms_info{$id}{"atom"};
    }
}

if (@overlap_atoms > 0) {
    print "\n警告：发现".scalar(@overlap_atoms)."个原子同时属于集合\"$selectedSetName1\"和集合\"$selectedSetName2\"\n";
    print "为避免混淆，这些原子之间的键不会被修改\n";
    foreach my $atom (@overlap_atoms) {
        print "  - ".$atom->Name." (ID: ".$atom->ID.", ".$atom->ElementSymbol.")\n";
    }
}

print "\n查找两个集合之间的键...\n";

# 获取文档中的所有键
my $bonds = $doc->UnitCell->Bonds;

foreach my $bond (@$bonds) {
    my $atom1 = $bond->Atom1;
    my $atom2 = $bond->Atom2;
    
    my $atom1_id = $atom1->ID;
    my $atom2_id = $atom2->ID;
    
    # 检查键是否连接集合1和集合2中的原子
    if (($set1_atoms{$atom1->ID} && $set2_atoms{$atom2->ID}) || 
        ($set1_atoms{$atom2->ID} && $set2_atoms{$atom1->ID})) {
        
        $found_bonds++;
        
        # 检查元素类型限制
        if ($elementType1 ne "" || $elementType2 ne "") {
            my $element1 = $atom1->ElementSymbol;
            my $element2 = $atom2->ElementSymbol;
            
            # 检查是否匹配指定的元素类型组合
            my $element_match = 0;
            
            # 判断是否满足元素类型限制
            if (($element1 eq $elementType1 && $element2 eq $elementType2) || 
                ($element1 eq $elementType2 && $element2 eq $elementType1)) {
                $element_match = 1;
            }
            
            if (!$element_match) {
                my $expected_elements = "";
                if ($elementType1 eq $elementType2) {
                    $expected_elements = "$elementType1-$elementType1";
                } else {
                    $expected_elements = "$elementType1-$elementType2";
                }
                
                print "跳过键：\n";
                print "  原子1: ".$atom1->Name." (ID: ".$atom1->ID.", ".$atom1->ElementSymbol.")\n";
                print "  原子2: ".$atom2->Name." (ID: ".$atom2->ID.", ".$atom2->ElementSymbol.")\n";
                print "  键长: ".sprintf("%.3f", $bond->Length)." Å\n";
                print "  原因: 不满足元素类型限制（需要$expected_elements键，当前为$element1-$element2键）\n\n";
                
                $skipped_element_bonds++;
                next; # 跳过这个键
            }
        }
        
        # 只有当键连接集合1和集合2中的原子，且满足元素类型限制时，才会修改
        my $oldBondType = $bond->BondType;
        if ($oldBondType ne $newBondType) {
            print "找到键：\n";
            print "  原子1: ".$atom1->Name." (ID: ".$atom1->ID.", ".$atom1->ElementSymbol.")\n";
            print "  原子2: ".$atom2->Name." (ID: ".$atom2->ID.", ".$atom2->ElementSymbol.")\n";
            print "  键长: ".sprintf("%.3f", $bond->Length)." Å\n";
            print "  将键类型从$oldBondType改为$newBondType\n\n";
            
            $bond->BondType = $newBondType;
            $modified_bonds++;
            
            # 添加到已处理列表，用于高亮显示
            push @processed_atom_pairs, [$atom1, $atom2];
        } else {
            print "找到键：\n";
            print "  原子1: ".$atom1->Name." (ID: ".$atom1->ID.", ".$atom1->ElementSymbol.")\n";
            print "  原子2: ".$atom2->Name." (ID: ".$atom2->ID.", ".$atom2->ElementSymbol.")\n";
            print "  键长: ".sprintf("%.3f", $bond->Length)." Å\n";
            print "  键类型已经是$newBondType，无需修改\n\n";
        }
    }
}

# 高亮显示处理的原子
if (@processed_atom_pairs > 0) {
    # 重置所有原子显示样式
    my $all_atoms = $doc->UnitCell->Atoms;
    foreach my $atom (@$all_atoms) {
        $atom->Style = "Ball and stick"; # 使用默认样式
    }
    
    # 高亮显示所有处理过的原子
    foreach my $pair (@processed_atom_pairs) {
        my ($atom1, $atom2) = @$pair;
        $atom1->Style = "CPK";  # 使用CPK样式突出显示
        $atom2->Style = "CPK";
    }
    
    print "已高亮显示所有处理的原子对\n";
} else {
    print "没有找到符合条件的键，无法修改键类型\n";
}

# 显示结果摘要
print "\n处理完成，结果摘要：\n";
print "找到的键数量：$found_bonds\n";
print "因元素类型限制跳过的键数量：$skipped_element_bonds\n";
print "修改的键数量：$modified_bonds\n";
