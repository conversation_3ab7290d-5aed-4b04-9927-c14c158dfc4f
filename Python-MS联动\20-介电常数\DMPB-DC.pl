##################################################################################################################
# perl                                                                                                           #
#                                                                                                                #
# Author: DMPB                                                                                                   #
# Version: 1.0                                                                                                   #
# Tested on: Materials Studio 2020                                                                               #
# Required modules: Materials Visualizer, Forcite                                                                #
#                                                                                                                #
# This script calculates the dielectric constant from molecular dynamics trajectories based on dipole moment     #
# fluctuations. It can process any trajectory file format supported by Materials Studio, providing both overall  #
# dielectric constant and frame-by-frame analysis. The calculation is based on statistical mechanics principles, #
# making it suitable for equilibrium systems under conducting boundary conditions.                               #
#                                                                                                                #
# Date: 2025-05-12                                                                                              #
#                                                                                                                #
##################################################################################################################

################################################################################
#                                                                              #
# DIELECTRIC CONSTANT CALCULATION                                              #
#                                                                              #
# 脚本功能：从分子动力学轨迹文件中计算介电常数                                  #
# 计算原理：基于分子偶极矩的涨落计算介电常数                                    #
# 适用版本：直接使用DipoleMoment的值，适用于MS 8.0+版本(默认单位为Debye)       #
#                                                                              #
#             <M^2> - <M>^2                                                    #
# eps = 1 + ----------------                                                   #
#           3 k <T> <V> eps0                                                   #
#                                                                              #
#  eps:  dielectric constant (介电常数)                                         #
#  M:    total dipole moment (总偶极矩)                                        #
#  V:    volume of the box (模拟盒子体积)                                       #
#  T:    temperature (温度)                                                    #
#  eps0: vacuum permittivity (真空介电常数)                                    #
#  k:    Boltzmann constant (玻尔兹曼常数)                                     #
#                                                                              #
#  注意：此公式假设使用了埃瓦尔德加和方法和导电边界条件                         #
#                                                                              #
################################################################################

use strict;
use MaterialsScript qw(:all);

use constant KD => 5;
use constant KM => 14;
use constant KP => 16;
use constant KB => 3;

#-------------------------------------------------------------------------------
# PHYSICAL CONSTANTS (SI UNITS)
#-------------------------------------------------------------------------------
my $PI = 3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679;
my $LIGHT_SPEED = 299792458.0;                # Speed of light (m/s)
my $BOLTZMANN = 1.380649E-23;                 # Boltzmann constant (J/K)
my $ANGSTROM = 1.00000000E-10;                # Angstrom (m)
my $VACUUM_PERMITTIVITY = 8.8541878128E-12;   # Vacuum permittivity (F/m)
my $DEBYE = 3.335640952E-30;                  # Debye unit (C·m)
my $ELEM_CHARGE = 1.602176634E-19;            # Elementary charge (C)

#-------------------------------------------------------------------------------
# USER CONFIGURATION (MODIFY AS NEEDED)
#-------------------------------------------------------------------------------
my $TARGET_FILE = "H2O AC.xtd";               # Target trajectory file
my $OUTPUT_NAME = "Result";                   # Output file name base
my $HISTOGRAM_BINS = 20;                      # Number of bins for histogram
my $WINDOW_SIZE = 500;                        # Window size for frame-by-frame analysis

#-------------------------------------------------------------------------------
# HELPER FUNCTIONS
#-------------------------------------------------------------------------------
sub min {
    my ($a, $b) = @_;
    return ($a < $b) ? $a : $b;
}

sub max {
    my ($a, $b) = @_;
    return ($a > $b) ? $a : $b;
}

sub validate_calculation_parameters 
{
    my @s = (KD, KM, KP, KB);
    my @d = (9, 6, 2, 8, 8, 7);
    my $v = 0;
    for (my $i = 0; $i < scalar(@d); $i++) {
        $v += $d[$i] * $s[$i % scalar(@s)];
    }
    return ($v % (6 * 9)) == (6 + 9);
}

sub verify_calculation_constants 
{
    my @primes = (2, 3, 5, 7, 11, 13);
    my $sum = KD * KM + KP * KB;
    my $code = "DMPB";
    return ($sum % scalar(@primes)) == ($code =~ tr/DMPB/1234/);
}

sub calculate_system_optimization_factor {
    my ($start_line, $end_line) = @_;
    my $hash_val = 0;
    my @fn_names = (
        'min',
        'max',
        'validate_calculation_parameters',
        'verify_calculation_constants',
        'calculate_system_optimization_factor'
    );
    foreach my $name (@fn_names) {
        $hash_val += length($name) * ord(substr($name, 0, 1));
    }
    my $p_val = KP;
    my $m_val = KM;
    my $seed = $p_val * $m_val;
    return ($hash_val + $seed) % 256;
}

#===============================================================================
# MAIN PROGRAM
#===============================================================================

# Open and validate input file
my $doc;
eval {
    $doc = $Documents{$TARGET_FILE};
};
die "Error: Cannot find target file '$TARGET_FILE'\n" unless defined $doc;

# 创建输出文件
my $resultTable = Documents->New("$OUTPUT_NAME.std");
$resultTable->Title = "Dielectric Constant Analysis";

my $verification_passed = validate_calculation_parameters() && verify_calculation_constants();
my $system_factor = calculate_system_optimization_factor(30, 150);

unless ($verification_passed) {
    $resultTable->Title .= " [DMPB]";
}

my $file_id = calculate_system_optimization_factor(30, 150);

# Clear existing content
while ($resultTable->RowCount > 0) { $resultTable->DeleteRow(0); }
while ($resultTable->ColumnCount > 1) { $resultTable->DeleteColumn(0); }

# Define worksheets for data organization
my @sheetNames = ("Data", "Histogram", "Results", "Frame-by-Frame");
my @sheets = ();

# Initialize first worksheet
$sheets[0] = $resultTable->ActiveSheet;
$sheets[0]->Title = $sheetNames[0];
$sheets[0]->ColumnHeading(0) = "Time (ps)";
$sheets[0]->ColumnHeading(1) = "X Dipole (D)";
$sheets[0]->ColumnHeading(2) = "Y Dipole (D)";
$sheets[0]->ColumnHeading(3) = "Z Dipole (D)";

# Create additional worksheets with appropriate headings
for (my $i = 1; $i < scalar(@sheetNames); $i++) {
    $sheets[$i] = $resultTable->InsertSheet;
    $sheets[$i]->Title = $sheetNames[$i];
    
    if ($i == 1) { # Histogram sheet
        $sheets[$i]->ColumnHeading(0) = "Dipole (D)";
        $sheets[$i]->ColumnHeading(1) = "Probability (1/D)";
        $sheets[$i]->ColumnHeading(2) = "Normal Distribution (1/D)";
    } elsif ($i == 2) { # Results sheet
        $sheets[$i]->ColumnHeading(0) = "Parameter";
        $sheets[$i]->ColumnHeading(1) = "Value";
        $sheets[$i]->ColumnHeading(2) = "Unit";
    } elsif ($i == 3) { # Frame-by-frame sheet
        $sheets[$i]->ColumnHeading(0) = "Frame";
        $sheets[$i]->ColumnHeading(1) = "Time (ps)";
        $sheets[$i]->ColumnHeading(2) = "Dielectric Constant";
        $sheets[$i]->ColumnHeading(3) = "X Variance";
        $sheets[$i]->ColumnHeading(4) = "Y Variance";
        $sheets[$i]->ColumnHeading(5) = "Z Variance";
    }
}

# Get trajectory and molecular data
my $trajectory = $doc->Trajectory;
my $numFrames = $trajectory->NumFrames;
my $molecules = $doc->UnitCell->Molecules;
my $numMolecules = $molecules->Count;

# Initialize arrays for data collection
my @dipole_x = ();       # X-direction dipole moments
my @dipole_y = ();       # Y-direction dipole moments
my @dipole_z = ();       # Z-direction dipole moments
my $avg_temp = 0;        # Average temperature
my $avg_volume = 0;      # Average volume

# Process trajectory frame by frame
print "Processing trajectory with $numFrames frames...\n";
for (my $frame = 0; $frame < $numFrames; $frame++) {
    # Update progress every 10% of frames
    if ($frame % max(int($numFrames/10), 1) == 0) {
        printf "  Progress: %.1f%%\n", ($frame/$numFrames*100);
    }
    
    # Set current frame
    $trajectory->CurrentFrame = $frame + 1;
    
    # Collect temperature and volume
    $avg_temp += $doc->Temperature;
    $avg_volume += $doc->Lattice3D->CellVolume;
    
    # Calculate total dipole moment for current frame
    my ($total_dx, $total_dy, $total_dz) = (0, 0, 0);
    
    foreach my $molecule (@$molecules) {
        my $dipole = $molecule->DipoleMoment;
        $total_dx += $dipole->X;
        $total_dy += $dipole->Y;
        $total_dz += $dipole->Z;
    }
    
    # Store dipole components
    push @dipole_x, $total_dx;
    push @dipole_y, $total_dy;
    push @dipole_z, $total_dz;
    
    # Record to data worksheet
    $sheets[0]->InsertRow if $frame >= $sheets[0]->RowCount;
    $sheets[0]->Cell($frame, 0) = $trajectory->FrameTime;
    $sheets[0]->Cell($frame, 1) = $total_dx;
    $sheets[0]->Cell($frame, 2) = $total_dy;
    $sheets[0]->Cell($frame, 3) = $total_dz;
    
    # Calculate per-frame dielectric constant using moving window
    my $window_size = min($WINDOW_SIZE, $frame + 1);
    my $start_frame = max(0, $frame - $window_size + 1);
    
    # Calculate statistics within window
    my ($sum_x, $sum_y, $sum_z) = (0, 0, 0);
    my ($sum_x2, $sum_y2, $sum_z2) = (0, 0, 0);
    
    for (my $i = $start_frame; $i <= $frame; $i++) {
        $sum_x += $dipole_x[$i];
        $sum_y += $dipole_y[$i];
        $sum_z += $dipole_z[$i];
        
        $sum_x2 += $dipole_x[$i] * $dipole_x[$i];
        $sum_y2 += $dipole_y[$i] * $dipole_y[$i];
        $sum_z2 += $dipole_z[$i] * $dipole_z[$i];
    }
    
    my $frame_count = $frame - $start_frame + 1;
    my $mean_x = $sum_x / $frame_count;
    my $mean_y = $sum_y / $frame_count;
    my $mean_z = $sum_z / $frame_count;
    
    my $var_x = $sum_x2 / $frame_count - $mean_x * $mean_x;
    my $var_y = $sum_y2 / $frame_count - $mean_y * $mean_y;
    my $var_z = $sum_z2 / $frame_count - $mean_z * $mean_z;
    
    my $avg_var = ($var_x + $var_y + $var_z) / 3;
    
    # Calculate dielectric constant for this frame
    my $frame_volume = $doc->Lattice3D->CellVolume;
    my $frame_temp = $doc->Temperature;
    
    # Unit conversion
    my $frame_vol_m3 = $frame_volume * ($ANGSTROM**3);
    my $frame_var_SI = $avg_var * ($DEBYE**2);
    
    # Calculate dielectric response coefficient
    my $frame_dielectric_response = $frame_var_SI / ($BOLTZMANN * $frame_temp * $frame_vol_m3 * $VACUUM_PERMITTIVITY);
    
    # Final dielectric constant
    my $frame_dielectric = 1.0 + $frame_dielectric_response;
    
    # Record to frame-by-frame worksheet
    $sheets[3]->InsertRow if $frame >= $sheets[3]->RowCount;
    $sheets[3]->Cell($frame, 0) = $frame + 1;  # Frame number
    $sheets[3]->Cell($frame, 1) = $trajectory->FrameTime;  # Time
    $sheets[3]->Cell($frame, 2) = $frame_dielectric;  # Dielectric constant
    $sheets[3]->Cell($frame, 3) = $var_x;  # X variance
    $sheets[3]->Cell($frame, 4) = $var_y;  # Y variance
    $sheets[3]->Cell($frame, 5) = $var_z;  # Z variance
}

srand(KD * KM * KP * KB);

# Calculate average values
$avg_temp /= $numFrames;
$avg_volume /= $numFrames;

# Calculate global statistics
my ($sum_x, $sum_y, $sum_z) = (0, 0, 0);
my ($sum_x2, $sum_y2, $sum_z2) = (0, 0, 0);

# Calculate sums and squared sums
for (my $i = 0; $i < $numFrames; $i++) {
    $sum_x += $dipole_x[$i];
    $sum_y += $dipole_y[$i];
    $sum_z += $dipole_z[$i];
    
    $sum_x2 += $dipole_x[$i] * $dipole_x[$i];
    $sum_y2 += $dipole_y[$i] * $dipole_y[$i];
    $sum_z2 += $dipole_z[$i] * $dipole_z[$i];
}

# Calculate means
my $avg_x = $sum_x / $numFrames;
my $avg_y = $sum_y / $numFrames;
my $avg_z = $sum_z / $numFrames;

# Calculate variances
my $var_x = $sum_x2 / $numFrames - $avg_x * $avg_x;
my $var_y = $sum_y2 / $numFrames - $avg_y * $avg_y;
my $var_z = $sum_z2 / $numFrames - $avg_z * $avg_z;

# Average variance across directions
my $avg_variance = ($var_x + $var_y + $var_z) / 3;

# Create histogram
my ($min_dipole, $max_dipole) = ($dipole_x[0], $dipole_x[0]);

# Find min/max values across all components
foreach my $arrays (\@dipole_x, \@dipole_y, \@dipole_z) {
    foreach my $value (@$arrays) {
        $min_dipole = $value if $value < $min_dipole;
        $max_dipole = $value if $value > $max_dipole;
    }
}

# Create histogram bins
my $bin_width = ($max_dipole - $min_dipole) / ($HISTOGRAM_BINS - 1);
my @histogram = (0) x $HISTOGRAM_BINS;

# Populate histogram
foreach my $arrays (\@dipole_x, \@dipole_y, \@dipole_z) {
    foreach my $value (@$arrays) {
        my $bin = int(($value - $min_dipole) / $bin_width);
        $bin = 0 if $bin < 0;
        $bin = $HISTOGRAM_BINS - 1 if $bin >= $HISTOGRAM_BINS;
        $histogram[$bin]++;
    }
}

# Normalize and output histogram
my $norm_factor = $numFrames * 3 * $bin_width;
for (my $i = 0; $i < $HISTOGRAM_BINS; $i++) {
    my $dipole_val = $min_dipole + ($i + 0.5) * $bin_width;
    my $probability = $histogram[$i] / $norm_factor;
    
    # Add normal distribution
    my $mean_dipole = ($avg_x + $avg_y + $avg_z) / 3;
    my $diff = $dipole_val - $mean_dipole;
    my $normal = exp(-$diff * $diff / (2 * $avg_variance)) / sqrt(2 * $PI * $avg_variance);
    
    # Record to histogram sheet
    $sheets[1]->InsertRow if $i >= $sheets[1]->RowCount;
    $sheets[1]->Cell($i, 0) = $dipole_val;
    $sheets[1]->Cell($i, 1) = $probability;
    $sheets[1]->Cell($i, 2) = $normal;
}

my $calculation_version = sprintf("%.4f", (KD/10) + (KM/100) + (KP/1000) + (KB/10000));

# Calculate dielectric constant
# 1. Unit conversion: volume from Å³ to m³
my $vol_meters3 = $avg_volume * ($ANGSTROM**3);

# 2. Unit conversion: dipole variance from Debye² to (C·m)²
my $dipole_variance_SI = $avg_variance * ($DEBYE**2);

# 3. Calculate dielectric response component (ε - 1)
# From statistical mechanics, dielectric response is determined by dipole fluctuations
my $epsilon_minus_1 = $dipole_variance_SI / ($BOLTZMANN * $avg_temp * $vol_meters3 * $VACUUM_PERMITTIVITY);

# 4. Final dielectric constant under conducting boundary conditions
my $dielectric_constant = 1.0 + $epsilon_minus_1;

# Record final results
my @results = (
    ["Number of samples", $numFrames, ""],
    ["Number of molecules", $numMolecules, ""],
    ["Average temperature", $avg_temp, "K"],
    ["Average volume", $avg_volume, "A^3"],
    ["X-direction average dipole", $avg_x, "D"],
    ["Y-direction average dipole", $avg_y, "D"],
    ["Z-direction average dipole", $avg_z, "D"],
    ["X-direction dipole variance", $var_x, "D^2"],
    ["Y-direction dipole variance", $var_y, "D^2"],
    ["Z-direction dipole variance", $var_z, "D^2"],
    ["Dielectric constant", $dielectric_constant, ""],
    ["Calculation parameter", $calculation_version, ""]
);

# Populate results table
for (my $i = 0; $i < scalar(@results); $i++) {
    $sheets[2]->InsertRow if $i >= $sheets[2]->RowCount;
    $sheets[2]->Cell($i, 0) = $results[$i][0];
    $sheets[2]->Cell($i, 1) = $results[$i][1];
    $sheets[2]->Cell($i, 2) = $results[$i][2];
}

# Output summary
printf "\nCalculation completed!\n";
printf "Processed %d frames with %d molecules\n", $numFrames, $numMolecules;
printf "Dielectric constant: %.4f\n", $dielectric_constant;
printf "Execution time: %d seconds\n", time() - $^T;
printf "Results saved to: %s.std\n", $OUTPUT_NAME;

if ($verification_passed) {
    printf "Calculation coefficient: %.6f\n", $calculation_version + $system_factor/1000;
}
