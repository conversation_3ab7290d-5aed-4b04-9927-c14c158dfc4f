import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, simpledialog
import math
import matplotlib
import matplotlib.font_manager as fm
import os

# 尝试多种方法设置中文字体
def setup_chinese_fonts():
    # 方法1: 设置sans-serif字体列表
    matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'DejaVu Sans']
    matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    
    # 方法2: 添加可能存在的系统字体(Windows)
    possible_fonts = [
        'C:/Windows/Fonts/simhei.ttf',  # 黑体
        'C:/Windows/Fonts/simsun.ttc',  # 宋体
        'C:/Windows/Fonts/msyh.ttc',    # 微软雅黑
    ]
    
    # 检查并添加找到的字体
    for font_path in possible_fonts:
        if os.path.exists(font_path):
            try:
                font_prop = fm.FontProperties(fname=font_path)
                matplotlib.rcParams['font.sans-serif'].insert(0, font_prop.get_name())
                print(f"已添加字体: {font_prop.get_name()} from {font_path}")
                break  # 找到一个可用字体就停止
            except:
                pass
    
    # 方法3: 如果以上方法都失败，使用matplotlib内置字体
    if len(matplotlib.rcParams['font.sans-serif']) == 0:
        matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans']
        print("使用默认DejaVu Sans字体")

# 设置中文字体支持
setup_chinese_fonts()

class DampingLossFactorCalculator:
    """
    基于Wang等人的研究论文 'Theoretical Study of Fast Calculation of Damping Loss Factors for
    Rubber Polymers' 实现的阻尼损耗因子计算器
    """
    
    def __init__(self):
        # 橡胶聚合物的类型与实验DLF范围
        self.rubber_data = {
            "IIR (异丁烯异戊二烯橡胶)": {"density_range": (0.87, 0.92), "exp_dlf": (2.0, 2.6)},
            "NBR (丁腈橡胶)": {"density_range": (0.95, 0.98), "exp_dlf": (1.8, 2.3)},
            "PMA (聚甲基丙烯酸酯)": {"density_range": (1.01, 1.22), "exp_dlf": (1.2, 2.0)},
            "EPDM (乙丙二烯单体)": {"density_range": (0.87, 1.04), "exp_dlf": (0.5, 0.7)},
            "PU (聚氨酯)": {"density_range": (1.05, 1.09), "exp_dlf": (0.4, 0.8)},
            "PVC (聚氯乙烯)": {"density_range": (1.36, 1.38), "exp_dlf": (0.5, 0.7)},
            "MQ (硅橡胶)": {"density_range": (0.59, 1.03), "exp_dlf": (0.4, 0.6)},
            "Custom": {"density_range": (0.80, 1.20), "exp_dlf": (0.0, 0.0)}  # 添加自定义选项
        }
        
        # 论文中的计算值
        self.calculated_values = {
            "IIR (异丁烯异戊二烯橡胶)": {
                "density_0": 0.92, "K_0": 3.20, 
                "density_e": 0.87, "K_e": 38.09, 
                "calc_dlf": 3.30
            },
            "NBR (丁腈橡胶)": {
                "density_0": 0.95, "K_0": 1.20, 
                "density_e": 0.98, "K_e": 7.19, 
                "calc_dlf": 2.23
            },
            "PMA (聚甲基丙烯酸酯)": {
                "density_0": 1.22, "K_0": 6.26, 
                "density_e": 1.01, "K_e": 22.03, 
                "calc_dlf": 1.59
            },
            "EPDM (乙丙二烯单体)": {
                "density_0": 0.87, "K_0": 1.32, 
                "density_e": 1.04, "K_e": 2.74, 
                "calc_dlf": 1.04
            },
            "PU (聚氨酯)": {
                "density_0": 1.05, "K_0": 1.65, 
                "density_e": 1.09, "K_e": 2.30, 
                "calc_dlf": 0.63
            },
            "PVC (聚氯乙烯)": {
                "density_0": 1.38, "K_0": 2.25, 
                "density_e": 1.36, "K_e": 3.04, 
                "calc_dlf": 0.59
            },
            "MQ (硅橡胶)": {
                "density_0": 1.03, "K_0": 3.49, 
                "density_e": 0.59, "K_e": 4.20, 
                "calc_dlf": 0.45
            },
            "Custom": {  # 添加自定义选项的默认值
                "density_0": 1.00, "K_0": 2.00, 
                "density_e": 1.00, "K_e": 6.00, 
                "calc_dlf": 1.22  # 这个值将根据输入的K值自动计算
            }
        }
        
        # 用于存储用户添加的自定义橡胶类型
        self.custom_rubbers = {}

    def calculate_dlf(self, K_e, K_0):
        """
        根据等式(6)计算阻尼损耗因子
        DLF (tan α) = sqrt(K(ε₀,0)/K(0,σ₀) - 1)
        
        参数:
        K_e: 点(ε₀,0)处的体积模量 (GPa)
        K_0: 点(0,σ₀)处的体积模量 (GPa)
        
        返回:
        阻尼损耗因子 (DLF)和计算步骤说明
        """
        if K_e <= 0 or K_0 <= 0:
            raise ValueError("Bulk moduli must be positive values")
        
        # 计算过程详情
        steps = []
        steps.append(f"Step 1: Input Parameters")
        steps.append(f"  • K(e0,0) = {K_e:.2f} GPa")
        steps.append(f"  • K(0,s0) = {K_0:.2f} GPa")
        
        steps.append(f"\nStep 2: Calculate Moduli Ratio K(e0,0)/K(0,s0)")
        ratio = K_e / K_0
        steps.append(f"  • Ratio = {K_e:.2f} / {K_0:.2f} = {ratio:.4f}")
        
        if ratio <= 1:
            steps.append(f"\nStep 3: Check Condition")
            steps.append(f"  • Ratio must be greater than 1 to calculate DLF")
            steps.append(f"  • Current ratio = {ratio:.4f} ≤ 1")
            steps.append(f"  • Condition not met, return DLF = 0")
            return 0, "\n".join(steps)
        
        steps.append(f"\nStep 3: Apply DLF Formula")
        steps.append(f"  • DLF = sqrt(K(e0,0)/K(0,s0) - 1)")
        steps.append(f"  • DLF = sqrt({ratio:.4f} - 1)")
        
        result = math.sqrt(ratio - 1)
        steps.append(f"  • DLF = sqrt({ratio-1:.4f}) = {result:.4f}")
        
        steps.append(f"\nStep 4: Calculate Phase Angle α")
        alpha_degrees = math.degrees(math.atan(result))
        steps.append(f"  • α = arctan(DLF) = arctan({result:.4f}) = {alpha_degrees:.2f}°")
        
        steps.append(f"\nConclusion: The damping loss factor (DLF) = {result:.4f}")
        
        return result, "\n".join(steps)
    
    def plot_hysteretic_loop(self, dlf):
        """
        绘制粘弹性材料的滞后环
        
        参数:
        dlf: 阻尼损耗因子
        
        返回:
        matplotlib图形对象
        """
        fig = Figure(figsize=(6, 5))
        ax = fig.add_subplot(111)
        
        # 计算相角
        alpha = math.atan(dlf)
        
        # 创建滞后环数据
        t = np.linspace(0, 2*np.pi, 100)
        
        # 应变和应力的参数方程
        epsilon = np.cos(t)
        sigma = np.cos(t + alpha)
        
        # 标记(ε₀,0)和(0,σ₀)点
        e0_point = (1, 0)
        s0_point = (0, np.cos(alpha))
        
        # 绘制滞后环
        ax.plot(epsilon, sigma, 'b-', linewidth=2)
        ax.plot(e0_point[0], e0_point[1], 'ro', markersize=8, label='(e0, 0)')
        ax.plot(s0_point[0], s0_point[1], 'go', markersize=8, label='(0, s0)')
        
        # 增加参考线
        ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        ax.axvline(x=0, color='k', linestyle='-', alpha=0.3)
        
        # 标签和标题 - 使用英文
        ax.set_xlabel('Strain (e)', fontsize=12)
        ax.set_ylabel('Stress (s)', fontsize=12)
        ax.set_title(f'Hysteretic Loop of Viscoelastic Material (DLF = {dlf:.2f})', fontsize=14)
        ax.legend(loc='best', fontsize=10)
        ax.grid(True, linestyle='--', alpha=0.6)
        
        # 设置坐标轴范围，保持比例
        ax.set_xlim(-1.2, 1.2)
        ax.set_ylim(-1.2, 1.2)
        
        # 添加滞后相角示意
        r = 0.4
        t_point = np.pi/4  # 选择一个π/4的位置来绘制角度
        
        # 添加相角标注
        arc = patches.Arc(xy=(0, 0), width=r*2, height=r*2, 
                         theta1=np.degrees(t_point), 
                         theta2=np.degrees(t_point+alpha),
                         angle=0, color='r', linewidth=2)
        ax.add_patch(arc)
        ax.text(r*0.7*np.cos(t_point+alpha/2), r*0.7*np.sin(t_point+alpha/2), 
                f'a = {np.degrees(alpha):.1f}°', 
                ha='center', va='center', fontsize=10,
                bbox=dict(facecolor='white', alpha=0.8))
        
        # 添加DLF计算公式 - 使用英文
        formula_text = 'DLF = tan a = sqrt(K(e0,0)/K(0,s0) - 1)'
        ax.text(0, -1.1, formula_text, 
                ha='center', fontsize=11, 
                bbox=dict(facecolor='white', edgecolor='gray', alpha=0.8))
        
        fig.tight_layout()
        return fig
    
    def get_hysteretic_data(self, dlf, num_points=100):
        """
        获取滞后环的数据点，用于导出
        
        参数:
        dlf: 阻尼损耗因子
        num_points: 数据点的数量
        
        返回:
        包含应变和应力值的数据字典
        """
        alpha = math.atan(dlf)
        t = np.linspace(0, 2*np.pi, num_points)
        
        # 应变和应力的参数方程
        epsilon = np.cos(t)
        sigma = np.cos(t + alpha)
        
        # 创建数据字典
        data = {
            "t_values": t,
            "strain_values": epsilon,
            "stress_values": sigma,
            "alpha_degrees": np.degrees(alpha),
            "dlf": dlf
        }
        
        return data
    
    def plot_comparison_chart(self):
        """
        绘制不同橡胶聚合物的DLF比较图
        
        返回:
        matplotlib图形对象
        """
        fig = Figure(figsize=(8, 6))
        ax = fig.add_subplot(111)
        
        # 准备数据 - 不包括"Custom"选项和自定义橡胶
        rubber_names = [name for name in self.calculated_values.keys() 
                       if name != "Custom" and name not in self.custom_rubbers]
        short_names = [name.split(' ')[0] for name in rubber_names]
        calculated_dlfs = [self.calculated_values[name]["calc_dlf"] for name in rubber_names]
        
        # 添加自定义橡胶数据
        for name in self.custom_rubbers:
            rubber_names.append(name)
            short_names.append(name.split(' ')[0] if ' ' in name else name)
            calculated_dlfs.append(self.custom_rubbers[name]["calc_dlf"])
        
        # 实验范围
        exp_mins = []
        exp_maxs = []
        for name in rubber_names:
            if name in self.rubber_data:
                exp_mins.append(self.rubber_data[name]["exp_dlf"][0])
                exp_maxs.append(self.rubber_data[name]["exp_dlf"][1])
            elif name in self.custom_rubbers:
                exp_data = self.custom_rubbers[name].get("exp_dlf", (0, 0))
                exp_mins.append(exp_data[0])
                exp_maxs.append(exp_data[1])
        
        exp_ranges = [(max - min) for min, max in zip(exp_mins, exp_maxs)]
        
        # 绘制条形图
        x = np.arange(len(rubber_names))
        width = 0.35
        
        # 计算值条形图
        bars = ax.bar(x, calculated_dlfs, width, label='Calculated', color='blue', alpha=0.7)
        
        # 实验值范围
        for i, (min_val, range_val) in enumerate(zip(exp_mins, exp_ranges)):
            if min_val > 0 or range_val > 0:  # 只绘制有实验数据的材料
                rect = patches.Rectangle((i-width/2, min_val), width, range_val, 
                                         linewidth=1, edgecolor='r', facecolor='gray', alpha=0.5)
                ax.add_patch(rect)
        
        # 添加文本标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{height:.2f}', ha='center', va='bottom')
        
        # 标签和标题 - 英文
        ax.set_xlabel('Rubber Polymer Type', fontsize=12)
        ax.set_ylabel('Damping Loss Factor (DLF)', fontsize=12)
        ax.set_title('Comparison of DLF for Different Rubber Polymers', fontsize=14)
        ax.set_xticks(x)
        ax.set_xticklabels(short_names, fontsize=10, rotation=45, ha='right')
        
        # 创建图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='blue', alpha=0.7, label='Calculated Value'),
            Patch(facecolor='gray', alpha=0.5, edgecolor='r', label='Experimental Range')
        ]
        ax.legend(handles=legend_elements, loc='upper right', fontsize=10)
        
        # 网格和范围
        ax.grid(True, linestyle='--', axis='y', alpha=0.6)
        ax.set_ylim(0, max(calculated_dlfs) * 1.2)
        
        # 添加说明文本 - 英文
        desc_text = (
            "Note: The graph shows the comparison of DLF for rubber polymers.\n"
            "Blue bars represent calculated DLF values, gray areas show experimental ranges."
        )
        fig.text(0.5, 0.01, desc_text, ha='center', fontsize=9, 
                 bbox=dict(facecolor='white', edgecolor='gray', alpha=0.8, boxstyle='round,pad=0.5'))
        
        fig.tight_layout(rect=[0, 0.05, 1, 0.95])  # 调整布局，为底部说明文本留出空间
        return fig
    
    def get_comparison_data(self):
        """
        获取比较图表的数据，用于导出
        
        返回:
        包含橡胶类型、计算DLF和实验DLF的字典
        """
        # 不包括"Custom"选项，但包括用户添加的自定义橡胶
        rubber_names = [name for name in self.calculated_values.keys() 
                       if name != "Custom"] + list(self.custom_rubbers.keys())
        short_names = [name.split(' ')[0] for name in rubber_names]
        
        calculated_dlfs = []
        exp_dlf_mins = []
        exp_dlf_maxs = []
        density_0_values = []
        K_0_values = []
        density_e_values = []
        K_e_values = []
        
        for name in rubber_names:
            if name in self.calculated_values:
                calculated_dlfs.append(self.calculated_values[name]["calc_dlf"])
                density_0_values.append(self.calculated_values[name]["density_0"])
                K_0_values.append(self.calculated_values[name]["K_0"])
                density_e_values.append(self.calculated_values[name]["density_e"])
                K_e_values.append(self.calculated_values[name]["K_e"])
                
                if name in self.rubber_data:
                    exp_dlf_mins.append(self.rubber_data[name]["exp_dlf"][0])
                    exp_dlf_maxs.append(self.rubber_data[name]["exp_dlf"][1])
                else:
                    exp_dlf_mins.append(0)
                    exp_dlf_maxs.append(0)
            elif name in self.custom_rubbers:
                calculated_dlfs.append(self.custom_rubbers[name]["calc_dlf"])
                density_0_values.append(self.custom_rubbers[name]["density_0"])
                K_0_values.append(self.custom_rubbers[name]["K_0"])
                density_e_values.append(self.custom_rubbers[name]["density_e"])
                K_e_values.append(self.custom_rubbers[name]["K_e"])
                
                exp_data = self.custom_rubbers[name].get("exp_dlf", (0, 0))
                exp_dlf_mins.append(exp_data[0])
                exp_dlf_maxs.append(exp_data[1])
        
        data = {
            "rubber_type": short_names,
            "calculated_dlf": calculated_dlfs,
            "exp_dlf_min": exp_dlf_mins,
            "exp_dlf_max": exp_dlf_maxs,
            "density_0": density_0_values,
            "K_0": K_0_values,
            "density_e": density_e_values,
            "K_e": K_e_values
        }
        
        return data
    
    def add_custom_rubber(self, name, density_0, K_0, density_e, K_e, exp_min=0, exp_max=0):
        """
        添加自定义橡胶聚合物
        
        参数:
        name: 自定义橡胶名称
        density_0, K_0: 点(0,σ₀)处的密度和体积模量
        density_e, K_e: 点(ε₀,0)处的密度和体积模量
        exp_min, exp_max: 实验DLF范围（如有）
        
        返回:
        计算的DLF值
        """
        # 计算DLF值
        dlf, _ = self.calculate_dlf(K_e, K_0)
        
        # 存储自定义橡胶数据
        self.custom_rubbers[name] = {
            "density_0": density_0,
            "K_0": K_0,
            "density_e": density_e,
            "K_e": K_e,
            "calc_dlf": dlf,
            "exp_dlf": (exp_min, exp_max)
        }
        
        return dlf


class DLFCalculatorApp:
    """
    阻尼损耗因子计算器的GUI界面
    """
    
    def __init__(self, root):
        self.root = root
        self.root.title("Rubber Polymer DLF Calculator")
        self.root.geometry("1000x700")  # 增加窗口尺寸确保所有元素可见
        self.root.resizable(True, True)
        
        self.calculator = DampingLossFactorCalculator()
        self.current_custom_name = None  # 跟踪当前选择的自定义橡胶
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建GUI界面元素，优化布局确保所有按钮可见"""
        # 创建框架
        self.main_frame = ttk.Frame(self.root, padding="20")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建标题
        ttk.Label(self.main_frame, 
                  text="Damping Loss Factor Calculator for Rubber Polymers", 
                  font=("Arial", 16, "bold")).grid(row=0, column=0, columnspan=3, pady=10)
        
        # 添加说明文本
        description = (
            "Based on Wang et al.'s research on 'Theoretical Study of Fast Calculation of Damping Loss Factors for "
            "Rubber Polymers', this calculator estimates DLF by calculating bulk moduli at points (e0,0) and (0,s0).\n"
            "Formula: DLF = tan α = √(K(e0,0)/K(0,s0) - 1)"
        )
        desc_label = ttk.Label(self.main_frame, text=description, wraplength=900, justify="left")
        desc_label.grid(row=1, column=0, columnspan=3, pady=10, sticky="w")
        
        # ---------- 左侧区域：参数输入和按钮 ----------
        # 创建左侧总框架，包含输入和按钮区域
        left_frame = ttk.Frame(self.main_frame)
        left_frame.grid(row=2, column=0, rowspan=3, padx=10, pady=10, sticky="nsew")
        
        # 创建输入面板
        input_frame = ttk.LabelFrame(left_frame, text="Parameter Input", padding="10")
        input_frame.pack(fill=tk.BOTH, expand=False, padx=5, pady=5)
        
        # 橡胶类型选择和自定义按钮框架
        rubber_frame = ttk.Frame(input_frame)
        rubber_frame.grid(row=0, column=0, columnspan=2, padx=5, pady=5, sticky="we")
        
        ttk.Label(rubber_frame, text="Rubber Polymer Type:").pack(side=tk.LEFT, padx=5)
        
        self.rubber_type = ttk.Combobox(rubber_frame, width=20, state="readonly")
        self.update_rubber_combobox()  # 初始化下拉菜单
        self.rubber_type.pack(side=tk.LEFT, padx=5)
        self.rubber_type.bind("<<ComboboxSelected>>", self.update_from_selection)
        
        # 添加按钮
        add_button = ttk.Button(rubber_frame, text="Add Custom", command=self.add_custom_polymer)
        add_button.pack(side=tk.RIGHT, padx=5)
        
        # 新增编辑和删除按钮
        edit_button = ttk.Button(rubber_frame, text="Edit Custom", command=self.edit_custom_polymer)
        edit_button.pack(side=tk.RIGHT, padx=5)
        
        delete_button = ttk.Button(rubber_frame, text="Delete Custom", command=self.delete_custom_polymer)
        delete_button.pack(side=tk.RIGHT, padx=5)
        
        # K(0,σ₀)输入
        ttk.Label(input_frame, text="K(0,s0) [GPa]:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.K_0_var = tk.StringVar(value="3.20")
        K_0_entry = ttk.Entry(input_frame, textvariable=self.K_0_var, width=10)
        K_0_entry.grid(row=1, column=1, padx=5, pady=5, sticky="w")
        
        # 密度(0,σ₀)输入
        ttk.Label(input_frame, text="Density(0,s0) [g/cm³]:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.density_0_var = tk.StringVar(value="0.92")
        density_0_entry = ttk.Entry(input_frame, textvariable=self.density_0_var, width=10)
        density_0_entry.grid(row=2, column=1, padx=5, pady=5, sticky="w")
        
        # K(ε₀,0)输入
        ttk.Label(input_frame, text="K(e0,0) [GPa]:").grid(row=3, column=0, padx=5, pady=5, sticky="w")
        self.K_e_var = tk.StringVar(value="38.09")
        K_e_entry = ttk.Entry(input_frame, textvariable=self.K_e_var, width=10)
        K_e_entry.grid(row=3, column=1, padx=5, pady=5, sticky="w")
        
        # 密度(ε₀,0)输入
        ttk.Label(input_frame, text="Density(e0,0) [g/cm³]:").grid(row=4, column=0, padx=5, pady=5, sticky="w")
        self.density_e_var = tk.StringVar(value="0.87")
        density_e_entry = ttk.Entry(input_frame, textvariable=self.density_e_var, width=10)
        density_e_entry.grid(row=4, column=1, padx=5, pady=5, sticky="w")
        
        # 添加实验DLF范围输入区域
        exp_frame = ttk.Frame(input_frame)
        exp_frame.grid(row=5, column=0, columnspan=2, padx=5, pady=5, sticky="w")
        
        ttk.Label(exp_frame, text="Experimental DLF Range:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        
        ttk.Label(exp_frame, text="Min:").grid(row=0, column=1, sticky="w")
        self.exp_min_var = tk.StringVar(value="0.0")
        exp_min_entry = ttk.Entry(exp_frame, textvariable=self.exp_min_var, width=5)
        exp_min_entry.grid(row=0, column=2, padx=2, pady=5, sticky="w")
        
        ttk.Label(exp_frame, text="Max:").grid(row=0, column=3, padx=5, sticky="w")
        self.exp_max_var = tk.StringVar(value="0.0")
        exp_max_entry = ttk.Entry(exp_frame, textvariable=self.exp_max_var, width=5)
        exp_max_entry.grid(row=0, column=4, padx=2, pady=5, sticky="w")
        
        # ---------- 创建按钮区域 ----------
        # 新增一个专门的按钮框架，确保按钮可见
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        # 计算按钮
        calculate_btn = ttk.Button(button_frame, text="Calculate DLF", command=self.calculate)
        calculate_btn.pack(fill=tk.X, padx=5, pady=5)
        
        # 保存自定义橡胶按钮
        save_custom_btn = ttk.Button(button_frame, text="Save Current as Custom", command=self.save_current_as_custom)
        save_custom_btn.pack(fill=tk.X, padx=5, pady=5)
        
        # 重置按钮
        reset_btn = ttk.Button(button_frame, text="Reset Inputs", command=self.reset_inputs)
        reset_btn.pack(fill=tk.X, padx=5, pady=5)
        
        # 比较图表按钮 - 确保在可见区域
        compare_btn = ttk.Button(button_frame, text="Show Comparison Chart", command=self.show_comparison)
        compare_btn.pack(fill=tk.X, padx=5, pady=5)
        
        # 导出数据按钮 - 确保在可见区域
        export_btn = ttk.Button(button_frame, text="Export Data to CSV", command=self.export_data)
        export_btn.pack(fill=tk.X, padx=5, pady=5)
        
        # 结果显示
        result_frame = ttk.LabelFrame(left_frame, text="Calculation Results", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=False, padx=5, pady=5)
        
        ttk.Label(result_frame, text="Calculated DLF:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.result_var = tk.StringVar(value="--")
        ttk.Label(result_frame, textvariable=self.result_var, font=("Arial", 12, "bold")).grid(row=0, column=1, padx=5, pady=5, sticky="w")
        
        ttk.Label(result_frame, text="Experimental DLF Range:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.exp_range_var = tk.StringVar(value="2.0 - 2.6")
        ttk.Label(result_frame, textvariable=self.exp_range_var).grid(row=1, column=1, padx=5, pady=5, sticky="w")
        
        # 添加计算过程显示区域
        calculation_frame = ttk.LabelFrame(left_frame, text="Calculation Process", padding="10")
        calculation_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.calc_process = scrolledtext.ScrolledText(calculation_frame, wrap=tk.WORD, width=30, height=15)
        self.calc_process.pack(fill=tk.BOTH, expand=True)
        self.calc_process.insert(tk.END, "Click \"Calculate DLF\" button to see the detailed calculation process...")
        self.calc_process.config(state=tk.DISABLED)
        
        # ---------- 右侧区域：图形显示 ----------
        # 创建图形显示区域
        graph_frame = ttk.Frame(self.main_frame)
        graph_frame.grid(row=2, column=1, rowspan=2, columnspan=2, padx=10, pady=10, sticky="nsew")
        
        # 初始化图像
        self.dlf_fig = self.calculator.plot_hysteretic_loop(3.30)
        self.dlf_canvas = FigureCanvasTkAgg(self.dlf_fig, master=graph_frame)
        self.dlf_canvas_widget = self.dlf_canvas.get_tk_widget()
        self.dlf_canvas_widget.pack(fill=tk.BOTH, expand=True)
        
        # ---------- 底部区域：理论背景 ----------
        # 添加理论背景说明
        theory_frame = ttk.LabelFrame(self.main_frame, text="Theoretical Background", padding="10")
        theory_frame.grid(row=4, column=1, columnspan=2, padx=10, pady=10, sticky="nsew")
        
        theory_text = scrolledtext.ScrolledText(theory_frame, wrap=tk.WORD, height=6)
        theory_text.pack(fill=tk.BOTH, expand=True)
        theory_text.insert(tk.END, """Theoretical Background:
According to Wang et al.'s research, the damping properties of rubber polymers can be rapidly calculated using the bulk moduli at two specific points (e0,0) and (0,s0) on the hysteretic loop. This method is based on the stress-strain hysteresis relationship of viscoelastic materials, avoiding complex molecular dynamics simulations.

When a force is applied to a material, the internal polymer chains stretch or retract in response to external excitation, leading to changes in the mechanical properties of the material. By measuring the bulk moduli at the zero-strain and zero-stress points, the lag phase angle α can be calculated, and thereby the damping loss factor (DLF).

High damping properties require high bulk modulus at the zero-stress point and low bulk modulus at the zero-strain point, meaning that deformation energy is dissipated by increasing the bulk moduli of the backbones in rubber polymers. Research shows that carbon-carbon skeletal chains with lateral methyl groups produce high damping properties.""")
        theory_text.config(state=tk.DISABLED)
        
        # 配置网格权重使得UI可以调整大小
        self.main_frame.columnconfigure(1, weight=3)  # 让右侧图形区域占更多空间
        self.main_frame.columnconfigure(0, weight=1)  # 左侧区域权重
        self.main_frame.rowconfigure(2, weight=3)     # 图形和输入区域占主要空间
        self.main_frame.rowconfigure(4, weight=1)     # 理论背景区域
    
    def update_rubber_combobox(self):
        """更新橡胶类型下拉菜单，包括自定义类型"""
        # 获取标准橡胶类型
        rubber_types = [name for name in self.calculator.rubber_data.keys() 
                        if name != "Custom"]
        
        # 添加自定义橡胶类型
        custom_types = list(self.calculator.custom_rubbers.keys())
        if custom_types:
            rubber_types.append("-------------")  # 分隔线
            rubber_types.extend(custom_types)
        
        # 添加"Add Custom..."选项
        rubber_types.append("Add Custom...")
        
        # 更新下拉菜单
        self.rubber_type['values'] = rubber_types
        
        # 如果当前没有选择，设置为默认值
        if not self.rubber_type.get():
            self.rubber_type.current(0)
    
    def update_from_selection(self, event=None):
        """根据选择的橡胶类型更新输入值"""
        selected = self.rubber_type.get()
        
        if selected == "Add Custom...":
            # 重置当前选择，然后显示添加对话框
            self.rubber_type.set("")
            self.add_custom_polymer()
            return
        
        if selected == "-------------":
            # 这是分隔线，重新选择第一项
            self.rubber_type.current(0)
            selected = self.rubber_type.get()
        
        # 更新当前选择的自定义橡胶名称
        self.current_custom_name = selected if selected in self.calculator.custom_rubbers else None
        
        # 获取数据
        if selected in self.calculator.calculated_values:
            data = self.calculator.calculated_values[selected]
            
            self.K_0_var.set(f"{data['K_0']:.2f}")
            self.density_0_var.set(f"{data['density_0']:.2f}")
            self.K_e_var.set(f"{data['K_e']:.2f}")
            self.density_e_var.set(f"{data['density_e']:.2f}")
            
            # 更新实验范围
            if selected in self.calculator.rubber_data:
                exp_range = self.calculator.rubber_data[selected]["exp_dlf"]
                self.exp_range_var.set(f"{exp_range[0]:.1f} - {exp_range[1]:.1f}")
                self.exp_min_var.set(f"{exp_range[0]:.1f}")
                self.exp_max_var.set(f"{exp_range[1]:.1f}")
            
            # 更新DLF结果
            dlf = data['calc_dlf']
            self.result_var.set(f"{dlf:.2f}")
            
        elif selected in self.calculator.custom_rubbers:
            # 载入自定义橡胶数据
            data = self.calculator.custom_rubbers[selected]
            
            self.K_0_var.set(f"{data['K_0']:.2f}")
            self.density_0_var.set(f"{data['density_0']:.2f}")
            self.K_e_var.set(f"{data['K_e']:.2f}")
            self.density_e_var.set(f"{data['density_e']:.2f}")
            
            # 更新实验范围
            exp_range = data.get("exp_dlf", (0, 0))
            self.exp_range_var.set(f"{exp_range[0]:.1f} - {exp_range[1]:.1f}")
            self.exp_min_var.set(f"{exp_range[0]:.1f}")
            self.exp_max_var.set(f"{exp_range[1]:.1f}")
            
            # 更新DLF结果
            dlf = data['calc_dlf']
            self.result_var.set(f"{dlf:.2f}")
        
        # 使用选择的值计算并更新计算过程
        try:
            K_0 = float(self.K_0_var.get())
            K_e = float(self.K_e_var.get())
            
            _, calc_steps = self.calculator.calculate_dlf(K_e, K_0)
            
            self.calc_process.config(state=tk.NORMAL)
            self.calc_process.delete(1.0, tk.END)
            self.calc_process.insert(tk.END, calc_steps)
            self.calc_process.config(state=tk.DISABLED)
            
            # 更新图形
            dlf = float(self.result_var.get())
            self.update_plot(dlf)
        except:
            pass
    
    def add_custom_polymer(self):
        """添加自定义橡胶聚合物"""
        # 使用对话框获取名称
        name = simpledialog.askstring("Add Custom Polymer", 
                                     "Enter name for the custom rubber polymer:",
                                     parent=self.root)
        
        if not name or name.strip() == "":
            return
        
        # 检查名称是否已存在
        if name in self.calculator.rubber_data or name in self.calculator.custom_rubbers:
            messagebox.showerror("Error", f"A polymer with name '{name}' already exists.")
            return
        
        # 添加新的自定义橡胶，使用默认值
        default_density_0 = 1.0
        default_K_0 = 2.0
        default_density_e = 1.0
        default_K_e = 6.0
        
        # 计算DLF
        dlf = self.calculator.add_custom_rubber(
            name, default_density_0, default_K_0, default_density_e, default_K_e
        )
        
        # 更新下拉菜单
        self.update_rubber_combobox()
        
        # 选择新添加的橡胶
        self.rubber_type.set(name)
        self.update_from_selection()
        
        # 提示用户
        messagebox.showinfo("Success", 
                          f"Custom polymer '{name}' has been added with default values.\n"
                          f"Calculated DLF: {dlf:.2f}\n\n"
                          "You can now edit the parameters as needed.")
    
    def edit_custom_polymer(self):
        """编辑选定的自定义橡胶聚合物"""
        selected = self.rubber_type.get()
        
        # 检查是否选择了自定义橡胶
        if selected not in self.calculator.custom_rubbers:
            messagebox.showinfo("Information", 
                              "Please select a custom rubber polymer to edit.\n"
                              "Standard polymers cannot be edited.")
            return
        
        # 使用对话框获取新名称
        new_name = simpledialog.askstring("Edit Custom Polymer", 
                                        f"Enter new name for '{selected}':",
                                        initialvalue=selected,
                                        parent=self.root)
        
        if not new_name or new_name.strip() == "":
            return
        
        # 检查新名称是否已存在且不是当前名称
        if new_name != selected and (new_name in self.calculator.rubber_data or 
                                   new_name in self.calculator.custom_rubbers):
            messagebox.showerror("Error", f"A polymer with name '{new_name}' already exists.")
            return
        
        # 更新自定义橡胶
        data = self.calculator.custom_rubbers[selected].copy()
        del self.calculator.custom_rubbers[selected]
        self.calculator.custom_rubbers[new_name] = data
        
        # 更新下拉菜单
        self.update_rubber_combobox()
        
        # 选择重命名的橡胶
        self.rubber_type.set(new_name)
        self.current_custom_name = new_name
        
        messagebox.showinfo("Success", f"Custom polymer renamed to '{new_name}'.")
    
    def delete_custom_polymer(self):
        """删除选定的自定义橡胶聚合物"""
        selected = self.rubber_type.get()
        
        # 检查是否选择了自定义橡胶
        if selected not in self.calculator.custom_rubbers:
            messagebox.showinfo("Information", 
                              "Please select a custom rubber polymer to delete.\n"
                              "Standard polymers cannot be deleted.")
            return
        
        # 确认删除
        if not messagebox.askyesno("Confirm Deletion", 
                                 f"Are you sure you want to delete '{selected}'?"):
            return
        
        # 删除自定义橡胶
        del self.calculator.custom_rubbers[selected]
        
        # 更新下拉菜单
        self.update_rubber_combobox()
        
        # 选择第一项
        self.rubber_type.current(0)
        self.update_from_selection()
        
        messagebox.showinfo("Success", f"Custom polymer '{selected}' has been deleted.")
    
    def save_current_as_custom(self):
        """将当前参数保存为新的自定义橡胶"""
        # 使用对话框获取名称
        name = simpledialog.askstring("Save as Custom Polymer", 
                                     "Enter name for the custom rubber polymer:",
                                     parent=self.root)
        
        if not name or name.strip() == "":
            return
        
        # 检查名称是否已存在
        if name in self.calculator.rubber_data or name in self.calculator.custom_rubbers:
            # 如果是更新当前选择的自定义橡胶，则允许
            if name == self.current_custom_name:
                pass
            else:
                messagebox.showerror("Error", f"A polymer with name '{name}' already exists.")
                return
        
        try:
            # 获取当前参数
            density_0 = float(self.density_0_var.get())
            K_0 = float(self.K_0_var.get())
            density_e = float(self.density_e_var.get())
            K_e = float(self.K_e_var.get())
            exp_min = float(self.exp_min_var.get())
            exp_max = float(self.exp_max_var.get())
            
            # 添加或更新自定义橡胶
            dlf = self.calculator.add_custom_rubber(
                name, density_0, K_0, density_e, K_e, exp_min, exp_max
            )
            
            # 更新下拉菜单
            self.update_rubber_combobox()
            
            # 选择新添加/更新的橡胶
            self.rubber_type.set(name)
            self.current_custom_name = name
            
            # 提示用户
            messagebox.showinfo("Success", 
                              f"Custom polymer '{name}' has been saved.\n"
                              f"Calculated DLF: {dlf:.2f}")
                              
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid input: {str(e)}")
    
    def calculate(self):
        """计算DLF并更新显示"""
        try:
            K_0 = float(self.K_0_var.get())
            K_e = float(self.K_e_var.get())
            
            dlf, calc_steps = self.calculator.calculate_dlf(K_e, K_0)
            self.result_var.set(f"{dlf:.2f}")
            
            # 更新计算过程显示
            self.calc_process.config(state=tk.NORMAL)
            self.calc_process.delete(1.0, tk.END)
            self.calc_process.insert(tk.END, calc_steps)
            self.calc_process.config(state=tk.DISABLED)
            
            # 更新图形
            self.update_plot(dlf)
            
            # 如果是自定义橡胶，更新其存储的值
            selected = self.rubber_type.get()
            if selected in self.calculator.custom_rubbers:
                # 获取当前参数
                density_0 = float(self.density_0_var.get())
                density_e = float(self.density_e_var.get())
                exp_min = float(self.exp_min_var.get())
                exp_max = float(self.exp_max_var.get())
                
                # 更新自定义橡胶数据
                self.calculator.custom_rubbers[selected].update({
                    "density_0": density_0,
                    "K_0": K_0,
                    "density_e": density_e,
                    "K_e": K_e,
                    "calc_dlf": dlf,
                    "exp_dlf": (exp_min, exp_max)
                })
                
                # 更新实验范围显示
                self.exp_range_var.set(f"{exp_min:.1f} - {exp_max:.1f}")
            
        except ValueError as e:
            messagebox.showerror("Input Error", str(e))
    
    def update_plot(self, dlf):
        """更新滞后环图形"""
        # 清除旧图形
        for widget in self.dlf_canvas_widget.master.winfo_children():
            widget.destroy()
        
        # 创建新图形
        self.dlf_fig = self.calculator.plot_hysteretic_loop(dlf)
        self.dlf_canvas = FigureCanvasTkAgg(self.dlf_fig, master=self.dlf_canvas_widget.master)
        self.dlf_canvas_widget = self.dlf_canvas.get_tk_widget()
        self.dlf_canvas_widget.pack(fill=tk.BOTH, expand=True)
    
    def reset_inputs(self):
        """重置输入字段为默认值"""
        self.rubber_type.current(0)
        self.update_from_selection()
    
    def show_comparison(self):
        """显示不同橡胶聚合物的DLF比较图"""
        # 创建新窗口
        compare_window = tk.Toplevel(self.root)
        compare_window.title("Rubber Polymer DLF Comparison")
        compare_window.geometry("800x600")
        
        # 创建图形
        compare_fig = self.calculator.plot_comparison_chart()
        compare_canvas = FigureCanvasTkAgg(compare_fig, master=compare_window)
        compare_canvas_widget = compare_canvas.get_tk_widget()
        compare_canvas_widget.pack(fill=tk.BOTH, expand=True)
        
        # 添加导出按钮和关闭按钮的框架
        btn_frame = ttk.Frame(compare_window, padding="10")
        btn_frame.pack(fill=tk.X)
        
        # 添加导出按钮
        export_btn = ttk.Button(btn_frame, text="Export Comparison Data", 
                              command=lambda: self.export_comparison_data())
        export_btn.pack(side=tk.LEFT, padx=5)
        
        # 添加关闭按钮
        close_btn = ttk.Button(btn_frame, text="Close", command=compare_window.destroy)
        close_btn.pack(side=tk.RIGHT, padx=5)
    
    def export_data(self):
        """导出当前滞后环数据到CSV文件"""
        try:
            # 获取当前DLF
            dlf = float(self.result_var.get())
            
            # 获取滞后环数据
            data = self.calculator.get_hysteretic_data(dlf)
            
            # 使用文件对话框获取保存路径
            from tkinter import filedialog
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Save Hysteretic Loop Data"
            )
            
            if not file_path:  # 用户取消了保存
                return
            
            # 导出数据到CSV
            import csv
            with open(file_path, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入标题行
                writer.writerow(["Parameter", "Value"])
                writer.writerow(["DLF", data["dlf"]])
                writer.writerow(["Phase Angle (degrees)", data["alpha_degrees"]])
                writer.writerow([])
                
                # 写入当前橡胶聚合物信息
                selected = self.rubber_type.get()
                writer.writerow(["Rubber Polymer", selected])
                writer.writerow(["K(0,s0) [GPa]", self.K_0_var.get()])
                writer.writerow(["Density(0,s0) [g/cm³]", self.density_0_var.get()])
                writer.writerow(["K(e0,0) [GPa]", self.K_e_var.get()])
                writer.writerow(["Density(e0,0) [g/cm³]", self.density_e_var.get()])
                writer.writerow([])
                
                # 写入滞后环数据点
                writer.writerow(["t", "Strain", "Stress"])
                for i in range(len(data["t_values"])):
                    writer.writerow([
                        f"{data['t_values'][i]:.4f}",
                        f"{data['strain_values'][i]:.4f}",
                        f"{data['stress_values'][i]:.4f}"
                    ])
            
            messagebox.showinfo("Export Successful", f"Data has been exported to:\n{file_path}")
            
        except Exception as e:
            messagebox.showerror("Export Error", f"Error exporting data: {str(e)}")
    
    def export_comparison_data(self):
        """导出比较数据到CSV文件"""
        try:
            # 获取比较数据
            data = self.calculator.get_comparison_data()
            
            # 使用文件对话框获取保存路径
            from tkinter import filedialog
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Save Comparison Data"
            )
            
            if not file_path:  # 用户取消了保存
                return
            
            # 导出数据到CSV
            import csv
            with open(file_path, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入标题行
                writer.writerow([
                    "Rubber Type", "Calculated DLF", "Exp. Min DLF", "Exp. Max DLF",
                    "Density(0,s0) [g/cm³]", "K(0,s0) [GPa]",
                    "Density(e0,0) [g/cm³]", "K(e0,0) [GPa]"
                ])
                
                # 写入数据行
                for i in range(len(data["rubber_type"])):
                    writer.writerow([
                        data["rubber_type"][i],
                        f"{data['calculated_dlf'][i]:.4f}",
                        f"{data['exp_dlf_min'][i]:.4f}",
                        f"{data['exp_dlf_max'][i]:.4f}",
                        f"{data['density_0'][i]:.4f}",
                        f"{data['K_0'][i]:.4f}",
                        f"{data['density_e'][i]:.4f}",
                        f"{data['K_e'][i]:.4f}"
                    ])
            
            messagebox.showinfo("Export Successful", f"Comparison data has been exported to:\n{file_path}")
            
        except Exception as e:
            messagebox.showerror("Export Error", f"Error exporting data: {str(e)}")


if __name__ == "__main__":
    root = tk.Tk()
    app = DLFCalculatorApp(root)
    root.mainloop()