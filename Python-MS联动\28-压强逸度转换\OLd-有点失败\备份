#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
逸度与压强转换工具
作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)
功能: 基于PR状态方程进行流体的逸度与压强之间的转换，并提供可视化界面

概念说明:
- 逸度(Fugacity): 用于描述非理想气体的热力学性质，与压力具有相同单位(Pa)
- 逸度系数(Fugacity Coefficient): 逸度与压力之比，无量纲
- PR状态方程: Peng-Robinson状态方程，一种用于计算实际气体性质的状态方程

使用方法:
1. 如果有图形界面库(tkinter和matplotlib)，将自动启动图形界面
2. 如果缺少相关库，将使用命令行界面
3. 输入物质的临界参数(临界温度、临界压力、偏心因子)和计算条件(温度、压力)
4. 计算结果包括逸度、逸度系数和压缩因子

注意:
- 所有输入参数必须使用SI单位(温度K，压力Pa)
- 零基础用户可以直接使用默认值进行计算
- 计算结果的显示精度已优化为10位小数
"""

import math
from math import log, exp, sqrt
import sys
import os
import csv
import numpy as np
from datetime import datetime
from tkinter import filedialog

# 尝试导入可视化相关的库
MATPLOTLIB_AVAILABLE = False
TKINTER_AVAILABLE = False

try:
    import tkinter as tk
    from tkinter import ttk, messagebox
    TKINTER_AVAILABLE = True
    
    try:
        import matplotlib
        matplotlib.use('TkAgg')
        import matplotlib.pyplot as plt
        from matplotlib.figure import Figure
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用黑体显示中文
        plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号
        MATPLOTLIB_AVAILABLE = True
    except ImportError:
        print("警告: 未找到matplotlib库，将使用命令行界面。")
        print("如果需要可视化功能，请安装matplotlib: pip install matplotlib")
except ImportError:
    print("警告: 未找到tkinter库，将使用命令行界面。")
    print("如果需要图形界面，请确保Python安装时包含了tkinter。")

print(sys.executable)  # 查看当前使用的Python解释器路径

# 用户需要输入的变量
# 示例：氮气 (N2) 物性参数 - 以下默认值来自于热力学数据库
DEFAULT_SUBSTANCE = "氮气 (N2)"
DEFAULT_Tc = 126.2  # 临界温度 (K)
DEFAULT_Pc = 3.4e6  # 临界压力 (Pa)
DEFAULT_OMEGA = 0.0372  # 偏心因子

# 默认计算条件 - 常温常压下
DEFAULT_T = 300  # 温度 (K) 约等于室温
DEFAULT_P = 1e6  # 压力 (Pa) = 1 MPa (约10个大气压)

# 精度控制参数 - 确保计算结果精确
CONVERGENCE_TOL = 1e-12  # 收敛容差，提高精度
MAX_ITERATIONS = 500     # 最大迭代次数，避免无限循环
ROOT_SELECTION_TOL = 1e-15  # 用于判断虚根的容差

# 可视化参数 - 控制图表范围
DEFAULT_P_MIN = 0.1e6  # 压力最小值 (Pa) = 0.1 MPa
DEFAULT_P_MAX = 10e6   # 压力最大值 (Pa) = 10 MPa
DEFAULT_POINTS = 200   # 曲线上的点数，增加点数提高曲线精度

# 添加常见物质的物性数据库
SUBSTANCE_DATABASE = {
    "氮气 (N2)": {"Tc": 126.2, "Pc": 3.4e6, "omega": 0.0372},
    "氧气 (O2)": {"Tc": 154.6, "Pc": 5.04e6, "omega": 0.0222},
    "二氧化碳 (CO2)": {"Tc": 304.1, "Pc": 7.38e6, "omega": 0.2236},
    "甲烷 (CH4)": {"Tc": 190.6, "Pc": 4.60e6, "omega": 0.0115},
    "乙烷 (C2H6)": {"Tc": 305.4, "Pc": 4.88e6, "omega": 0.0995},
    "乙烯 (C2H4)": {"Tc": 282.3, "Pc": 5.04e6, "omega": 0.0866},
    "丙烷 (C3H8)": {"Tc": 370.0, "Pc": 4.25e6, "omega": 0.1523},
    "氨 (NH3)": {"Tc": 405.6, "Pc": 11.3e6, "omega": 0.2526},
    "水 (H2O)": {"Tc": 647.1, "Pc": 22.06e6, "omega": 0.3443},
    "氦气 (He)": {"Tc": 5.2, "Pc": 0.23e6, "omega": -0.3900},
    "氩气 (Ar)": {"Tc": 150.9, "Pc": 4.90e6, "omega": 0.0000},
    "氢气 (H2)": {"Tc": 33.2, "Pc": 1.30e6, "omega": -0.2160},
}

# 单位转换常数
ATM_TO_PA = 101325.0  # 1 atm = 101325 Pa
BAR_TO_PA = 1e5       # 1 bar = 100000 Pa
MPA_TO_PA = 1e6       # 1 MPa = 1000000 Pa
PSI_TO_PA = 6894.76   # 1 psi = 6894.76 Pa
MMHG_TO_PA = 133.322  # 1 mmHg = 133.322 Pa

def solve_cubic_equation(a, b, c, d):
    """
    纯Python实现求解三次方程: ax^3 + bx^2 + cx + d = 0
    采用改进的Cardano方法，增强数值稳定性
    
    参数:
    a, b, c, d: 三次方程系数
    
    返回:
    list: 三个根的列表(可能包含复根)
    """
    # 处理特殊情况：系数为零
    if abs(a) < 1e-15:  # 如果a接近于0，退化为二次方程
        return solve_quadratic_equation(b, c, d)
    
    # 标准化为 x^3 + px^2 + qx + r = 0
    p = b / a
    q = c / a
    r = d / a
    
    # 消去二次项，代换 x = y - p/3
    p_over_3 = p / 3.0
    p_over_3_squared = p_over_3 * p_over_3
    
    # 计算新的系数 y^3 + ay + b = 0 的a和b
    a_new = q - 3.0 * p_over_3_squared
    b_new = r + 2.0 * p_over_3 * p_over_3_squared - p_over_3 * q
    
    # 计算判别式 delta = (b/2)^2 + (a/3)^3
    a_new_over_3 = a_new / 3.0
    b_new_over_2 = b_new / 2.0
    
    delta = b_new_over_2 * b_new_over_2 + a_new_over_3 * a_new_over_3 * a_new_over_3
    
    # 根据判别式的符号确定根的性质
    roots = []
    
    if abs(delta) < 1e-15:  # delta ≈ 0，有三重根或一个单根和一个二重根
        if abs(a_new) < 1e-15:  # a ≈ 0，有三重根
            y = 0.0 if abs(b_new) < 1e-15 else -b_new**(1/3.0)
            roots = [complex(y - p_over_3, 0)] * 3
        else:  # 一个单根和一个二重根
            y1 = 3.0 * b_new / a_new
            y2 = -y1 / 2.0
            roots = [complex(y1 - p_over_3, 0), complex(y2 - p_over_3, 0), complex(y2 - p_over_3, 0)]
    elif delta > 0:  # delta > 0，有一个实根和一对共轭复根
        # 计算辅助变量 u 和 v
        sqrt_delta = math.sqrt(delta)
        u = (-b_new_over_2 + sqrt_delta)**(1/3.0)
        v = (-b_new_over_2 - sqrt_delta)**(1/3.0) if b_new_over_2 + sqrt_delta != 0 else 0
        
        # 确保u和v的符号正确
        if u * v + a_new_over_3 < 0:
            if u > 0:
                v = -abs(v)
            else:
                u = -abs(u)
        
        # 实根
        y1 = u + v
        
        # 复根
        real_part = -(u + v) / 2.0
        imag_part = sqrt(3.0) * (u - v) / 2.0
        
        roots = [
            complex(y1 - p_over_3, 0),
            complex(real_part - p_over_3, imag_part),
            complex(real_part - p_over_3, -imag_part)
        ]
    else:  # delta < 0，有三个不相等的实根
        # 使用三角函数法
        a_new_abs = abs(a_new)
        sqrt_a_new_abs = math.sqrt(a_new_abs)
        theta = math.acos(-b_new / (2.0 * sqrt_a_new_abs * a_new_abs / sqrt_a_new_abs))
        
        # 计算三个实根
        y1 = 2.0 * sqrt_a_new_abs / sqrt_a_new_abs * math.cos(theta / 3.0)
        y2 = 2.0 * sqrt_a_new_abs / sqrt_a_new_abs * math.cos((theta + 2.0 * math.pi) / 3.0)
        y3 = 2.0 * sqrt_a_new_abs / sqrt_a_new_abs * math.cos((theta + 4.0 * math.pi) / 3.0)
        
        if a_new < 0:  # 如果a < 0，需要添加负号
            y1, y2, y3 = -y1, -y2, -y3
            
        roots = [
            complex(y1 - p_over_3, 0),
            complex(y2 - p_over_3, 0),
            complex(y3 - p_over_3, 0)
        ]
    
    # 精确到足够小的虚部设为0
    for i in range(len(roots)):
        if abs(roots[i].imag) < 1e-15:
            roots[i] = complex(roots[i].real, 0)
    
    return roots


def solve_quadratic_equation(a, b, c):
    """
    纯Python实现求解二次方程: ax^2 + bx + c = 0
    使用改进的公式，增强数值稳定性
    
    参数:
    a, b, c: 二次方程系数
    
    返回:
    list: 两个根的列表和一个0(作为三次方程的第三个根)
    """
    if abs(a) < 1e-15:  # 如果a接近于0，退化为一次方程
        if abs(b) < 1e-15:  # 如果b也接近于0，那么方程没有变量，要么无解要么无穷解
            return [complex(0, 0), complex(0, 0), complex(0, 0)]
        else:
            # bx + c = 0 => x = -c/b
            return [complex(-c / b, 0), complex(0, 0), complex(0, 0)]
    
    # 计算判别式
    delta = b * b - 4 * a * c
    
    if abs(delta) < 1e-15:  # 判别式接近0，有两个相等的实根
        root = -b / (2 * a)
        return [complex(root, 0), complex(root, 0), complex(0, 0)]
    elif delta > 0:  # 有两个不同的实根
        # 使用更稳定的公式，避免精度损失
        if b >= 0:
            q = -0.5 * (b + math.sqrt(delta))
        else:
            q = -0.5 * (b - math.sqrt(delta))
        
        root1 = q / a
        root2 = c / q
        return [complex(root1, 0), complex(root2, 0), complex(0, 0)]
    else:  # 有两个共轭复根
        real_part = -b / (2 * a)
        imag_part = math.sqrt(-delta) / (2 * a)
        return [complex(real_part, imag_part), complex(real_part, -imag_part), complex(0, 0)]


class PRStateEquation:
    """
    Peng-Robinson状态方程的实现
    用于计算逸度系数和压强等热力学性质
    
    改进点：
    1. 使用更精确的参数常数
    2. 增强数值稳定性
    3. 优化计算逻辑
    """
    
    # 气体常数 (J/(mol·K))
    R = 8.3144598  # 更精确的气体常数值
    
    # PR状态方程参数常数
    OMEGA_A = 0.45723553
    OMEGA_B = 0.07779607
    
    def __init__(self):
        """初始化PR状态方程类"""
        pass
    
    def calculate_a_b(self, Tc, Pc, omega):
        """
        计算PR方程的参数a和b
        
        参数:
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        
        返回:
        tuple: (a, b) PR方程的参数
        """
        # 计算b参数 - 使用精确常数
        b = self.OMEGA_B * self.R * Tc / Pc
        
        # 计算a参数
        a = self.OMEGA_A * ((self.R * Tc)**2) / Pc
        
        return a, b
    
    def calculate_kappa(self, omega):
        """
        计算kappa参数，用于计算alpha
        
        参数:
        omega (float): 偏心因子
        
        返回:
        float: kappa值
        """
        # 使用改进的kappa计算公式
        if omega <= 0.491:
            return 0.37464 + 1.54226 * omega - 0.26992 * omega**2
        else:
            # 对于高偏心因子的物质使用另一个公式
            return 0.379642 + 1.48503 * omega - 0.164423 * omega**2 + 0.016666 * omega**3
    
    def calculate_alpha(self, T, Tc, omega):
        """
        计算温度相关的alpha参数
        
        参数:
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        omega (float): 偏心因子
        
        返回:
        float: alpha值
        """
        Tr = T / Tc  # 约化温度
        kappa = self.calculate_kappa(omega)
        
        # 确保数值稳定性
        sqrt_Tr = math.sqrt(max(Tr, 1e-10))
        term = 1.0 + kappa * (1.0 - sqrt_Tr)
        return term * term
    
    def calculate_a_T(self, T, Tc, Pc, omega):
        """
        计算给定温度下的a(T)参数
        
        参数:
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        
        返回:
        float: a(T)参数值
        """
        a, _ = self.calculate_a_b(Tc, Pc, omega)
        alpha = self.calculate_alpha(T, Tc, omega)
        return a * alpha
    
    def select_proper_root(self, roots, P, T, Tc, Pc, B):
        """
        从三次方程的根中选择热力学上最合理的根
        
        参数:
        roots (list): 三次方程的根
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        B (float): PR方程中的B参数
        
        返回:
        float: 选择的根
        """
        # 筛选实根 (使用更严格的判断标准)
        real_roots = [root.real for root in roots if abs(root.imag) < ROOT_SELECTION_TOL]
        
        if not real_roots:
            raise ValueError(f"没有找到实根，无法计算压缩因子Z。压力={P} Pa，温度={T} K")
        
        # 如果只有一个实根，直接返回
        if len(real_roots) == 1:
            return real_roots[0]
        
        # 检查温度与临界温度的关系，判断是否为超临界状态
        Tr = T / Tc
        Pr = P / Pc
        
        # 接近临界点时的特殊处理
        if abs(Tr - 1) < 0.05 and abs(Pr - 1) < 0.05:
            # 接近临界点时，直接返回中间值的根
            real_roots.sort()
            return real_roots[len(real_roots) // 2]
        
        # 计算吉布斯自由能来选择最稳定的相
        min_G = float('inf')
        best_root = None
        
        for Z in real_roots:
            if Z < B:  # 排除物理不合理的根
                continue
                
            # 近似计算吉布斯自由能
            # 对于纯物质，我们可以使用Z值偏离理想气体的程度来近似比较
            G_deviation = abs(Z - 1.0) + abs(1.0 - B/Z)
            
            if G_deviation < min_G:
                min_G = G_deviation
                best_root = Z
        
        # 如果没有找到有效根，则使用备选策略
        if best_root is None:
            if T > Tc:  # 超临界温度，通常选择最大的实根
                return max(real_roots)
            else:  # 亚临界温度
                # 亚临界温度时，根据压力选择不同策略
                real_roots.sort()
                if P > Pc:  # 高压，倾向选择最小的根（液相）
                    return real_roots[0]
                else:  # 低压，倾向选择最大的根（气相）
                    return real_roots[-1]
        
        return best_root
    
    def calculate_Z(self, P, T, Tc, Pc, omega):
        """
        计算压缩因子Z
        
        参数:
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        
        返回:
        float: 压缩因子Z (选择热力学上最合理的根)
        """
        # 计算PR方程参数
        a_T = self.calculate_a_T(T, Tc, Pc, omega)
        _, b = self.calculate_a_b(Tc, Pc, omega)
        
        # 计算无量纲参数
        A = a_T * P / ((self.R * T)**2)
        B = b * P / (self.R * T)
        
        # 求解三次方程: Z^3 - (1-B)Z^2 + (A-3B^2-2B)Z - (AB-B^2-B^3) = 0
        # 转换为标准形式 a*Z^3 + b*Z^2 + c*Z + d = 0
        a = 1.0
        b = -(1.0 - B)
        c = (A - 3.0*B**2 - 2.0*B)
        d = -(A*B - B**2 - B**3)
        
        # 使用纯Python实现求解
        roots = solve_cubic_equation(a, b, c, d)
        
        # 使用改进的根选择方法
        return self.select_proper_root(roots, P, T, Tc, Pc, B)
    
    def calculate_fugacity_coefficient(self, P, T, Tc, Pc, omega):
        """
        计算逸度系数phi
        
        参数:
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        
        返回:
        float: 逸度系数phi
        """
        # 确保输入参数有效
        if P <= 0 or T <= 0 or Tc <= 0 or Pc <= 0:
            raise ValueError("压力、温度、临界温度和临界压力必须为正值")
        
        # 计算PR方程参数
        a_T = self.calculate_a_T(T, Tc, Pc, omega)
        _, b = self.calculate_a_b(Tc, Pc, omega)
        
        # 计算无量纲参数
        A = a_T * P / ((self.R * T)**2)
        B = b * P / (self.R * T)
        
        # 计算压缩因子
        Z = self.calculate_Z(P, T, Tc, Pc, omega)
        
        # 计算辅助参数
        sqrt_2 = math.sqrt(2.0)
        
        # 计算逸度系数，采用更稳定的数值方法
        # 避免Z-B接近0导致的数值不稳定
        Z_minus_B = max(Z - B, 1e-10)
        
        # 计算ln(phi)的三个项
        term1 = Z - 1.0
        term2 = math.log(Z_minus_B)
        
        # 计算第三项，避免数值不稳定
        if abs(B) < 1e-10:
            term3 = 0.0
        else:
            # 计算分母，避免接近0的情况
            denominator1 = max(Z + (1.0 + sqrt_2) * B, 1e-10)
            denominator2 = max(Z + (1.0 - sqrt_2) * B, 1e-10)
            
            # 避免对负数取对数
            if denominator1 <= 0 or denominator2 <= 0:
                # 当分母可能为负时，使用近似公式
                term3 = A / (2.0 * sqrt_2 * B) * (2.0 * B / (Z + B))
            else:
                term3 = A / (2.0 * sqrt_2 * B) * math.log(denominator1 / denominator2)
        
        # 计算ln(phi)并返回phi
        ln_phi = term1 - term2 - term3
        return math.exp(ln_phi)


class FugacityPressureConverter:
    """
    逸度与压强转换工具
    基于PR状态方程计算逸度系数
    
    特性:
    1. 支持常见物质的物性参数查询
    2. 支持多种压力单位的转换
    3. 增强的数值稳定性和精确度
    4. 用户友好的错误处理
    """
    
    def __init__(self):
        """初始化转换工具"""
        self.pr_equation = PRStateEquation()
        self.substance_database = SUBSTANCE_DATABASE
    
    def get_substance_properties(self, substance_name):
        """
        获取物质的物性参数
        
        参数:
        substance_name (str): 物质名称
        
        返回:
        dict: 包含Tc, Pc, omega的字典，如果未找到则返回None
        """
        return self.substance_database.get(substance_name)
    
    def list_available_substances(self):
        """
        列出所有可用的物质
        
        返回:
        list: 物质名称列表
        """
        return list(self.substance_database.keys())
    
    def convert_pressure_units(self, pressure, from_unit="Pa", to_unit="Pa"):
        """
        转换压力单位
        
        参数:
        pressure (float): 压力值
        from_unit (str): 源单位 ("Pa", "atm", "bar", "MPa", "psi", "mmHg")
        to_unit (str): 目标单位 ("Pa", "atm", "bar", "MPa", "psi", "mmHg")
        
        返回:
        float: 转换后的压力值
        """
        # 先转换为Pa
        if from_unit == "Pa":
            pressure_pa = pressure
        elif from_unit == "atm":
            pressure_pa = pressure * ATM_TO_PA
        elif from_unit == "bar":
            pressure_pa = pressure * BAR_TO_PA
        elif from_unit == "MPa":
            pressure_pa = pressure * MPA_TO_PA
        elif from_unit == "psi":
            pressure_pa = pressure * PSI_TO_PA
        elif from_unit == "mmHg":
            pressure_pa = pressure * MMHG_TO_PA
        else:
            raise ValueError(f"不支持的单位：{from_unit}")
        
        # 从Pa转换为目标单位
        if to_unit == "Pa":
            return pressure_pa
        elif to_unit == "atm":
            return pressure_pa / ATM_TO_PA
        elif to_unit == "bar":
            return pressure_pa / BAR_TO_PA
        elif to_unit == "MPa":
            return pressure_pa / MPA_TO_PA
        elif to_unit == "psi":
            return pressure_pa / PSI_TO_PA
        elif to_unit == "mmHg":
            return pressure_pa / MMHG_TO_PA
        else:
            raise ValueError(f"不支持的单位：{to_unit}")
    
    def convert_pressure(self, pressure, from_unit="Pa", to_unit="Pa"):
        """
        转换压力单位 (作为convert_pressure_units的别名)
        
        参数:
        pressure (float): 压力值
        from_unit (str): 源单位 ("Pa", "atm", "bar", "MPa", "psi", "mmHg")
        to_unit (str): 目标单位 ("Pa", "atm", "bar", "MPa", "psi", "mmHg")
        
        返回:
        float: 转换后的压力值
        """
        return self.convert_pressure_units(pressure, from_unit, to_unit)
    
    def pressure_to_fugacity(self, P, T, Tc, Pc, omega):
        """
        从压强计算逸度
        
        参数:
        P (float): 压力 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        
        返回:
        float: 逸度 (Pa)
        """
        # 输入验证
        if P <= 0:
            raise ValueError("压力必须为正值")
        if T <= 0:
            raise ValueError("温度必须为正值")
        if Tc <= 0:
            raise ValueError("临界温度必须为正值")
        if Pc <= 0:
            raise ValueError("临界压力必须为正值")
        
        try:
            phi = self.pr_equation.calculate_fugacity_coefficient(P, T, Tc, Pc, omega)
            fugacity = phi * P
            return fugacity
        except Exception as e:
            raise ValueError(f"计算逸度失败: {str(e)}")
    
    def fugacity_to_pressure(self, fugacity, T, Tc, Pc, omega, P_initial=None, tol=None, max_iter=None):
        """
        从逸度计算压强 (使用迭代求解)
        
        参数:
        fugacity (float): 逸度 (Pa)
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        P_initial (float, optional): 压力初值 (Pa)，默认为None，会自动设置为fugacity
        tol (float, optional): 收敛容差
        max_iter (int, optional): 最大迭代次数
        
        返回:
        float: 压力 (Pa)
        """
        # 输入验证
        if fugacity <= 0:
            raise ValueError("逸度必须为正值")
        if T <= 0:
            raise ValueError("温度必须为正值")
        if Tc <= 0:
            raise ValueError("临界温度必须为正值")
        if Pc <= 0:
            raise ValueError("临界压力必须为正值")
        
        # 使用全局定义的精度控制参数
        tol = tol if tol is not None else CONVERGENCE_TOL
        max_iter = max_iter if max_iter is not None else MAX_ITERATIONS
        
        if P_initial is None:
            P = fugacity  # 初始猜测：理想气体(phi=1时)
        else:
            P = P_initial
        
        # 防止除零问题
        if abs(P) < 1e-10:
            P = 1e-10
        
        # 记录迭代历史，用于分析收敛性
        convergence_history = []
        
        for i in range(max_iter):
            try:
                phi = self.pr_equation.calculate_fugacity_coefficient(P, T, Tc, Pc, omega)
                P_new = fugacity / phi
                
                # 采用更严格的收敛标准：相对误差和绝对误差的组合
                rel_error = abs(P_new - P) / max(abs(P), 1e-10)
                abs_error = abs(P_new - P)
                
                convergence_history.append((i, P, phi, rel_error))
                
                if rel_error < tol or abs_error < tol:
                    return P_new
                
                # 更新压力，使用自适应阻尼因子防止震荡
                # 如果连续迭代方向相同，加大步长；如果方向变化，减小步长
                if i > 0 and (P_new - P) * (P - convergence_history[-2][1]) > 0:
                    # 方向一致，加大步长
                    damping = min(0.9, 0.7 + 0.1 * i / max_iter)
                else:
                    # 方向改变，减小步长
                    damping = 0.5
                    
                P = (1 - damping) * P + damping * P_new
                
            except Exception as e:
                raise ValueError(f"在迭代过程中出错 (迭代次数={i}): {str(e)}")
        
        # 如果未收敛，返回最佳估计并给出警告
        print(f"警告: 逸度转压强迭代未完全收敛，返回最佳估计值。相对误差={rel_error:.2e}")
        return P
    
    def calculate_pressure_fugacity_points(self, T, Tc, Pc, omega, p_min=DEFAULT_P_MIN, p_max=DEFAULT_P_MAX, num_points=DEFAULT_POINTS):
        """
        计算一系列压力点下的逸度和逸度系数
        
        参数:
        T (float): 温度 (K)
        Tc (float): 临界温度 (K)
        Pc (float): 临界压力 (Pa)
        omega (float): 偏心因子
        p_min (float): 最小压力 (Pa)
        p_max (float): 最大压力 (Pa)
        num_points (int): 点数
        
        返回:
        tuple: (pressures, fugacities, phi_values, z_values) 压力列表、逸度列表、逸度系数列表和压缩因子列表
        """
        # 输入验证
        if p_min <= 0 or p_max <= 0 or p_min >= p_max:
            raise ValueError("压力范围无效")
        if T <= 0 or Tc <= 0 or Pc <= 0:
            raise ValueError("温度和临界参数必须为正值")
        if num_points < 10:
            raise ValueError("点数太少，至少需要10个点")
        
        # 生成一系列压力点
        pressures = []
        fugacities = []
        phi_values = []
        z_values = []  # 添加压缩因子列表
        
        # 使用对数刻度生成压力点，确保低压区域有足够的点
        log_p_min = math.log10(max(p_min, 1e-6))
        log_p_max = math.log10(p_max)
        log_pressures = [10 ** (log_p_min + (log_p_max - log_p_min) * i / (num_points - 1)) for i in range(num_points)]
        
        for P in log_pressures:
            try:
                # 计算逸度系数和压缩因子
                phi = self.pr_equation.calculate_fugacity_coefficient(P, T, Tc, Pc, omega)
                Z = self.pr_equation.calculate_Z(P, T, Tc, Pc, omega)  # 计算压缩因子
                f = phi * P
                
                pressures.append(P)
                fugacities.append(f)
                phi_values.append(phi)
                z_values.append(Z)  # 添加压缩因子
                
            except Exception as e:
                # 跳过计算失败的点，但提供更详细的错误信息
                print(f"计算压力 {P:.6e} Pa 时出错: {e}")
                continue
                
        return pressures, fugacities, phi_values, z_values
    
    def get_unit_choices(self):
        """
        获取所有可用的压力单位选项
        
        返回:
        list: 单位选项列表
        """
        return ["Pa", "MPa", "bar", "atm", "psi", "mmHg"]


def get_user_input():
    """获取用户输入的物性参数和计算条件"""
    print("\n" + "="*50)
    print("逸度与压强转换工具 (基于PR状态方程)")
    print("作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)")
    print("="*50)
    
    # 显示物质数据库中的可用物质
    print("\n可用的物质列表:")
    converter = FugacityPressureConverter()
    substances = converter.list_available_substances()
    
    # 分列显示物质列表
    columns = 3
    for i, substance in enumerate(substances):
        if i % columns == 0 and i > 0:
            print()
        print(f"{i+1}. {substance:15}", end=" ")
    print("\n")
    
    # 选择物质或输入自定义物质参数
    print("选择方式:")
    print("1. 从物质列表中选择")
    print("2. 手动输入物质参数")
    choice = input("请选择 (1/2) [默认:1]: ") or "1"
    
    if choice == "1":
        # 从列表选择
        valid_choice = False
        substance_index = -1
        while not valid_choice:
            try:
                substance_index = int(input(f"请输入物质编号 (1-{len(substances)}) [默认:1]: ") or "1") - 1
                if 0 <= substance_index < len(substances):
                    valid_choice = True
                else:
                    print(f"错误: 请输入1到{len(substances)}之间的数字")
            except ValueError:
                print("错误: 请输入有效的数字")
        
        substance = substances[substance_index]
        properties = converter.get_substance_properties(substance)
        Tc = properties["Tc"]
        Pc = properties["Pc"]
        omega = properties["omega"]
        
        print(f"\n已选择: {substance}")
        print(f"临界温度: {Tc} K")
        print(f"临界压力: {Pc/1e6:.6f} MPa")
        print(f"偏心因子: {omega:.6f}")
        
    else:
        # 手动输入
        print("\n请输入物质的物性参数:")
        substance = input("物质名称: ")
        
        # 获取临界温度
        valid_input = False
        while not valid_input:
            try:
                tc_input = input(f"临界温度 (K) [{DEFAULT_Tc}]: ")
                Tc = float(tc_input) if tc_input else DEFAULT_Tc
                if Tc <= 0:
                    print("错误: 临界温度必须为正值")
                else:
                    valid_input = True
            except ValueError:
                print("错误: 请输入有效的数字")
        
        # 获取临界压力
        valid_input = False
        while not valid_input:
            try:
                pc_unit = input("临界压力单位 (1=Pa, 2=MPa, 3=bar, 4=atm) [默认:2]: ") or "2"
                pc_input = input(f"临界压力 ({pc_unit}) [{DEFAULT_Pc/1e6 if pc_unit=='2' else DEFAULT_Pc}]: ")
                
                if pc_input:
                    pc_value = float(pc_input)
                    if pc_value <= 0:
                        print("错误: 临界压力必须为正值")
                        continue
                    
                    if pc_unit == "2":  # MPa
                        Pc = pc_value * 1e6
                    elif pc_unit == "3":  # bar
                        Pc = pc_value * BAR_TO_PA
                    elif pc_unit == "4":  # atm
                        Pc = pc_value * ATM_TO_PA
                    else:  # Pa
                        Pc = pc_value
                else:
                    Pc = DEFAULT_Pc
                
                valid_input = True
            except ValueError:
                print("错误: 请输入有效的数字")
        
        # 获取偏心因子
        valid_input = False
        while not valid_input:
            try:
                omega_input = input(f"偏心因子 [{DEFAULT_OMEGA}]: ")
                omega = float(omega_input) if omega_input else DEFAULT_OMEGA
                valid_input = True
            except ValueError:
                print("错误: 请输入有效的数字")
    
    print("\n请输入计算条件:")
    
    # 获取温度
    valid_input = False
    while not valid_input:
        try:
            t_input = input(f"温度 (K) [{DEFAULT_T}]: ")
            T = float(t_input) if t_input else DEFAULT_T
            if T <= 0:
                print("错误: 温度必须为正值")
            else:
                valid_input = True
        except ValueError:
            print("错误: 请输入有效的数字")
    
    # 获取压力
    valid_input = False
    while not valid_input:
        try:
            p_unit = input("压力单位 (1=Pa, 2=MPa, 3=bar, 4=atm, 5=psi, 6=mmHg) [默认:2]: ") or "2"
            p_input = input(f"压力 ({p_unit}) [{DEFAULT_P/1e6 if p_unit=='2' else DEFAULT_P}]: ")
            
            if p_input:
                p_value = float(p_input)
                if p_value <= 0:
                    print("错误: 压力必须为正值")
                    continue
                
                if p_unit == "2":  # MPa
                    P = p_value * 1e6
                elif p_unit == "3":  # bar
                    P = p_value * BAR_TO_PA
                elif p_unit == "4":  # atm
                    P = p_value * ATM_TO_PA
                elif p_unit == "5":  # psi
                    P = p_value * PSI_TO_PA
                elif p_unit == "6":  # mmHg
                    P = p_value * MMHG_TO_PA
                else:  # Pa
                    P = p_value
            else:
                P = DEFAULT_P
            
            valid_input = True
        except ValueError:
            print("错误: 请输入有效的数字")
    
    return substance, Tc, Pc, omega, T, P


def main_console():
    """命令行界面的主函数"""
    
    print("\n欢迎使用逸度与压强转换工具！")
    print("本工具基于Peng-Robinson状态方程计算实际流体的热力学性质。")
    print("作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)")
    print("对于零基础用户，只需按照提示操作或直接按回车使用默认值即可。")
    
    # 获取用户输入或使用默认值
    substance, Tc, Pc, omega, T, P = get_user_input()
    
    # 创建转换器实例
    converter = FugacityPressureConverter()
    
    print("\n计算结果:")
    print("="*50)
    
    print(f"物质: {substance}")
    print(f"临界温度: {Tc:.4f} K")
    print(f"临界压力: {Pc/1e6:.6f} MPa ({Pc:.2f} Pa)")
    print(f"偏心因子: {omega:.6f}")
    print("-"*50)
    
    try:
        # 计算逸度
        fugacity = converter.pressure_to_fugacity(P, T, Tc, Pc, omega)
        print(f"温度: {T:.4f} K")
        print(f"压力: {P/1e6:.10f} MPa ({P:.2f} Pa)")
        print(f"计算得到的逸度: {fugacity/1e6:.10f} MPa ({fugacity:.2f} Pa)")
    
        # 从逸度反向计算压力以验证
        P_calculated = converter.fugacity_to_pressure(fugacity, T, Tc, Pc, omega)
        print(f"从逸度反向计算得到的压力: {P_calculated/1e6:.10f} MPa")
    
        # 计算压缩因子和逸度系数
        Z = converter.pr_equation.calculate_Z(P, T, Tc, Pc, omega)
        phi = fugacity / P
        print(f"压缩因子 (Z): {Z:.10f}")
        print(f"逸度系数 (phi): {phi:.10f}")
        
        # 计算理想气体与实际气体的偏差
        ideal_gas_deviation = abs(Z - 1.0) * 100
        print(f"与理想气体的偏差: {ideal_gas_deviation:.4f}%")
        
        # 转换为常用单位
        print("\n其他单位下的结果:")
        print(f"压力: {converter.convert_pressure_units(P, 'Pa', 'bar'):.6f} bar")
        print(f"      {converter.convert_pressure_units(P, 'Pa', 'atm'):.6f} atm")
        print(f"      {converter.convert_pressure_units(P, 'Pa', 'psi'):.6f} psi")
        print(f"逸度: {converter.convert_pressure_units(fugacity, 'Pa', 'bar'):.6f} bar")
        print(f"      {converter.convert_pressure_units(fugacity, 'Pa', 'atm'):.6f} atm")
        print(f"      {converter.convert_pressure_units(fugacity, 'Pa', 'psi'):.6f} psi")
        
    except Exception as e:
        print(f"计算过程中出错: {str(e)}")
        print("请检查输入参数是否合理，或尝试使用不同的计算条件。")
    
    print("-"*50)
    
    # 询问是否显示详细的使用说明
    show_help = input("\n是否显示详细使用说明? (y/n) [默认:n]: ").lower() == 'y'
    if show_help:
        print("\n使用说明:")
        print("1. 逸度是用于描述非理想气体的热力学性质，与压力具有相同单位(Pa)")
        print("2. 逸度系数(phi)是逸度与压力之比，无量纲")
        print("3. 压缩因子(Z)用于描述气体的压缩性，理想气体Z=1")
        print("4. PR状态方程适用于大多数气体，但对于强极性分子或氢键较强的物质精度可能降低")
        print("5. 计算中使用的物性参数来源于热力学数据库，对于特殊物质可能需要查询更精确的数据")
        
    # 询问是否继续进行新的计算
    continue_calc = input("\n是否进行新的计算? (y/n) [默认:n]: ").lower() == 'y'
    if continue_calc:
        main_console()  # 递归调用
    else:
        print("\n感谢使用逸度与压强转换工具！作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)")
        print("祝您工作愉快！\n")


class FugacityVisualizer(tk.Tk):
    """
    逸度可视化工具的图形界面
    
    特性:
    1. 支持物质数据库选择
    2. 支持多种压力单位
    3. 实时更新计算结果
    4. 提供逸度-压力和逸度系数-压力曲线
    """
    
    def __init__(self):
        # 确保没有其他tk根窗口
        if tk._default_root is not None:
            try:
                tk._default_root.destroy()
            except:
                pass
            tk._default_root = None
            
        super().__init__()
        self.title("逸度与压强转换可视化工具 - 作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)")
        self.geometry("1100x850")
        
        # 设置图标和背景
        self.configure(bg="#f0f0f0")
        
        # 创建转换器实例
        self.converter = FugacityPressureConverter()
        
        # 创建变量
        self.setup_variables()
        
        # 创建界面组件
        self.create_widgets()
        
        # 添加帮助提示信息
        self.create_tooltips()
        
        # 初始化物质参数显示
        self.initialize_substance_display()
        
        # 初始化图表
        self.update_plots()
        
        # 添加延迟检查，确保界面完全初始化后参数显示正确
        self.after(100, self.check_parameters_display)
    
    def setup_variables(self):
        """初始化GUI中使用的所有变量"""
        # 物质选择相关变量
        self.substance_selection = tk.StringVar(value="database")  # 默认使用数据库选择
        self.substance_var = tk.StringVar(value=DEFAULT_SUBSTANCE)  # 默认物质
        self.custom_substance_var = tk.StringVar(value="自定义物质")  # 自定义物质名称
        
        # 临界参数相关变量
        self.tc_var = tk.DoubleVar(value=DEFAULT_Tc)  # 临界温度
        self.pc_var = tk.DoubleVar(value=DEFAULT_Pc/1e6)  # 临界压力，默认显示单位为MPa
        
        # 偏心因子
        self.omega_var = tk.DoubleVar(value=DEFAULT_OMEGA)
        
        # 计算条件相关变量
        self.t_var = tk.DoubleVar(value=DEFAULT_T)  # 温度
        self.p_var = tk.DoubleVar(value=DEFAULT_P/1e6)  # 单点压力，默认显示单位为MPa
        self.p_single_unit_var = tk.StringVar(value="MPa")  # 单点压力单位
        
        # 压力范围变量
        self.p_min_var = tk.DoubleVar(value=DEFAULT_P_MIN/1e6)  # 最小压力，默认显示单位为MPa
        self.p_max_var = tk.DoubleVar(value=DEFAULT_P_MAX/1e6)  # 最大压力，默认显示单位为MPa
        self.p_unit_var = tk.StringVar(value="MPa")  # 压力范围单位
        self.points_var = tk.IntVar(value=DEFAULT_POINTS)  # 计算点数
        
        # 计算结果变量
        self.fugacity_var = tk.StringVar(value="")  # 逸度
        self.phi_var = tk.StringVar(value="")  # 逸度系数
        self.z_var = tk.StringVar(value="")  # 压缩因子
        self.deviation_var = tk.StringVar(value="")  # 理想气体偏差
    
    def create_widgets(self):
        """创建所有GUI组件并布局"""
        # 创建主框架
        main_frame = ttk.Frame(self, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建左侧输入面板
        input_frame = ttk.LabelFrame(main_frame, text="输入参数", padding=10)
        input_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=5, pady=5)
        
        # 创建右侧计算结果和图表面板
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建计算结果面板
        result_frame = ttk.LabelFrame(right_frame, text="计算结果", padding=10)
        result_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        # 创建图表面板
        plot_frame = ttk.LabelFrame(right_frame, text="逸度-压力关系图", padding=10)
        plot_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # ==================== 输入面板内容 ====================
        # 物质选择部分
        substance_frame = ttk.LabelFrame(input_frame, text="物质选择", padding=5)
        substance_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 物质选择模式
        ttk.Label(substance_frame, text="选择模式:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        mode_frame = ttk.Frame(substance_frame)
        mode_frame.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Radiobutton(mode_frame, text="数据库选择", variable=self.substance_selection, 
                        value="database", command=self.toggle_substance_mode).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(mode_frame, text="自定义参数", variable=self.substance_selection, 
                        value="custom", command=self.toggle_substance_mode).pack(side=tk.LEFT, padx=5)
        
        # 物质数据库选择下拉框
        ttk.Label(substance_frame, text="选择物质:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.substance_combobox = ttk.Combobox(substance_frame, textvariable=self.substance_var, 
                                              values=self.converter.list_available_substances(), state="readonly")
        self.substance_combobox.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        self.substance_combobox.bind("<<ComboboxSelected>>", self.on_substance_selected)
        
        # 自定义物质名称输入框
        ttk.Label(substance_frame, text="自定义名称:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.custom_substance_entry = ttk.Entry(substance_frame, textvariable=self.custom_substance_var, width=20)
        self.custom_substance_entry.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 物性参数部分
        properties_frame = ttk.LabelFrame(input_frame, text="物质物性参数", padding=5)
        properties_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 临界温度
        ttk.Label(properties_frame, text="临界温度 (K):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.tc_entry = ttk.Entry(properties_frame, textvariable=self.tc_var, width=15)
        self.tc_entry.grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 临界压力及单位
        ttk.Label(properties_frame, text="临界压力:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        pc_frame = ttk.Frame(properties_frame)
        pc_frame.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        
        self.pc_entry = ttk.Entry(pc_frame, textvariable=self.pc_var, width=15)
        self.pc_entry.pack(side=tk.LEFT, padx=0)
        
        self.pc_unit_combobox = ttk.Combobox(pc_frame, values=["Pa", "MPa", "bar", "atm"], 
                                            width=6, state="readonly")
        self.pc_unit_combobox.set("MPa")
        self.pc_unit_combobox.pack(side=tk.LEFT, padx=5)
        self.pc_unit_combobox.bind("<<ComboboxSelected>>", self.on_pc_unit_changed)
        
        # 偏心因子
        ttk.Label(properties_frame, text="偏心因子 (ω):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.omega_entry = ttk.Entry(properties_frame, textvariable=self.omega_var, width=15)
        self.omega_entry.grid(row=2, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 计算条件部分
        conditions_frame = ttk.LabelFrame(input_frame, text="计算条件", padding=5)
        conditions_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 温度
        ttk.Label(conditions_frame, text="温度 (K):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(conditions_frame, textvariable=self.t_var, width=15).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 单点压力及单位
        ttk.Label(conditions_frame, text="计算压力:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        p_frame = ttk.Frame(conditions_frame)
        p_frame.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        
        ttk.Entry(p_frame, textvariable=self.p_var, width=15).pack(side=tk.LEFT, padx=0)
        
        p_unit_combobox = ttk.Combobox(p_frame, textvariable=self.p_single_unit_var, 
                                       values=self.converter.get_unit_choices(), width=6, state="readonly")
        p_unit_combobox.pack(side=tk.LEFT, padx=5)
        
        # 压力范围及点数
        range_frame = ttk.LabelFrame(conditions_frame, text="压力范围(用于图表)", padding=5)
        range_frame.grid(row=2, column=0, columnspan=2, sticky=tk.EW, padx=5, pady=5)
        
        ttk.Label(range_frame, text="最小压力:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(range_frame, textvariable=self.p_min_var, width=10).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        
        ttk.Label(range_frame, text="最大压力:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(range_frame, textvariable=self.p_max_var, width=10).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        
        ttk.Label(range_frame, text="单位:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        range_unit_combobox = ttk.Combobox(range_frame, textvariable=self.p_unit_var, 
                                          values=self.converter.get_unit_choices(), width=6, state="readonly")
        range_unit_combobox.grid(row=0, column=3, rowspan=2, sticky=tk.NS, padx=5, pady=2)
        
        ttk.Label(range_frame, text="点数:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(range_frame, textvariable=self.points_var, width=10).grid(row=2, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 计算按钮
        ttk.Button(input_frame, text="计算并更新图表", command=self.update_plots).pack(fill=tk.X, padx=5, pady=10)
        
        # 导出数据按钮
        export_frame = ttk.Frame(input_frame)
        export_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(export_frame, text="导出数据", command=lambda: self.export_data("csv")).pack(side=tk.LEFT, padx=5, pady=5, expand=True, fill=tk.X)
        ttk.Button(export_frame, text="导出HTML报告", command=lambda: self.export_data("html")).pack(side=tk.RIGHT, padx=5, pady=5, expand=True, fill=tk.X)
        
        # 帮助和关于按钮
        buttons_frame = ttk.Frame(input_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(buttons_frame, text="使用帮助", command=self.show_help).pack(side=tk.LEFT, padx=5, pady=5, expand=True, fill=tk.X)
        ttk.Button(buttons_frame, text="关于", command=self.show_about).pack(side=tk.RIGHT, padx=5, pady=5, expand=True, fill=tk.X)
        
        # 添加计算原理按钮
        ttk.Button(input_frame, text="计算原理与过程", command=self.show_calculation_theory).pack(fill=tk.X, padx=5, pady=5)
        
        # ==================== 结果面板内容 ====================
        # 创建结果显示网格
        result_grid = ttk.Frame(result_frame)
        result_grid.pack(fill=tk.BOTH, padx=5, pady=5)
        
        # 逸度
        ttk.Label(result_grid, text="逸度:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(result_grid, textvariable=self.fugacity_var, width=30, relief='sunken', anchor=tk.CENTER).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 逸度系数
        ttk.Label(result_grid, text="逸度系数:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Label(result_grid, textvariable=self.phi_var, width=30, relief='sunken', anchor=tk.CENTER).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # 压缩因子
        ttk.Label(result_grid, text="压缩因子:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Label(result_grid, textvariable=self.z_var, width=30, relief='sunken', anchor=tk.CENTER).grid(row=0, column=3, sticky=tk.EW, padx=5, pady=2)
        
        # 理想气体偏差
        ttk.Label(result_grid, text="理想气体偏差:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Label(result_grid, textvariable=self.deviation_var, width=30, relief='sunken', anchor=tk.CENTER).grid(row=1, column=3, sticky=tk.EW, padx=5, pady=2)
        
        # ==================== 图表面板内容 ====================
        # 创建图表
        self.fig = Figure(figsize=(10, 8), dpi=100)
        self.ax1 = self.fig.add_subplot(211)  # 上半部分：压力-逸度关系
        self.ax2 = self.fig.add_subplot(212)  # 下半部分：压力-逸度系数关系
        
        # 添加画布
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
    def export_data(self, format_type):
        """导出计算数据到CSV或HTML文件"""
        # 检查是否有计算结果
        if not hasattr(self, 'display_results') or not self.display_results:
            messagebox.showerror("导出错误", "没有可导出的数据，请先进行计算。")
            return
        
        try:
            # 获取当前时间作为文件名的一部分
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 获取物质名称，考虑自定义模式
            if self.substance_selection.get() == "database":
                substance = self.substance_var.get().replace(" ", "_").replace("(", "").replace(")", "")
            else:
                substance = self.custom_substance_var.get().replace(" ", "_").replace("(", "").replace(")", "")
            
            if format_type == "csv":
                # 让用户选择保存位置
                file_path = filedialog.asksaveasfilename(
                    defaultextension=".csv",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                    initialfile=f"逸度计算_{substance}_{timestamp}.csv"
                )
                
                if not file_path:  # 用户取消了对话框
                    return
                
                # 导出到CSV
                self.export_to_csv(file_path)
                
            elif format_type == "html":
                # 让用户选择保存位置
                file_path = filedialog.asksaveasfilename(
                    defaultextension=".html",
                    filetypes=[("HTML files", "*.html"), ("All files", "*.*")],
                    initialfile=f"逸度计算_{substance}_{timestamp}.html"
                )
                
                if not file_path:  # 用户取消了对话框
                    return
                
                # 直接导出为HTML
                self.export_to_html(file_path)
            
            # 导出成功后显示消息
            messagebox.showinfo("导出成功", f"数据已成功导出到:\n{file_path}")
            
        except Exception as e:
            messagebox.showerror("导出错误", f"导出数据时出错:\n{str(e)}\n调试信息: {type(e).__name__}")
    
    def export_to_html(self, file_path):
        """导出数据到HTML文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as html_file:
                # 写入HTML头部
                html_file.write('<!DOCTYPE html>\n<html>\n<head>\n')
                html_file.write('<meta charset="utf-8">\n')
                html_file.write('<title>逸度计算数据</title>\n')
                html_file.write('<style>\n')
                html_file.write('body { font-family: SimSun, Arial, sans-serif; margin: 20px; }\n')
                html_file.write('table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }\n')
                html_file.write('th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n')
                html_file.write('th { background-color: #f2f2f2; }\n')
                html_file.write('tr:nth-child(even) { background-color: #f9f9f9; }\n')
                html_file.write('h1 { color: #333; text-align: center; }\n')
                html_file.write('h2 { color: #333; margin-top: 30px; }\n')
                html_file.write('.container { max-width: 1200px; margin: 0 auto; }\n')
                html_file.write('</style>\n')
                html_file.write('</head>\n<body>\n')
                html_file.write('<div class="container">\n')
                
                # 标题和信息
                html_file.write(f'<h1>逸度计算数据报告</h1>\n')
                html_file.write(f'<p>生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>\n')
                
                # 单点计算结果表格
                html_file.write('<h2>单点计算结果</h2>\n')
                html_file.write('<table>\n')
                html_file.write('<tr><th>参数</th><th>数值</th></tr>\n')
                
                for key, value in self.single_point_results.items():
                    html_file.write(f'<tr><td>{key}</td><td>{value}</td></tr>\n')
                
                html_file.write('</table>\n')
                
                # 曲线数据表格
                html_file.write('<h2>曲线数据</h2>\n')
                html_file.write('<table>\n')
                
                # 表头
                headers = list(self.display_results.keys())
                html_file.write('<tr>\n')
                for header in headers:
                    html_file.write(f'<th>{header}</th>\n')
                html_file.write('</tr>\n')
                
                # 获取最大行数
                max_rows = max(len(self.display_results[header]) for header in headers)
                
                # 写入数据行
                for i in range(max_rows):
                    html_file.write('<tr>\n')
                    for header in headers:
                        try:
                            value = self.display_results[header][i]
                            # 格式化数值
                            if isinstance(value, float):
                                value = f"{value:.6f}"
                            html_file.write(f'<td>{value}</td>\n')
                        except IndexError:
                            html_file.write('<td></td>\n')  # 超出范围则写入空单元格
                    html_file.write('</tr>\n')
                
                html_file.write('</table>\n')
                
                # 添加页脚
                html_file.write('<p style="text-align: center; margin-top: 30px; color: #666;">')
                html_file.write('逸度与压强转换工具 - 作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)')
                html_file.write('</p>\n')
                
                html_file.write('</div>\n')
                html_file.write('</body>\n</html>')
            
            # 使用默认浏览器打开HTML文件
            os.startfile(file_path)
            
        except Exception as e:
            messagebox.showerror("导出错误", f"HTML导出失败: {str(e)}")
            # 如果HTML导出失败，尝试导出为CSV作为备份
            csv_path = os.path.splitext(file_path)[0] + '.csv'
            self.export_to_csv(csv_path)
            messagebox.showinfo("已改用CSV格式", 
                               f"由于HTML导出失败，数据已改为CSV格式保存至：\n{csv_path}")
    
    def export_to_csv(self, file_path):
        """导出数据到CSV文件"""
        # 使用UTF-8-SIG编码确保Excel可以正确识别中文
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入单点计算结果
            writer.writerow(["单点计算结果"])
            for key, value in self.single_point_results.items():
                writer.writerow([key, value])
            
            writer.writerow([])  # 空行分隔
            
            # 写入曲线数据
            writer.writerow(["曲线数据"])
            # 获取所有列名
            headers = list(self.display_results.keys())
            writer.writerow(headers)
            
            # 转置数据以便按行写入
            rows = zip(*[self.display_results[header] for header in headers])
            for row in rows:
                writer.writerow(row)
    
    def create_tooltips(self):
        """为各个界面组件添加提示信息"""
        # 这里只实现基本的提示功能，完整实现会需要一个ToolTip类
        # 物质选择提示
        self.substance_combobox.bind("<Enter>", lambda e: self.show_tooltip(e, "从预定义的物质数据库中选择物质"))
        self.substance_combobox.bind("<Leave>", lambda e: self.hide_tooltip())
        
        # 临界参数提示
        self.tc_entry.bind("<Enter>", lambda e: self.show_tooltip(e, "临界温度：物质的临界点温度，单位为K"))
        self.tc_entry.bind("<Leave>", lambda e: self.hide_tooltip())
        
        self.pc_entry.bind("<Enter>", lambda e: self.show_tooltip(e, "临界压力：物质的临界点压力，可选择不同单位"))
        self.pc_entry.bind("<Leave>", lambda e: self.hide_tooltip())
        
        self.omega_entry.bind("<Enter>", lambda e: self.show_tooltip(e, "偏心因子：表征分子非球形程度的参数，无量纲"))
        self.omega_entry.bind("<Leave>", lambda e: self.hide_tooltip())
    
    def show_tooltip(self, event, text):
        """显示工具提示"""
        x, y, _, _ = event.widget.bbox("insert")
        x += event.widget.winfo_rootx() + 25
        y += event.widget.winfo_rooty() + 25
        
        # 创建一个提示窗口
        self.tooltip = tk.Toplevel(event.widget)
        self.tooltip.wm_overrideredirect(True)  # 移除窗口边框
        self.tooltip.wm_geometry(f"+{x}+{y}")
        
        label = ttk.Label(self.tooltip, text=text, background="#ffffe0", relief="solid", borderwidth=1, padding=2)
        label.pack()
    
    def hide_tooltip(self):
        """隐藏工具提示"""
        if hasattr(self, "tooltip"):
            self.tooltip.destroy()
            self.tooltip = None
    
    def initialize_substance_display(self):
        """初始化物质参数显示，确保界面显示正确的物性参数"""
        # 默认选择第一个物质
        if not self.substance_var.get():
            self.substance_var.set(DEFAULT_SUBSTANCE)
        
        # 设置组件状态，确保后续更新物性参数时组件状态正确
        mode = self.substance_selection.get()
        if mode == "database":
            # 启用数据库选择，禁用自定义参数
            self.substance_combobox.config(state="readonly")
            self.custom_substance_entry.config(state="disabled")
            self.tc_entry.config(state="disabled")
            self.pc_entry.config(state="disabled")
            self.pc_unit_combobox.config(state="disabled")
            self.omega_entry.config(state="disabled")
            
            # 在数据库模式下，直接调用更新物性参数
            substance = self.substance_var.get()
            properties = self.converter.get_substance_properties(substance)
            if properties:
                # 更新界面显示
                self.tc_var.set(properties["Tc"])
                
                # 处理临界压力单位
                pc_unit = self.pc_unit_combobox.get()
                pc_pa = properties["Pc"]
                
                if pc_unit == "MPa":
                    self.pc_var.set(pc_pa / 1e6)
                elif pc_unit == "bar":
                    self.pc_var.set(pc_pa / BAR_TO_PA)
                elif pc_unit == "atm":
                    self.pc_var.set(pc_pa / ATM_TO_PA)
                else:  # Pa
                    self.pc_var.set(pc_pa)
                
                self.omega_var.set(properties["omega"])
        else:
            # 禁用数据库选择，启用自定义参数
            self.substance_combobox.config(state="disabled")
            self.custom_substance_entry.config(state="normal")
            self.tc_entry.config(state="normal")
            self.pc_entry.config(state="normal")
            self.pc_unit_combobox.config(state="readonly")
            self.omega_entry.config(state="normal")
        
        # 强制更新界面
        self.update_idletasks()
        
        # 重新检查值是否已正确显示
        print(f"Debug - 初始化后物性参数显示状态:")
        print(f"物质: {self.substance_var.get()}")
        print(f"临界温度: {self.tc_var.get()}")
        print(f"临界压力: {self.pc_var.get()} {self.pc_unit_combobox.get()}")
        print(f"偏心因子: {self.omega_var.get()}")
        print(f"选择模式: {self.substance_selection.get()}")
    
    def toggle_substance_mode(self, update_params=True):
        """
        切换物质选择模式
        
        参数:
        update_params: 是否在切换到数据库模式后自动更新物性参数
        """
        mode = self.substance_selection.get()
        if mode == "database":
            # 启用数据库选择，禁用自定义参数
            self.substance_combobox.config(state="readonly")
            self.custom_substance_entry.config(state="disabled")
            self.tc_entry.config(state="disabled")
            self.pc_entry.config(state="disabled")
            self.pc_unit_combobox.config(state="disabled")
            self.omega_entry.config(state="disabled")
            
            # 更新物性参数，但仅当需要时
            if update_params:
                self.on_substance_selected()
        else:
            # 禁用数据库选择，启用自定义参数
            self.substance_combobox.config(state="disabled")
            self.custom_substance_entry.config(state="normal")
            self.tc_entry.config(state="normal")
            self.pc_entry.config(state="normal")
            self.pc_unit_combobox.config(state="readonly")
            self.omega_entry.config(state="normal")
    
    def on_substance_selected(self, event=None):
        """当从数据库选择物质时更新参数"""
        # 健壮性检查：确保组件已经创建
        if not hasattr(self, 'substance_var') or not hasattr(self, 'tc_var') or not hasattr(self, 'pc_var') or not hasattr(self, 'omega_var'):
            print("警告: 组件尚未完全初始化，跳过更新")
            return
            
        # 获取当前选择的物质
        substance = self.substance_var.get()
        if not substance:  # 防止空值
            return
        
        # 从数据库获取物性参数    
        properties = self.converter.get_substance_properties(substance)
        if properties:
            # 更新临界温度
            self.tc_var.set(properties["Tc"])
            
            # 健壮性检查：确保pc_unit_combobox已创建
            pc_unit = "MPa"  # 默认单位
            if hasattr(self, 'pc_unit_combobox'):
                pc_unit = self.pc_unit_combobox.get()
            
            # 更新临界压力显示，考虑单位
            pc_pa = properties["Pc"]
            
            if pc_unit == "MPa":
                self.pc_var.set(pc_pa / 1e6)
            elif pc_unit == "bar":
                self.pc_var.set(pc_pa / BAR_TO_PA)
            elif pc_unit == "atm":
                self.pc_var.set(pc_pa / ATM_TO_PA)
            else:  # Pa
                self.pc_var.set(pc_pa)
            
            # 更新偏心因子
            self.omega_var.set(properties["omega"])
            
            # 打印调试信息
            print(f"已更新物质 '{substance}' 的物性参数:")
            print(f"临界温度: {properties['Tc']} K")
            print(f"临界压力: {pc_pa} Pa ({self.pc_var.get()} {pc_unit})")
            print(f"偏心因子: {properties['omega']}")
    
    def on_pc_unit_changed(self, event=None):
        """当临界压力单位改变时转换数值"""
        if self.substance_selection.get() == "custom":
            # 获取当前值和单位
            current_value = self.pc_var.get()
            old_unit = event.widget._prev_value if hasattr(event.widget, '_prev_value') else "MPa"
            new_unit = self.pc_unit_combobox.get()
            
            # 先转换为Pa
            if old_unit == "MPa":
                value_pa = current_value * 1e6
            elif old_unit == "bar":
                value_pa = current_value * BAR_TO_PA
            elif old_unit == "atm":
                value_pa = current_value * ATM_TO_PA
            else:  # Pa
                value_pa = current_value
            
            # 再转换为新单位
            if new_unit == "MPa":
                self.pc_var.set(value_pa / 1e6)
            elif new_unit == "bar":
                self.pc_var.set(value_pa / BAR_TO_PA)
            elif new_unit == "atm":
                self.pc_var.set(value_pa / ATM_TO_PA)
            else:  # Pa
                self.pc_var.set(value_pa)
            
            # 保存当前单位供下次使用
            event.widget._prev_value = new_unit
    
    def update_plots(self):
        """更新图表和单点计算结果"""
        try:
            # 获取用户输入的参数
            mode = self.substance_selection.get()
            if mode == "database":
                substance = self.substance_var.get()
            else:
                substance = self.custom_substance_var.get()
                
            Tc = float(self.tc_var.get())
            
            # 处理临界压力单位转换
            pc_value = float(self.pc_var.get())
            pc_unit = self.pc_unit_combobox.get()
            
            if pc_unit == "MPa":
                Pc = pc_value * 1e6
            elif pc_unit == "bar":
                Pc = pc_value * BAR_TO_PA
            elif pc_unit == "atm":
                Pc = pc_value * ATM_TO_PA
            else:  # Pa
                Pc = pc_value
            
            omega = float(self.omega_var.get())
            T = float(self.t_var.get())
            
            # 处理压力单位转换
            p_value = float(self.p_var.get())
            p_unit = self.p_single_unit_var.get()
            P = self.converter.convert_pressure_units(p_value, p_unit, "Pa")
            
            # 处理压力范围单位转换
            p_min_value = float(self.p_min_var.get())
            p_max_value = float(self.p_max_var.get())
            p_range_unit = self.p_unit_var.get()
            p_min = self.converter.convert_pressure_units(p_min_value, p_range_unit, "Pa")
            p_max = self.converter.convert_pressure_units(p_max_value, p_range_unit, "Pa")
            
            num_points = int(self.points_var.get())
            
            # 参数验证
            if Tc <= 0 or Pc <= 0 or T <= 0 or P <= 0 or p_min <= 0 or p_max <= 0 or p_min >= p_max:
                messagebox.showerror("参数错误", "所有温度、压力和临界参数必须为正值，且最小压力必须小于最大压力")
                return
            
            # 计算一系列压力点下的逸度和逸度系数
            pressures, fugacities, phi_values, z_values = self.converter.calculate_pressure_fugacity_points(
                T, Tc, Pc, omega, p_min, p_max, num_points
            )
            
            # 单点计算结果
            try:
                phi = self.converter.pr_equation.calculate_fugacity_coefficient(P, T, Tc, Pc, omega)
                fugacity = phi * P
                Z = self.converter.pr_equation.calculate_Z(P, T, Tc, Pc, omega)
                
                # 计算理想气体偏差
                ideal_gas_deviation = abs(Z - 1.0) * 100
                
                # 更新结果标签，考虑显示单位
                self.fugacity_var.set(f"{self.converter.convert_pressure_units(fugacity, 'Pa', p_unit):.4e} {p_unit}")
                self.phi_var.set(f"{phi:.4f}")
                self.z_var.set(f"{Z:.4f}")
                self.deviation_var.set(f"{ideal_gas_deviation:.4f}%")
                
                # 保存单点计算结果用于导出
                self.single_point_results = {
                    "物质名称": substance,
                    "温度 (K)": f"{T:.2f}",
                    "压力": f"{p_value} {p_unit}",
                    "临界温度 (K)": f"{Tc:.2f}",
                    "临界压力": f"{pc_value} {pc_unit}",
                    "偏心因子": f"{omega:.4f}",
                    "逸度 ({p_unit})": f"{self.converter.convert_pressure_units(fugacity, 'Pa', p_unit):.4e}",
                    "逸度系数": f"{phi:.4f}",
                    "压缩因子": f"{Z:.4f}",
                    "理想气体偏差 (%)": f"{ideal_gas_deviation:.4f}"
                }
            except Exception as e:
                messagebox.showerror("计算错误", f"单点计算失败: {str(e)}")
                return
            
            # 清除旧图
            self.ax1.clear()
            self.ax2.clear()
            
            # 绘制新图
            # 将Pa转换为用户选择的单位以便更好地显示
            display_unit = p_range_unit
            pressures_converted = [self.converter.convert_pressure_units(p, 'Pa', display_unit) for p in pressures]
            fugacities_converted = [self.converter.convert_pressure_units(f, 'Pa', display_unit) for f in fugacities]
            
            # 保存转换后的数据以便导出
            self.display_results = {
                f"压力 ({display_unit})": pressures_converted,
                f"逸度 ({display_unit})": fugacities_converted,
                "逸度系数": phi_values,
                "压缩因子": z_values
            }
            
            # 第一个子图：压力-逸度关系
            self.ax1.plot(pressures_converted, fugacities_converted, 'b-', linewidth=2)
            self.ax1.set_title(f"{substance} 的压力-逸度关系 (T = {T} K)")
            self.ax1.set_xlabel(f"压力 ({display_unit})")
            self.ax1.set_ylabel(f"逸度 ({display_unit})")
            self.ax1.grid(True)
            
            # 添加理想气体线 (f = P)
            ideal_line = [p for p in pressures_converted]
            self.ax1.plot(pressures_converted, ideal_line, 'r--', linewidth=1, label="理想气体")
            
            # 标记单点计算位置
            p_point = self.converter.convert_pressure_units(P, 'Pa', display_unit)
            f_point = self.converter.convert_pressure_units(fugacity, 'Pa', display_unit)
            self.ax1.plot([p_point], [f_point], 'go', markersize=7, label="计算点")
            self.ax1.legend()
            
            # 第二个子图：压力-逸度系数关系
            self.ax2.plot(pressures_converted, phi_values, 'g-', linewidth=2)
            self.ax2.set_title(f"{substance} 的压力-逸度系数关系 (T = {T} K)")
            self.ax2.set_xlabel(f"压力 ({display_unit})")
            self.ax2.set_ylabel("逸度系数 φ")
            self.ax2.grid(True)
            
            # 添加理想气体线 (phi = 1)
            ideal_phi = [1] * len(pressures_converted)
            self.ax2.plot(pressures_converted, ideal_phi, 'r--', linewidth=1, label="理想气体")
            
            # 标记单点计算位置
            self.ax2.plot([p_point], [phi], 'go', markersize=7, label="计算点")
            self.ax2.legend()
            
            # 调整布局并重绘
            self.fig.tight_layout()
            self.canvas.draw()
            
            # 设置鼠标悬停显示坐标功能
            self.setup_hover_info(self.ax1, "逸度", pressures_converted, fugacities_converted)
            self.setup_hover_info(self.ax2, "逸度系数", pressures_converted, phi_values)
            
        except ValueError as e:
            error_msg = str(e)
            if "could not convert string to float" in error_msg:
                messagebox.showerror("输入错误", "请确保所有输入都是有效的数字。")
            else:
                messagebox.showerror("计算错误", f"计算过程中出现错误：\n{str(e)}")
        except Exception as e:
            messagebox.showerror("程序错误", f"程序运行出错：\n{str(e)}")
    
    def setup_hover_info(self, ax, label, xdata, ydata):
        """设置鼠标悬停时显示坐标信息的功能"""
        # 创建用于显示坐标的annotation
        annot = ax.annotate("", xy=(0,0), xytext=(20,20),
                         textcoords="offset points",
                         bbox=dict(boxstyle="round", fc="yellow", alpha=0.7),
                         arrowprops=dict(arrowstyle="->"))
        annot.set_visible(False)
        
        # 保存数据和注释对象
        ax.plot_data = (xdata, ydata)
        ax.plot_label = label
        ax.plot_annot = annot
        
        # 连接事件处理函数
        self.fig.canvas.mpl_connect("motion_notify_event", lambda event: self.hover(event, ax))

    def hover(self, event, ax):
        """鼠标悬停事件处理函数"""
        # 确保鼠标在坐标轴内部
        if event.inaxes == ax:
            annot = ax.plot_annot
            xdata, ydata = ax.plot_data
            
            # 找到最近的数据点
            x, y = event.xdata, event.ydata
            distances = np.sqrt((xdata - x)**2)
            idx = np.argmin(distances)
            
            # 更新注释内容和位置
            annot.xy = (xdata[idx], ydata[idx])
            text = f"压力: {xdata[idx]:.4f}\n{ax.plot_label}: {ydata[idx]:.4e}"
            annot.set_text(text)
            annot.set_visible(True)
            
            # 重绘图表以显示注释
            self.fig.canvas.draw_idle()
        else:
            # 当鼠标移出坐标轴时隐藏注释
            if hasattr(ax, 'plot_annot'):
                ax.plot_annot.set_visible(False)
                self.fig.canvas.draw_idle()
    
    def show_help(self):
        """显示使用帮助信息"""
        help_text = """
逸度与压强转换工具使用说明:

1. 物质选择:
   - 可以从数据库中选择常见物质，自动加载物性参数
   - 也可以手动输入自定义物质的参数
  
2. 物性参数:
   - 临界温度 (K): 物质的临界温度
   - 临界压力: 物质的临界压力，支持不同单位
   - 偏心因子: 用于表征分子非球形程度的参数
  
3. 计算条件:
   - 温度 (K): 计算条件下的温度
   - 单点压力: 用于计算单点结果的压力
   - 压力范围: 用于绘制曲线的压力范围
   - 曲线点数: 曲线上的点数，增加点数可提高精度
  
4. 计算结果:
   - 逸度: 在给定条件下的逸度，与压力具有相同单位
   - 逸度系数: 逸度与压力之比，无量纲
   - 压缩因子: 描述实际气体与理想气体的偏差
   - 理想气体偏差: 压缩因子偏离1的百分比
  
5. 图表:
   - 上图: 压力-逸度关系曲线
   - 下图: 压力-逸度系数关系曲线
   - 红色虚线: 理想气体行为
   - 绿点: 单点计算位置
  
6. 特殊功能:
   - 计算原理与过程: 点击此按钮可查看详细的计算理论、公式和步骤说明
   - 导出数据: 可将计算结果导出为CSV或Excel格式
   - 悬停显示: 鼠标悬停在图表上可显示坐标值
  
注意:
  - 所有温度必须为正值
  - 所有压力必须为正值
  - PR状态方程适用于大多数气体，但对于强极性分子精度可能降低
        """
        messagebox.showinfo("使用帮助", help_text)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """
逸度与压强转换可视化工具
版本: 1.0.0

作者: 材料模拟路漫漫&端木鹏博 (<EMAIL>)

基于Python和Peng-Robinson状态方程实现
用于计算实际气体的逸度、逸度系数和压缩因子

© 2023 材料模拟路漫漫&端木鹏博
        """
        messagebox.showinfo("关于", about_text)

    def check_parameters_display(self):
        """检查并更新物性参数显示，确保界面显示正确的物性参数"""
        # 检查当前模式
        mode = self.substance_selection.get()
        
        # 如果是数据库模式，重新检查物质参数
        if mode == "database":
            substance = self.substance_var.get()
            properties = self.converter.get_substance_properties(substance)
            if properties:
                # 直接强制更新参数，不进行比较，确保显示正确
                self.tc_var.set(properties["Tc"])
                
                pc_unit = self.pc_unit_combobox.get()
                pc_pa = properties["Pc"]
                
                if pc_unit == "MPa":
                    self.pc_var.set(pc_pa / 1e6)
                elif pc_unit == "bar":
                    self.pc_var.set(pc_pa / BAR_TO_PA)
                elif pc_unit == "atm":
                    self.pc_var.set(pc_pa / ATM_TO_PA)
                else:  # Pa
                    self.pc_var.set(pc_pa)
                
                self.omega_var.set(properties["omega"])
                
                print(f"强制更新物质 '{substance}' 的物性参数显示:")
                print(f"临界温度: {properties['Tc']} K")
                print(f"临界压力: {pc_pa} Pa ({self.pc_var.get()} {pc_unit})")
                print(f"偏心因子: {properties['omega']}")
                
                # 强制更新界面显示
                self.update()
                
        # 更新图表，确保显示的是正确的计算结果
        self.update_plots()

    def show_calculation_theory(self):
        """显示计算原理和详细过程"""
        # 创建新窗口
        theory_window = tk.Toplevel(self)
        theory_window.title("逸度计算原理与过程详解")
        theory_window.geometry("800x700")
        theory_window.configure(bg="#f5f5f5")
        
        # 创建可滚动文本区域
        frame = ttk.Frame(theory_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 创建文本区域
        text = tk.Text(frame, wrap=tk.WORD, font=("SimSun", 11), 
                      yscrollcommand=scrollbar.set, bg="#ffffff", padx=10, pady=10)
        text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=text.yview)
        
        # 定义标签样式
        text.tag_configure("title", font=("SimHei", 14, "bold"), foreground="#003366", 
                           spacing1=10, spacing3=10, justify=tk.CENTER)
        text.tag_configure("subtitle", font=("SimHei", 12, "bold"), foreground="#1a1a1a", 
                           spacing1=6, spacing3=2)
        text.tag_configure("equation", font=("SimSun", 11), foreground="#000066", 
                           spacing1=5, spacing3=5, justify=tk.CENTER)
        text.tag_configure("normal", font=("SimSun", 11))
        text.tag_configure("note", font=("SimSun", 10, "italic"), foreground="#666666")
        
        # 添加内容
        text.insert(tk.END, "逸度计算原理与过程详细说明\n", "title")
        
        # 逸度基本概念
        text.insert(tk.END, "\n1. 逸度与逸度系数基本概念\n", "subtitle")
        text.insert(tk.END, """
逸度(Fugacity)是一个热力学概念，用来描述非理想气体的行为。在理想气体中，化学势由压力决定；而在实际气体中，化学势则由逸度决定。

逸度系数(φ)定义为逸度与压力的比值：
""", "normal")
        text.insert(tk.END, "\nφ = f / P\n", "equation")
        text.insert(tk.END, """
其中：
f = 逸度 (Pa)
P = 压力 (Pa)
φ = 逸度系数 (无量纲)

对于理想气体，逸度系数等于1（即逸度等于压力）。对于实际气体，逸度系数通常不等于1，这反映了实际气体与理想气体的偏差。
""", "normal")
        
        # PR状态方程
        text.insert(tk.END, "\n2. Peng-Robinson (PR) 状态方程\n", "subtitle")
        text.insert(tk.END, """
Peng-Robinson方程是一种立方型状态方程，用于描述实际气体的行为。PR方程的一般形式为：
""", "normal")
        text.insert(tk.END, """
P = RT/(V-b) - a(T)/(V(V+b)+b(V-b))

或表示为压缩因子Z的形式：

Z³ - (1-B)Z² + (A-3B²-2B)Z - (AB-B²-B³) = 0
""", "equation")
        text.insert(tk.END, """
其中：
Z = 压缩因子 = PV/(RT)
A = a(T)P/(RT)²
B = bP/(RT)

参数a和b与物质的临界性质有关：
a = 0.45724·(R²Tc²)/Pc
b = 0.07780·(RTc)/Pc

α(T)为温度相关函数：
α(T) = [1 + κ(1-√(T/Tc))]²

其中κ与物质的偏心因子ω有关：
κ = 0.37464 + 1.54226ω - 0.26992ω² (对于ω≤0.491)
κ = 0.379642 + 1.48503ω - 0.164423ω² + 0.016666ω³ (对于ω>0.491)
""", "normal")
        
        # 计算压缩因子Z
        text.insert(tk.END, "\n3. 计算压缩因子Z的过程\n", "subtitle")
        text.insert(tk.END, """
计算压缩因子Z是PR方程应用的关键步骤：

① 根据物质的临界温度Tc、临界压力Pc和偏心因子ω，计算参数a和b
② 计算α(T)函数值
③ 计算无量纲参数A和B
④ 求解立方方程以得到Z值
⑤ 如果方程有多个实根，选择最符合热力学稳定性的根

对于本程序，当计算得到多个实根时：
- 对于超临界状态(T>Tc)，通常选择最大的实根
- 对于亚临界状态(T<Tc)，根据压力选择不同的根
- 在某些情况下，通过计算吉布斯自由能最小值来选择最稳定的根
""", "normal")
        
        # 逸度系数计算
        text.insert(tk.END, "\n4. 逸度系数的精确计算\n", "subtitle")
        text.insert(tk.END, """
基于PR状态方程，逸度系数的精确计算公式为：
""", "normal")
        text.insert(tk.END, """
ln(φ) = (Z-1) - ln(Z-B) - A/(2√2·B)·ln[(Z+(1+√2)B)/(Z+(1-√2)B)]
""", "equation")
        text.insert(tk.END, """
这个公式考虑了实际气体与理想气体的偏差，可以在不同温度和压力条件下提供准确的逸度系数。
""", "normal")
        
        # 具体计算步骤
        text.insert(tk.END, "\n5. 本程序的计算步骤详解\n", "subtitle")
        text.insert(tk.END, """
当用户提供物质参数和计算条件后，程序按以下步骤进行计算：

① 首先获取物质的临界参数(Tc, Pc, ω)和计算条件(T, P)
② 计算PR方程的参数a、b和α(T)
③ 计算无量纲参数A和B
④ 求解立方方程得到压缩因子Z
   a. 构建立方方程系数
   b. 使用纯Python数值方法求解方程
   c. 根据热力学稳定性选择适当的根
⑤ 使用选定的Z值计算逸度系数φ
⑥ 计算逸度f = φ·P
⑦ 计算理想气体偏差 = |Z-1|×100%
⑧ 返回并显示计算结果

程序中使用了多种数值优化技术，包括：
- 改进的三次方程求解算法
- 健壮的根选择方法
- 防止数值不稳定的保护措施
- 自适应迭代的逸度求解方法
""", "normal")
        
        # 理想气体偏差
        text.insert(tk.END, "\n6. 理想气体偏差的计算\n", "subtitle")
        text.insert(tk.END, """
理想气体偏差是指实际气体与理想气体行为的差异程度，通过压缩因子Z偏离1的程度来衡量：
""", "normal")
        text.insert(tk.END, """
理想气体偏差(%) = |Z - 1.0| × 100%
""", "equation")
        text.insert(tk.END, """
理想气体的Z值恒等于1，因此理想气体偏差为0%。
实际气体的Z值通常不等于1：
- Z > 1 表示气体分子间的排斥力占主导
- Z < 1 表示气体分子间的引力占主导

偏差越大，表明实际气体行为越偏离理想气体行为，使用理想气体定律的误差就越大。
""", "normal")
        
        # 图表说明
        text.insert(tk.END, "\n7. 逸度-压力和逸度系数-压力图表说明\n", "subtitle")
        text.insert(tk.END, """
程序绘制的两个图表展示了逸度和逸度系数与压力的关系：

上图(逸度-压力关系)：
- 蓝色实线表示实际气体的逸度随压力变化曲线
- 红色虚线表示理想气体的逸度(等于压力)
- 两条线的差距表示实际气体与理想气体的偏差
- 绿色点表示当前计算点

下图(逸度系数-压力关系)：
- 绿色实线表示逸度系数随压力变化曲线
- 红色虚线表示理想气体的逸度系数(恒等于1)
- 曲线形状反映了不同压力下实际气体的非理想性
- 绿色点表示当前计算点

通过观察这两个图表，可以直观地了解实际气体行为与理想气体的偏差程度。
""", "normal")
        
        # 参考文献
        text.insert(tk.END, "\n8. 理论基础与参考文献\n", "subtitle")
        text.insert(tk.END, """
① Peng, D. Y.; Robinson, D. B. (1976). "A New Two-Constant Equation of State". Industrial & Engineering Chemistry Fundamentals. 15 (1): 59–64.

② Smith, J. M.; Van Ness, H. C.; Abbott, M. M. "Introduction to Chemical Engineering Thermodynamics", 7th ed.; McGraw-Hill: New York, 2005.

③ Poling, B. E.; Prausnitz, J. M.; O'Connell, J. P. "The Properties of Gases and Liquids", 5th ed.; McGraw-Hill: New York, 2001.

④ Sandler, S. I. "Chemical, Biochemical, and Engineering Thermodynamics", 4th ed.; John Wiley & Sons: New York, 2006.
""", "normal")
        
        # 使文本框只读
        text.config(state=tk.DISABLED)
        
        # 确保窗口置于前台
        theory_window.lift()
        theory_window.focus_force()


def check_environment():
    """
    检查运行环境，返回可用的界面类型
    
    返回:
    str: "gui" 或 "console"
    """
    # 检查是否有图形界面库
    if not TKINTER_AVAILABLE or not MATPLOTLIB_AVAILABLE:
        return "console"
    
    # 尝试创建一个Tk对象测试GUI是否可用
    root = None
    try:
        # 使用root变量作为显式引用，确保后面可以销毁它
        root = tk.Tk()
        # 隐藏窗口，避免闪烁
        root.withdraw()
        # 如果到这里没有异常，GUI环境可用
        return "gui"
    except Exception:
        return "console"
    finally:
        # 确保在任何情况下都尝试销毁测试窗口
        if root:
            try:
                root.destroy()
                # 尝试刷新事件循环确保销毁完成
                if hasattr(root, 'update'):
                    root.update()
            except:
                pass  # 忽略销毁过程中的任何错误


def main():
    """主函数：选择是使用命令行界面还是图形界面"""
    print("初始化逸度与压强转换工具...")
    print("检查运行环境...")
    
    interface_type = check_environment()
    
    if interface_type == "gui":
        print("启动图形界面...\n")
        try:
            # 防止tkinter在导入时创建隐藏的根窗口
            tk._default_root = None
            
            # 设置样式主题，如果系统支持
            if hasattr(ttk, 'Style'):
                style = ttk.Style()
                try:
                    style.theme_use('clam')  # 使用更现代的主题
                except Exception:
                    pass  # 如果主题不可用，忽略错误
            
            # 创建并运行图形界面
            visualizer = FugacityVisualizer()
            visualizer.mainloop()
        except Exception as e:
            print(f"启动图形界面时出错: {e}")
            print("回退到命令行界面...")
            main_console()
    else:
        print("图形界面不可用，使用命令行界面...\n")
        main_console()


if __name__ == "__main__":
    main()
