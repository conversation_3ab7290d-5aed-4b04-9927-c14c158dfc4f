模块一：分子动力学基础与润滑油应用场景
目标：掌握分子动力学原理及其在润滑油研发中的适用性
内容：

理论部分
分子动力学（MD）基本原理：力场选择（COMPASS、PCFF等）、积分算法、周期性边界条件
润滑油关键性能指标（摩擦系数、粘度、极压性）与分子模拟的关联性
润滑油典型体系建模：基础油（PAO/矿物油）、添加剂（ZDDP、MoDTC）、金属表面界面
应用场景
添加剂分子在金属表面的吸附行为模拟
不同温度/压力下油膜形成与失效机制分析
润滑油混合体系相容性预测（如不同添加剂在基础油中的分散性）


模块二：Materials Studio软件操作实战
目标：掌握MS Forcite模块在润滑油模拟中的操作流程
内容：

建模与参数设置
基础油分子构建（如PAO链状分子）
金属表面（Fe、Al）晶面建模与表面修饰
添加剂分子与金属界面的初始构型优化
模拟计算
能量最小化与NVT/NPT系综选择
剪切模拟设置（模拟边界滑移行为）
关键参数：模拟时长（1-10 ns）、步长（1 fs）、温度控制（Langevin/ Berendsen）
结果分析
摩擦系数计算：剪切力与法向力关系分析
RDF（径向分布函数）判断分子聚集状态
扩散系数计算评估润滑油流动性
