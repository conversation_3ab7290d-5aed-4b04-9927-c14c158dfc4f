
以下内容基于 Materials Studio 2024《CASTEP GUIDE》和 CASTEP 经典工作流程整理，给出在 CASTEP 模块中通过密度泛函理论（DFT）计算晶体电导率（electrical conductivity）的完整、专业步骤。相关章节页码均来自官方 PDF 手册 [CASTEP GUIDE]。

--------------------------------------------------------------------
一、计算原理概述  
1. CASTEP 直接提供的电学性质为介电函数 ε(ω)（实部 ε₁ 与虚部 ε₂）。  
2. 光学导率 σ(ω) 与 ε₂(ω) 的关系：  
   σ(ω)=ω·ε₂(ω)·ε₀  (ε₀ 为真空介电常数)。  
3. 低频极限 (ω→0) 的 σ(0) 可作为直流电导率近似；若需温度依赖或各向异性，可结合 Boltzmann 输运（BoltzTraP）或 Kubo-Greenwood 后处理。  

--------------------------------------------------------------------
二、前期准备  
1. **结构模型**  
   • 导入 CIF / POSCAR / Materials Studio 原生文件，检查化学计量和占位。  
   • 对 2D 材料或表面模型确保真空层≥15 Å。  
2. **选择赝势与交换-相关泛函**  
   • 推荐 Ultrasoft / norm-conserving OTFG 赝势（手册 p17）。  
   • GGA-PBEsol 或 PBE 用于常规体系；半导体带隙要求高时可用 HSE06（Hybrid 功能需额外许可）。  
3. **k-点与能量截断测试**  
   • 先做 6 × 6 × 6 及以上网格收敛性测试；金属体系至少 12 × 12 × 12。  
   • 能量截断 E_cut 建议 ≥ 1.3× 最高赝势推荐值。  

--------------------------------------------------------------------
三、详细计算流程  
1. **几何优化（Tasks → Geometry Optimization）**     
   参数关键点：  
   • 精度 Quality 选 Fine 以上；  
   • SCF 收敛阈值 1 × 10⁻⁶ eV；  
   • Forces < 0.01 eV/Å；  
   • 对于金属体系启用 Fermi-Dirac/Gaussian Smearing，宽度 0.1 eV（手册 p22）。  

2. **电子结构单点计算（Tasks → Energy）**     
   • 读取已优化结构；  
   • 将 k-点提高到目标密度；  
   • 设置 Bands → Number of empty bands ≥ 2~3 × 价带数（保证光学跃迁收敛）；  
   • Output → 写出 .check / .castep_bin 供后续使用。  

3. **介电函数/光学性质计算（Tasks → Properties → Optical Properties）**     
   对应手册章节：Requesting optical properties (DFT) p45、Dielectric function calculation p72。  
   关键设置：  
   a) Frequency range（Energy range）0–20 eV（或按需求扩大）；  
   b) Polarization directions：选择 xx/yy/zz 或自动各向异性平均；  
   c) Smearing width 0.1–0.2 eV；  
   d) Empty bands 同第 2 步；  
   e) Convergence tolerance 1 × 10⁻⁵ eV；  
   运行后获得 *.epsilon* 与 *.castep_bin。  

4. **提取电导率**     
   • 打开 Analysis → Optical Constants，导出介电函数数据 (ε₁, ε₂)。  
   • 使用 Python/Excel 计算：  
      ω(rad/s)=E(eV)·e/ħ，  
      σ(ω)=ω·ε₂·ε₀。  
   • 取 ω→0 附近的 σ 值作为直流电导率 σ_dc；若图形平滑，可用 0–0.01 eV 区间线性外推。  

--------------------------------------------------------------------
四、进阶：Boltzmann 输运法  
1. 在单点计算时额外勾选 “Write BANDS file”（手册 p88），生成 *.bands。  
2. 使用 BoltzTraP2（或 Materials Studio Transport 模块）读取 *.bands + *.cell：  
   • 设定温度 T 与弛豫时间 τ；  
   • 得到 σ/τ ( T ) → 若测得实验 σ，可反推出 τ。  
3. BoltzTraP 计算需更密集 k-点：通常 20 × 20 × 20 以上。  

--------------------------------------------------------------------
五、关键数值精度注意  
| 项目 | 建议值 | 备注 |  
|------|--------|------|  
| k-点密度 | 金属 ≥ 12³；半导体 ≥ 8³ | 收敛到 ε₂ < 2% 波动 |  
| Empty bands | ≥ 2–3 × 价电子带数 | 保证 20 eV 内跃迁完整 |  
| Smearing | 0.1–0.2 eV | 金属偏小、半导体偏大 |  
| E_cut | ≥ 500 eV（常见主族元素） | 按最硬赝势调高 |  
| SCF tol. | 1 × 10⁻⁶ eV | 光学计算要求高 |  

--------------------------------------------------------------------
六、结果验证与常见问题  
1. **收敛性**：逐步提高 k-点与 Empty bands，确认 σ(ω) 曲线不再变化。  
2. **金属 Drude 峰**：若 ε₂(ω) 在低能出现尖峰，需更小 smearing 或使用 Drude 拟合分离自由载流子贡献。  
3. **半导体带隙误差**：GGA 下带隙低估 → σ(ω) 起始能偏低；可改用 HSE06 或做 scissor 校正。  
4. **弛豫时间 τ**：DFT 不能直接给出；若需绝对 σ_dc，需依实验或声子散射计算估计 τ。  

--------------------------------------------------------------------
七、总结流程图  
结构导入 → 几何优化 → 高密度 SCF 单点 → Optical Properties 介电函数 → 公式 σ(ω)=ω·ε₂·ε₀ → 得到 σ(ω) 曲线与 σ_dc  
  └─►（可选）写 .bands → BoltzTraP → 温度依赖 σ(T)  

按照上述步骤便可在 CASTEP 内完成从 DFT 出发的电导率计算，并获得可与实验对照的 σ_dc 与光学导率谱。


需要给予的数据：
要让脚本／程序给出 **定量** 的 \(\sigma(\omega)\) 曲线及外推直流电导率 \(\sigma_{dc}\)，用户只需把 Materials Studio-CASTEP 已算好的“光学性质”结果导出即可，具体应提供下列文件（或其中一种）：

1. **CASTEP 原始输出文件**  
   • `<job>.epsilon` — 光学常数文件，内含列：  
  Energy (eV)、ε₁、ε₂。  
   这是 CASTEP 自动生成、脚本可直接读取的首选文件。

2. **手动导出的表格**（若想用 Excel/CSV）  
   在 Materials Studio → Analysis → Optical Constants 面板中：  
   • 选中 *ε₂* 列，点击 *Export*，保存为 `.csv` / `.xlsx`。  
   要求表格至少包含两列：  
     ① Energy (eV) ② ε₂(ω)  
   如果同时导出了 ε₁ 或重复的 Energy 列也无妨，脚本会自动识别。

3. **如需计算温度依赖或各向异性 σ(T)**（BoltzTraP 路径）：  
   额外提供  
   • `<job>.bands` • `<job>.cell`  
   之后用 BoltzTraP2 得到 σ/τ，再结合实验或声子散射给出的弛豫时间 τ 即可获得绝对电导率。

---

因此，**最小化输入** 就是把 `<job>.epsilon` 或导出的 **Energy + ε₂** 表格发给脚本；其余参数（ε₀、ħ 等物理常数与公式）由程序内部处理。
