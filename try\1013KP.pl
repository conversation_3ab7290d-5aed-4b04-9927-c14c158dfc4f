#!perl
use strict;
use Getopt::Long;
use MaterialsScript qw(:all);
my $doc = $Documents{"1013KP.xsd"};               #NAME HERE
my $newStudyTable = Documents->New("jieguo.std");
my $calcSheet = $newStudyTable->ActiveSheet;
$calcSheet->ColumnHeading(0) = "original structure";
$calcSheet->ColumnHeading(1) = "original atom number";
$calcSheet->ColumnHeading(2) = "C_33 Carbon number";
$calcSheet->ColumnHeading(3) = "C_R2 Carbon number";
$calcSheet->ColumnHeading(4) = "C_R1 Carbon number";
$calcSheet->ColumnHeading(5) = "H_ Hydrogen number";
$calcSheet->ColumnHeading(7) = "H_delete atom number";
$calcSheet->ColumnHeading(8) = "H_delete_6A";
$calcSheet->ColumnHeading(10) = "closecontact_num_6A";
$calcSheet->ColumnHeading(11) = "C_33 delete_6A";
$calcSheet->ColumnHeading(12) = "C_R2 delete_6A";
$calcSheet->ColumnHeading(13) = "C_22 delete_6A";
$calcSheet->ColumnHeading(14) = "Carbon_delete_6A";
$calcSheet->ColumnHeading(16) = "closecontact_num_3A";
$calcSheet->ColumnHeading(17) = "c_R1 delete_3A";
$calcSheet->ColumnHeading(18) = "Carbon_delete_3A";
$calcSheet->ColumnHeading(20) = "new structure carbon atom num";
$calcSheet->ColumnHeading(21) = "new structure hydrogen atom num";
$calcSheet->ColumnHeading(22) = "new structure total atom num";
$calcSheet->ColumnHeading(23) = "new structure";
Modules->Forcite->ChangeSettings([Ensemble3D => "NVT",
	Temperature =>1273,                        #temperature
	Thermostat =>"Andersen", 
	CurrentForcefield => 'Dreiding', 
	Quality=>"Coarse",
	ChargeAssignment => 'Use current', 
	NumberOfSteps => 1500,
	EnergyDeviation=>900000000000000000000000.0,
	"3DPeriodicElectrostaticSummationMethod"=>"Atom based",
	"****************************"=>"Atom based"]);
for (my $counter = 1; $counter <= 1500; ++$counter)                            #step
{my $doc = $Documents{"1013KP.xsd"};                           #NAME HERE
my $opt=Modules->Forcite->GeometryOptimization->Run($doc);
my $resultsDoc = $opt->Structure; 
my $results = Modules->Forcite->Dynamics->Run($resultsDoc);
my $structure=$results->Structure;
$calcSheet->Cell($counter-1,0) = $structure;
my $x2 = $Documents{"1013KP.xtd"};                            #NAME HERE
$x2->Delete;
my $x5 = $Documents{"Status.txt"};
$x5->Delete;
# -------------------------------------------------------
# count the atom number in structure
my $carbon=0;
my $C_33=0;
my $C_R2=0;
my $C_R1=0;
my $C_R=0;
my $H_=0;
my @atoms;
my @oxygen;
foreach my $atom(@{$structure->UnitCell->Atoms})
{if($atom->ElementSymbol eq 'C')
{push(@atoms, $atom);
$carbon+=1;
if($atom->ForcefieldType eq "C_33")
{$C_33+=1;}
if($atom->ForcefieldType eq "C_R2")
{$C_R2+=1;}
if($atom->ForcefieldType eq "C_R1")
{$C_R1+=1;}
if($atom->ForcefieldType eq "C_R")
{$C_R+=1;}}
if($atom->ElementSymbol eq 'O')
{push(@oxygen, $atom);}
if($atom->ElementSymbol eq 'H')
{push(@atoms, $atom);
$H_+=1;}}
$calcSheet->Cell($counter-1,1) = $carbon+$H_;
$calcSheet->Cell($counter-1,2) = $C_33;
$calcSheet->Cell($counter-1,3) = $C_R2;
$calcSheet->Cell($counter-1,4) = $C_R1;
$calcSheet->Cell($counter-1,5) = $H_;
$doc->CreateSet("coal", \@atoms);
my $csets=$doc->UnitCell->Sets("coal");
$csets->IsVisible="No";
$doc->CreateSet("oxygen", \@oxygen);
my $osets=$doc->UnitCell->Sets("oxygen");
$osets->IsVisible="No";
#--------------------------------------------------------- delete H_
$structure->CalculateCloseContacts(Settings(DistanceCriterionMode => "Absolute", 
MaxAbsoluteDistance => 6,                                                       #distance
MaxRadiusFactor => 0.7, 
RadiusType => "VDW", 
ExclusionMode => "Set", 
PathLength => 2));
my $H_delete=0;
my $numclosecontacts_1=0;
my $closecontacts=$structure->UnitCell->CloseContacts;
foreach my $closecontact(@$closecontacts)
{$numclosecontacts_1+=1;
my $val1=$closecontact->Atom1;
my $val2=$closecontact->Atom2;
if ($val1->ElementSymbol eq "O")
{if($val2->ForcefieldType eq "H_")
{$val2->ElementSymbol = "Si";
#print "H_\n";
$H_delete+=1;}}
if ($val2->ElementSymbol eq "O")
{if($val1->ForcefieldType eq "H_")
{$val1->ElementSymbol = "Si";
$H_delete+=1;}}}
$calcSheet->Cell($counter-1,7) =$H_delete; 
$structure->UnitCell->CloseContacts->Delete;
$calcSheet->Cell($counter-1,8) = $structure;
my $deletealtom= $calcSheet->Cell($counter-1,8);
my $ats = $deletealtom->UnitCell->Atoms;
foreach my $at (@$ats)
{unless (($at->ElementSymbol eq "Si"))
{$at->Delete;}}
$calcSheet->Cell($counter-1,8) = $deletealtom;
#$structure->UnbuildCrystal;# delete the "Si"
my $atoms = $structure->UnitCell->Atoms;
foreach my $atom (@$atoms)
{if ($atom->ElementSymbol eq "Si")
{$atom->Delete;}}
#--------------------------------------------------------- delete C_33, C_R2
$structure->CalculateCloseContacts(Settings(DistanceCriterionMode => "Absolute", 
MaxAbsoluteDistance => 6,                                                                   #distance
MaxRadiusFactor => 0.7, 
RadiusType => "VDW", 
ExclusionMode => "Set", 
PathLength => 2));
my $C_33=0;
my $C_R2=0;
my $C_22=0; 
my $numclosecontacts_2=0;
my $closecontacts=$structure->UnitCell->CloseContacts;
foreach my $closecontact(@$closecontacts)
{$numclosecontacts_2+=1;
my $val1=$closecontact->Atom1;
my $val2=$closecontact->Atom2;
if($val1->ElementSymbol eq "O") 
{if($val2->ForcefieldType eq "C_33")
{$val2->ElementSymbol = "Si";
$C_33+=1;}
if($val2->ForcefieldType eq "C_R2")
{$val2->ElementSymbol = "Si";
$C_R2+=1;}
if($val2->ForcefieldType eq "C_22")
{$val2->ElementSymbol = "Si";
$C_22+=1;}}
if($val2->ElementSymbol eq "O") 
{if($val1->ForcefieldType eq "C_33")
{$val1->ElementSymbol = "Si";
$C_33+=1;}
if($val1->ForcefieldType eq "C_R2")
{$val1->ElementSymbol = "Si";
$C_R2+=1;}
if($val1->ForcefieldType eq "C_22")
{$val1->ElementSymbol = "Si";
$C_22+=1;}}}
$calcSheet->Cell($counter-1,10) =$C_33+$C_R2; 
$calcSheet->Cell($counter-1,11) =$C_33;
$calcSheet->Cell($counter-1,12) =$C_R2;
$calcSheet->Cell($counter-1,13) =$C_22;
$structure->UnitCell->CloseContacts->Delete;
$calcSheet->Cell($counter-1,14) = $structure;
my $deletealtom= $calcSheet->Cell($counter-1,14);
my $ats = $deletealtom->UnitCell->Atoms;
foreach my $at (@$ats)
{unless ($at->ElementSymbol eq "Si")
{$at->Delete;}}
$calcSheet->Cell($counter-1,14) = $deletealtom;
#$structure->UnbuildCrystal;
# delete the "Si"
my $atoms = $structure->UnitCell->Atoms;
foreach my $atom (@$atoms)
{if ($atom->ElementSymbol eq "Si")
{$atom->Delete;}}
#-------------------------------------------------------------------------delete C_R1
$structure->CalculateCloseContacts(Settings(DistanceCriterionMode => "Absolute", 
MaxAbsoluteDistance => 3.2,                                                            #distance
MaxRadiusFactor => 0.7, 
RadiusType => "VDW", 
ExclusionMode => "Set", 
PathLength => 2));
my $numclosecontacts_3=0;
my $C_R1=0;
my $closecontacts=$structure->UnitCell->CloseContacts;
foreach my $closecontact(@$closecontacts)
{$numclosecontacts_3+=1;
my $val1=$closecontact->Atom1;
my $val2=$closecontact->Atom2;
if ($val1->ElementSymbol eq "O")
{if($val2->ForcefieldType eq "C_R1")
{$val2->ElementSymbol = "Si";
$C_R1+=1;}}
if ($val2->ElementSymbol eq "O")
{if($val1->ForcefieldType eq "C_R1")
{$val1->ElementSymbol = "Si";
$C_R1+=1;}}}
$calcSheet->Cell($counter-1,16) =$C_R1;
$calcSheet->Cell($counter-1,16) =$C_R1;
$structure->UnitCell->CloseContacts->Delete;
$csets->Delete;
$osets->Delete;
$calcSheet->Cell($counter-1,18) = $structure;
my $deletealtom= $calcSheet->Cell($counter-1,18);
my $ats = $deletealtom->UnitCell->Atoms;
foreach my $at (@$ats)
{unless ($at->ElementSymbol eq "Si")
{$at->Delete;}}
$calcSheet->Cell($counter-1,18) = $deletealtom;
#$structure->UnbuildCrystal;
# delete the "Si"
my $stelc=0;
my $stelh=0;
#calculate the carbon number of new structure 
my $atoms = $structure->UnitCell->Atoms;
foreach my $atom (@$atoms)
{if($atom->ElementSymbol eq "H")
{$stelh+=1;}
if ($atom->ElementSymbol eq "C")
{$stelc+=1;}
if ($atom->ElementSymbol eq "Si")
{$atom->Delete;}}
$calcSheet->Cell($counter-1,20) = $stelc;
$calcSheet->Cell($counter-1,21) = $stelh;
$calcSheet->Cell($counter-1,22) = $stelc+$stelh;
$calcSheet->Cell($counter-1,23) = $structure;
$doc->Close;}
