<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CIF晶格转换工具</title>
    <style>
        /* 全局样式 */
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        .file-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
            margin-right: 10px;
        }
        .checkbox-item input {
            margin-right: 5px;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        
        /* 消息和信息框样式 */
        .error-message {
            color: #e74c3c;
            background-color: #fadbd8;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success-message {
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .info-box {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            font-size: 14px;
            line-height: 1.5;
        }
        
        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        
        /* 结果区域和文件列表 */
        .result-area {
            display: none;
            margin-top: 30px;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .file-list {
            margin: 20px 0;
        }
        .file-item {
            background-color: #f8f9fa;
            border-left: 3px solid #3498db;
            padding: 10px 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .file-name {
            flex-grow: 1;
            font-family: Consolas, monospace;
        }
        .file-actions {
            display: flex;
            gap: 10px;
        }
        .btn {
            display: inline-block;
            padding: 6px 12px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            color: white;
            cursor: pointer;
            background-color: #3498db;
        }
        
        /* 加载指示器 */
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
            display: none;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 文件内容查看 */
        .file-content-view {
            display: none;
            margin-top: 20px;
        }
        .file-content {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: Consolas, monospace;
            white-space: pre-wrap;
            overflow-x: auto;
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #ddd;
        }
        .back-button {
            background-color: #95a5a6;
            margin-top: 10px;
        }
        .back-button:hover {
            background-color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CIF晶格转换工具</h1>
        
        <!-- 上传表单 -->
        <div id="upload-form">
            <div id="error-message" class="error-message" style="display: none;"></div>
            
            <div class="form-group">
                <label for="cifFile">选择CIF文件：</label>
                <input type="file" id="cifFile" accept=".cif" class="file-input" required>
            </div>
            
            <div class="form-group">
                <label>选择要转换的晶面指数：</label>
                <small>（不选则处理全部晶面）</small>
                <div class="checkbox-group" id="planes-group">
                    <!-- 晶面选择框将由JavaScript动态填充 -->
                </div>
            </div>
            
            <div class="form-group">
                <label for="prefix">输出文件名前缀（可选）：</label>
                <input type="text" id="prefix" placeholder="例如：trans_">
            </div>
            
            <div class="form-group">
                <label for="suffix">输出文件名后缀（可选）：</label>
                <input type="text" id="suffix" placeholder="例如：_ortho">
            </div>
            
            <div class="form-group">
                <button id="convert-button" type="button">开始转换</button>
            </div>
            
            <div class="loader" id="loader"></div>
            
            <div class="info-box">
                <h3>工具说明</h3>
                <p>本工具用于批量将CIF文件中的晶格进行转换，使垂直于001的面变为原晶胞的特定晶面指数，同时保持坐标轴正交。</p>
                
                <h4>支持的晶面指数</h4>
                <ul>
                    <li>100、010</li>
                    <li>101、-101</li>
                    <li>110、-110</li>
                    <li>011、0-11</li>
                </ul>
                
                <h4>转换参考</h4>
                <table>
                    <tr>
                        <th>目标晶面</th>
                        <th>新坐标轴A</th>
                        <th>新坐标轴B</th>
                        <th>新坐标轴C</th>
                    </tr>
                    <tr>
                        <td>010</td>
                        <td>[0, 0, 1]</td>
                        <td>[1, 0, 0]</td>
                        <td>[0, 1, 0]</td>
                    </tr>
                    <tr>
                        <td>100</td>
                        <td>[0, 1, 0]</td>
                        <td>[0, 0, 1]</td>
                        <td>[1, 0, 0]</td>
                    </tr>
                    <tr>
                        <td>101</td>
                        <td>[1, 0, -1]</td>
                        <td>[0, 1, 0]</td>
                        <td>[1, 0, 1]</td>
                    </tr>
                    <tr>
                        <td>-101</td>
                        <td>[1, 0, 1]</td>
                        <td>[0, 1, 0]</td>
                        <td>[1, 0, -1]</td>
                    </tr>
                    <tr>
                        <td>110</td>
                        <td>[-1, 1, 0]</td>
                        <td>[0, 0, 1]</td>
                        <td>[1, 1, 0]</td>
                    </tr>
                    <tr>
                        <td>-110</td>
                        <td>[1, 1, 0]</td>
                        <td>[0, 0, 1]</td>
                        <td>[-1, 1, 0]</td>
                    </tr>
                    <tr>
                        <td>011</td>
                        <td>[1, 0, 0]</td>
                        <td>[0, -1, 1]</td>
                        <td>[0, 1, 1]</td>
                    </tr>
                    <tr>
                        <td>0-11</td>
                        <td>[1, 0, 0]</td>
                        <td>[0, 1, 1]</td>
                        <td>[0, -1, 1]</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- 结果区域 -->
        <div id="result-area" class="result-area">
            <div class="success-message" id="success-message"></div>
            
            <div class="file-list" id="file-list">
                <!-- 转换后的文件列表将由JavaScript动态填充 -->
            </div>
            
            <button id="back-button" class="back-button">返回</button>
        </div>
        
        <!-- 文件内容查看区域 -->
        <div id="file-content-view" class="file-content-view">
            <h2 id="file-view-title"></h2>
            
            <div class="file-content" id="file-content"></div>
            
            <button id="back-to-list-button" class="back-button">返回文件列表</button>
        </div>
    </div>

    <script>
        // 定义晶面转换配置
        const PLANE_CONFIGS = {
            // 晶面: [A轴, B轴, C轴]
            "010": [[0, 0, 1], [1, 0, 0], [0, 1, 0]],
            "100": [[0, 1, 0], [0, 0, 1], [1, 0, 0]],
            "101": [[1, 0, -1], [0, 1, 0], [1, 0, 1]],
            "-101": [[1, 0, 1], [0, 1, 0], [1, 0, -1]],
            "110": [[-1, 1, 0], [0, 0, 1], [1, 1, 0]],
            "-110": [[1, 1, 0], [0, 0, 1], [-1, 1, 0]],
            "011": [[1, 0, 0], [0, -1, 1], [0, 1, 1]],
            "0-11": [[1, 0, 0], [0, 1, 1], [0, -1, 1]]
        };
        
        // 全局变量
        let convertedFiles = []; // 存储转换后的文件
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 填充晶面选择框
            const planesGroup = document.getElementById('planes-group');
            for (const plane in PLANE_CONFIGS) {
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = `plane-${plane}`;
                checkbox.value = plane;
                checkbox.name = 'planes';
                
                const label = document.createElement('label');
                label.htmlFor = `plane-${plane}`;
                label.textContent = plane;
                
                div.appendChild(checkbox);
                div.appendChild(label);
                planesGroup.appendChild(div);
            }
            
            // 事件监听
            document.getElementById('convert-button').addEventListener('click', startConversion);
            document.getElementById('back-button').addEventListener('click', backToForm);
            document.getElementById('back-to-list-button').addEventListener('click', backToResults);
        });
        
        // 开始转换
        function startConversion() {
            // 获取文件
            const fileInput = document.getElementById('cifFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showError('请选择一个CIF文件');
                return;
            }
            
            if (!file.name.toLowerCase().endsWith('.cif')) {
                showError('请选择一个扩展名为.cif的文件');
                return;
            }
            
            // 获取选择的晶面
            const checkboxes = document.querySelectorAll('input[name="planes"]:checked');
            let selectedPlanes = Array.from(checkboxes).map(cb => cb.value);
            
            // 如果没有选择晶面，使用所有支持的晶面
            if (selectedPlanes.length === 0) {
                selectedPlanes = Object.keys(PLANE_CONFIGS);
            }
            
            // 获取前缀和后缀
            const prefix = document.getElementById('prefix').value || '';
            const suffix = document.getElementById('suffix').value || '';
            
            // 显示加载指示器
            document.getElementById('loader').style.display = 'block';
            
            // 读取文件内容
            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                processCifFile(content, file.name, selectedPlanes, prefix, suffix);
            };
            reader.readAsText(file);
        }
        
        // 解析和处理CIF文件
        function processCifFile(content, filename, planes, prefix, suffix) {
            try {
                // 解析CIF文件内容
                const parsedCif = parseCifFile(content);
                
                // 清空之前的转换结果
                convertedFiles = [];
                
                // 对每个选定的晶面进行转换
                for (const plane of planes) {
                    const transformedCif = transformCifToPlane(parsedCif, plane);
                    
                    // 创建文件名
                    const baseName = filename.replace(/\.cif$/i, '');
                    const newFilename = `${prefix}${baseName}_${plane}${suffix}.cif`;
                    
                    // 存储转换结果
                    convertedFiles.push({
                        filename: newFilename,
                        content: transformedCif,
                        plane: plane
                    });
                }
                
                // 显示结果
                showResults();
            } catch (error) {
                showError('处理CIF文件时出错: ' + error.message);
                document.getElementById('loader').style.display = 'none';
            }
        }
        
        // 解析CIF文件内容
        function parseCifFile(content) {
            // 这是一个简化版的CIF解析，实际项目中需要更复杂的解析逻辑
            const lines = content.split('\n');
            const parsedCif = {
                cellParams: {},
                atoms: [],
                originalContent: content
            };
            
            // 提取晶胞参数和原子位置
            for (const line of lines) {
                const trimmedLine = line.trim();
                
                // 提取晶胞长度
                if (trimmedLine.startsWith('_cell_length_a')) {
                    parsedCif.cellParams.a = parseFloat(trimmedLine.split(/\s+/)[1]);
                } else if (trimmedLine.startsWith('_cell_length_b')) {
                    parsedCif.cellParams.b = parseFloat(trimmedLine.split(/\s+/)[1]);
                } else if (trimmedLine.startsWith('_cell_length_c')) {
                    parsedCif.cellParams.c = parseFloat(trimmedLine.split(/\s+/)[1]);
                }
                
                // 提取晶胞角度
                else if (trimmedLine.startsWith('_cell_angle_alpha')) {
                    parsedCif.cellParams.alpha = parseFloat(trimmedLine.split(/\s+/)[1]);
                } else if (trimmedLine.startsWith('_cell_angle_beta')) {
                    parsedCif.cellParams.beta = parseFloat(trimmedLine.split(/\s+/)[1]);
                } else if (trimmedLine.startsWith('_cell_angle_gamma')) {
                    parsedCif.cellParams.gamma = parseFloat(trimmedLine.split(/\s+/)[1]);
                }
                
                // 这里简化了原子位置的提取，实际上需要更复杂的逻辑
                // 在实际应用中，需要正确解析原子循环数据
            }
            
            // 将角度转换为弧度
            parsedCif.cellParams.alphaRad = toRadians(parsedCif.cellParams.alpha);
            parsedCif.cellParams.betaRad = toRadians(parsedCif.cellParams.beta);
            parsedCif.cellParams.gammaRad = toRadians(parsedCif.cellParams.gamma);
            
            return parsedCif;
        }
        
        // 将CIF转换为指定晶面
        function transformCifToPlane(parsedCif, plane) {
            // 在实际实现中，这里应该包含完整的转换逻辑
            // 由于晶体学转换非常复杂，这里只提供一个简化版示例
            
            // 获取变换矩阵
            const transformMatrix = PLANE_CONFIGS[plane];
            
            // 构建注释信息
            const comment = `# 此文件由CIF晶格转换工具（Web版）生成\n`;
            const comment2 = `# 原始文件分析： a=${parsedCif.cellParams.a}, b=${parsedCif.cellParams.b}, c=${parsedCif.cellParams.c}\n`;
            const comment3 = `# 转换晶面: ${plane}\n`;
            const comment4 = `# 新坐标轴: A=${transformMatrix[0]}, B=${transformMatrix[1]}, C=${transformMatrix[2]}\n\n`;
            
            // 注意：这里只是添加了注释，实际上需要执行真正的转换
            // 由于完整实现需要线性代数库和复杂的坐标转换，这里做了简化
            
            return comment + comment2 + comment3 + comment4 + 
                  "# 注意: 这是演示版本，仅添加了转换信息\n" +
                  "# 真正的转换需要使用命令行工具或服务器端程序\n\n" +
                  parsedCif.originalContent;
        }
        
        // 显示转换结果
        function showResults() {
            // 隐藏加载指示器
            document.getElementById('loader').style.display = 'none';
            
            // 显示成功消息
            const successMessage = document.getElementById('success-message');
            successMessage.textContent = `转换成功！共生成 ${convertedFiles.length} 个CIF文件。`;
            
            // 填充文件列表
            const fileList = document.getElementById('file-list');
            fileList.innerHTML = '';
            
            convertedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                
                const fileName = document.createElement('div');
                fileName.className = 'file-name';
                fileName.textContent = file.filename;
                
                const fileActions = document.createElement('div');
                fileActions.className = 'file-actions';
                
                const viewButton = document.createElement('a');
                viewButton.className = 'btn';
                viewButton.textContent = '查看';
                viewButton.href = '#';
                viewButton.onclick = function(e) {
                    e.preventDefault();
                    viewFile(index);
                };
                
                const downloadButton = document.createElement('a');
                downloadButton.className = 'btn';
                downloadButton.textContent = '下载';
                downloadButton.href = '#';
                downloadButton.onclick = function(e) {
                    e.preventDefault();
                    downloadFile(index);
                };
                
                fileActions.appendChild(viewButton);
                fileActions.appendChild(downloadButton);
                
                fileItem.appendChild(fileName);
                fileItem.appendChild(fileActions);
                
                fileList.appendChild(fileItem);
            });
            
            // 切换显示区域
            document.getElementById('upload-form').style.display = 'none';
            document.getElementById('result-area').style.display = 'block';
            document.getElementById('file-content-view').style.display = 'none';
        }
        
        // 查看文件内容
        function viewFile(index) {
            const file = convertedFiles[index];
            
            document.getElementById('file-view-title').textContent = `文件内容: ${file.filename}`;
            document.getElementById('file-content').textContent = file.content;
            
            document.getElementById('result-area').style.display = 'none';
            document.getElementById('file-content-view').style.display = 'block';
            
            // 简单的语法高亮
            const content = document.getElementById('file-content');
            let html = content.innerHTML;
            
            // 高亮注释
            html = html.replace(/(#.+)$/mg, '<span style="color: #2ecc71;">$1</span>');
            
            // 高亮关键字和值
            html = html.replace(/(_[a-zA-Z0-9_.]+)/g, '<span style="color: #3498db; font-weight: bold;">$1</span>');
            html = html.replace(/([-+]?[0-9]*\.?[0-9]+)/g, '<span style="color: #e74c3c;">$1</span>');
            
            // 高亮原子标签
            html = html.replace(/\b([A-Z][a-z]?)\b/g, '<span style="color: #9b59b6;">$1</span>');
            
            content.innerHTML = html;
        }
        
        // 下载文件
        function downloadFile(index) {
            const file = convertedFiles[index];
            
            const blob = new Blob([file.content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = file.filename;
            document.body.appendChild(a);
            a.click();
            
            setTimeout(function() {
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            }, 0);
        }
        
        // 返回上传表单
        function backToForm() {
            document.getElementById('upload-form').style.display = 'block';
            document.getElementById('result-area').style.display = 'none';
            document.getElementById('file-content-view').style.display = 'none';
        }
        
        // 返回结果列表
        function backToResults() {
            document.getElementById('result-area').style.display = 'block';
            document.getElementById('file-content-view').style.display = 'none';
        }
        
        // 显示错误消息
        function showError(message) {
            const errorElement = document.getElementById('error-message');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            
            setTimeout(function() {
                errorElement.style.display = 'none';
            }, 5000);
        }
        
        // 辅助函数：角度转弧度
        function toRadians(degrees) {
            return degrees * Math.PI / 180;
        }
        
        // 辅助函数：弧度转角度
        function toDegrees(radians) {
            return radians * 180 / Math.PI;
        }
        
        // 辅助函数：向量点积
        function dotProduct(v1, v2) {
            return v1[0] * v2[0] + v1[1] * v2[1] + v1[2] * v2[2];
        }
    </script>
</body>
</html> 