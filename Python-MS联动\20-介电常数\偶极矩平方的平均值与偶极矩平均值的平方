
在脚本中，`<M^2> - <M>^2`的计算过程如下：

1. **收集偶极矩数据**：
   ```perl
   # 遍历所有轨迹帧，计算每一帧的总偶极矩（x、y、z分量）
   for (my $frame = 0; $frame < $numFrames; $frame++) {
       # 计算当前帧总偶极矩
       my ($total_dx, $total_dy, $total_dz) = (0, 0, 0);
       
       foreach my $molecule (@$molecules) {
           my $dipole = $molecule->DipoleMoment;
           $total_dx += $dipole->X;  # x分量累加
           $total_dy += $dipole->Y;  # y分量累加
           $total_dz += $dipole->Z;  # z分量累加
       }
       
       # 存储各方向总偶极矩
       push @dipole_x, $total_dx;
       push @dipole_y, $total_dy;
       push @dipole_z, $total_dz;
   }
   ```

2. **计算偶极矩和与平方和**：
   ```perl
   # 初始化和与平方和
   my ($sum_x, $sum_y, $sum_z) = (0, 0, 0);
   my ($sum_x2, $sum_y2, $sum_z2) = (0, 0, 0);
   
   # 计算各方向偶极矩的和与平方和
   for (my $i = 0; $i < $numFrames; $i++) {
       $sum_x += $dipole_x[$i];      # 累加x方向偶极矩
       $sum_y += $dipole_y[$i];      # 累加y方向偶极矩
       $sum_z += $dipole_z[$i];      # 累加z方向偶极矩
       
       $sum_x2 += $dipole_x[$i] * $dipole_x[$i];  # 累加x方向偶极矩的平方
       $sum_y2 += $dipole_y[$i] * $dipole_y[$i];  # 累加y方向偶极矩的平方
       $sum_z2 += $dipole_z[$i] * $dipole_z[$i];  # 累加z方向偶极矩的平方
   }
   ```

3. **计算`<M>`（平均偶极矩）**：
   ```perl
   # 计算各方向平均偶极矩
   my $avg_x = $sum_x / $numFrames;  # x方向平均值
   my $avg_y = $sum_y / $numFrames;  # y方向平均值
   my $avg_z = $sum_z / $numFrames;  # z方向平均值
   ```

4. **计算`<M^2> - <M>^2`（偶极矩方差）**：
   ```perl
   # 计算各方向偶极矩方差
   my $var_x = $sum_x2 / $numFrames - $avg_x * $avg_x;  # x方向方差
   my $var_y = $sum_y2 / $numFrames - $avg_y * $avg_y;  # y方向方差
   my $var_z = $sum_z2 / $numFrames - $avg_z * $avg_z;  # z方向方差
   ```

   其中：
   - `$sum_x2 / $numFrames` 是偶极矩平方的平均值，即 `<M^2>`
   - `$avg_x * $avg_x` 是偶极矩平均值的平方，即 `<M>^2`
   - 它们的差值就是公式中需要的偶极矩涨落

5. **计算三个方向的平均方差**：
   ```perl
   # 取三个方向的平均方差作为最终结果
   my $avg_variance = ($var_x + $var_y + $var_z) / 3;
   ```

这个`$avg_variance`值就是介电常数计算公式中分子部分的`<M^2> - <M>^2`。它代表了系统偶极矩的统计涨落，直接关系到介电响应强度。
