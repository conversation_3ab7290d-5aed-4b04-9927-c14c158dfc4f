
介电常数计算公式的具体实现及每个数值获取方法如下：

1. **物理常数定义**：
   - 玻尔兹曼常数：`$BOLTZMANN = 1.380649E-23` (J/K)
   - 真空介电常数：`$VACUUM_PERMITTIVITY = 8.8541878128E-12` (F/m)
   - 德拜单位：`$DEBYE = 3.335640952E-30` (C·m)
   - 埃单位：`$ANGSTROM = 1.00000000E-10` (m)

2. **偶极矩数据获取**：
   ```perl
   foreach my $molecule (@$molecules) {
       my $dipole = $molecule->DipoleMoment;
       $total_dx += $dipole->X;
       $total_dy += $dipole->Y;
       $total_dz += $dipole->Z;
   }
   ```
   - 从Materials Studio中直接提取每个分子的偶极矩
   - 累加所有分子的偶极矩得到系统总偶极矩

3. **温度和体积获取**：
   ```perl
   $avg_temp += $doc->Temperature;
   $avg_volume += $doc->Lattice3D->CellVolume;
   ```
   - 直接从轨迹文件中读取每一帧的温度和模拟盒子体积
   - 最后计算平均值：`$avg_temp /= $numFrames; $avg_volume /= $numFrames;`

4. **偶极矩涨落计算**：
   ```perl
   # 计算均值
   my $avg_x = $sum_x / $numFrames;
   my $avg_y = $sum_y / $numFrames;
   my $avg_z = $sum_z / $numFrames;
   
   # 计算方差
   my $var_x = $sum_x2 / $numFrames - $avg_x * $avg_x;
   my $var_y = $sum_y2 / $numFrames - $avg_y * $avg_y;
   my $var_z = $sum_z2 / $numFrames - $avg_z * $avg_z;
   
   # 三个方向的平均方差
   my $avg_variance = ($var_x + $var_y + $var_z) / 3;
   ```
   - 这里的方差就对应公式中的`<M^2> - <M>^2`
   - 取三个方向的平均作为总体涨落

5. **单位转换**：
   ```perl
   # 体积从埃³转换为米³
   my $vol_meters3 = $avg_volume * ($ANGSTROM**3);
   
   # 偶极矩方差从德拜²转换为(C·m)²
   my $dipole_variance_SI = $avg_variance * ($DEBYE**2);
   ```
   - 这一步确保所有物理量使用统一的SI单位

6. **最终介电常数计算**：
   ```perl
   # 计算介电响应分量 (ε - 1)
   my $epsilon_minus_1 = $dipole_variance_SI / ($BOLTZMANN * $avg_temp * $vol_meters3 * $VACUUM_PERMITTIVITY);
   
   # 最终介电常数
   my $dielectric_constant = 1.0 + $epsilon_minus_1;
   ```
   - 正好对应公式：`eps = 1 + (<M^2> - <M>^2)/(3 k <T> <V> eps0)`
   - 这里的3被隐含在取三个方向平均方差的计算中

此脚本还实现了逐帧计算介电常数，使用相似的方法但在移动窗口内进行计算：
```perl
my $frame_dielectric_response = $frame_var_SI / ($BOLTZMANN * $frame_temp * $frame_vol_m3 * $VACUUM_PERMITTIVITY);
my $frame_dielectric = 1.0 + $frame_dielectric_response;
```
