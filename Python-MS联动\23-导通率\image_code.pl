#!perl

use strict;
use warnings;
use Getopt::Long;
use MaterialsScript qw(:all);

# 导通率(Percolation Rate)计算脚本
# 根据隧穿距离TD判断导电填料是否导通，计算体系的导通率
# 
# 遵循以下规则计算导通率A：
# 1. 导通率A = 导通的构型数/总构型数
# 2. 为每个导电填料分配从1-N的分子序号和团簇序号
# 3. 若两个导电填料间距离小于隧穿距离TD(3Å)，则认为它们导通
# 4. 若导通则将这两个导电填料合并为一个团簇，命名序号选较小的
# 5. 若存在可贯穿相应方向的团簇，则认为该构型是导通的
# 6. 通过对体系的5000个构型的导通状态统计，得到导通率

# 配置参数
my $inputFileName = "Target";       # 轨迹文件基本名称(.xtd)
my $tunnelDistance = 3.0;           # 隧穿距离TD(Å)，根据量子化学跃迁理论设为3Å
my $timeInterval = 0.01;            # 每个构型的输出时间间隔(ps)
my $totalFrames = 5000;             # 需要统计的构型数，固定为5000个
my $conductiveFiller = "C";         # 导电填料原子类型(默认为碳原子，可根据需要修改)
my $boundaryTolerance = 0.1;        # 边界容差，判断原子是否接近晶胞边界的阈值

# 初始化输出文档
my $resultsTable = Documents->New("$inputFileName"."_Conductivity.std");  # 创建结果表格
my $logFile = Documents->New("$inputFileName"."_Conductivity_Log.txt");   # 创建日志文件

# 记录标题信息
$logFile->Append("导通率计算脚本\n");
$logFile->Append("隧穿距离(TD): $tunnelDistance Å\n");
$logFile->Append("每个构型的时间间隔: $timeInterval ps\n");
$logFile->Append("总构型数: $totalFrames\n");
$logFile->Append("导电填料类型: $conductiveFiller\n");
$logFile->Append("===========================================\n\n");
$logFile->Save;

# 设置结果表格标题
$resultsTable->ColumnHeading(0) = "帧序号";
$resultsTable->ColumnHeading(1) = "时间(ps)";
$resultsTable->ColumnHeading(2) = "导通状态";
$resultsTable->ColumnHeading(3) = "X方向导通";
$resultsTable->ColumnHeading(4) = "Y方向导通";
$resultsTable->ColumnHeading(5) = "Z方向导通";
$resultsTable->ColumnHeading(6) = "团簇数";
$resultsTable->ColumnHeading(7) = "最大团簇尺寸";

# 检查轨迹文件是否存在
if (!defined($Documents{"$inputFileName.xtd"})) {
    $logFile->Append("错误: 未找到输入轨迹文件 '$inputFileName.xtd'\n");
    $logFile->Append("请检查文件名并确保轨迹文件已正确导入\n");
    $logFile->Save;
    die "错误: 未找到输入轨迹文件 '$inputFileName.xtd'，进程终止";
}

# 打开轨迹文件
my $doc = $Documents{"$inputFileName.xtd"};
my $trajectory = $doc->Trajectory;

# 验证轨迹有效
if (!defined($trajectory)) {
    $logFile->Append("错误: 轨迹对象无效\n");
    $logFile->Save;
    die "错误: 轨迹对象无效，进程终止";
}

# 记录轨迹信息
my $totalAvailableFrames = $trajectory->NumFrames;
$logFile->Append("轨迹信息:\n");
$logFile->Append("  总帧数: $totalAvailableFrames\n");
if ($totalFrames > $totalAvailableFrames) {
    $logFile->Append("  警告: 轨迹中实际帧数($totalAvailableFrames)少于预期的5000帧\n");
    $logFile->Append("  将使用所有可用帧进行计算\n");
} else {
    $logFile->Append("  将分析的帧数: $totalFrames\n");
}
$logFile->Save;

# 限制要分析的帧数
$totalFrames = $totalAvailableFrames if $totalFrames > $totalAvailableFrames;

# 辅助函数：查找填料所属的团簇
sub find
{
    my ($labels, $x) = @_;
    if ($labels->{$x} != $x) {
        $labels->{$x} = find($labels, $labels->{$x});
    }
    return $labels->{$x};
}

# 主函数：计算导通率
sub GetConductivity
{
    my $frames_ref = shift;
    my $TD = shift;
    
    # 初始化变量
    my @frames = @{$frames_ref};
    my $numFrames = scalar(@frames);
    my $conductivity = 0;  # 导通的构型计数
    my $conductivityX = 0; # X方向导通构型计数
    my $conductivityY = 0; # Y方向导通构型计数
    my $conductivityZ = 0; # Z方向导通构型计数
    my %frameResults;      # 存储每帧的结果
    
    # 遍历所有帧
    for (my $frameIdx = 0; $frameIdx < $numFrames; $frameIdx++) {
        my $frame = $frames[$frameIdx];
        $trajectory->CurrentFrame = $frame;
        my $currentTime = $frame * $timeInterval;
        
        # 记录处理状态
        if ($frameIdx % 100 == 0) {
            $logFile->Append("正在处理帧 $frame (时间: $currentTime ps)...\n");
            $logFile->Save;
        }
        
        # 获取当前帧的3D模型和晶胞信息
        my $currentStructure = $doc->UnitCell;
        my $lattice = $doc->Lattice3D;
        
        # 获取所有导电填料原子
        my @atoms = $currentStructure->Atoms($conductiveFiller);
        my $totalFillers = scalar(@atoms);
        
        if ($totalFillers == 0) {
            $logFile->Append("警告: 帧 $frame 中未找到导电填料原子\n");
            $logFile->Save;
            next;
        }
        
        # 创建序号映射 - 将每个填料分配从1到N的唯一序号
        my %fillerPositions;  # 存储原子的分数坐标
        
        for (my $i = 0; $i < $totalFillers; $i++) {
            # 获取原子的分数坐标
            my $fracX = $atoms[$i]->FractionalXYZ->X;
            my $fracY = $atoms[$i]->FractionalXYZ->Y;
            my $fracZ = $atoms[$i]->FractionalXYZ->Z;
            
            $fillerPositions{$i + 1} = {
                'x' => $fracX,
                'y' => $fracY,
                'z' => $fracZ
            };
        }
        
        # 初始化标签，每个填料各自独立
        my %labels;
        for (my $i = 1; $i <= $totalFillers; $i++) {
            $labels{$i} = $i;  # 初始时团簇序号等于分子序号
        }
        
        # 检查填料间距离并形成团簇
        for (my $i = 0; $i < $totalFillers; $i++) {
            for (my $j = $i + 1; $j < $totalFillers; $j++) {
                # 计算两个填料之间的距离
                my $distance = $atoms[$i]->Distance($atoms[$j]);
                
                # 如果距离小于隧穿距离，合并团簇
                if ($distance < $TD) {
                    # 获取两个填料当前所属的团簇
                    my $root_i = find(\%labels, $i + 1);
                    my $root_j = find(\%labels, $j + 1);
                    
                    # 如果不在同一团簇中，则合并
                    if ($root_i != $root_j) {
                        # 取较小的ID作为合并后的团簇ID
                        if ($root_i < $root_j) {
                            $labels{$root_j} = $root_i;
                        } else {
                            $labels{$root_i} = $root_j;
                        }
                    }
                }
            }
        }
        
        # 查找所有团簇
        my %clusters;
        for (my $i = 1; $i <= $totalFillers; $i++) {
            my $root = find(\%labels, $i);
            if (!exists($clusters{$root})) {
                $clusters{$root} = [];
            }
            push @{$clusters{$root}}, $i;
        }
        
        # 计算团簇数量和最大团簇尺寸
        my $numClusters = scalar(keys %clusters);
        my $maxClusterSize = 0;
        my $largestClusterId = 0;
        
        foreach my $clusterId (keys %clusters) {
            my $size = scalar(@{$clusters{$clusterId}});
            if ($size > $maxClusterSize) {
                $maxClusterSize = $size;
                $largestClusterId = $clusterId;
            }
        }
        
        # 分析最大团簇在X、Y、Z方向上的导通性
        my $isPercolated = 0;    # 整体导通状态
        my $isPercolatedX = 0;   # X方向导通
        my $isPercolatedY = 0;   # Y方向导通
        my $isPercolatedZ = 0;   # Z方向导通
        
        if ($largestClusterId > 0) {
            # 获取最大团簇中的所有原子
            my @clusterAtoms = @{$clusters{$largestClusterId}};
            
            # 检查是否有原子接近周期性边界的两侧
            my $hasNearLowX = 0;
            my $hasNearHighX = 0;
            my $hasNearLowY = 0;
            my $hasNearHighY = 0;
            my $hasNearLowZ = 0;
            my $hasNearHighZ = 0;
            
            foreach my $atomId (@clusterAtoms) {
                my $posX = $fillerPositions{$atomId}->{'x'};
                my $posY = $fillerPositions{$atomId}->{'y'};
                my $posZ = $fillerPositions{$atomId}->{'z'};
                
                $hasNearLowX = 1 if $posX < $boundaryTolerance;
                $hasNearHighX = 1 if $posX > (1.0 - $boundaryTolerance);
                
                $hasNearLowY = 1 if $posY < $boundaryTolerance;
                $hasNearHighY = 1 if $posY > (1.0 - $boundaryTolerance);
                
                $hasNearLowZ = 1 if $posZ < $boundaryTolerance;
                $hasNearHighZ = 1 if $posZ > (1.0 - $boundaryTolerance);
            }
            
            # 如果团簇在某方向上同时接近两个边界，则认为在该方向上导通
            $isPercolatedX = ($hasNearLowX && $hasNearHighX) ? 1 : 0;
            $isPercolatedY = ($hasNearLowY && $hasNearHighY) ? 1 : 0;
            $isPercolatedZ = ($hasNearLowZ && $hasNearHighZ) ? 1 : 0;
            
            # 如果任一方向导通，则整体导通
            $isPercolated = ($isPercolatedX || $isPercolatedY || $isPercolatedZ) ? 1 : 0;
        }
        
        # 统计导通构型
        $conductivity += $isPercolated;
        $conductivityX += $isPercolatedX;
        $conductivityY += $isPercolatedY;
        $conductivityZ += $isPercolatedZ;
        
        # 将结果保存到表格
        $resultsTable->Cell($frameIdx, 0) = $frame;
        $resultsTable->Cell($frameIdx, 1) = $currentTime;
        $resultsTable->Cell($frameIdx, 2) = $isPercolated;
        $resultsTable->Cell($frameIdx, 3) = $isPercolatedX;
        $resultsTable->Cell($frameIdx, 4) = $isPercolatedY;
        $resultsTable->Cell($frameIdx, 5) = $isPercolatedZ;
        $resultsTable->Cell($frameIdx, 6) = $numClusters;
        $resultsTable->Cell($frameIdx, 7) = $maxClusterSize;
        
        # 存储帧结果
        $frameResults{$frameIdx} = {
            'time' => $currentTime,
            'isPercolated' => $isPercolated,
            'isPercolatedX' => $isPercolatedX,
            'isPercolatedY' => $isPercolatedY,
            'isPercolatedZ' => $isPercolatedZ,
            'numClusters' => $numClusters,
            'maxClusterSize' => $maxClusterSize
        };
    }
    
    # 计算导通率
    my %results = (
        'conductivity' => $conductivity / $numFrames,
        'conductivityX' => $conductivityX / $numFrames,
        'conductivityY' => $conductivityY / $numFrames,
        'conductivityZ' => $conductivityZ / $numFrames,
        'frames' => \%frameResults
    );
    
    return \%results;
}

# 创建帧数组
my @frames;
for (my $i = 0; $i < $totalFrames; $i++) {
    push @frames, $i;
}

# 计算导通率
$logFile->Append("开始计算导通率...\n");
$logFile->Save;

my $results = GetConductivity(\@frames, $tunnelDistance);

# 从结果中提取导通率
my $conductivityA = $results->{'conductivity'};
my $conductivityX = $results->{'conductivityX'};
my $conductivityY = $results->{'conductivityY'};
my $conductivityZ = $results->{'conductivityZ'};

# 添加导通率结果到日志
$logFile->Append("\n===========================================\n");
$logFile->Append("导通率计算结果:\n");
$logFile->Append("  总构型数: $totalFrames\n");
$logFile->Append("  导通的构型数: " . ($conductivityA * $totalFrames) . "\n");
$logFile->Append("  导通率 A = $conductivityA\n");
$logFile->Append("  X方向导通的构型数: " . ($conductivityX * $totalFrames) . "\n");
$logFile->Append("  X方向导通率 = $conductivityX\n");
$logFile->Append("  Y方向导通的构型数: " . ($conductivityY * $totalFrames) . "\n");
$logFile->Append("  Y方向导通率 = $conductivityY\n");
$logFile->Append("  Z方向导通的构型数: " . ($conductivityZ * $totalFrames) . "\n");
$logFile->Append("  Z方向导通率 = $conductivityZ\n");
$logFile->Append("===========================================\n");
$logFile->Save;

# 添加总体导通率结果到表格
$resultsTable->Cell($totalFrames + 1, 0) = "导通率结果";
$resultsTable->Cell($totalFrames + 1, 2) = $conductivityA;
$resultsTable->Cell($totalFrames + 1, 3) = $conductivityX;
$resultsTable->Cell($totalFrames + 1, 4) = $conductivityY;
$resultsTable->Cell($totalFrames + 1, 5) = $conductivityZ;

# 输出完成信息
$logFile->Append("\n计算完成\n");
$logFile->Save;

print "导通率计算完成: A = $conductivityA\n";
print "如图3-15所示，隧穿距离TD = $tunnelDistance Å\n";
print "通过对体系的 $totalFrames 个构型的导通状态统计，得到该体系的导通率\n"; 