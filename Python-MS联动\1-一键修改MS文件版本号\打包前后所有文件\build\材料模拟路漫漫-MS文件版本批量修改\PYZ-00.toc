('C:\\Users\\<USER>\\Python程序打包生成\\build\\材料模拟路漫漫-MS文件版本批量修改\\PYZ-00.pyz',
 [('_compat_pickle',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Users\\<USER>\\miniconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Users\\<USER>\\miniconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Users\\<USER>\\miniconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'C:\\Users\\<USER>\\miniconda3\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Users\\<USER>\\miniconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Users\\<USER>\\miniconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Users\\<USER>\\miniconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Users\\<USER>\\miniconda3\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Users\\<USER>\\miniconda3\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Users\\<USER>\\miniconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Users\\<USER>\\miniconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'C:\\Users\\<USER>\\miniconda3\\Lib\\dis.py', 'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Users\\<USER>\\miniconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Users\\<USER>\\miniconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'C:\\Users\\<USER>\\miniconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Users\\<USER>\\miniconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'C:\\Users\\<USER>\\miniconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Users\\<USER>\\miniconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Users\\<USER>\\miniconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Users\\<USER>\\miniconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'C:\\Users\\<USER>\\miniconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Users\\<USER>\\miniconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Users\\<USER>\\miniconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\py_compile.py',
   'PYMODULE'),
  ('quopri', 'C:\\Users\\<USER>\\miniconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Users\\<USER>\\miniconda3\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'C:\\Users\\<USER>\\miniconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'C:\\Users\\<USER>\\miniconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Users\\<USER>\\miniconda3\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\Users\\<USER>\\miniconda3\\Lib\\socket.py', 'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'C:\\Users\\<USER>\\miniconda3\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Users\\<USER>\\miniconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Users\\<USER>\\miniconda3\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('token', 'C:\\Users\\<USER>\\miniconda3\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Users\\<USER>\\miniconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'C:\\Users\\<USER>\\miniconda3\\Lib\\typing.py', 'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])
