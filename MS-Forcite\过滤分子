#!perl

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);

# 配置参数，方便修改
my $inputFile = "all.xtd";            # 输入轨迹文件名
my $outputFile = "number.std";         # 输出表格文件名
my $atomType = "Na";                   # 要分析的原子类型
my $zThreshold = 0.57;                 # Z坐标阈值

# 打开输入文件
my $doc = $Documents{$inputFile};
my $numFrames = $doc->Trajectory->NumFrames;

# 创建输出表格
my $newStudyTable = Documents->New($outputFile);
my $calcSheet = $newStudyTable->ActiveSheet;
$calcSheet->ColumnHeading(0) = "Time";
$calcSheet->ColumnHeading(1) = "number";

# 遍历所有帧并分析
for (my $frameIndex = 1; $frameIndex <= $numFrames; ++$frameIndex) {
    # 设置当前帧
    $doc->Trajectory->CurrentFrame = $frameIndex;
    
    # 统计符合条件的原子数
    my $count = 0;
    my $atoms = $doc->UnitCell->Sets($atomType)->Atoms;
    
    foreach my $atom (@$atoms) {
        my $z = $atom->FractionalXYZ->Z;
        if ($z > $zThreshold) {
            $count++;
        }
    }
    
    # 将结果写入表格
    $calcSheet->Cell($frameIndex-1, 0) = $frameIndex;
    $calcSheet->Cell($frameIndex-1, 1) = $count;
}

# 结束脚本
print "分析完成！共处理了 $numFrames 帧，结果保存在 $outputFile\n";








