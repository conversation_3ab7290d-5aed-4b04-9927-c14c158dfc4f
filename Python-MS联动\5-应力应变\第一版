#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import curve_fit
import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib as mpl

# 设置绘图参数，使图表达到期刊要求
plt.rcParams['font.family'] = 'Arial'
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.5
plt.rcParams['xtick.direction'] = 'in'
plt.rcParams['ytick.direction'] = 'in'
plt.rcParams['xtick.major.width'] = 1.5
plt.rcParams['ytick.major.width'] = 1.5
plt.rcParams['xtick.major.size'] = 5
plt.rcParams['ytick.major.size'] = 5
plt.rcParams['axes.unicode_minus'] = False


def linear_fit(x, y, initial_points=7):
    """对数据的弹性区进行线性拟合，计算弹性模量"""
    def linear(x, k, b):
        return k * x + b
    
    # 使用前initial_points个点进行拟合
    x_fit = x[:initial_points]
    y_fit = y[:initial_points]
    
    # 线性拟合
    popt, pcov = curve_fit(linear, x_fit, y_fit)
    k, b = popt
    r_squared = np.corrcoef(y_fit, linear(x_fit, k, b))[0, 1]**2
    
    return k, b, r_squared


class StressStrainAnalyzerApp:
    def __init__(self, root):
        self.root = root
        self.root.title("应力应变数据分析工具")
        self.root.geometry("1200x800")
        
        # 数据存储
        self.data = None
        self.results = None
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建左右分割
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建数据输入区域（左侧）
        input_frame = ttk.LabelFrame(left_frame, text="数据输入")
        input_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建表格
        self.create_data_table(input_frame)
        
        # 创建按钮区域
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="导入数据", command=self.import_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="分析数据", command=self.analyze_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="导出结果", command=self.export_results).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空数据", command=self.clear_data).pack(side=tk.LEFT, padx=5)
        
        # 创建结果显示区域（右侧）
        result_frame = ttk.LabelFrame(right_frame, text="分析结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(result_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建图表选项卡
        self.plot_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.plot_frame, text="应力-应变曲线")
        
        self.elastic_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.elastic_frame, text="弹性区拟合")
        
        # 创建文本结果选项卡
        self.text_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.text_frame, text="文本结果")
        
        # 创建文本结果区域
        self.result_text = tk.Text(self.text_frame, wrap=tk.WORD)
        self.result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
    def create_data_table(self, parent):
        # 创建表格框架
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建滚动条
        yscroll = ttk.Scrollbar(table_frame, orient=tk.VERTICAL)
        yscroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        xscroll = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        xscroll.pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建表格
        columns = ("frame", "strain", "stress_xx", "stress_yy", "stress_zz")
        self.table = ttk.Treeview(table_frame, columns=columns, show="headings",
                                 yscrollcommand=yscroll.set, xscrollcommand=xscroll.set)
        
        # 设置列宽和标题
        self.table.column("frame", width=150)
        self.table.column("strain", width=100)
        self.table.column("stress_xx", width=120)
        self.table.column("stress_yy", width=120)
        self.table.column("stress_zz", width=120)
        
        self.table.heading("frame", text="Frame document")
        self.table.heading("strain", text="Strain")
        self.table.heading("stress_xx", text="Stress XX (GPa)")
        self.table.heading("stress_yy", text="Stress YY (GPa)")
        self.table.heading("stress_zz", text="Stress ZZ (GPa)")
        
        # 放置表格
        self.table.pack(fill=tk.BOTH, expand=True)
        
        # 连接滚动条
        yscroll.config(command=self.table.yview)
        xscroll.config(command=self.table.xview)
        
        # 添加右键菜单
        self.context_menu = tk.Menu(self.table, tearoff=0)
        self.context_menu.add_command(label="添加行", command=self.add_row)
        self.context_menu.add_command(label="删除行", command=self.delete_row)
        self.table.bind("<Button-3>", self.show_context_menu)
        
        # 双击事件，用于编辑单元格
        self.table.bind("<Double-1>", self.edit_cell)
        
    def show_context_menu(self, event):
        """显示右键菜单"""
        self.context_menu.post(event.x_root, event.y_root)
        
    def add_row(self):
        """添加新行"""
        # 获取当前选中的行
        selected = self.table.selection()
        if selected:
            # 在选中行之后插入
            idx = self.table.index(selected[0]) + 1
        else:
            # 如果没有选中，则在最后添加
            idx = len(self.table.get_children())
        
        # 插入新行
        values = [f"strain_analysis_{idx}", f"{idx*0.02:.2f}", "0.0", "0.0", "0.0"]
        self.table.insert("", idx, values=values)
        
    def delete_row(self):
        """删除选中行"""
        selected = self.table.selection()
        if selected:
            for item in selected:
                self.table.delete(item)
                
    def edit_cell(self, event):
        """编辑单元格内容"""
        # 获取点击的单元格
        cell = self.table.identify("cell", event.x, event.y)
        if not cell:
            return
        
        column = int(cell[1])
        item = cell[0]
        
        # 如果是第一列（帧名称），不可编辑
        if column == 0:
            return
        
        # 获取当前值
        current_value = self.table.item(item, "values")[column]
        
        # 创建编辑框
        edit_frame = tk.Frame(self.table)
        edit_entry = tk.Entry(edit_frame)
        edit_entry.insert(0, current_value)
        edit_entry.pack()
        edit_entry.select_range(0, tk.END)
        
        # 计算编辑框位置
        x, y, width, height = self.table.bbox(item, column)
        edit_frame.place(x=x, y=y, width=width, height=height)
        
        # 焦点给编辑框
        edit_entry.focus_set()
        
        def save_edit(event=None):
            """保存编辑结果"""
            # 获取新值
            new_value = edit_entry.get()
            
            # 获取当前行的所有值
            current_values = list(self.table.item(item, "values"))
            
            # 更新对应列的值
            current_values[column] = new_value
            
            # 更新表格
            self.table.item(item, values=current_values)
            
            # 移除编辑框
            edit_frame.destroy()
            
        # 绑定事件
        edit_entry.bind("<Return>", save_edit)
        edit_entry.bind("<FocusOut>", save_edit)
        
    def import_data(self):
        """导入数据文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[
                ("所有支持的文件", "*.csv *.txt *.xls *.xlsx"),
                ("CSV文件", "*.csv"),
                ("文本文件", "*.txt"),
                ("Excel文件", "*.xls *.xlsx"),
                ("所有文件", "*.*")
            ]
        )
        
        if not file_path:
            return
        
        try:
            # 读取文件
            if file_path.endswith(('.csv', '.txt')):
                data = pd.read_csv(file_path, sep=None, engine='python')
            elif file_path.endswith(('.xls', '.xlsx')):
                data = pd.read_excel(file_path)
            else:
                messagebox.showerror("错误", "不支持的文件格式")
                return
            
            # 检查必要的列是否存在
            required_columns = ["Strain", "Stress XX (GPa)", "Stress YY (GPa)", "Stress ZZ (GPa)"]
            for col in required_columns:
                if col not in data.columns:
                    messagebox.showerror("错误", f"缺少必要的列: {col}")
                    return
            
            # 清空表格
            for item in self.table.get_children():
                self.table.delete(item)
            
            # 填充表格
            for i, row in data.iterrows():
                frame_name = row.get("Frame document", f"strain_analysis_{i}")
                values = [
                    frame_name, 
                    row["Strain"], 
                    row["Stress XX (GPa)"],
                    row["Stress YY (GPa)"],
                    row["Stress ZZ (GPa)"]
                ]
                self.table.insert("", "end", values=values)
            
            # 存储数据
            self.data = data
            
            messagebox.showinfo("成功", "数据导入成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"导入数据时出错:\n{str(e)}")
            
    def table_to_dataframe(self):
        """将表格数据转换为DataFrame"""
        data = []
        for item in self.table.get_children():
            values = self.table.item(item, "values")
            data.append({
                "Frame document": values[0],
                "Strain": float(values[1]),
                "Stress XX (GPa)": float(values[2]),
                "Stress YY (GPa)": float(values[3]),
                "Stress ZZ (GPa)": float(values[4])
            })
        
        return pd.DataFrame(data)
    
    def clear_data(self):
        """清空表格数据"""
        for item in self.table.get_children():
            self.table.delete(item)
        self.data = None
        
        # 清空图表和结果
        for widget in self.plot_frame.winfo_children():
            widget.destroy()
        for widget in self.elastic_frame.winfo_children():
            widget.destroy()
        self.result_text.delete(1.0, tk.END)
    
    def analyze_data(self):
        """分析数据"""
        # 检查是否有数据
        if not self.table.get_children():
            messagebox.showerror("错误", "没有数据可分析")
            return
        
        try:
            # 从表格获取数据
            self.data = self.table_to_dataframe()
            
            # 确保数据排序
            self.data = self.data.sort_values(by="Strain")
            
            # 分析数据
            self.results = self.analyze_stress_strain_data(self.data)
            
            # 更新文本结果
            self.update_text_results()
            
            messagebox.showinfo("成功", "数据分析完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"分析数据时出错:\n{str(e)}")
    
    def analyze_stress_strain_data(self, data):
        """分析应力应变数据"""
        # 绘制应力-应变曲线
        strain = data["Strain"].values
        stress_xx = data["Stress XX (GPa)"].values
        stress_yy = data["Stress YY (GPa)"].values
        stress_zz = data["Stress ZZ (GPa)"].values
        
        # 计算弹性模量
        k, b, r_squared = linear_fit(strain, stress_zz)
        elastic_modulus = k
        used_points = 7  # 默认使用前7个点
        
        # 绘制曲线图
        self.plot_stress_strain_curve(data)
        
        # 绘制弹性区拟合图
        self.plot_elastic_region(data)
        
        # 查找屈服点（应力达到最大值后开始下降的点）
        zz_max_idx = data["Stress ZZ (GPa)"].values.argmax()
        yield_strain = strain[zz_max_idx]
        yield_stress = stress_zz[zz_max_idx]
        
        return {
            'elastic_modulus': elastic_modulus,
            'r_squared': r_squared,
            'yield_strain': yield_strain,
            'yield_stress': yield_stress,
            'max_stress_zz': stress_zz.max(),
            'data': data
        }
    
    def plot_stress_strain_curve(self, data):
        """绘制应力-应变曲线"""
        # 清空旧图
        for widget in self.plot_frame.winfo_children():
            widget.destroy()
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 绘制三个方向的应力-应变曲线
        ax.plot(data['Strain'], data['Stress XX (GPa)'], 'o-', color='#1f77b4', label='Stress XX', markersize=6, linewidth=2)
        ax.plot(data['Strain'], data['Stress YY (GPa)'], 's-', color='#ff7f0e', label='Stress YY', markersize=6, linewidth=2)
        ax.plot(data['Strain'], data['Stress ZZ (GPa)'], '^-', color='#2ca02c', label='Stress ZZ', markersize=6, linewidth=2)
        
        # 计算ZZ方向的弹性模量
        strain = data['Strain'].values
        stress_zz = data['Stress ZZ (GPa)'].values
        k, b, r_squared = linear_fit(strain, stress_zz)
        elastic_modulus = k
        
        # 在图中标注弹性模量
        textstr = f'Elastic Modulus (ZZ): {elastic_modulus:.2f} GPa\nR²: {r_squared:.4f}'
        props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
        ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=12,
                verticalalignment='top', bbox=props)
        
        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 设置坐标轴标签和标题
        ax.set_xlabel('Strain', fontsize=14, fontweight='bold')
        ax.set_ylabel('Stress (GPa)', fontsize=14, fontweight='bold')
        ax.set_title('Stress-Strain Curves', fontsize=16, fontweight='bold')
        
        # 添加图例
        ax.legend(fontsize=12, frameon=True, loc='best')
        
        # 设置坐标轴范围和刻度
        xmin, xmax = 0, data['Strain'].max() * 1.05
        ymin = min(data['Stress XX (GPa)'].min(), data['Stress YY (GPa)'].min(), 
                  data['Stress ZZ (GPa)'].min()) * 1.1
        ymax = max(data['Stress XX (GPa)'].max(), data['Stress YY (GPa)'].max(), 
                  data['Stress ZZ (GPa)'].max()) * 1.1
        
        ax.set_xlim(xmin, xmax)
        ax.set_ylim(ymin, ymax)
        
        # 优化图表布局
        plt.tight_layout()
        
        # 在Tkinter窗口中显示图表
        canvas = FigureCanvasTkAgg(fig, master=self.plot_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def plot_elastic_region(self, data):
        """绘制弹性区应力-应变曲线及拟合线"""
        # 清空旧图
        for widget in self.elastic_frame.winfo_children():
            widget.destroy()
        
        # 创建图形
        fig, ax = plt.subplots(figsize=(8, 6))
        
        # 获取数据
        strain = data['Strain'].values
        stress_zz = data['Stress ZZ (GPa)'].values
        points_used = 7  # 默认使用前7个点
        
        # 弹性区线性拟合
        k, b, r_squared = linear_fit(strain, stress_zz, points_used)
        
        # 绘制原始数据点
        ax.scatter(strain, stress_zz, color='#2ca02c', label='实验数据', s=60, alpha=0.7)
        
        # 绘制弹性区的数据点（用不同颜色突出显示）
        ax.scatter(strain[:points_used], stress_zz[:points_used], color='red', 
                  label=f'弹性区 (前{points_used}个点)', s=80, alpha=0.9)
        
        # 绘制拟合线
        x_fit = np.linspace(0, strain[points_used-1]*1.2, 100)
        y_fit = k * x_fit + b
        ax.plot(x_fit, y_fit, '--', color='blue', 
                label=f'线性拟合: E = {k:.2f} GPa, R² = {r_squared:.4f}', linewidth=2)
        
        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 设置坐标轴标签和标题
        ax.set_xlabel('应变', fontsize=14, fontweight='bold')
        ax.set_ylabel('应力 ZZ (GPa)', fontsize=14, fontweight='bold')
        ax.set_title('弹性区应力-应变曲线及线性拟合', fontsize=16, fontweight='bold')
        
        # 添加图例
        ax.legend(fontsize=10, frameon=True, loc='best')
        
        # 优化图表布局
        plt.tight_layout()
        
        # 在Tkinter窗口中显示图表
        canvas = FigureCanvasTkAgg(fig, master=self.elastic_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def update_text_results(self):
        """更新文本结果区域"""
        if not self.results:
            return
        
        # 清空文本区域
        self.result_text.delete(1.0, tk.END)
        
        # 添加标题
        self.result_text.insert(tk.END, "====== 应力应变数据分析报告 ======\n\n")
        
        # 添加基本信息
        data = self.results['data']
        self.result_text.insert(tk.END, f"数据点数: {len(data)}\n")
        self.result_text.insert(tk.END, f"应变范围: {data['Strain'].min():.4f} - {data['Strain'].max():.4f}\n")
        self.result_text.insert(tk.END, f"ZZ方向最大应力: {self.results['max_stress_zz']:.4f} GPa\n\n")
        
        # 添加弹性模量信息
        self.result_text.insert(tk.END, f"弹性模量: {self.results['elastic_modulus']:.4f} GPa\n")
        self.result_text.insert(tk.END, f"拟合优度 R²: {self.results['r_squared']:.4f}\n\n")
        
        # 添加屈服点信息
        self.result_text.insert(tk.END, f"估计屈服点:\n")
        self.result_text.insert(tk.END, f"  应变 = {self.results['yield_strain']:.4f}\n")
        self.result_text.insert(tk.END, f"  应力 = {self.results['yield_stress']:.4f} GPa\n\n")
        
        # 添加分析结论
        self.result_text.insert(tk.END, "====== 材料特性分析 ======\n\n")
        
        # 根据弹性模量判断材料刚性
        if self.results['elastic_modulus'] > 300:
            rigidity = "极高"
        elif self.results['elastic_modulus'] > 200:
            rigidity = "高"
        elif self.results['elastic_modulus'] > 100:
            rigidity = "中等"
        else:
            rigidity = "低"
            
        self.result_text.insert(tk.END, f"1. 材料刚性: {rigidity}（弹性模量 {self.results['elastic_modulus']:.2f} GPa）\n")
        
        # 分析应力方向特性
        xx_max = data['Stress XX (GPa)'].max()
        yy_max = data['Stress YY (GPa)'].max()
        zz_max = data['Stress ZZ (GPa)'].max()
        
        if zz_max > 2 * xx_max and zz_max > 2 * yy_max:
            anisotropy = "强烈各向异性，Z方向强度显著高于X和Y方向"
        elif zz_max > xx_max and zz_max > yy_max:
            anisotropy = "一定程度的各向异性，Z方向强度较高"
        else:
            anisotropy = "相对各向同性，三个方向强度相近"
            
        self.result_text.insert(tk.END, f"2. 方向性: {anisotropy}\n")
        
        # 屈服特性
        if self.results['yield_strain'] > 0.2:
            yield_character = "高延展性"
        elif self.results['yield_strain'] > 0.1:
            yield_character = "中等延展性"
        else:
            yield_character = "低延展性/脆性"
            
        self.result_text.insert(tk.END, f"3. 屈服特性: {yield_character}（屈服应变 {self.results['yield_strain']:.2f}）\n\n")
        
        # 添加潜在应用建议
        self.result_text.insert(tk.END, "====== 潜在应用建议 ======\n\n")
        
        if self.results['elastic_modulus'] > 300 and self.results['yield_stress'] > 80:
            self.result_text.insert(tk.END, "高刚性高强度材料，适合结构支撑和高负载场景\n")
        elif self.results['elastic_modulus'] > 200 and self.results['yield_strain'] > 0.15:
            self.result_text.insert(tk.END, "综合性能良好，同时具有较高刚性和延展性\n")
        elif self.results['yield_strain'] > 0.2:
            self.result_text.insert(tk.END, "高延展性材料，适合需要大形变的场景\n")
        else:
            self.result_text.insert(tk.END, "一般用途材料，根据实际需求选择应用场景\n")
    
    def export_results(self):
        """导出分析结果到文本文件"""
        if not self.results:
            messagebox.showerror("错误", "没有结果可导出")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存分析结果",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if not file_path:
            return
        
        try:
            # 获取文本区域的内容
            text_content = self.result_text.get(1.0, tk.END)
            
            # 写入文件
            with open(file_path, "w", encoding="utf-8") as file:
                file.write(text_content)
            
            # 保存图表
            base_path = os.path.splitext(file_path)[0]
            
            # 保存应力-应变曲线
            for widget in self.plot_frame.winfo_children():
                if isinstance(widget, FigureCanvasTkAgg):
                    widget.figure.savefig(f"{base_path}_stress_strain_curve.png", dpi=300, bbox_inches='tight')
                    break
            
            # 保存弹性区拟合图
            for widget in self.elastic_frame.winfo_children():
                if isinstance(widget, FigureCanvasTkAgg):
                    widget.figure.savefig(f"{base_path}_elastic_fit.png", dpi=300, bbox_inches='tight')
                    break
            
            messagebox.showinfo("成功", f"分析结果已保存至:\n{file_path}")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出结果时出错:\n{str(e)}")


if __name__ == "__main__":
    root = tk.Tk()
    app = StressStrainAnalyzerApp(root)
    root.mainloop() 