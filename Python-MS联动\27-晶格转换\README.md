# CIF晶格转换工具

## 项目概述
本工具用于批量处理CIF晶体结构文件，执行坐标轴变换，使垂直于001的面变为原晶胞的特定晶面指数，同时保持坐标轴正交。这对于材料科学和晶体学研究中需要从不同方向研究晶体结构的场景非常有用。

## 功能特点
- 批量处理CIF文件
- 将垂直于001的面转换为以下晶面指数：
  - 100、010
  - 101、-101
  - 110、-110
  - 011、0-11
- 确保转换后坐标轴保持正交
- 针对非立方晶系结构提供正交化处理
- 保留原子位置相对关系

## 技术原理
工具基于晶体学坐标变换原理，实现了以下转换方法：
1. 获取原始晶胞参数
2. 根据目标晶面指数计算变换矩阵
3. 应用变换矩阵到晶胞参数和原子坐标
4. 对非立方晶系进行正交化处理
5. 生成新的CIF文件

## 使用方法
```bash
python cif_transformer.py --input <input_dir> --output <output_dir> --planes <plane_indices>
```

### 参数说明
- `--input`: 输入CIF文件或目录路径
- `--output`: 输出目录路径
- `--planes`: 要生成的晶面指数列表，以逗号分隔，如 "100,010,110"
- `--prefix`: (可选) 输出文件名前缀
- `--suffix`: (可选) 输出文件名后缀

### 使用示例
```bash
# 处理单个文件，转换为所有支持的晶面
python cif_transformer.py --input example.cif --output transformed_cifs/

# 处理目录中所有CIF文件，仅转换为100和010晶面
python cif_transformer.py --input cif_files/ --output transformed_cifs/ --planes 100,010
```

## 环境要求
- Python 3.7+
- NumPy
- PyCifRW (用于CIF文件读写)

## 安装依赖
```bash
pip install numpy pycifrw
```

## 转换参考
下表显示了不同晶面的坐标轴设置：

| 目标晶面 | 新坐标轴A | 新坐标轴B | 新坐标轴C |
|---------|----------|----------|----------|
| 010     | 001      | 100      | 010      |
| 100     | 010      | 001      | 100      |
| 101     | 10-1     | 010      | 101      |
| -101    | 101      | 010      | 10-1     |
| 110     | -110     | 001      | 110      |
| -110    | 110      | 001      | -110     |
| 011     | 100      | 0-11     | 011      |
| 0-11    | 100      | 011      | 0-11     | 