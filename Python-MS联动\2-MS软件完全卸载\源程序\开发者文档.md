# MS卸载助手 - 开发者文档

## 项目概述

MS卸载助手是一个专门用于完全卸载 Materials Studio 及其相关组件的工具。本工具采用 Python 编写，具有图形用户界面，能够自动化处理卸载过程中的各种对话框、清理系统残留文件和注册表项，并设有一次性使用限制的激活机制。

## 系统架构

### 核心组件

1. **主程序 (`ms_uninstaller.py`)**
   - 负责执行卸载流程
   - 提供图形用户界面
   - 实现激活机制验证
   - 管理卸载过程中的各种自动化操作

2. **激活工具 (`激活工具.py`)**
   - 根据用户机器码生成唯一的激活序列码
   - 提供简单的图形用户界面
   - 支持特殊码和管理员码的生成

3. **重置码生成器 (`重置码生成器.py`)**
   - 生成用于重置程序使用状态的重置码
   - 基于用户的机器码生成唯一的重置码

4. **构建脚本 (`build.py`)**
   - 用于打包和构建可分发的应用程序
   - 管理依赖和资源文件

### 文件结构

```
MS卸载助手/
├── ms_uninstaller.py      # 主程序源代码
├── 激活工具.py            # 激活工具源代码
├── 重置码生成器.py        # 重置码生成器源代码
├── build.py               # 构建脚本
├── requirements.txt       # 依赖列表
├── README.md              # 项目说明文件
├── LICENSE                # 许可证文件
└── dist/                  # 构建输出目录
    ├── MS卸载助手.exe     # 打包后的主程序
    ├── 激活工具.py        # 复制的激活工具
    ├── 重置码生成器.py    # 复制的重置码生成器
    └── README.txt         # 使用说明
```

## 技术实现详情

### 1. 主程序 (`ms_uninstaller.py`)

#### 1.1 UI设计与实现

主程序使用 Tkinter 库实现图形用户界面，采用多页面结构：

- **欢迎页面**：显示使用说明和激活码输入界面
- **进度页面**：显示卸载进度和实时日志
- **完成页面**：显示卸载结果和摘要信息

界面采用的主要技术：
- `ttk` 模块提供的现代化UI控件
- 自定义样式以美化界面
- 滚动画布实现内容溢出处理
- 进度条实时展示卸载进度

#### 1.2 卸载流程实现

卸载流程主要涉及以下步骤：

1. **扫描并识别安装组件**
   - 扫描控制面板中的已安装程序
   - 识别 Materials Studio 及相关组件

2. **自动卸载主程序和组件**
   - 调用卸载程序并自动处理各种对话框
   - 使用 PyWinAuto 和 PyAutoGUI 实现窗口自动化
   - 监控卸载进程并实时更新界面

3. **清理残留项**
   - 清理注册表中的残留项
   - 删除残留文件和文件夹
   - 清理环境变量
   - 清理临时文件

#### 1.3 激活机制实现

主程序实现了一次性使用限制机制：

- 获取机器唯一标识 (`get_machine_id`)：
  ```python
  def get_machine_id():
      # 使用更多硬件特征来生成稳定ID
      machine_id = str(uuid.getnode())
      disk_serial = ''
      try:
          c_drive = 'C:\\'
          if os.path.exists(c_drive):
              volume_info = subprocess.check_output('vol C:', shell=True).decode('utf-8')
              for line in volume_info.split('\n'):
                  if 'Volume Serial Number' in line:
                      disk_serial = line.split(':')[1].strip()
                      break
      except:
          pass
      
      return machine_id + disk_serial
  ```

- 激活码验证 (`validate_activation_code`)：
  ```python
  def validate_activation_code(code, machine_id):
      # 清理输入
      cleaned_machine_id = ''.join(c for c in machine_id if c in string.ascii_letters + string.digits)
      
      # 检查特殊激活码
      special_codes = ["M3A7E5R1I9A2L8S6", "B5I9O3V7I1A4_2023"]
      if code in special_codes:
          return True
      
      # 检查管理员激活码
      admin_codes = ["MSADMIN2023UNLOCK", "MS2023RESETCODE16"]
      if code in admin_codes:
          return True
      
      # 使用确定性方法生成激活码进行验证
      seed_string = f"MS_ACTIVATION_{cleaned_machine_id}"
      random.seed(seed_string)
      expected_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=16))
      
      return code == expected_code
  ```

- 使用状态管理：
  - 使用 Windows 的 APPDATA 目录存储配置文件
  - 成功卸载后标记程序已使用
  - 提供重置机制以允许再次使用

### 2. 激活工具 (`激活工具.py`)

激活工具是一个独立的脚本，专门用于生成激活码：

- 使用与主程序相同的机器码获取方法
- 使用相同的激活码生成算法，确保兼容性
- 提供特殊测试码和管理员码生成功能
- 简单的剪贴板集成，方便复制粘贴

关键代码段：
```python
def generate_code(machine_id):
    """根据机器码生成激活序列码"""
    # 清理输入，只保留字母和数字
    cleaned_machine_id = ''.join(c for c in machine_id if c in string.ascii_letters + string.digits)
    
    # 使用确定性种子，确保生成的激活码可重复
    seed_string = f"MS_ACTIVATION_{cleaned_machine_id}"
    random.seed(seed_string)
    
    # 生成16位激活码
    activation_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=16))
    
    return activation_code
```

### 3. 重置码生成器 (`重置码生成器.py`)

重置码生成器与激活工具类似，但专门用于生成重置码：

- 使用不同的种子值和算法生成重置码
- 生成10位的重置码（区别于16位的激活码）
- 提供简单的UI方便管理员操作

关键代码段：
```python
def generate_reset_code(machine_id):
    """生成重置码"""
    # 清理输入，只保留字母和数字
    machine_id = ''.join(c for c in machine_id if c in string.ascii_letters + string.digits)
    
    # 使用确定性种子，确保生成的重置码可重复
    random.seed(f"MS_RESET_{machine_id}")
    
    # 生成10位重置码
    reset_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
    return reset_code
```

### 4. 构建系统 (`build.py`)

构建脚本用于将项目打包成可分发的应用程序：

- 使用 PyInstaller 打包主程序为单独的可执行文件
- 复制激活工具和重置码生成器到输出目录
- 创建 README.txt 文件提供使用说明
- 管理依赖和排除不必要的库

```python
def build():
    """构建MS卸载助手"""
    # 创建输出目录
    output_dir = "dist"
    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)
    os.makedirs(output_dir)
    
    # 使用PyInstaller打包主程序
    cmd = [
        sys.executable,  # 当前Python解释器路径
        "-m", "PyInstaller",
        "--name=MS卸载助手",
        "--onefile",
        "--windowed",
        "--clean",
        "--noconfirm",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy",
        "--exclude-module=scipy",
        "--exclude-module=pandas",
        "--exclude-module=pytest",
        "ms_uninstaller.py"
    ]
    
    # 复制其他文件到输出目录
    if os.path.exists("激活工具.py"):
        shutil.copy("激活工具.py", os.path.join(output_dir, "激活工具.py"))
    
    if os.path.exists("重置码生成器.py"):
        shutil.copy("重置码生成器.py", os.path.join(output_dir, "重置码生成器.py"))
    
    # 创建说明文件
    with open(os.path.join(output_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_text)
```

## 自动化卸载技术详解

### 1. 对话框自动化处理

主程序使用 PyWinAuto 和 PyAutoGUI 实现对话框的自动化处理：

- 延迟导入这些库以加快启动速度
- 使用窗口查找和匹配技术识别对话框
- 自动点击常见的按钮（如 "下一步"、"是"、"完成" 等）
- 处理不可预测的对话框和警告

```python
def import_automation_modules():
    global PYWINAUTO_AVAILABLE
    
    if not PYWINAUTO_AVAILABLE:
        return False
        
    try:
        global pywinauto, Application, find_windows, pyautogui
        
        import pyautogui
        from pywinauto.application import Application
        from pywinauto.findwindows import find_windows
        
        # 设置pyautogui的默认行为
        pyautogui.PAUSE = 0.1  # 设置操作间隔
        pyautogui.FAILSAFE = False  # 关闭故障保护
        
        return True
    except Exception as e:
        print(f"无法导入自动化模块: {str(e)}")
        PYWINAUTO_AVAILABLE = False
        return False
```

### 2. 注册表操作

主程序使用 `winreg` 模块和 `subprocess` 调用来操作注册表：

- 查询与 Materials Studio 相关的注册表项
- 删除注册表中的残留项
- 处理权限和异常情况

### 3. 文件和文件夹操作

主程序使用以下技术安全地操作文件系统：

- 使用 `os` 和 `shutil` 模块删除文件和文件夹
- 添加错误处理和重试机制处理文件锁定情况
- 使用延迟删除机制处理系统保护的文件

## 安全性考虑

1. **管理员权限检查**
   - 程序启动时检查是否具有管理员权限
   - 如果没有，则自动请求权限

2. **执行安全限制**
   - 禁用网络访问和浏览器调用功能
   - 限制第三方进程的调用范围

3. **数据安全**
   - 提供选项可选择是否清理用户数据
   - 默认保留用户的重要数据

## 激活系统安全设计

激活系统采用多层安全设计：

1. **机器绑定**
   - 基于硬件特征生成机器唯一标识
   - 激活码绑定到特定机器

2. **确定性随机**
   - 使用确定性随机数生成算法
   - 相同输入（机器码）始终产生相同输出（激活码）

3. **特殊码机制**
   - 内置特殊测试码用于测试和演示
   - 内置管理员码用于紧急情况

4. **使用后标记**
   - 成功执行后在用户 APPDATA 目录创建标记
   - 防止多次使用同一激活码

5. **重置机制**
   - 提供受控的重置机制
   - 需要管理员生成的特定重置码

## 二次开发指南

### 环境配置

1. 安装必要的 Python 依赖：
   ```
   pip install -r requirements.txt
   ```

2. 主要依赖项：
   - `tkinter` - GUI 界面
   - `pywin32` - Windows API 访问
   - `pywinauto` - 窗口自动化
   - `pyautogui` - 屏幕自动化
   - `pyinstaller` - 应用程序打包

### 修改激活机制

如需修改激活机制，需要同时修改主程序和激活工具中的相关函数：

1. 在 `ms_uninstaller.py` 中修改 `validate_activation_code` 函数
2. 在 `激活工具.py` 中修改 `generate_code` 函数
3. 确保两者使用相同的算法和参数

### 自定义卸载流程

可以通过修改 `perform_uninstall` 方法自定义卸载流程：

1. 添加新的卸载步骤
2. 修改对话框处理逻辑
3. 添加清理其他组件的代码

### 界面定制

1. 修改 `create_welcome_page`、`create_progress_page` 等方法自定义界面
2. 自定义样式可以通过修改 `style` 对象实现
3. 添加新控件和功能可以遵循已有的设计模式

### 重新构建应用

修改完成后，可以使用 `build.py` 脚本重新构建应用：

```
python build.py
```

## 故障排除与常见问题

### 1. 激活码无效

可能的原因：
- 机器码获取不一致
- 激活码生成算法不一致
- 用户输入错误

解决方案：
- 确保 `get_machine_id` 函数在主程序和激活工具中完全一致
- 确保随机数种子和生成逻辑一致
- 使用特殊测试码验证程序功能

### 2. 自动化操作失败

可能的原因：
- 对话框标题或内容变化
- 窗口层叠顺序异常
- PyWinAuto 或 PyAutoGUI 兼容性问题

解决方案：
- 更新窗口识别逻辑
- 增加等待和重试机制
- 增加日志记录，辅助调试

### 3. 构建失败

可能的原因：
- PyInstaller 版本兼容性问题
- 缺少必要的依赖
- 路径或权限问题

解决方案：
- 指定 PyInstaller 的具体版本
- 确保安装所有必要的依赖
- 使用管理员权限运行构建脚本

## 性能优化

1. **懒加载机制**
   - 延迟导入耗时的模块（如 PyWinAuto）
   - 仅在需要时初始化重量级组件

2. **资源管理**
   - 及时释放不再需要的资源
   - 使用线程而非进程以减少资源消耗

3. **超时机制**
   - 为所有长时间操作添加超时机制
   - 防止程序因单一操作卡住

## 版本历史

记录程序的主要版本和变更历史。

## 贡献指南

如果您希望为项目做出贡献，请遵循以下步骤：
1. Fork 仓库
2. 创建功能分支
3. 提交更改
4. 创建合并请求

## 许可说明

本项目为专用软件，未经授权不得分发或修改。详情请参考 LICENSE 文件。 

- **管理员模式**：适用于特殊情况下绕过激活验证
  - 使用内置管理员码：MSADMIN2023UNLOCK 或 MS2023RESETCODE16