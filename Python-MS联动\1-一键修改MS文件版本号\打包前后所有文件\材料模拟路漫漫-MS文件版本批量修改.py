import os
import subprocess
import tkinter as tk
from tkinter import simpledialog, messagebox

# 定义默认要查找和替换的关键字及版本号，合并到字典中
VERSION_CONFIG = {
    '<XSD Version="': '18.1',
    '<XCD Version="': '18.1'
}
XCD_NUM_CHARTS = 'NumCharts="1">'

def modify_file(file_path):
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        # 检查文件行数是否足够
        if len(lines) >= 3:
            for key, target_version in VERSION_CONFIG.items():
                if file_path.endswith(('.xsd', '.xtd')) and key == '<XSD Version="':
                    if key in lines[2]:
                        start_index = lines[2].find(key) + len(key)
                        end_index = lines[2].find('"', start_index)
                        lines[2] = lines[2][:start_index] + target_version + lines[2][end_index:]
                elif file_path.endswith('.xcd') and key == '<XCD Version="':
                    if key in lines[2]:
                        start_index = lines[2].find(key) + len(key)
                        end_index = lines[2].find('"', start_index)
                        lines[2] = lines[2][:start_index] + target_version + lines[2][end_index:]
                        if 'NumCharts=' not in lines[2]:
                            lines[2] = lines[2].rstrip() + ' ' + XCD_NUM_CHARTS + '\n'
        # 将修改后的内容写回文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.writelines(lines)
        print(f"已修改文件: {file_path}")
    except PermissionError:
        print(f"权限错误，无法修改文件: {file_path}")
    except UnicodeDecodeError:
        print(f"编码错误，无法读取文件: {file_path}，请检查文件编码是否为 UTF-8")
    except Exception as e:
        print(f"处理文件 {file_path} 时出现未知错误: {e}")

def process_folder(folder):
    try:
        for root, dirs, files in os.walk(folder):
            for file in files:
                if file.endswith(('.xsd', '.xtd', '.xcd')):
                    file_path = os.path.join(root, file)
                    modify_file(file_path)
    except PermissionError:
        print(f"权限错误，无法访问文件夹: {folder}")
    except Exception as e:
        print(f"处理文件夹 {folder} 时出现未知错误: {e}")

def package_script():
    # 获取当前工作目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    # 构建脚本文件的完整路径
    script_path = os.path.join(current_dir, 'modify_files.py')
    # 检查脚本文件是否存在
    if not os.path.exists(script_path):
        print(f"脚本文件 {script_path} 不存在，请检查路径。")
        print("您可以手动指定脚本文件的完整路径，例如：")
        print('script_path = "C:/your/full/path/to/modify_files.py"')
        return
    try:
        result = subprocess.run(['pyinstaller', '--onefile', script_path], capture_output=True, text=True, check=True)
        print("脚本打包成功！")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"打包脚本时出现错误: {e}")
        print(e.stderr)
    except FileNotFoundError:
        print("未找到 PyInstaller，请确保已经安装。")

if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()

    # 免责声明
    disclaimer = """
免责声明：

本程序仅供学习和参考使用。作者尽力确保程序的准确性和可靠性，但不对程序的使用结果承担任何法律责任。在使用本程序前，请确保您已经了解并遵守相关法律法规。使用本程序所产生的一切风险和后果由使用者自行承担。

如果您觉得作者的工作对您有帮助，请不要吝啬，可以联系作者进行打赏。


感谢您的理解和支持！
    """
    messagebox.showinfo("免责声明与打赏信息", disclaimer)

    # 弹出输入框获取版本号
    target_version = simpledialog.askstring("输入版本号", "请输入目标版本号（默认: 18.1）", initialvalue=list(VERSION_CONFIG.values())[0])
    if target_version is None:
        target_version = list(VERSION_CONFIG.values())[0]
    # 更新 VERSION_CONFIG 字典中的值
    for key in VERSION_CONFIG:
        VERSION_CONFIG[key] = target_version

    current_folder = '.'  # 当前文件夹
    process_folder(current_folder)
    # 如果需要再次打包可以取消注释下面这行
    # package_script()
    