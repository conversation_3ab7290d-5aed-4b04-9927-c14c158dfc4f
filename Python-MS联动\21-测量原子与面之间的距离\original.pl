#!perl

use strict;
use warnings;
use MaterialsScript qw(:all);

# 打开当前文档
my $doc = $Documents{"Graphene.xsd"};
die "错误：未找到XTD文件\n" unless defined $doc;

# 查找Top和Bottom原子集
my $topSet = $doc->UnitCell->Sets("Top");
die "错误：未找到名为'Top'的原子集\n" unless defined $topSet;

my $bottomSet = $doc->UnitCell->Sets("Bottom");
die "错误：未找到名为'Bottom'的原子集\n" unless defined $bottomSet;

# 获取Bottom原子集中的所有原子
my @bottomAtoms = $bottomSet->Atoms;
die "错误：Bottom原子集中没有原子\n" unless @bottomAtoms;

# 获取Top原子集中的所有原子
my @topAtoms = $topSet->Atoms;
die "错误：Top原子集中没有原子\n" unless @topAtoms;

# 从Bottom原子创建最佳拟合平面
my $plane = $doc->CreateBestFitPlane($bottomSet->Atoms);
print "已创建Bottom原子集的最佳拟合平面\n";

# 从Top原子集中选择第一个原子（修正：使用Item方法获取单个原子）
my $selectedAtom = $topSet->Atoms->Item(0);
print "已选择Top原子集中的原子\n";

# 创建从平面到原子的距离测量
my $distance = $doc->CreateDistance([$plane, $selectedAtom]);
print "距离测量已创建\n";

# 修改测量的显示属性
# 使用更精确的属性控制显示方式
$plane->IsHidden = "Yes";  # 隐藏平面，但保留距离测量
print "已隐藏平面，保留距离值\n";

# 获取并打印距离值
my $distanceValue = $distance->Distance;
print "从Bottom平面到Top原子的距离是: $distanceValue Å\n";

print "脚本执行完成\n";
