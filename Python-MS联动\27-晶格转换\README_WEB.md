# CIF晶格转换工具 - Web应用版

## 功能介绍
这是一个基于Web的CIF晶格转换工具，将命令行版本的功能封装到了友好的网页界面中。用户可以通过浏览器上传CIF文件、选择转换选项，并获取转换后的结果。

## 主要功能
- 通过网页上传CIF文件
- 可视化选择需要转换的晶面指数
- 设置输出文件名的前缀和后缀
- 转换后以ZIP格式下载所有结果
- 可在线查看转换后的CIF文件内容
- 转换文件24小时内可随时下载

## 技术实现
Web应用基于以下技术构建：
- Flask: Python Web框架
- HTML/CSS/JavaScript: 前端界面
- 复用了原有的CifTransformer类进行核心转换处理

## 安装和运行

### 安装依赖
```bash
pip install flask numpy pycifrw
```

### 运行Web应用
```bash
python cif_transformer_web.py
```
启动后，通过浏览器访问 http://localhost:5000 即可使用。

## 使用说明

### 上传文件并选择选项
1. 在首页点击"选择文件"按钮，选择要转换的CIF文件
2. 在"选择要转换的晶面指数"下勾选需要的晶面（不选则处理全部）
3. 可选填写输出文件名前缀和后缀
4. 点击"开始转换"按钮

### 查看和下载结果
- 转换完成后会显示结果页面
- 可选择"下载所有文件(ZIP)"或"查看文件列表"
- 在文件列表中可查看每个转换后文件的具体内容

## 项目结构
```
Python-MS联动/27-晶格转换/
├── cif_transformer.py      # 核心转换逻辑
├── cif_transformer_web.py  # Web应用入口
├── templates/              # HTML模板目录
│   ├── index.html          # 首页/上传表单
│   ├── result.html         # 结果页面
│   ├── list_files.html     # 文件列表页面
│   └── view_file.html      # 文件内容查看页面
├── uploads/                # 上传文件临时存储
└── results/                # 转换结果存储
```

## 注意事项
- 上传文件大小限制为16MB
- 转换后的文件会在服务器上保存24小时后自动删除
- Web应用默认监听本机所有网络接口的5000端口
- 生产环境部署时请使用合适的WSGI服务器（如Gunicorn）

## 与命令行版本的关系
Web应用是对命令行版本的封装，核心转换逻辑完全一致，只是提供了更友好的用户界面。如果需要批量自动化处理大量文件，仍然推荐使用命令行版本。 