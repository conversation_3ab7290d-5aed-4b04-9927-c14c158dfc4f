"""
结果导出模块
提供计算结果的多种格式导出功能
"""

import numpy as np
import pandas as pd
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from ..utils.constants import PhysicalConstants

class ResultExporter:
    """结果导出器类"""
    
    def __init__(self):
        self.constants = PhysicalConstants()
    
    def export_to_csv(self, energy: np.ndarray, omega: np.ndarray, sigma_omega: np.ndarray,
                     epsilon_1: Optional[np.ndarray] = None, epsilon_2: Optional[np.ndarray] = None,
                     output_path: str = "conductivity_results.csv") -> str:
        """
        导出结果为CSV格式
        
        Args:
            energy (np.ndarray): 能量数组 (eV)
            omega (np.ndarray): 角频率数组 (rad/s)
            sigma_omega (np.ndarray): 光学导率数组 (S/m)
            epsilon_1 (Optional[np.ndarray]): 介电函数实部
            epsilon_2 (Optional[np.ndarray]): 介电函数虚部
            output_path (str): 输出文件路径
            
        Returns:
            str: 输出文件路径
        """
        # 准备数据字典
        data_dict = {
            'Energy_eV': energy,
            'Frequency_Hz': omega / (2 * np.pi),
            'Angular_Frequency_rad_per_s': omega,
            'Optical_Conductivity_S_per_m': sigma_omega
        }
        
        # 添加介电函数数据（如果提供）
        if epsilon_1 is not None:
            data_dict['Epsilon_1_Real'] = epsilon_1
        if epsilon_2 is not None:
            data_dict['Epsilon_2_Imaginary'] = epsilon_2
        
        # 创建DataFrame
        df = pd.DataFrame(data_dict)
        
        # 添加元数据注释
        metadata_lines = [
            "# CASTEP电导率计算结果",
            f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"# 数据点数: {len(energy)}",
            f"# 能量范围: {energy[0]:.6f} - {energy[-1]:.3f} eV",
            f"# 导率范围: {np.min(sigma_omega):.6e} - {np.max(sigma_omega):.6e} S/m",
            "# 列说明:",
            "#   Energy_eV: 光子能量 (电子伏特)",
            "#   Frequency_Hz: 频率 (赫兹)",
            "#   Angular_Frequency_rad_per_s: 角频率 (弧度/秒)",
            "#   Optical_Conductivity_S_per_m: 光学导率 (西门子/米)",
            "#   Epsilon_1_Real: 介电函数实部",
            "#   Epsilon_2_Imaginary: 介电函数虚部",
            ""
        ]
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            for line in metadata_lines:
                f.write(line + '\n')
        
        # 追加数据
        df.to_csv(output_path, mode='a', index=False, float_format='%.6e')
        
        print(f"结果已导出至CSV文件: {output_path}")
        return output_path
    
    def export_to_excel(self, energy: np.ndarray, omega: np.ndarray, sigma_omega: np.ndarray,
                       epsilon_1: Optional[np.ndarray] = None, epsilon_2: Optional[np.ndarray] = None,
                       fit_result: Optional[Dict[str, Any]] = None,
                       output_path: str = "conductivity_results.xlsx") -> str:
        """
        导出结果为Excel格式
        
        Args:
            energy (np.ndarray): 能量数组 (eV)
            omega (np.ndarray): 角频率数组 (rad/s)
            sigma_omega (np.ndarray): 光学导率数组 (S/m)
            epsilon_1 (Optional[np.ndarray]): 介电函数实部
            epsilon_2 (Optional[np.ndarray]): 介电函数虚部
            fit_result (Optional[Dict[str, Any]]): 拟合结果
            output_path (str): 输出文件路径
            
        Returns:
            str: 输出文件路径
        """
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 主数据表
            data_dict = {
                'Energy_eV': energy,
                'Frequency_Hz': omega / (2 * np.pi),
                'Angular_Frequency_rad_per_s': omega,
                'Optical_Conductivity_S_per_m': sigma_omega
            }
            
            if epsilon_1 is not None:
                data_dict['Epsilon_1_Real'] = epsilon_1
            if epsilon_2 is not None:
                data_dict['Epsilon_2_Imaginary'] = epsilon_2
            
            df_main = pd.DataFrame(data_dict)
            df_main.to_excel(writer, sheet_name='主要数据', index=False)
            
            # 元数据表
            metadata = {
                '参数': ['生成时间', '数据点数', '能量范围(eV)', '导率范围(S/m)', '计算公式'],
                '值': [
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    len(energy),
                    f"{energy[0]:.6f} - {energy[-1]:.3f}",
                    f"{np.min(sigma_omega):.6e} - {np.max(sigma_omega):.6e}",
                    "σ(ω) = ω·ε₂(ω)·ε₀"
                ]
            }
            
            df_metadata = pd.DataFrame(metadata)
            df_metadata.to_excel(writer, sheet_name='元数据', index=False)
            
            # 拟合结果表（如果有）
            if fit_result:
                fit_data = {
                    '参数': [],
                    '值': []
                }
                
                for key, value in fit_result.items():
                    if key not in ['fit_function', 'coefficients', 'covariance', 'fit_indices']:
                        fit_data['参数'].append(key)
                        if isinstance(value, (int, float)):
                            fit_data['值'].append(f"{value:.6e}" if abs(value) < 1e-3 or abs(value) > 1e3 else f"{value:.6f}")
                        else:
                            fit_data['值'].append(str(value))
                
                df_fit = pd.DataFrame(fit_data)
                df_fit.to_excel(writer, sheet_name='拟合结果', index=False)
        
        print(f"结果已导出至Excel文件: {output_path}")
        return output_path
    
    def export_summary_report(self, energy: np.ndarray, sigma_omega: np.ndarray,
                            fit_result: Optional[Dict[str, Any]] = None,
                            output_path: str = "conductivity_summary.txt") -> str:
        """
        导出计算摘要报告
        
        Args:
            energy (np.ndarray): 能量数组 (eV)
            sigma_omega (np.ndarray): 光学导率数组 (S/m)
            fit_result (Optional[Dict[str, Any]]): 拟合结果
            output_path (str): 输出文件路径
            
        Returns:
            str: 输出文件路径
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("CASTEP电导率计算摘要报告\n")
            f.write("=" * 60 + "\n\n")
            
            # 基本信息
            f.write("基本信息:\n")
            f.write("-" * 30 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据点数: {len(energy)}\n")
            f.write(f"能量范围: {energy[0]:.6f} - {energy[-1]:.3f} eV\n")
            f.write(f"最小能量步长: {np.min(np.diff(energy)):.6f} eV\n")
            f.write(f"最大能量步长: {np.max(np.diff(energy)):.6f} eV\n\n")
            
            # 导率统计
            f.write("光学导率统计:\n")
            f.write("-" * 30 + "\n")
            f.write(f"最小值: {np.min(sigma_omega):.6e} S/m\n")
            f.write(f"最大值: {np.max(sigma_omega):.6e} S/m\n")
            f.write(f"平均值: {np.mean(sigma_omega):.6e} S/m\n")
            f.write(f"标准差: {np.std(sigma_omega):.6e} S/m\n")
            f.write(f"低能值(前5点平均): {np.mean(sigma_omega[:5]):.6e} S/m\n")
            f.write(f"高能值(后5点平均): {np.mean(sigma_omega[-5:]):.6e} S/m\n\n")
            
            # 拟合结果
            if fit_result:
                f.write("直流电导率拟合结果:\n")
                f.write("-" * 30 + "\n")
                f.write(f"拟合方法: {fit_result.get('method', 'N/A')}\n")
                f.write(f"拟合点数: {fit_result.get('fit_points', 'N/A')}\n")
                f.write(f"能量阈值: {fit_result.get('energy_threshold', 'N/A')} eV\n")
                
                if 'fit_energy_range' in fit_result:
                    energy_range = fit_result['fit_energy_range']
                    f.write(f"拟合能量范围: {energy_range[0]:.6f} - {energy_range[1]:.6f} eV\n")
                
                f.write(f"直流电导率 σ_dc: {fit_result.get('sigma_dc', 0):.6e} S/m\n")
                f.write(f"拟合质量 R²: {fit_result.get('r_squared', 0):.6f}\n")
                f.write(f"拟合误差: {fit_result.get('fit_error', 0):.6e} S/m\n")
                
                if fit_result.get('method') == 'linear':
                    f.write(f"线性拟合斜率: {fit_result.get('slope', 0):.6e} S/m/eV\n")
                    f.write(f"线性拟合截距: {fit_result.get('intercept', 0):.6e} S/m\n")
                
                f.write("\n")
            
            # 物理常数
            f.write("使用的物理常数:\n")
            f.write("-" * 30 + "\n")
            f.write(f"真空介电常数 ε₀: {self.constants.epsilon_0:.6e} F/m\n")
            f.write(f"元电荷 e: {self.constants.e:.6e} C\n")
            f.write(f"约化普朗克常数 ℏ: {self.constants.hbar:.6e} J·s\n")
            f.write(f"计算公式: σ(ω) = ω·ε₂(ω)·ε₀\n")
            f.write(f"单位转换: ω(rad/s) = E(eV) × e/ℏ\n\n")
            
            f.write("=" * 60 + "\n")
            f.write("报告结束\n")
            f.write("=" * 60 + "\n")
        
        print(f"摘要报告已导出至: {output_path}")
        return output_path
    
    def export_json_results(self, energy: np.ndarray, omega: np.ndarray, sigma_omega: np.ndarray,
                          epsilon_1: Optional[np.ndarray] = None, epsilon_2: Optional[np.ndarray] = None,
                          fit_result: Optional[Dict[str, Any]] = None,
                          output_path: str = "conductivity_results.json") -> str:
        """
        导出结果为JSON格式
        
        Args:
            energy (np.ndarray): 能量数组 (eV)
            omega (np.ndarray): 角频率数组 (rad/s)
            sigma_omega (np.ndarray): 光学导率数组 (S/m)
            epsilon_1 (Optional[np.ndarray]): 介电函数实部
            epsilon_2 (Optional[np.ndarray]): 介电函数虚部
            fit_result (Optional[Dict[str, Any]]): 拟合结果
            output_path (str): 输出文件路径
            
        Returns:
            str: 输出文件路径
        """
        # 准备JSON数据
        json_data = {
            'metadata': {
                'generation_time': datetime.now().isoformat(),
                'data_points': len(energy),
                'energy_range_eV': [float(energy[0]), float(energy[-1])],
                'conductivity_range_S_per_m': [float(np.min(sigma_omega)), float(np.max(sigma_omega))],
                'calculation_formula': 'σ(ω) = ω·ε₂(ω)·ε₀',
                'physical_constants': {
                    'vacuum_permittivity_F_per_m': float(self.constants.epsilon_0),
                    'elementary_charge_C': float(self.constants.e),
                    'reduced_planck_constant_J_s': float(self.constants.hbar)
                }
            },
            'data': {
                'energy_eV': energy.tolist(),
                'angular_frequency_rad_per_s': omega.tolist(),
                'frequency_Hz': (omega / (2 * np.pi)).tolist(),
                'optical_conductivity_S_per_m': sigma_omega.tolist()
            }
        }
        
        # 添加介电函数数据
        if epsilon_1 is not None:
            json_data['data']['epsilon_1_real'] = epsilon_1.tolist()
        if epsilon_2 is not None:
            json_data['data']['epsilon_2_imaginary'] = epsilon_2.tolist()
        
        # 添加拟合结果
        if fit_result:
            # 清理拟合结果，移除不能序列化的对象
            clean_fit_result = {}
            for key, value in fit_result.items():
                if key not in ['fit_function', 'covariance']:
                    if isinstance(value, np.ndarray):
                        clean_fit_result[key] = value.tolist()
                    elif isinstance(value, (np.integer, np.floating)):
                        clean_fit_result[key] = float(value)
                    else:
                        clean_fit_result[key] = value
            
            json_data['fit_result'] = clean_fit_result
        
        # 写入JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        print(f"结果已导出至JSON文件: {output_path}")
        return output_path
    
    def export_all_formats(self, energy: np.ndarray, omega: np.ndarray, sigma_omega: np.ndarray,
                          epsilon_1: Optional[np.ndarray] = None, epsilon_2: Optional[np.ndarray] = None,
                          fit_result: Optional[Dict[str, Any]] = None,
                          base_name: str = "conductivity_results") -> Dict[str, str]:
        """
        导出所有格式的结果文件
        
        Args:
            energy (np.ndarray): 能量数组 (eV)
            omega (np.ndarray): 角频率数组 (rad/s)
            sigma_omega (np.ndarray): 光学导率数组 (S/m)
            epsilon_1 (Optional[np.ndarray]): 介电函数实部
            epsilon_2 (Optional[np.ndarray]): 介电函数虚部
            fit_result (Optional[Dict[str, Any]]): 拟合结果
            base_name (str): 基础文件名
            
        Returns:
            Dict[str, str]: 各格式文件路径字典
        """
        output_files = {}
        
        # CSV格式
        csv_path = f"{base_name}.csv"
        output_files['csv'] = self.export_to_csv(energy, omega, sigma_omega, epsilon_1, epsilon_2, csv_path)
        
        # Excel格式
        excel_path = f"{base_name}.xlsx"
        output_files['excel'] = self.export_to_excel(energy, omega, sigma_omega, epsilon_1, epsilon_2, fit_result, excel_path)
        
        # 摘要报告
        summary_path = f"{base_name}_summary.txt"
        output_files['summary'] = self.export_summary_report(energy, sigma_omega, fit_result, summary_path)
        
        # JSON格式
        json_path = f"{base_name}.json"
        output_files['json'] = self.export_json_results(energy, omega, sigma_omega, epsilon_1, epsilon_2, fit_result, json_path)
        
        print(f"所有格式文件已导出，基础名称: {base_name}")
        return output_files
