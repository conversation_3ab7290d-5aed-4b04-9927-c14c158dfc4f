use strict;
use Getopt::Long;
use MaterialsScript qw(:all);

# Load the trajectory document

my $Set = "Target";
my $doc = $Documents{"NH4Cl Solution.xtd"};
my $trajectory = $doc->Trajectory;
# Create a single table for all frames
my $Table = Documents->New("XYZ.std");
my $sheet = $Table->ActiveSheet;
$sheet->InsertRow($trajectory->NumFrames);
$sheet->ColumnHeading(0) = "FrameTime";
$sheet->ColumnHeading(1) = "Element";
$sheet->ColumnHeading(2) = "X_Cartesian coordinates";
$sheet->ColumnHeading(3) = "Y_Cartesian coordinates";
$sheet->ColumnHeading(4) = "Z_Cartesian coordinates";

# Create a new sheet for centroids
my $newSheet = $Table->InsertSheet(1, "Centroid");
my $sheet1 = $Table->Sheets(1);  # Ensure the second sheet is active
$sheet1->ColumnHeading(0) = "FrameTime";
$sheet1->ColumnHeading(1) = "X_Cartesian coordinates";
$sheet1->ColumnHeading(2) = "Y_Cartesian coordinates";
$sheet1->ColumnHeading(3) = "Z_Cartesian coordinates";

# Ensure data starts from the first row
my $rowIndexAtoms = 0;
my $rowIndexCentroids = 0;

# Iterate over trajectory frames
for (my $frame = 1; $frame <= $trajectory->NumFrames; ++$frame) {
    $trajectory->CurrentFrame = $frame;

    # Create a new .xsd document for the current frame
    my $newDocName = "$frame.xsd";
    my $newDoc = Documents->New("$newDocName");
    
    # Copy the content of the current frame to the new document
    $newDoc->CopyFrom($doc);

    # Get atoms in the set for the current frame
    my $atoms = $newDoc->AsymmetricUnit->Sets($Set)->Atoms;

    # Write atom data
    foreach my $atom (@$atoms) {
        my $name = $atom->Name;
        my $X = $atom->X;
        my $Y = $atom->Y;
        my $Z = $atom->Z;

        $sheet->Cell($rowIndexAtoms, 0) = $trajectory->FrameTime; # Frame time
        $sheet->Cell($rowIndexAtoms, 1) = $name;
        $sheet->Cell($rowIndexAtoms, 2) = $X;
        $sheet->Cell($rowIndexAtoms, 3) = $Y;
        $sheet->Cell($rowIndexAtoms, 4) = $Z;

        $rowIndexAtoms++;
    }

    # Get centroids for the current frame
    my $centroids = $newDoc->UnitCell->Centroids;
    foreach my $centroid (@$centroids) {
        my $center = $centroid->CentroidXYZ;
        my $X = $center->X;
        my $Y = $center->Y;
        my $Z = $center->Z;

        $sheet1->Cell($rowIndexCentroids, 0) = $trajectory->FrameTime; # Frame time
        $sheet1->Cell($rowIndexCentroids, 1) = $X;
        $sheet1->Cell($rowIndexCentroids, 2) = $Y;
        $sheet1->Cell($rowIndexCentroids, 3) = $Z;
        $rowIndexCentroids++;
    }

    # Save and delete the new document if needed
    $newDoc->Delete;
}

# Save the combined table
$Table->Save();