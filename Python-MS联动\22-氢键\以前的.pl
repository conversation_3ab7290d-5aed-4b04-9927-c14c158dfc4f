##################################################################################################################
# perl                                                                                                           #
#                                                                                                                #
# Author: IMATSOFT.DM                                                                                            #
# Version: 1.0                                                                                                   #
# Tested on: Materials Studio 2020                                                                               #
#                                                                                                                #
# Required modules: Materials Visualizer, Forcite                                                                #
# This script aims to calculate hydrogen bonds in a given structure and classify them as intramolecular or       #
# intermolecular. The script outputs the total number of hydrogen bonds, the number of intramolecular hydrogen   #
# bonds, and the number of intermolecular hydrogen bonds. It also records the lengths of each hydrogen bond and  #
# whether it is intramolecular or intermolecular. Additionally, it processes trajectory files to output the      #
# hydrogen bond statistics for each frame.                                                                       #
#                                                                                                                #
# Date: 2024-06-13                                                                                               #
#                                                                                                                #
##################################################################################################################

use strict;
use warnings;
use MaterialsScript qw(:all);
my $starttime = time;
my $timedoc = Documents->New("Time run cost.txt");
#################################################################################################################
#                                         BEGIN USER INPUT                                                      #
#################################################################################################################
# 读取轨迹文件
my $trajectory = $Documents{"S10.xtd"};
my $NumTrj = $trajectory->NumFrames;

#################################################################################################################
#                                         END USER INPUT                                                        #
#################################################################################################################
# 元素列表作为氢键给体
my @speciesList = ("N", "O", "S", "F", "Cl");

# 创建一个新的Study Table用于存储结果
my $statsDoc = Documents->New("HBondStats.std");
$statsDoc->Sheets(0)->Title = "Statistics";
$statsDoc->ColumnHeading(0) = "Frame";
$statsDoc->ColumnHeading(1) = "Total HBonds";
$statsDoc->ColumnHeading(2) = "Intramolecular HBonds";
$statsDoc->ColumnHeading(3) = "Intermolecular HBonds";
$statsDoc->InsertRow($NumTrj);  
my $sheet1 = $Documents{"HBondStats.std"}->ActiveSheet;

# 创建一个新的表格用于存储所有帧的氢键长度和类型
my $allLengthsSheet = $statsDoc->Sheets->InsertSheet();
$allLengthsSheet->Title = "All HBond Lengths";
$allLengthsSheet->ColumnHeading(0) = "Frame";
$allLengthsSheet->ColumnHeading(1) = "Length (A)";
$allLengthsSheet->ColumnHeading(2) = "Type";



# 遍历每一帧
my $lengthRowIndex = 0;
for (my $i = 1; $i <= $trajectory->NumFrames; ++$i) {
    $trajectory->CurrentFrame = $i;
   

    # 创建一个新的文档用于当前帧
    my $frameDoc = Documents->New("Frame_$i.xsd");
    $frameDoc->CopyFrom($trajectory);

    # 初始化变量用于整体统计
    my $totalHBonds = 0;
    my $intramolecularHBonds = 0;

    # 计算目标结构的氢键数目并输出每个氢键的键长
    Tools->BondCalculation->HBonds->Calculate($frameDoc);
    my $hbonds = $frameDoc->UnitCell->HydrogenBonds;
    $totalHBonds = scalar(@$hbonds);

    # 输出每个氢键的键长到All HBond Lengths表格中
    my $rowIndex = $lengthRowIndex;
    for my $hbond (@$hbonds) {
        my $length = $hbond->Length;
        $allLengthsSheet->InsertRow($rowIndex);
        $allLengthsSheet->Cell($rowIndex, 0) = $i;
        $allLengthsSheet->Cell($rowIndex, 1) = $length;
        $allLengthsSheet->Cell($rowIndex, 2) = "Intermolecular";  # 先设为Intermolecular，后面再更新类型
        $rowIndex++;
    }
    $lengthRowIndex = $rowIndex;

    # 复制目标结构生成B结构
    my $bStruct = Documents->New("BStructure_$i.xsd");
    $bStruct->CopyFrom($frameDoc);

    # 处理B结构中的每个片段
    while ($bStruct->UnitCell->Atoms->Count > 0) {
        my $foundDonorOrAcceptor = 0;
        foreach my $hbond (@$hbonds) {
            my $donor = $hbond->Donor;
            my $acceptor = $hbond->Acceptor;

            my $donorElement = $donor->ElementSymbol;
            my $acceptorElement = $acceptor->ElementSymbol;

            next unless grep { $_ eq $donorElement } @speciesList;
            next unless grep { $_ eq $acceptorElement } @speciesList;

            my $donorFragmentAtoms = $donor->FragmentUnitCell;

            # 创建临时文件并复制片段
            my $tempDoc = Documents->New("tempFragment.xsd");
            $tempDoc->CopyFrom($donorFragmentAtoms);

            # 计算分子内氢键数目
            Tools->BondCalculation->HBonds->Calculate($tempDoc);
            my $tempHBonds = $tempDoc->UnitCell->HydrogenBonds;
            my $numTempHBonds = scalar(@$tempHBonds);
            if ($numTempHBonds > 0) {
                $intramolecularHBonds += $numTempHBonds;
                for my $tempHbond (@$tempHBonds) {
                    my $length = $tempHbond->Length;

                    for (my $j = $lengthRowIndex; $j < $rowIndex; $j++) {
                        if ($allLengthsSheet->Cell($j, 1) == $length && $allLengthsSheet->Cell($j, 2) eq "Intermolecular") {
                            $allLengthsSheet->Cell($j, 2) = "Intramolecular";
                            last;
                        }
                    }
                }
            }

            # 删除B结构中的已处理片段
            $donorFragmentAtoms->Delete;

            $foundDonorOrAcceptor = 1;
            $tempDoc->Delete;
            last;
        }

        # 如果没有找到供体或者受体原子，则停止循环
        last unless $foundDonorOrAcceptor;
    }

    # 计算分子间氢键数目
    my $intermolecularHBonds = $totalHBonds - $intramolecularHBonds;

    # 输出整体统计信息
    $sheet1->Cell($i-1, 0) = $i;
    $sheet1->Cell($i-1, 1) = $totalHBonds;
    $sheet1->Cell($i-1, 2) = $intramolecularHBonds;
    $sheet1->Cell($i-1, 3) = $intermolecularHBonds;

    # 删除临时文档
    $frameDoc->Delete;
    $bStruct->Delete;
}

# 保存Study Table文档
$statsDoc->Save;
my $endtime = time;
sub FormatTimeOut {
    my ($secs) = @_;
    if ($secs > 3600) {
        my $h = int($secs / 3600);
        my $m = int(($secs - 3600 * $h) / 60);
        my $s = $secs - 3600 * $h - 60 * $m;
        $timedoc->Append("Time Used: $h hours $m minutes $s seconds");
    } elsif ($secs == 3600) {
        $timedoc->Append("Time Used: 1 hour");
    } elsif ($secs > 60) {
        my $m = int($secs / 60);
        my $s = $secs - 60 * $m;
        $timedoc->Append("Time Used: $m minutes $s seconds");
    } elsif ($secs == 60) {
        $timedoc->Append("Time Used: 1 minute");
    } else {
        $timedoc->Append("Time Used: $secs seconds");
    }
}
FormatTimeOut($endtime-$starttime);