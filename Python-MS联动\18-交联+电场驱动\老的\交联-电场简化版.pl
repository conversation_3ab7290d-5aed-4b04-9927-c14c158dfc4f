#!perl

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);
use constant TRUE 	=> 1;
use constant FALSE 	=> 0;
use constant DEBUG	=> 1; # 较小的值减少输出
use constant PICO_TO_FEMTO => 1000;

# 目的：在电场条件下对原子模型进行交联
# 模块：Forcite
# 修订日期：改进版本 - 优化电场交联效果

# 设置参数（直接硬编码，无GUI界面）
my $xsdDocName			= "Original";	# 初始结构文件名
my $conversionTarget 		= 80;		# 目标转化率（百分比）
my $MinRxnRadius 		= 3;		# 初始反应半径（降低初始半径以更精细地开始）
my $StepRxnRadius 		= 0.5;		# 半径步长（更小的步长以更细致地搜索反应对）
my $MaxRxnRadius 		= 9;		# 最大反应半径
my $IterationsPerRadius		= 3;		# 每个半径的最大迭代次数（增加迭代次数）

# 反应原子和分子的名称
my $monomerName 		= "monomer";	# 单体分子名称
my $monomerReactiveAtom 	= "R1";		# 单体反应原子名称
my $xlinkerName 		= "xlinker";	# 交联剂分子名称
my $xlinkerReactiveAtom 	= "R2";		# 交联剂反应原子名称

# 不使用分子对象（用于自交联或界面交联）
my $noMol			= TRUE;

# 允许单个原子多次反应？
my $react_multi_monomer		= FALSE;
my $react_multi_xlinker		= FALSE;

# 防止相同两个片段之间进行多次交联
my $prevent_intraxlinks		= TRUE;
my %xconnect; # 连接表，用于防止内部交联

# 模拟设置
my $forcefield		 	= "COMPASSIII";
my $timeStep			= 0.1;		# 动力学时间步长（fs）（减小时间步长以提高精度）
my $chargeMethod 		= "Ewald";	# 电荷方法：Atom based, Group based or Ewald（更精确的电荷模型）
my $Quality			= "Fine";	# 质量：Coarse/Medium/Fine/Ultra Fine（提高计算质量）
my $thermostat			= "Nose";	# 恒温器：Andersen, Nose, Velocity Scale, Berendsen or NHL（更适合电场模拟）
my $xlinkTemperature		= 350;		# 主要温度（略微提高以促进反应）
my $xlinkPressure		= 0.0001;	# 主要压力
my $ensemble			= "NVE";	# 系综，通常为NPT，但对于有真空层的液体层等情况，使用NVE（更改为NVE以更好控制温度）

# 电场设置
my $electricFieldX = 0;          # 电场X方向分量
my $electricFieldY = 0;          # 电场Y方向分量  
my $electricFieldZ = 1;          # 电场Z方向分量
my $electricFieldStrength = 0.05;  # 电场强度（单位：V/Å，调整为更合理的值）
my $electricFieldRampUp = TRUE;  # 是否逐渐增加电场强度
my $electricFieldInitial = 0.01; # 初始电场强度
my $electricFieldIncrement = 0.01; # 每次电场强度增量
my $continuousDynamicsRepeats = 5; # 连续电场动力学重复次数（增加重复次数）

# 平衡设置
my $one_time_equilibration	= 20;	# 初始平衡的动力学时间（ps）（增加平衡时间）
my $straightDynamicsTime	= 2;	# 每个新距离前的动力学时间（ps）（增加动力学时间）
my $analyzeDuration		= 2;	# 用于采样热力学数据的动力学时间（ps）（增加分析时间）

# 临时约束键设置
my $UseRestraintBonds		= TRUE;	# 开启/关闭平滑算法
my $RestraintBondingTargetLength = 1.47;	# 使用CVFF中的C-N键参数
my $RestraintForceConstant 	= 356.5988;
my $nRestraintBondingStages	= 5;	# 平滑增加约束键的增量（增加阶段数使约束更平滑）
my $relax_length 		= 2;	# 键合前结构松弛时间（ps）（增加松弛时间）

# 反应性控制
my $preferentialCrosslinking = TRUE; # 启用优先反应选择
my $reactivityThreshold = 0.3; # 反应性阈值（以键长的分数表示）
my $reactivityBias = 1.5; # 优先反应偏好系数

# 为主程序准备变量
my $xlinkCounter = 0;
my $conversion = 0;
my $statTable;
my $structureTable;
my $textDoc;
my $timeDoc;
my $doc;
my $Forcite;
my $Periodicity;
my $rowCounter = 0;
my $mdcounter = 0;
my $geomoptcounter = 0;
my $nstatsheets = 1;
my $currentElectricFieldStrength = $electricFieldInitial; # 当前电场强度

# 反应性数据结构
my %reactivityMap;

###############################################################################
#                             主程序开始                                       #
###############################################################################

# 打开活动文档或指定文档
my $xsdDoc;
eval{ $xsdDoc = Documents->ActiveDocument; };

if ($@) # 无活动文档 - 使用上面定义的参数
{
    $xsdDoc	= $Documents{"$xsdDocName.xsd"};
}

# 创建日志文件
$textDoc = Documents->New("Progress.txt");
$timeDoc = Documents->New("Timings.txt");
$timeDoc->Append("距离 迭代 总时间(小时) 段时间(小时) 转化率(%)\n");
my $segtime = time; # 段时间（秒）

# 复制初始结构以保存起始状态
my $rootName = "xlink";
$doc = Documents->New("$rootName.xsd");
$doc->CopyFrom($xsdDoc);

# 使用全局设置初始化Forcite
$Forcite = Modules->Forcite;
$Forcite->ChangeSettings([
    CurrentForcefield    => $forcefield,
    Quality              => $Quality,
    Temperature          => $xlinkTemperature,
    Pressure             => $xlinkPressure,
    Thermostat           => $thermostat,
    Barostat             => "Andersen",
    TimeStep             => $timeStep,
    TrajectoryFrequency  => 5000,
    AppendTrajectory     => "No",
    WriteVelocities      => "Yes",
    EnergyDeviation      => 1e+024,
    WriteLevel           => "Silent"
]);

# 周期性：支持0和3，2可能也可以工作
if (($doc->SymmetrySystems->Count == 0) || ($doc->SymmetrySystem->SymmetryDefinition->Periodicity == 0))
{
    $Periodicity = 0;
    die "非周期性系统不能使用Ewald方法\n" if ($chargeMethod eq "Ewald");
    $Forcite->ChangeSettings([
        NonPeriodicElectrostaticSummationMethod    => $chargeMethod,
        NonPeriodicvdWSummationMethod              => "Atom based"
    ]);
}
else
{
    $Periodicity = $doc->SymmetrySystem->SymmetryDefinition->Periodicity;
    $Forcite->ChangeSettings([
        '3DPeriodicElectrostaticSummationMethod'   => $chargeMethod,
        '****************************'             => "Atom based",
        '2DPeriodicElectrostaticSummationMethod'   => $chargeMethod,
        '2DPeriodicvdWSummationMethod'             => "Atom based",
    ]);
} 
warn "2D周期性尚未测试\n" if ($Periodicity == 2);

# 防止文件名冲突
$xsdDoc->Name = "initial" if ($xsdDoc->Name =~ /^xlink_/);
$xsdDoc->Close;

# 创建研究表格以保存统计数据
$statTable = Documents->New($rootName."_statistics.std");

# 创建研究表格以保存每个距离循环结束时的中间结构
$structureTable = Documents->New($rootName."_structures.std");
$structureTable->ColumnHeading(1) = "距离 (A)";
$structureTable->ColumnHeading(2) = "迭代";
$structureTable->ColumnHeading(3) = "转化率百分比";

###############################################################################
# 初始化交联

# 计算已反应和可反应原子
my $reactiveMonomerAtoms = 0;
my $reactiveXLinkerAtoms = 0;
my $reactedMonomerAtoms  = 0;
my $reactedXLinkerAtoms  = 0;
my $totalMonomerAtoms    = 0;
foreach my $atom (@{$doc->UnitCell->Atoms})
{
    $reactiveMonomerAtoms++ if (isReactiveR1($atom));
    $reactiveXLinkerAtoms++ if (isReactiveR2($atom));
    $reactedMonomerAtoms++  if ($atom->Name =~ /^$monomerReactiveAtom-\d/);
    $reactedXLinkerAtoms++  if ($atom->Name =~ /^$xlinkerReactiveAtom-\d/);
    $totalMonomerAtoms++    if ($atom->Name =~ /^$monomerReactiveAtom/);
}

# xlinkPotential是交联剂可以形成的可能交联数
my $xlinkPotential = $reactiveXLinkerAtoms;
if ($react_multi_xlinker)
{
    $xlinkPotential = 0;
    foreach my $atom (@{$doc->UnitCell->Atoms})
    {
        if ($atom->Name =~ /^$xlinkerReactiveAtom/)
        {
            $xlinkPotential += HCount($atom);
        }
    }
}

# 计算之前运行中形成的交联数
foreach my $bond (@{$doc->UnitCell->Bonds})
{
    $xlinkCounter++ if ($bond->Name =~ /^xlink/);
}

$conversion = calculateConversion($doc);

# 检查单体和交联剂原子的数量是否与转化率一致
my $targetMonomerAtoms = $totalMonomerAtoms * ($conversionTarget / 100) - $reactedMonomerAtoms;
$textDoc->Append("转化率 = 已反应的单体反应原子的百分比\n");
$textDoc->Append("转化率目标 = $conversionTarget, 当前转化率 = $conversion\n");
$textDoc->Append("需要反应的单体原子总数: $targetMonomerAtoms \n");
$textDoc->Append("反应物原子: 单体 $reactiveMonomerAtoms, 交联剂 $reactiveXLinkerAtoms\n");
$textDoc->Save;
die "需要更多的交联剂才能达到指定的转化率\n" 
    if ($xlinkPotential < $targetMonomerAtoms);

###############################################################################
# 一次性平衡

if ($one_time_equilibration > 0)
{
    my $steps = ($one_time_equilibration * PICO_TO_FEMTO / $timeStep);
    $textDoc->Append("\n初始平衡\n");
    
    # 先进行NVE平衡，不加电场
    $textDoc->Append("进行NVE平衡，不加电场...\n");
    my $results = $Forcite->Dynamics->Run($doc, [
        Ensemble3D => "NVE",
        Temperature => $xlinkTemperature,
        TimeStep => $timeStep,
        NumberOfSteps => $steps,
        TrajectoryFrequency => 5000,
        WriteVelocities => "Yes"
    ]);
    $results->Trajectory->Delete;
    
    # 逐步增加电场强度，进行多次动力学平衡
    if ($electricFieldRampUp)
    {
        $textDoc->Append("使用逐步增加的电场强度进行多次动力学平衡...\n");
        $currentElectricFieldStrength = $electricFieldInitial;
        
        while ($currentElectricFieldStrength <= $electricFieldStrength)
        {
            $textDoc->Append(sprintf "  使用电场强度 %.4f V/Å 进行%s系综平衡...\n", 
                             $currentElectricFieldStrength, $ensemble);
            $textDoc->Save;
            
            my $results = $Forcite->Dynamics->Run($doc, [
                Ensemble3D => $ensemble,
                Temperature => $xlinkTemperature,
                TimeStep => $timeStep,
                NumberOfSteps => int($steps/3), # 减少步数
                TrajectoryFrequency => 5000,
                WriteVelocities => "Yes",
                ElectricFieldX => $electricFieldX,
                ElectricFieldY => $electricFieldY,
                ElectricFieldZ => $electricFieldZ,
                ElectricFieldStrength => $currentElectricFieldStrength
            ]);
            $results->Trajectory->Delete;
            
            # 增加电场强度
            $currentElectricFieldStrength += $electricFieldIncrement;
            if ($currentElectricFieldStrength > $electricFieldStrength) {
                $currentElectricFieldStrength = $electricFieldStrength;
            }
        }
    }
    
    # 使用最终电场强度进行系综平衡
    $textDoc->Append(sprintf "使用最终电场强度 %.4f V/Å 进行$ensemble平衡，连续%d次...\n", 
                     $electricFieldStrength, $continuousDynamicsRepeats);
    
    for (my $i = 1; $i <= $continuousDynamicsRepeats; $i++) {
        $textDoc->Append("  进行第$i/$continuousDynamicsRepeats次连续电场动力学...\n");
        $textDoc->Save;
        
        my $results = $Forcite->Dynamics->Run($doc, [
            Ensemble3D => $ensemble,
            Temperature => $xlinkTemperature,
            TimeStep => $timeStep,
            NumberOfSteps => $steps,
            TrajectoryFrequency => 5000,
            WriteVelocities => "Yes",
            ElectricFieldX => $electricFieldX,
            ElectricFieldY => $electricFieldY,
            ElectricFieldZ => $electricFieldZ,
            ElectricFieldStrength => $electricFieldStrength
        ]);
        $results->Trajectory->Delete;
        
        # 每次动力学后进行简短几何优化
        if ($i < $continuousDynamicsRepeats) {
            $textDoc->Append("  电场动力学后进行简短几何优化...\n");
            ForciteGeomOpt($doc, 500);
        }
    }
    
    $textDoc->Append("连续电场动力学平衡完成\n");
    $textDoc->Save;
}

###############################################################################
# 主交联循环
# 逐步增加反应半径
$textDoc->Append("进入主交联循环\n");
$textDoc->Save;

for (my $RxnRadius = $MinRxnRadius; $RxnRadius <= $MaxRxnRadius; $RxnRadius += $StepRxnRadius) 
{
    # 结构的基本名称    
    my $xsdNameDist = sprintf("%s_R%.2f", $rootName, $RxnRadius);

    # 对每个新半径进行平衡（第一个除外）
    if ($RxnRadius > $MinRxnRadius)  
    {
        $textDoc->Append("在新反应半径下平衡\n");
        $doc->Name = $xsdNameDist . "_init";
        ForciteGeomOpt($doc, 2000);
        
        # 进行电场动力学
        $textDoc->Append(sprintf "在新反应半径下进行电场动力学 (%.4f V/Å)，连续%d次...\n", 
                         $electricFieldStrength, $continuousDynamicsRepeats);
        my $steps = ($straightDynamicsTime * PICO_TO_FEMTO / $timeStep);
        
        # 电场辅助构型搜索
        # 首先进行短时间的高温电场动力学，然后降温
        $textDoc->Append("  进行高温构型搜索...\n");
        my $highTempResults = $Forcite->Dynamics->Run($doc, [
            Ensemble3D => $ensemble,
            Temperature => $xlinkTemperature + 50, # 临时提高温度
            TimeStep => $timeStep,
            NumberOfSteps => int($steps/2),
            TrajectoryFrequency => 5000,
            WriteVelocities => "Yes",
            ElectricFieldX => $electricFieldX,
            ElectricFieldY => $electricFieldY,
            ElectricFieldZ => $electricFieldZ,
            ElectricFieldStrength => $electricFieldStrength * 1.2 # 临时提高电场
        ]);
        $highTempResults->Trajectory->Delete;
        
        # 然后进行正常温度的连续动力学
        for (my $i = 1; $i <= $continuousDynamicsRepeats; $i++) {
            $textDoc->Append("  进行第$i/$continuousDynamicsRepeats次连续电场动力学...\n");
            $textDoc->Save;
            
            my $results = $Forcite->Dynamics->Run($doc, [
                Ensemble3D => $ensemble,
                Temperature => $xlinkTemperature,
                TimeStep => $timeStep,
                NumberOfSteps => $steps,
                TrajectoryFrequency => 5000,
                WriteVelocities => "Yes",
                ElectricFieldX => $electricFieldX,
                ElectricFieldY => $electricFieldY,
                ElectricFieldZ => $electricFieldZ,
                ElectricFieldStrength => $electricFieldStrength
            ]);
            $results->Trajectory->Delete;
            
            # 每次动力学后进行简短几何优化
            if ($i < $continuousDynamicsRepeats) {
                ForciteGeomOpt($doc, 300);
            }
        }
        
        $textDoc->Append("连续电场动力学完成\n");
        $textDoc->Save;
    }

    # 在每个半径进行交联迭代，直到以下条件之一满足：
    # A) 达到最大迭代次数
    # B) 没有新的交联形成
    # C) 达到转化率目标
    for (my $iteration = 1; $iteration <= $IterationsPerRadius; $iteration++)
    {
        $textDoc->Append("\n\n##########################################################\n");
        $textDoc->Append("###### 反应半径 $RxnRadius, 迭代 $iteration\n");
        $textDoc->Append("##########################################################\n\n");
        $textDoc->Save;
            
        $doc->Name = $xsdNameDist."_".$iteration;
        
        # 先进行电场调整，以帮助定向反应性基团
        $textDoc->Append("调整电场定向反应性基团...\n");
        
        # 增强电场动力学以促进定向 - 先短暂提高电场强度
        my $enhancedFieldStrength = $electricFieldStrength * 1.5;
        $textDoc->Append(sprintf "  临时增强电场 (%.4f V/Å) 以定向反应基团...\n", $enhancedFieldStrength);
        
        my $steps = ($straightDynamicsTime * PICO_TO_FEMTO / $timeStep);
        my $results = $Forcite->Dynamics->Run($doc, [
            Ensemble3D => $ensemble,
            Temperature => $xlinkTemperature,
            TimeStep => $timeStep,
            NumberOfSteps => int($steps/2),
            TrajectoryFrequency => 5000,
            WriteVelocities => "Yes",
            ElectricFieldX => $electricFieldX,
            ElectricFieldY => $electricFieldY,
            ElectricFieldZ => $electricFieldZ,
            ElectricFieldStrength => $enhancedFieldStrength
        ]);
        $results->Trajectory->Delete;
        
        # 恢复正常电场进行平衡
        $textDoc->Append(sprintf "  恢复正常电场 (%.4f V/Å) 进行平衡...\n", $electricFieldStrength);
        $results = $Forcite->Dynamics->Run($doc, [
            Ensemble3D => $ensemble,
            Temperature => $xlinkTemperature,
            TimeStep => $timeStep,
            NumberOfSteps => int($steps/2),
            TrajectoryFrequency => 5000,
            WriteVelocities => "Yes",
            ElectricFieldX => $electricFieldX,
            ElectricFieldY => $electricFieldY,
            ElectricFieldZ => $electricFieldZ,
            ElectricFieldStrength => $electricFieldStrength
        ]);
        $results->Trajectory->Delete;
        $textDoc->Append("电场动力学完成\n");
        $textDoc->Save;

        # 在创建新交联前进行一次几何优化，使结构更稳定
        $textDoc->Append("交联前进行几何优化...\n");
        ForciteGeomOpt($doc, 1000);
        $textDoc->Save;

        # 对当前结构创建新的交联键
        my $numBonds = createNewXlinks($doc, $RxnRadius);

        # 更新转化率
        $conversion = calculateConversion($doc);
        $textDoc->Append(sprintf "交联键数量= %d \n转化率= %.01F %%\n", $xlinkCounter, $conversion);                
        $textDoc->Save;
            
        # 如果没有新键形成，退出循环并进入下一个反应半径
        if ($numBonds == 0)
        {
            $textDoc->Append("没有新键生成，增加反应距离\n");
            last;
        }
        
        # 扰动以消除可能产生的长键
        optimizeAndPerturb($doc);

        # 将结构保存到研究表格中
        $textDoc->Append("将中间结构保存到研究表格\n");                
        $doc->InsertInto($structureTable);
        $structureTable->Cell($rowCounter,1) = $RxnRadius;
        $structureTable->Cell($rowCounter,2) = $iteration;
        $structureTable->Cell($rowCounter,3) = $conversion;
        $rowCounter++;

        # 运行电场下的动力学以记录热力学性质
        $textDoc->Append("\n\n进行电场下的额外动力学分析，连续$continuousDynamicsRepeats次\n");
        $steps = ($analyzeDuration * PICO_TO_FEMTO / $timeStep);
        my $freq = int($steps/20);
        
        for (my $i = 1; $i <= $continuousDynamicsRepeats; $i++) {
            $textDoc->Append("  进行第$i/$continuousDynamicsRepeats次连续分析动力学...\n");
            $textDoc->Save;
            
            my $results = $Forcite->Dynamics->Run($doc, [
                Ensemble3D => $ensemble,
                Temperature => $xlinkTemperature,
                TimeStep => $timeStep,
                NumberOfSteps => $steps,
                TrajectoryFrequency => $freq,
                WriteVelocities => "Yes",
                ElectricFieldX => $electricFieldX,
                ElectricFieldY => $electricFieldY,
                ElectricFieldZ => $electricFieldZ,
                ElectricFieldStrength => $electricFieldStrength
            ]);                            
            $results->Trajectory->Delete;
            
            # 每次分析动力学后进行一次简短几何优化
            if ($i < $continuousDynamicsRepeats) {
                ForciteGeomOpt($doc, 200);
            }
        }
        
        $textDoc->Append("连续分析动力学完成\n");
        $textDoc->Save;

        # 记录此次迭代所用的时间
        $timeDoc->Append(sprintf "%-8.2f %-9d %-17.1f %-16.2f %-8.1f\n", 
            $RxnRadius, $iteration, (time-$^T)/3600, 
            (time-$segtime)/3600, $conversion);
        $segtime = time;
        
        # 保存所有文档，以防脚本终止导致数据丢失        
        Documents->SaveAll;
    
        last if ($conversion >= $conversionTarget);

    } # 下一次迭代
    
    last if ($conversion >= $conversionTarget);

} # 下一个半径
    
# 重命名最终xsd文件
$doc->Name = $rootName."_final";

# 创建交联原子集
XlinkSet($doc);

$textDoc->Append("\n##############################################################\n\n");
$textDoc->Append("计算完成\n");
$textDoc->Append("系统中有 $xlinkCounter 个交联\n");
$textDoc->Append(sprintf "最终转化率 %.1f%\n", $conversion);
$textDoc->Append("几何优化总步数: $geomoptcounter\n");
$textDoc->Append("分子动力学总步数: $mdcounter\n");

# 报告总用时
my $time_hr = (time-$^T)/3600;
$textDoc->Append(sprintf ("\n总时间 %.2f 小时\n", $time_hr));
$textDoc->Append("\n##############################################################\n");
$textDoc->Save;
Documents->SaveAll;

###############################################################################
#                             辅助函数                                         #
###############################################################################

# 计算转化率
sub calculateConversion
{
    my $doc1 = shift;
    # 计算已反应原子
    my $reactedMonomerAtoms  = 0;
    my $totalMonomerAtoms    = 0;
    foreach my $atom (@{$doc1->UnitCell->Atoms})
    {
        $reactedMonomerAtoms++  if ($atom->Name =~ /^$monomerReactiveAtom-\d/);
        $totalMonomerAtoms++    if ($atom->Name =~ /^$monomerReactiveAtom/);
    }

    my $conversion = 100.0 * $reactedMonomerAtoms / $totalMonomerAtoms;
    return $conversion;
}

# 创建'notR1'和'notR2'集合
# 这些用于近接计算，以排除模式进行。
# 寻找接触点：
# (A) 单元格中的R1和R2原子之间
# (B) 单元格中的R1或R2原子与相邻单元格中的映像原子之间。
sub createReactiveAtomSets 
{
    my $doc = shift;

    $textDoc->Append("  createReactiveAtomSets\n");

    # 初始化反应性原子计数器    
    my $R1Counter = 0;
    my $R2Counter = 0;
    
    # 为原子创建两个perl数组    
    my @notR1;
    my @notR2;

    # 根据原子名称创建两个集合
    # 一个包含除R1之外的所有原子，另一个包含除R2之外的所有原子
    my $atoms = $doc->UnitCell->Atoms;
    foreach my $atom (@$atoms) 
    {
        # 检查原子是否为反应性原子    
        if (isReactiveR1($atom)) 
        {                    
            push (@notR2, $atom);
            $R1Counter++;        
        } 
        elsif (isReactiveR2($atom)) 
        {                    
            push (@notR1, $atom);
            $R2Counter++;        
        } 
        else 
        {        
            push (@notR1, $atom);
            push (@notR2, $atom);
        }    
    }

    # 根据原子数组创建集合并隐藏它们
    my $notR1Set = $doc->CreateSet("notR1", \@notR1);
    my $notR2Set = $doc->CreateSet("notR2", \@notR2);
    $notR1Set->IsVisible = 0;
    $notR2Set->IsVisible = 0;

    $textDoc->Append("    $R1Counter 个反应性单体原子\n");
    $textDoc->Append("    $R2Counter 个反应性交联剂原子\n\n");
    $textDoc->Save;
        
    return ($doc, $R1Counter, $R2Counter);
}

# 确定原子是否为单体反应原子
sub isReactiveR1
{
    my $atom = shift;
    my $name = $atom->Name;
    if ($name =~ /^$monomerReactiveAtom/)
    {
        return TRUE if (not $react_multi_monomer and $name eq "$monomerReactiveAtom");
        return TRUE if ($react_multi_monomer and HCount($atom) > 0);
    }
    return FALSE;
}

# 确定原子是否为交联剂反应原子
sub isReactiveR2
{
    my $atom = shift;
    my $name = $atom->Name;
    if ($name =~ /^$xlinkerReactiveAtom/)
    {
        return TRUE if (not $react_multi_xlinker and $name eq "$xlinkerReactiveAtom");
        return TRUE if ($react_multi_xlinker and HCount($atom) > 0);
    }
    return FALSE;
}

# 计算原子连接的氢原子数
sub HCount
{
    my $atom = shift;
    my $n = 0;
    foreach (@{$atom->AttachedAtoms})
    {
        $n++ if ($_->ElementSymbol eq "H");
    }
    return $n;
}

# 几何优化
sub ForciteGeomOpt
{
    my $t0 = time;
    my $doc1 = shift;
    my $steps = shift;
    
    my $results;    
    eval 
    {
        $results = $Forcite->GeometryOptimization->Run($doc1, [MaxIterations => $steps]);
    };

    if ($@) 
    {    
        $textDoc->Append("ForciteGeomOpt: 几何优化失败\n");
        $textDoc->Append($@);
    }

    if (DEBUG) { 
        $textDoc->Append(sprintf "ForciteGeomOpt %d 步, %d 秒\n", $steps, time-$t0); 
        $textDoc->Save; 
    }

    $geomoptcounter += $steps;
    return $results;
}

# Forcite动力学
sub ForciteDynamics
{
    # 启动计时器
    my $t0 = time;
    
    # 必需参数
    my $doc1 = shift;
    my $steps = shift;
    my $ensemble = shift;
    
    # 可选参数
    my $repeats = shift || 1;  # 默认执行1次
    
    # 基本设置
    my @settings = (
        NumberOfSteps => $steps,
        Ensemble3D => $ensemble,
        Temperature => $xlinkTemperature,
        TimeStep => $timeStep,
        TrajectoryFrequency => 5000,
        WriteVelocities => "Yes",
        ElectricFieldX => $electricFieldX,
        ElectricFieldY => $electricFieldY,
        ElectricFieldZ => $electricFieldZ,
        ElectricFieldStrength => $electricFieldStrength
    );
    
    # 添加自定义设置 - 允许覆盖默认值
    if (@_) {
        my %custom_settings = @_;
        foreach my $key (keys %custom_settings) {
            my $found = 0;
            for (my $i = 0; $i < @settings; $i += 2) {
                if ($settings[$i] eq $key) {
                    $settings[$i+1] = $custom_settings{$key};
                    $found = 1;
                    last;
                }
            }
            if (!$found) {
                push @settings, $key, $custom_settings{$key};
            }
        }
    }
    
    my $results;
    for (my $i = 1; $i <= $repeats; $i++) {
        # 如果是多次重复，输出进度
        $textDoc->Append(sprintf "  ForciteDynamics: 第%d/%d次连续动力学\n", $i, $repeats) if ($repeats > 1);
        $textDoc->Save if ($repeats > 1);
            
        # 在eval中运行动力学以防止失败停止脚本
        eval 
        {
            $results = $Forcite->Dynamics->Run($doc1, \@settings);
        };
        if ($@) 
        {
            $textDoc->Append("错误: ForciteDynamics失败\n");
            $textDoc->Append($@);
            die "ForciteDynamics失败\n";
        }
        
        # 删除轨迹以节省空间
        $results->Trajectory->Delete if ($results->Trajectory);
        
        # 如果需要进行多次动力学，在中间添加短的几何优化
        if ($repeats > 1 && $i < $repeats) {
            ForciteGeomOpt($doc1, 100) unless ($i % 2 == 0); # 每隔一次动力学进行一次优化
        }
    }

    # 报告使用的时间
    if (DEBUG) 
    { 
        my $fieldStr = "";
        for (my $i = 0; $i < @settings; $i += 2) {
            if ($settings[$i] eq "ElectricFieldStrength") {
                $fieldStr = sprintf ", 电场强度=%.4f", $settings[$i+1];
                last;
            }
        }
        
        $textDoc->Append(sprintf "ForciteDynamics %d 步 x %d 次, %s 系综%s, %d 秒\n", 
            $steps, $repeats, $ensemble, $fieldStr, time-$t0);
        $textDoc->Save;
    }

    $mdcounter += $steps * $repeats;
    return $results;    
}

# 创建一个包含交联原子和键的集合
sub XlinkSet
{
    my $doc = shift;

    my @xlinked_atoms;
    foreach my $bond (@{$doc->UnitCell->Bonds}) 
    {
        if ($bond->Name =~ /^xlink/) 
        {
            push @xlinked_atoms, $bond->Atom1;
            push @xlinked_atoms, $bond->Atom2;
            push @xlinked_atoms, $bond;
        }
    }
    return if (scalar(@xlinked_atoms) == 0);

    $textDoc->Append("\n创建交联集合\n");
    $doc->CreateSet("Crosslinks", \@xlinked_atoms, [IsVisible => "No", Style => "None"]);
}

# 交联R1-R2近接原子，可以使用约束或真实键
# 重命名原子以防止进一步交联（除非multi选项开启）
sub createNewXlinks 
{
    my $doc1 = shift;
    my $distance = shift;
    
    my $t0 = time;
    $textDoc->Append("createNewXlinks\n");    

    # 更新反应集
    ($doc1, my $R1Count, my $R2Count) = createReactiveAtomSets($doc1);
            
    # 使用基于当前反应距离截断和更新集的集合排除方法重新计算近接
    Tools->BondCalculation->ChangeSettings([
        DistanceCriterionMode    => "Absolute",
        ExclusionMode        => "Set", 
        MaxAbsoluteDistance    => $distance
    ]);
    my $closeContacts = $doc1->CalculateCloseContacts;        
        
    # 删除与相邻单元格中的非R原子的接触
    $textDoc->Append(sprintf "找到 %d 个近接\n", $closeContacts->Count) if (DEBUG);
    foreach my $closeContact (@$closeContacts) 
    {
        my $atom1 = $closeContact->Atom1;
        my $atom2 = $closeContact->Atom2;    
        if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
        {
            $atom1 = $closeContact->Atom2;
            $atom2 = $closeContact->Atom1;
        }
        # 删除，除非两个原子都是反应性的并且类型相反
        $closeContact->Delete unless (isReactiveR1($atom1) and isReactiveR2($atom2));
    }
    $textDoc->Append(sprintf "排除非R原子后: %d\n", $closeContacts->Count) if (DEBUG > 1);

    # 基于电场方向评估反应性
    # 在电场方向上更接近的原子对被优先考虑
    if ($preferentialCrosslinking && $closeContacts->Count > 0) {
        $closeContacts = evaluateReactivityInField($doc1, $closeContacts, $distance);
    }

    # 不允许"内部"交联，即相同单元之间的多重键
    $closeContacts = excludeIntra($doc1, $closeContacts) if ($closeContacts->Count and $prevent_intraxlinks);

    # 排除具有相同原子的交联（每轮每个原子只允许一个新交联）
    $closeContacts = excludeSameAtom($doc1, $closeContacts) if ($closeContacts->Count);
    
    # 排除任何会超过转换目标的交联
    $closeContacts = excludeConversionLimit($doc1, $closeContacts) if ($closeContacts->Count);

    # 将幸存的近接转换为约束或键
    my $newBondCounter = 0;
    
    foreach my $closeContact (@$closeContacts) 
    {
        my $atom1 = $closeContact->Atom1;
        my $atom2 = $closeContact->Atom2;
        if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
        {
            $atom1 = $closeContact->Atom2;
            $atom2 = $closeContact->Atom1;
        }

        # 创建交联（真实或虚拟）                
        if ($UseRestraintBonds)
        {
            createNewBondRestraint($doc1, $atom1, $atom2);
        } 
        else 
        {
            createNewBond($doc1, $atom1, $atom2);
        }
        $newBondCounter++;
    }
    
    $textDoc->Append("  形成了 $newBondCounter 个链接\n");    
    
    EquilibrateRestraintXlinks($doc1) if ($UseRestraintBonds and $newBondCounter > 0);

    $textDoc->Append("  $newBondCounter 个新交联键\n\n");
    $textDoc->Save;
    
    # 调整氢原子
    $doc1->AdjustHydrogen;
    
    # 确保所有R原子具有正确的配位
    testCoordination($doc1);
    
    # 删除近接和集合，为下一个循环做准备    
    $doc1->UnitCell->CloseContacts->Delete;
    $doc1->UnitCell->Sets->Delete;
    
    # 几何优化
    $textDoc->Append("  ");
    ForciteGeomOpt($doc1, 2000);
    
    # 报告时间
    $textDoc->Append(sprintf "\ncreateNewXlinks 耗时 %d 秒\n\n", time-$t0); 
    $textDoc->Save;

    return ($newBondCounter);
}

# 基于电场方向评估反应性
sub evaluateReactivityInField
{
    my $doc1 = shift;
    my $closeContacts = shift;
    my $distance = shift;  # 添加距离参数
    
    $textDoc->Append("  评估电场中的反应性...\n");
    
    # 创建一个临时数组来存储每个近接对的反应性得分
    my @scoredContacts = ();
    
    foreach my $closeContact (@$closeContacts) 
    {
        my $atom1 = $closeContact->Atom1;
        my $atom2 = $closeContact->Atom2;
        
        # 确保R1是第一个原子
        if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
        {
            $atom1 = $closeContact->Atom2;
            $atom2 = $closeContact->Atom1;
        }
        
        # 计算原子对的方向向量
        my $dx = $atom2->X - $atom1->X;
        my $dy = $atom2->Y - $atom1->Y;
        my $dz = $atom2->Z - $atom1->Z;
        
        # 与电场方向的点积（归一化）
        my $length = sqrt($dx*$dx + $dy*$dy + $dz*$dz);
        my $dotProduct = ($dx*$electricFieldX + $dy*$electricFieldY + $dz*$electricFieldZ) / $length;
        
        # 计算与电场方向的相似度 (0-1)，1表示完全平行
        my $alignment = abs($dotProduct);
        
        # 根据与电场方向的对齐程度计算得分
        # 考虑距离因素 - 更近的原子对得分更高
        my $distanceFactor = 1.0 - ($length / $distance);
        my $reactivityScore = $alignment * $reactivityBias + $distanceFactor;
        
        # 存储得分和近接对
        push @scoredContacts, [$closeContact, $reactivityScore];
    }
    
    # 按反应性得分从高到低排序
    my @sortedContacts = sort { $b->[1] <=> $a->[1] } @scoredContacts;
    
    # 清空原近接对象并添加排好序的近接
    $closeContacts->Delete;
    $closeContacts = $doc1->CalculateCloseContacts;
    
    # 仅保留得分高于阈值的一部分近接
    my $kept = 0;
    foreach my $scoredContact (@sortedContacts) {
        if ($scoredContact->[1] > $reactivityThreshold) {
            $closeContacts->Add($scoredContact->[0]);
            $kept++;
        }
        else {
            $scoredContact->[0]->Delete;
        }
    }
    
    $textDoc->Append(sprintf "  基于电场方向，保留 %d/%d 个近接对\n", $kept, scalar(@scoredContacts));
    
    return $closeContacts;
}

# 测试所有R原子是否具有正确的键数
sub testCoordination
{
    my $doc2 = shift;
    my %Coord = (
        "C" => 4,
        "N" => 3,
        "O" => 2,
        "Si" => 4
    );
    my $nmatch = 0; my $nmismatch = 0;
    foreach (@{$doc2->UnitCell->Atoms})
    {
        next unless ($_->Name =~ /^$monomerReactiveAtom/ or $_->Name =~ /^$xlinkerReactiveAtom/);
        next unless (exists $Coord{$_->ElementSymbol});
        my $expectedCoordination = $Coord{$_->ElementSymbol};
        my $actualCoordination = calcCoord($_);
        if ($expectedCoordination == $actualCoordination)
        {
            $nmatch++;
        }
        else
        {
            $nmismatch++;
            $textDoc->Append(sprintf "配位不匹配: 名称 %s, 元素 %s, 预期 %.1f, 实际 %.1f\n", 
                $_->Name, $_->ElementSymbol, $expectedCoordination, $actualCoordination)
                if (DEBUG > 1);
        }
    }
    if ($nmismatch)
    {
        $textDoc->Append(sprintf "警告: %d 个反应性原子中的 %d 个存在配位问题\n", 
            $nmismatch, $nmatch+$nmismatch);
    }
    else
    {
        $textDoc->Append("对 $nmatch 个原子的配位测试通过\n") if (DEBUG);
    }
}

# 计算配位数
sub calcCoord
{
    my $atom = shift;
    my $coord = 0;
    foreach (@{$atom->Bonds})
    {
        $coord++ if ($_->BondType eq "Single");
        $coord+2 if ($_->BondType eq "Double");
        $coord+3 if ($_->BondType eq "Triple");
        $coord+1.5 if ($_->BondType eq "Aromatic");
        $coord+1.5 if ($_->BondType eq "Partial double");
    }
    return $coord;
}

# 删除内部交联
# 内部交联被定义为两个相同原子或片段之间的第二个交联

sub excludeIntra
{
    my $doc2 = shift;
    my $closeContacts = shift;
    
    # 这里我们仅防止已经通过3个或更少键路径长度链接在一起的原子之间的交联
    my @tmpbonds = ();
    foreach my $closeContact (@$closeContacts) 
    {
        my $atom1 = $closeContact->Atom1;
        my $atom2 = $closeContact->Atom2;
        if (alreadyLinked($atom1, $atom2))
        {
            $closeContact->Delete;
        }
        else
        {
            # 创建临时键以防止并发内部交联
            my $bond = $doc2->CreateBond($atom1, $atom2, "Single", [Name=>"tmpbond",Color=>RGB(128,255,0)]);
            push @tmpbonds, $bond;
        }
    }
    foreach (@tmpbonds)
    {
        $_->Delete;
    }
    
    $textDoc->Append(sprintf "内部交联排除后: %d 个近接\n", $closeContacts->Count) if (DEBUG > 1);
    return $closeContacts;
}

# 检查两个原子是否通过3个或更少的键连接
sub alreadyLinked
{
    my ($atom1, $atom2) = @_;
    my $atom2name_orig = $atom2->Name;
    my $atom2name = "atom2_alreadyLinked";
    $atom2->Name = $atom2name;
    my $atom1name = "atom1_alreadyLinked";
    my $atom1name_orig = $atom1->Name;
    $atom1->Name = $atom1name;
    my $onebondconnection = FALSE;
    my $twobondconnection = FALSE;
    my $threebondconnection = FALSE;
    foreach my $atomA (@{$atom1->AttachedAtoms})
    {
        my $atomAname = "atomA_alreadyLinked";
        my $atomAname_orig = $atomA->Name;
        $atomA->Name = $atomAname;
        $onebondconnection = TRUE if ($atomA->Name eq $atom2name);
        foreach my $atomB (@{$atomA->AttachedAtoms})
        {
            next if ($atomB->Name eq $atom1name);
            $twobondconnection = TRUE if ($atomB->Name eq $atom2name);
            foreach my $atomC (@{$atomB->AttachedAtoms})
            {
                next if ($atomC->Name eq $atomAname);
                $threebondconnection = TRUE if ($atomC->Name eq $atom2name);
            }
        }
        $atomA->Name = $atomAname_orig;
    }
    $atom1->Name = $atom1name_orig;
    $atom2->Name = $atom2name_orig;
    return TRUE if ($onebondconnection or $twobondconnection or $threebondconnection);
    return FALSE;
}

# 只允许每个原子在每轮中有一个新的交联（以保守起见）
sub excludeSameAtom
{
    my $doc2 = shift;
    my $closeContacts = shift;
    
    my $lockedAtoms = $doc2->UnitCell->Beads->Atoms; # 空集合
    foreach my $closeContact (@$closeContacts) 
    {
        my $atom1 = $closeContact->Atom1;
        my $atom2 = $closeContact->Atom2;    
        if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
        {
            $atom1 = $closeContact->Atom2;
            $atom2 = $closeContact->Atom1;
        }
        if ($atom1->Name =~ /_LOCKED$/ or $atom2->Name =~ /_LOCKED$/)
        {
            $closeContact->Delete;
            next;
        }
        # 锁定这些原子，使它们不再反应
        $atom1->Name .= "_LOCKED";
        $atom2->Name .= "_LOCKED";
        $lockedAtoms->Add($atom1);
        $lockedAtoms->Add($atom2);
    }
    # 解锁原子
    foreach my $atom (@$lockedAtoms)
    {
        $atom->Name =~ s/_LOCKED$//;
    }
    $textDoc->Append(sprintf "相同原子排除后: %d 个近接\n", $closeContacts->Count) 
        if (DEBUG > 1);
    return $closeContacts;
}

# 删除任何会超过转化率目标的交联
sub excludeConversionLimit
{
    my $doc2 = shift;
    my $closeContacts = shift;
    
    # 创建临时文档来测试交联方案
    my $tmpdoc = $doc2->SaveAs("CrosslinkTest2.xsd");
    my $tmpCC = $tmpdoc->UnitCell->CloseContacts;
    $textDoc->Append("警告: 近接数量不匹配\n") if ($tmpCC->Count != $closeContacts->Count);
    
    # 同步每个文档中的近接
    my $nAsync=0;
    my @cclist = ();
    for (my $i=0; $i < $closeContacts->Count; $i++)
    {
        my $name = sprintf("cc%d", $i+1);
        push @cclist, $name;
        my $ccA = $closeContacts->[$i];
        my $ccB = $tmpCC->[$i];
        $ccA->Name = $name;
        $ccB->Name = $name;
        # 为每个近接创建唯一ID
        my $ccAtest = sprintf("X1%f_X2%f", $ccA->Atom1->X, $ccA->Atom2->X);
        my $ccBtest = sprintf("X1%f_X2%f", $ccB->Atom1->X, $ccB->Atom2->X);
        $nAsync++ if ($ccAtest ne $ccBtest);
    }
    $textDoc->Append("警告: $nAsync 个近接不匹配\n") if ($nAsync);
    
    # 遍历近接，优先考虑列表中较早的
    my $nLimit = -1;
    for (my $i=0; $i<@cclist; $i++)
    {
        my $ccname = $cclist[$i];
        my $cc = $closeContacts->Item($ccname);
        my $closeContact = $tmpCC->Item($ccname);
        
        # 获取原子，单体原子在前
        my $atom1; my $atom2;
        $atom1 = $closeContact->Atom1;
        $atom2 = $closeContact->Atom2;
        if ($atom1->Name =~ m/^$xlinkerReactiveAtom/)
        {
            $atom1 = $closeContact->Atom2;
            $atom2 = $closeContact->Atom1;
        }
        
        # 在临时文档中更新键合
        createNewBond($tmpdoc, $atom1, $atom2, TRUE);
        $tmpdoc->AdjustHydrogen;
        
        my $tmpConversion = calculateConversion($tmpdoc);
        if ($tmpConversion > $conversionTarget)
        {
            $nLimit = $i;
            last;
        }
    }
    $tmpdoc->Delete;
    
    # 删除超过限制的所有接触
    # 这里我们允许一个超过限制的接触触发主循环中的脚本结束
    if ($nLimit >= 0)
    {
        for (my $i=$nLimit+1; $i<@cclist; $i++)
        {
            my $ccname = $cclist[$i];
            my $cc = $closeContacts->Item($ccname);
            $cc->Delete;
        }
    }
    $textDoc->Append(sprintf "转化率限制后: %d 个近接\n", $closeContacts->Count) 
        if (DEBUG > 1);
    return $closeContacts;
}

# 在两个原子之间施加谐波距离约束
sub createNewBondRestraint
{    
    my ($doc2, $atom1, $atom2) = @_;    
    
    # 测量反应原子之间的距离            
    my $distance = $doc2->CreateDistance([$atom1, $atom2]);
    
    # 创建约束，并设置初始平衡键长和力常数
    # (这些在平衡例程中适当更新)                    
            
    my $restraint = $distance->CreateRestraint("Harmonic");
    $restraint->HarmonicForceConstant = 0;
    $restraint->HarmonicMinimum = $distance->Distance;
}

# 创建新的交联键并更改链接的显示样式和名称
# 期望$atom1来自单体，$atom2来自交联剂
sub createNewBond
{    
    my $doc1 = shift;
    my $atom1 = shift;
    my $atom2 = shift;
    my $test = shift;

    # 创建新键
    $xlinkCounter++ unless ($test);
    my $newBond = $doc1->CreateBond($atom1, $atom2, "Single", ([Name => "xlink-".$xlinkCounter]));        
    
    # 设置创建的键的显示样式为球棍式       
    $atom1->Style = "Ball and stick";
    $atom2->Style = "Ball and stick";
    
    # 将交联索引附加到每个原子名称（不会影响未来的交联）
    $atom1->Name .= "-".$xlinkCounter;
    $atom2->Name .= "-".$xlinkCounter;

    # 更新连接表
    if ($prevent_intraxlinks and not $test)
    {
        my $mol1; my $mol2;
        eval{ 
            $mol1 = $atom1->Ancestors->Molecule->Name;
            $mol2 = $atom2->Ancestors->Molecule->Name;
            push @{$xconnect{$mol1}}, $mol2;
            push @{$xconnect{$mol2}}, $mol1;
        };
    }
    
    $textDoc->Append(sprintf "    创建了 %s 和 %s 之间的键 \n\n", $atom1->Name, $atom2->Name)
        unless ($test);
}

# 优化、短NVE动力学
sub optimizeAndPerturb 
{
    $textDoc->Append("\noptimizeAndPerturb\n");
    $textDoc->Save; 
    my $t0 = time; 
    my $mdStepCounter = 0; 
    my ($doc1) = @_;

    # 先做一次较长的几何优化
    $textDoc->Append("  进行全面几何优化...\n");
    ForciteGeomOpt($doc1, 500);
    
    # 使用电场进行短时动力学，帮助结构松弛
    $textDoc->Append("  在电场下进行动力学松弛...\n");
    my $results = ForciteDynamics($doc1, 2000, $ensemble, $continuousDynamicsRepeats, 
                                Temperature => $xlinkTemperature,
                                ElectricFieldStrength => $electricFieldStrength * 0.8); # 略微降低电场强度
    $mdStepCounter += 2000 * $continuousDynamicsRepeats;
    
    # 再次优化，确保结构稳定
    $textDoc->Append("  最终几何优化...\n");
    ForciteGeomOpt($doc1, 300);

    if (DEBUG) {
        $textDoc->Append(sprintf "optimizeAndPerturb %d 总步数, %d 秒\n", 
            $mdStepCounter, time-$t0); 
    }
    $textDoc->Save; 
}

# 逐渐增加约束力并减小约束距离
# 每次迭代包括几何优化和动力学以放松系统
# 最后用键替换约束
sub EquilibrateRestraintXlinks 
{
    my $t0 = time;
    if (DEBUG) { $textDoc->Append("\n  EquilibrateRestraintXlinks\n") };
    my ($doc) = @_;

    my $ForceConstantInc = $RestraintForceConstant/$nRestraintBondingStages;

    # 循环变化约束参数
    for (my $i = 1; $i <= $nRestraintBondingStages; $i++) 
    {
        my $k = $i * $ForceConstantInc;
        $textDoc->Append(sprintf "\n    阶段 %d/%d, 约束 K=%.1f\n", $i, $nRestraintBondingStages, $k) if (DEBUG);
        
        # 收紧约束（增加力常数和减小平衡距离）
        my $restraints = $doc->UnitCell->Restraints;
        foreach my $restraint (@$restraints) 
        {
            my $b0inc = ($restraint->HarmonicMinimum - $RestraintBondingTargetLength)
                    / ($nRestraintBondingStages+1-$i);
            my $b0 = $restraint->HarmonicMinimum - $b0inc;
                    
            $restraint->HarmonicForceConstant = $k;
            $restraint->HarmonicMinimum = $b0;
            
            $textDoc->Append(sprintf "    设置约束 B=%f\n", $b0) if (DEBUG > 3);
        }
        
        # 先做几何优化
        $textDoc->Append("    ");
        ForciteGeomOpt($doc, 1000);
        
        # 然后在电场下做动力学，帮助约束松弛
        $textDoc->Append("    ");
        my $steps = ($relax_length * PICO_TO_FEMTO / $timeStep);
        my $fieldStrength = $electricFieldStrength;
        
        # 对于最后一个阶段，略微增加电场强度以帮助完成键合
        if ($i == $nRestraintBondingStages) {
            $fieldStrength *= 1.2;
            $textDoc->Append(sprintf "    最后阶段，增强电场至 %.4f V/Å\n", $fieldStrength) if (DEBUG);
        }
        
        my $results = ForciteDynamics($doc, $steps, $ensemble, 2, # 减少重复次数
                                    ElectricFieldStrength => $fieldStrength);
    }    
    $textDoc->Append("\n");

    # 现在使用约束对象来识别涉及的原子，完成完整的键合
    my $restraints = $doc->UnitCell->Restraints;
    foreach my $restraint (@$restraints) 
    {
        if (RestraintType($restraint) eq "distance") 
        {
            my $dist = $restraint->Ancestors->Distance;
            my $atom1 = $dist->DistanceNodeI;
            my $atom2 = $dist->DistanceNodeJ;                 
            my $atom1_name = $atom1->Name;
            my $atom2_name = $atom2->Name;
            
            $textDoc->Append(
                sprintf "    在 %s 和 %s 之间创建交联 %d 并删除约束\n",
                    $atom1_name, $atom2_name, $xlinkCounter+1)
            if (DEBUG);
            
            createNewBond($doc, $atom1, $atom2);
                        
            $restraint->Delete;
            $dist->Delete;
        }
    }
    
    # 最终几何优化
    $textDoc->Append("    进行最终几何优化...\n");
    ForciteGeomOpt($doc, 500);
        
    if (DEBUG) 
    { 
        $textDoc->Append(sprintf "\n  EquilibrateRestraintXlinks 耗时 %d 秒\n\n", time-$t0); 
        $textDoc->Save; 
    }
}

# 确定约束类型
sub RestraintType
{
    my ($restraint) = @_;
    my $type = "unknown";
    eval {
        my $ancestors = $restraint->Ancestors;
        if ($ancestors->Distance->Count) {
            $type = "distance";
        }
        elsif ($ancestors->Angle->Count) {
            $type = "angle";
        }
        elsif ($ancestors->Torsion->Count) {
            $type = "torsion";
        }
    };
    return $type;
} 