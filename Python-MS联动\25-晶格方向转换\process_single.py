#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
晶格方向转换演示 - 处理单个CIF文件并展示转换过程
"""

import os
import numpy as np
from pymatgen.core import Structure
from pymatgen.transformations.standard_transformations import (
    RotationTransformation,
    DeformStructureTransformation
)
from pymatgen.io.cif import CifWriter

def process_cif(input_file, output_dir):
    """处理单个CIF文件并保存转换后的结构"""
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 读取原始结构
    print(f"读取CIF文件: {input_file}")
    structure = Structure.from_file(input_file)
    
    # 打印原始结构信息
    print("原始晶胞信息:")
    print(f"晶胞参数: a={structure.lattice.a:.4f}, b={structure.lattice.b:.4f}, c={structure.lattice.c:.4f}")
    print(f"晶胞角度: α={structure.lattice.alpha:.2f}°, β={structure.lattice.beta:.2f}°, γ={structure.lattice.gamma:.2f}°")
    print(f"晶胞体积: {structure.lattice.volume:.2f} Å³")
    print(f"包含原子数: {len(structure)}")
    
    # 定义要处理的晶面方向
    miller_indices = [[1, 0, 0], [0, 1, 0], [0, 0, 1]]
    
    for miller in miller_indices:
        h, k, l = miller
        print(f"\n处理 ({h}{k}{l}) 方向...")
        
        # 步骤1: 创建旋转矩阵，将(hkl)面转向z方向
        # 将Miller面的法向量标准化
        direction = np.array([h, k, l], dtype=float)
        direction = direction / np.linalg.norm(direction)
        
        # 构建旋转矩阵
        # 首先找到一个与direction不平行的向量作为辅助向量
        if abs(direction[0]) < abs(direction[1]) and abs(direction[0]) < abs(direction[2]):
            aux = np.array([1, 0, 0])
        elif abs(direction[1]) < abs(direction[2]):
            aux = np.array([0, 1, 0])
        else:
            aux = np.array([0, 0, 1])
        
        # 构建正交基底
        v3 = direction
        v1 = np.cross(aux, v3)
        v1 = v1 / np.linalg.norm(v1)
        v2 = np.cross(v3, v1)
        v2 = v2 / np.linalg.norm(v2)
        
        # 旋转矩阵
        rotation_matrix = np.column_stack((v1, v2, v3))
        print("旋转矩阵:")
        print(rotation_matrix)
        
        # 步骤2: 应用旋转变换
        rotation = RotationTransformation(rotation_matrix)
        rotated_structure = rotation.apply_transformation(structure)
        
        print("旋转后晶胞信息:")
        print(f"晶胞参数: a={rotated_structure.lattice.a:.4f}, b={rotated_structure.lattice.b:.4f}, c={rotated_structure.lattice.c:.4f}")
        print(f"晶胞角度: α={rotated_structure.lattice.alpha:.2f}°, β={rotated_structure.lattice.beta:.2f}°, γ={rotated_structure.lattice.gamma:.2f}°")
        
        # 步骤3: 正交化处理
        # 使用Gram-Schmidt正交化方法
        a, b, c = rotated_structure.lattice.matrix
        
        # 正交化
        new_a = a
        new_b = b - np.dot(b, new_a) * new_a / np.dot(new_a, new_a)
        new_c = c - np.dot(c, new_a) * new_a / np.dot(new_a, new_a) - np.dot(c, new_b) * new_b / np.dot(new_b, new_b)
        
        # 创建变换矩阵
        trans_matrix = np.column_stack((new_a, new_b, new_c)) / rotated_structure.lattice.matrix
        
        # 应用变换
        deform = DeformStructureTransformation(trans_matrix)
        ortho_structure = deform.apply_transformation(rotated_structure)
        
        print("正交化后晶胞信息:")
        print(f"晶胞参数: a={ortho_structure.lattice.a:.4f}, b={ortho_structure.lattice.b:.4f}, c={ortho_structure.lattice.c:.4f}")
        print(f"晶胞角度: α={ortho_structure.lattice.alpha:.2f}°, β={ortho_structure.lattice.beta:.2f}°, γ={ortho_structure.lattice.gamma:.2f}°")
        
        # 保存结果
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_file = os.path.join(output_dir, f"{base_name}_{h}{k}{l}.cif")
        
        # 写出CIF文件
        writer = CifWriter(ortho_structure, symprec=0.1)
        writer.write_file(output_file)
        print(f"已保存到: {output_file}")

if __name__ == "__main__":
    # 处理当前目录下的CrTe-Pre.cif文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    input_file = os.path.join(current_dir, "CrTe-Pre.cif")
    output_dir = os.path.join(current_dir, "demo_output")
    
    process_cif(input_file, output_dir) 