#!perl

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);
use constant TRUE 	=> 1;
use constant FALSE 	=> 0;
use constant DEBUG	=> 2; # larger is more verbose
use constant PICO_TO_FEMTO => 1000;

# 电场交联代码 - 简化版
# 在电场动力学轨迹中逐帧处理，实时检查R1-R2距离是否满足交联条件

# 用户设置
my $xsdDocName			= "Original";	# 输入文件名
my $conversionTarget 	        = 80;		# 反应原子的百分比目标
my $MinRxnRadius 		= 4;		# 初始接触截断距离
my $StepRxnRadius 		= 0.5;		# 接触距离步长
my $MaxRxnRadius 		= 5;		# 最大接触距离

# 反应原子的特殊名称
my $monomerReactiveAtom 	= "R1";		# 单体分子中的反应原子名称
my $xlinkerReactiveAtom 	= "R2";		# 交联剂中的反应原子名称

# 模拟设置
my $forcefield		 	= "COMPASSIII";
my $timeStep			= 0.1;		# 动力学时间步长（fs）
my $chargeMethod 		= "Atom based";	
my $xlinkTemperature		= 300;		# 模拟温度
my $xlinkPressure		= 0.0001;	# 模拟压力
my $stepCount                   = 10000;      # 动力学步数
my $trajectory_freq             = 100;        # 轨迹输出频率（用于监控距离）
my $ensemble			= "NVE";	# 系综

# 电场设置
my $electricFieldX              = 0;          # X方向电场强度
my $electricFieldY              = 0;          # Y方向电场强度
my $electricFieldZ              = 1;          # Z方向电场强度
my $electricFieldStrength       = 10;         # 电场强度大小

# 获取活动文档
my $xsdDoc;
eval{ $xsdDoc = Documents->ActiveDocument; };

if ($@) # 无活动文档 - 使用上面定义的参数
{
	$xsdDoc	= $Documents{"$xsdDocName.xsd"};
}
else
{
	# 如果有活动文档，可以从GUI中获取参数
	# 在这里可以添加GUI参数处理代码
}

# 创建一个副本以保存初始结构
my $rootName = "electric_xlink";
my $doc = Documents->New("$rootName.xsd");
$doc->CopyFrom($xsdDoc);

# 文件用于报告进度
my $textDoc = Documents->New("Progress.txt");

# 记录时间的文件
my $timeDoc = Documents->New("Timings.txt");
$timeDoc->Append("Frame RxnRadius Elapsted_Time(hr) Conversion(%)\n");
my $segtime = time; # 用于计时

# 初始化Forcite模块
my $Forcite = Modules->Forcite;
$Forcite->ChangeSettings([
	CurrentForcefield	=> $forcefield,
	Temperature		=> $xlinkTemperature,
	Pressure		=> $xlinkPressure,
	TimeStep		=> $timeStep,
	TrajectoryFrequency	=> $trajectory_freq,
	WriteVelocities         => "Yes",
	WriteLevel		=> "Silent"
]);

# 创建统计表
my $statTable = Documents->New($rootName."_statistics.std");
my $structureTable = Documents->New($rootName."_structures.std");
$structureTable->ColumnHeading(1) = "Frame";
$structureTable->ColumnHeading(2) = "Reaction Distance (A)";
$structureTable->ColumnHeading(3) = "Percent Conversion";
$structureTable->ColumnHeading(4) = "Number of Crosslinks";

# 计算初始反应原子数量和转化率
my $reactiveMonomerAtoms = 0;
my $reactiveXLinkerAtoms = 0;
foreach my $atom (@{$doc->UnitCell->Atoms})
{
	$reactiveMonomerAtoms++ if ($atom->Name eq "$monomerReactiveAtom");
	$reactiveXLinkerAtoms++ if ($atom->Name eq "$xlinkerReactiveAtom");
}

my $conversion = calculateConversion($doc);
my $rowCounter = 0;	# 统计表中的当前行

$textDoc->Append("转化率 = 已反应单体原子百分比\n");
$textDoc->Append("转化目标 = $conversionTarget%, 当前转化率 = $conversion%\n");
$textDoc->Append("反应原子: 单体 $reactiveMonomerAtoms 个, 交联剂 $reactiveXLinkerAtoms 个\n");
$textDoc->Save;

# 计数器
my $xlinkCounter = 0;
foreach my $bond (@{$doc->UnitCell->Bonds})
{
	$xlinkCounter++ if ($bond->Name =~ /^xlink/);
}

###########################################################################################
# 一次性平衡
$textDoc->Append("\n进行初始平衡\n");
my $results = $Forcite->Dynamics->Run($doc, [
	Ensemble3D => "NVT",
	Temperature => $xlinkTemperature,
	TimeStep => $timeStep,
	NumberOfSteps => 5000,
	TrajectoryFrequency => 1000
]);
$results->Trajectory->Delete;

###########################################################################################
# 运行电场动力学
$textDoc->Append("\n运行电场动力学\n");
$textDoc->Save;

# 运行一次完整的电场动力学模拟
$doc->Name = $rootName."_dynamics";
$textDoc->Append("开始电场动力学模拟，步数: $stepCount\n");
my $results = $Forcite->Dynamics->Run($doc, [
	Ensemble3D => $ensemble,
	Temperature => $xlinkTemperature,
	TimeStep => $timeStep,
	NumberOfSteps => $stepCount,
	TrajectoryFrequency => $trajectory_freq,
	WriteVelocities => "Yes",
	ElectricFieldX => $electricFieldX,
	ElectricFieldY => $electricFieldY,
	ElectricFieldZ => $electricFieldZ,
	ElectricFieldStrength => $electricFieldStrength
]);

my $trajectory = $results->Trajectory;
my $numFrames = $trajectory->NumFrames;
$textDoc->Append("电场动力学完成，生成了 $numFrames 帧\n");
$textDoc->Save;

###########################################################################################
# 逐帧处理轨迹
$textDoc->Append("\n开始处理动力学轨迹\n");

# 创建工作副本（用于尝试交联）
my $workDoc = Documents->New("work_doc.xsd");

for (my $frame = 1; $frame <= $numFrames; $frame++)
{
	$textDoc->Append("\n##########################################################\n");
	$textDoc->Append("###### 处理帧 $frame\n");
	$textDoc->Append("##########################################################\n\n");
	
	# 将轨迹当前帧复制到工作文档
	$trajectory->CurrentFrame = $frame;
	$workDoc->CopyFrom($trajectory);
	
	# 在这一帧中尝试不同的反应距离
	my $createBonds = FALSE;
	
	for (my $RxnRadius = $MinRxnRadius; $RxnRadius <= $MaxRxnRadius; $RxnRadius += $StepRxnRadius)
	{
		$textDoc->Append("尝试反应距离: $RxnRadius Å\n");
		
		# 尝试在当前距离下创建交联键
		my $numBonds = createNewXlinks($workDoc, $RxnRadius);
		
		if ($numBonds > 0)
		{
			$createBonds = TRUE;
			$conversion = calculateConversion($workDoc);
			$textDoc->Append(sprintf "成功创建 %d 个交联键\n", $numBonds);
			$textDoc->Append(sprintf "当前交联数: %d, 转化率: %.1f%%\n", $xlinkCounter, $conversion);
			
			# 保存当前状态到结构表
			$workDoc->InsertInto($structureTable);
			$structureTable->Cell($rowCounter, 1) = $frame;
			$structureTable->Cell($rowCounter, 2) = $RxnRadius;
			$structureTable->Cell($rowCounter, 3) = $conversion;
			$structureTable->Cell($rowCounter, 4) = $xlinkCounter;
			$rowCounter++;
			
			# 报告所用时间
			$timeDoc->Append(sprintf "%-5d %-10.2f %-17.1f %-8.1f\n", 
				$frame, $RxnRadius, (time-$^T)/3600, $conversion);
			
			# 如果达到目标转化率，结束处理
			if ($conversion >= $conversionTarget)
			{
				$textDoc->Append("达到目标转化率 ($conversionTarget%)，停止处理\n");
				last;
			}
			
			# 在当前距离成功创建键后，跳出当前距离循环，继续处理下一帧
			last;
		}
	}
	
	# 如果在当前帧创建了键，将工作文档复制回轨迹
	if ($createBonds)
	{
		# 调整氢原子位置
		$workDoc->AdjustHydrogen;
		
		# 简单优化以减轻应力
		eval {
			ForciteGeomOpt($workDoc, 100);
		};
		
		# 将优化后的结构复制回轨迹
		$trajectory->CurrentFrame = $frame;
		$trajectory->CopyFrom($workDoc);
		Documents->SaveAll;
	}
	
	# 检查是否达到目标转化率
	if ($conversion >= $conversionTarget)
	{
		$textDoc->Append("整体达到目标转化率，结束处理\n");
		last;
	}
}

# 从最终轨迹帧创建结果文档
$trajectory->CurrentFrame = $trajectory->NumFrames;
my $finalDoc = Documents->New($rootName."_final.xsd");
$finalDoc->CopyFrom($trajectory);

# 优化最终结构
$textDoc->Append("\n优化最终结构\n");
ForciteGeomOpt($finalDoc, 5000);

# 计算键分布
analyzeBonds($finalDoc);

# 创建交联原子集合
XlinkSet($finalDoc);

# 删除工作文档
$workDoc->Delete;

$textDoc->Append("\n##############################################################\n\n");
$textDoc->Append("计算完成\n");
$textDoc->Append("系统中有 $xlinkCounter 个交联\n");
$textDoc->Append(sprintf "最终转化率 %.1f%%\n", $conversion);

# 报告总时间
my $time_hr = (time-$^T)/3600;
$textDoc->Append(sprintf ("\n总时间 %.2f 小时\n", $time_hr));
$textDoc->Append("\n##############################################################\n");
$textDoc->Save;
Documents->SaveAll;

##########################################################################################################
#
#		子程序定义
#
##########################################################################################################

# 计算转化率（已反应的R1原子百分比）
sub calculateConversion
{
	my $doc1 = shift;
	# 计算已反应的原子
	my $reactedMonomerAtoms = 0;
	my $totalMonomerAtoms = 0;
	foreach my $atom (@{$doc1->UnitCell->Atoms})
	{
		$reactedMonomerAtoms++ if ($atom->Name =~ /^$monomerReactiveAtom-\d/);
		$totalMonomerAtoms++ if ($atom->Name =~ /^$monomerReactiveAtom/);
	}

	my $conversion = 100.0 * $reactedMonomerAtoms / $totalMonomerAtoms;
	return $conversion;
}

# 计算两个原子之间的距离
sub getAtomDistance
{
	my $doc = shift;
	my $atom1 = shift;
	my $atom2 = shift;
	
	my $dx = $atom1->X - $atom2->X;
	my $dy = $atom1->Y - $atom2->Y;
	my $dz = $atom1->Z - $atom2->Z;
	
	# 考虑周期性边界条件(如果适用)
	if ($doc->SymmetrySystems->Count > 0)
	{
		my $a = 0;
		my $b = 0;
		my $c = 0;
		my $hasPeriodicity = FALSE;
		
		# 尝试获取第一个具有周期性的对称系统
		eval {
			foreach my $symSystem (@{$doc->SymmetrySystems})
			{
				if ($symSystem->SymmetryDefinition->Periodicity > 0)
				{
					$hasPeriodicity = TRUE;
					# 尝试获取晶胞参数
					$a = $symSystem->SymmetryDefinition->LengthA;
					$b = $symSystem->SymmetryDefinition->LengthB;
					$c = $symSystem->SymmetryDefinition->LengthC;
					last;
				}
			}
		};
		
		# 如果上面方法失败，尝试其他路径
		if ($@ || $a == 0) {
			eval {
				$a = $doc->UnitCell->Parameters->A;
				$b = $doc->UnitCell->Parameters->B;
				$c = $doc->UnitCell->Parameters->C;
			};
		}
		
		# 如果仍然失败，尝试第三种方法
		if ($@ || $a == 0) {
			eval {
				my $cell = $doc->UnitCell;
				$a = $cell->A;
				$b = $cell->B;
				$c = $cell->C;
			};
		}
		
		# 记录晶胞参数以便调试
		if ($a > 0 && $b > 0 && $c > 0) {
			$textDoc->Append(sprintf "  晶胞参数: a=%.2f, b=%.2f, c=%.2f\n", $a, $b, $c) if (DEBUG > 2);
			
			# 调整x方向距离
			if (abs($dx) > 0.5 * $a && $a > 0) {
				$dx = $dx - sign($dx) * $a;
			}
			# 调整y方向距离
			if (abs($dy) > 0.5 * $b && $b > 0) {
				$dy = $dy - sign($dy) * $b;
			}
			# 调整z方向距离
			if (abs($dz) > 0.5 * $c && $c > 0) {
				$dz = $dz - sign($dz) * $c;
			}
		} else {
			$textDoc->Append("  警告: 无法获取晶胞参数，不应用周期性边界条件\n");
		}
	}
	
	return sqrt($dx*$dx + $dy*$dy + $dz*$dz);
}

# 辅助函数 - 返回数的符号
sub sign {
    my $x = shift;
    return ($x > 0) - ($x < 0);
}

# 创建反应性原子集合
sub createReactiveAtomSets 
{
	my $doc = shift;

	$textDoc->Append("  创建反应性原子集合\n");

	# 初始化反应性原子计数器	
	my $R1Counter = 0;
	my $R2Counter = 0;
	
	# 直接计数而非创建集合
	my $atoms = $doc->UnitCell->Atoms;
	foreach my $atom (@$atoms) 
	{
		# 检查原子是否为反应性原子
		if ($atom->Name eq "$monomerReactiveAtom") 
		{					
			$R1Counter++;		
		} 
		elsif ($atom->Name eq "$xlinkerReactiveAtom") 
		{					
			$R2Counter++;		
		} 
	}

	$textDoc->Append("    $R1Counter 个单体反应原子\n");
	$textDoc->Append("    $R2Counter 个交联剂反应原子\n\n");
	$textDoc->Save;
		
	return ($doc, $R1Counter, $R2Counter);
}

# 创建新的交联键
sub createNewXlinks 
{
	my $doc1 = shift;
	my $distance = shift;
	
	my $t0 = time;
	$textDoc->Append("  检查反应距离 $distance Å\n");	

	# 获取反应性原子数量
	my ($R1Count, $R2Counter);
	eval {
		($doc1, $R1Count, $R2Counter) = createReactiveAtomSets($doc1);
	};
	
	if ($@) {
		$textDoc->Append("  错误: 创建反应性原子集合失败: $@\n");
		# 直接计算反应性原子数量
		$R1Count = 0;
		$R2Counter = 0;
		foreach my $atom (@{$doc1->UnitCell->Atoms})
		{
			$R1Count++ if ($atom->Name eq "$monomerReactiveAtom");
			$R2Counter++ if ($atom->Name eq "$xlinkerReactiveAtom");
		}
		$textDoc->Save;
	}
	
	# 如果没有反应性原子，直接返回
	if ($R1Count == 0 || $R2Counter == 0) {
		$textDoc->Append("  没有可用的反应原子！\n");
		return 0;
	}
			
	# 直接遍历所有原子，查找可能的反应对
	my @r1Atoms;
	my @r2Atoms;
	
	# 收集所有R1和R2原子
	foreach my $atom (@{$doc1->UnitCell->Atoms})
	{
		push @r1Atoms, $atom if ($atom->Name eq "$monomerReactiveAtom");
		push @r2Atoms, $atom if ($atom->Name eq "$xlinkerReactiveAtom");
	}
	
	# 找出所有在反应距离内的R1-R2对
	my @reactivePairs;
	foreach my $r1atom (@r1Atoms)
	{
		foreach my $r2atom (@r2Atoms)
		{
			my $dist = getAtomDistance($doc1, $r1atom, $r2atom);
			if ($dist <= $distance)
			{
				push @reactivePairs, [$r1atom, $r2atom, $dist];
			}
		}
	}
	
	# 按距离排序（从小到大）
	@reactivePairs = sort { $a->[2] <=> $b->[2] } @reactivePairs;
	
	$textDoc->Append(sprintf "  找到 %d 对可能的反应对\n", scalar @reactivePairs);
	
	# 如果没有反应对，直接返回
	if (scalar @reactivePairs == 0) {
		return 0;
	}
	
	# 创建新键
	my $newBondCounter = 0;
	my %usedR1Atoms; # 用于跟踪已使用的R1原子
	my %usedR2Atoms; # 用于跟踪已使用的R2原子
	
	foreach my $pair (@reactivePairs)
	{
		my ($r1atom, $r2atom, $dist) = @$pair;
		
		# 确认原子名称仍然是未反应的R1和R2，且未被本轮次使用
		if ($r1atom->Name eq "$monomerReactiveAtom" && 
		    $r2atom->Name eq "$xlinkerReactiveAtom" &&
		    !$usedR1Atoms{$r1atom} &&
		    !$usedR2Atoms{$r2atom})
		{
			# 创建键
			createNewBond($doc1, $r1atom, $r2atom);
			$newBondCounter++;
			
			# 标记这些原子已被使用
			$usedR1Atoms{$r1atom} = 1;
			$usedR2Atoms{$r2atom} = 1;
		}
	}
	
	if ($newBondCounter > 0) {
		$textDoc->Append("  成功形成 $newBondCounter 个交联键\n");
	} else {
		$textDoc->Append("  未能形成交联键\n");
	}
	
	$textDoc->Save;
	return $newBondCounter;
}

# 创建新的交联键并更改链接的显示样式和名称
sub createNewBond
{	
	my $doc1 = shift;
	my $atom1 = shift;
	my $atom2 = shift;

	# 创建新键
    $xlinkCounter++;
	my $newBond = $doc1->CreateBond($atom1, $atom2, "Single", ([Name => "xlink-".$xlinkCounter]));        
    
    # 设置创建的键的显示样式为球棍模型
    $atom1->Style = "Ball and stick";
    $atom2->Style = "Ball and stick";
    
    # 在每个原子名称后附加交联索引（不会影响未来的交联）
    $atom1->Name .= "-".$xlinkCounter;
    $atom2->Name .= "-".$xlinkCounter;
    
    $textDoc->Append(sprintf "    在 %s 和 %s 之间创建了键\n", $atom1->Name, $atom2->Name);
}

# 分析键长分布
sub analyzeBonds
{
	my $doc1 = shift;
	
	# 计算所有键的分布
	eval {
		my $bondAnalysis = $Forcite->Analysis->LengthDistribution($doc1, [
			LengthDistributionUseBonds	=> "Yes"
		]);	
		$bondAnalysis->LengthDistributionChartAsStudyTable->Delete;	
		$bondAnalysis->LengthDistributionChart->Name = "AllBonds";
	};
	
	if ($@) {
		$textDoc->Append("分析键长分布时出错: $@\n");
		return;
	}
	
	# 为交联键单独分析
	my @xlinkBonds;
	my $hasXlinks = FALSE;
	
	foreach my $bond (@{$doc1->UnitCell->Bonds}) 
	{	
		if ($bond->Name =~ /^xlink/) 
		{
			$hasXlinks = TRUE;
			push @xlinkBonds, $bond;
		}	
	}
	
	if (!$hasXlinks || scalar(@xlinkBonds) == 0) {
		$textDoc->Append("没有找到交联键，跳过交联键分析\n");
		return;
	}
	
	# 单独分析交联键长分布（不使用CreateSet方法）
	my $totalXlinkLength = 0;
	my $minLength = 999;
	my $maxLength = 0;
	my $countXlinks = 0;
	
	foreach my $bond (@xlinkBonds) {
		my $length = $bond->Length;
		$totalXlinkLength += $length;
		$minLength = $length if $length < $minLength;
		$maxLength = $length if $length > $maxLength;
		$countXlinks++;
	}
	
	my $avgLength = $countXlinks > 0 ? $totalXlinkLength / $countXlinks : 0;
	
	$textDoc->Append("\n交联键分析结果:\n");
	$textDoc->Append(sprintf "  交联键数量: %d\n", $countXlinks);
	$textDoc->Append(sprintf "  平均键长: %.4f Å\n", $avgLength);
	$textDoc->Append(sprintf "  最短键长: %.4f Å\n", $minLength);
	$textDoc->Append(sprintf "  最长键长: %.4f Å\n", $maxLength);
}

# 几何优化（使用当前Forcite设置）
sub ForciteGeomOpt
{
	my $t0 = time;
	my $doc1 = shift;
	my $steps = shift;
	
	my $results;	
	eval 
	{
		$results = $Forcite->GeometryOptimization->Run($doc1, [MaxIterations => $steps]);
	};

	if ($@) 
	{	 
	 	$textDoc->Append("ForciteGeomOpt: 几何优化期间失败\n");
	 	$textDoc->Append($@);
	}

	$textDoc->Append(sprintf "几何优化完成: %d 步, %d 秒\n", $steps, time-$t0); 
	$textDoc->Save; 

	return $results;
}

# 创建包含交联原子和键的集合
sub XlinkSet
{
	my $doc = shift;

	my @xlinked_atoms;
	my $hasXlinks = FALSE;
	
	foreach my $bond (@{$doc->UnitCell->Bonds}) 
	{
		if ($bond->Name =~ /^xlink/) 
		{
			push @xlinked_atoms, $bond->Atom1;
			push @xlinked_atoms, $bond->Atom2;
			push @xlinked_atoms, $bond;
			$hasXlinks = TRUE;
		}
	}
	
	if (!$hasXlinks || scalar(@xlinked_atoms) == 0) {
		$textDoc->Append("没有交联键，跳过创建交联集合\n");
		return;
	}

	$textDoc->Append("\n交联统计:\n");
	$textDoc->Append(sprintf "  交联键数量: %d\n", $xlinkCounter);
	$textDoc->Append(sprintf "  涉及原子数量: %d\n", scalar(@xlinked_atoms) - $xlinkCounter);
	$textDoc->Append(sprintf "  转化率: %.1f%%\n", $conversion);
} 
