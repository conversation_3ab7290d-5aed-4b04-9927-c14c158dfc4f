#!perl

##################################################################################################################
# 简化版ASS计算脚本                                                                                               #
# 目的：计算轨迹文件中的可接触溶剂表面数据                                                                        #
# 作者：Based on DMPB's scripts                                                                                   #
##################################################################################################################

use strict;
use warnings;
use Getopt::Long;
use MaterialsScript qw(:all);

# 默认参数设置 
my $trajectoryFile = "PE.xtd";   # 默认轨迹文件
my $resultsFile = "ASS_Results.std"; # 默认结果文件

# 轨迹帧设置
my $startFrameNumber = 1;          # 起始帧
my $endFrameNumber = 10;           # 结束帧 (-1表示使用全部帧)
my $frameInterval = 1;             # 帧间隔

# 计算参数设置
my $meshGridSize = 0.4;            # 网格间隔 (Å)
my $isoValue = 1.4;                # 等值面阈值

# 解析命令行参数
GetOptions(
    "input=s"     => \$trajectoryFile,
    "output=s"    => \$resultsFile,
    "start=i"     => \$startFrameNumber,
    "end=i"       => \$endFrameNumber,
    "step=i"      => \$frameInterval,
    "grid=f"      => \$meshGridSize,
    "iso=f"       => \$isoValue,
) or die "参数错误！\n";

# 创建输出研究表
my $resultsTable;

# 尝试检查文件是否存在并处理
my $existingDoc = undef;
eval {
    $existingDoc = $Documents{$resultsFile};
};

if (defined $existingDoc) {
    # 如果文件已存在，先关闭它
    print "发现已存在的结果文件，将关闭并重新创建...\n";
    eval {
        $existingDoc->Close();
        Documents->Delete($resultsFile);
    };
    if ($@) {
        warn "关闭或删除已存在的结果文件时出错: $@\n";
        warn "将尝试创建新文件...\n";
    }
}

# 创建新的研究表
$resultsTable = Documents->New($resultsFile);
my $dataSheet = $resultsTable->ActiveSheet;

# 设置列标题
$dataSheet->ColumnHeading(0) = "Frame";
$dataSheet->ColumnHeading(1) = "Time (ps)";
$dataSheet->ColumnHeading(2) = "Cell Volume (A3)";
$dataSheet->ColumnHeading(3) = "ASS Occupied Volume (A3)";
$dataSheet->ColumnHeading(4) = "ASS Free Volume (A3)";
$dataSheet->ColumnHeading(5) = "ASS Surface Area (A2)";
$dataSheet->ColumnHeading(6) = "Density (g/cm3)";

print "\n==========================================\n";
print "开始处理文件: $trajectoryFile\n";
print "==========================================\n";

# 打开文档
my $document;
eval {
    $document = $Documents{$trajectoryFile};
    if (!$document) {
        $document = Documents->Open($trajectoryFile);
    }
};
if ($@) {
    die "无法打开文件 $trajectoryFile: $@\n";
}

# 获取轨迹信息
my $totalFrames = $document->Trajectory->NumFrames;
print "该文件包含 $totalFrames 帧\n";

# 如果结束帧是-1，设置为最后一帧
if ($endFrameNumber == -1) {
    $endFrameNumber = $totalFrames;
} elsif ($endFrameNumber > $totalFrames) {
    $endFrameNumber = $totalFrames;
    print "结束帧调整为文件最大帧数: $totalFrames\n";
}

# 确保起始帧有效
if ($startFrameNumber < 1) {
    $startFrameNumber = 1;
    print "起始帧调整为: 1\n";
}

# 创建进度信息
my $totalFramesToProcess = int(($endFrameNumber - $startFrameNumber) / $frameInterval) + 1;
my $processedCount = 0;

# 遍历指定帧
my $rowCount = 0;
for (my $frameIndex = $startFrameNumber; $frameIndex <= $endFrameNumber; $frameIndex += $frameInterval) {
    # 更新进度
    $processedCount++;
    my $progressPercent = int($processedCount / $totalFramesToProcess * 100);
    print "处理第 $frameIndex 帧 ($processedCount/$totalFramesToProcess)，完成 $progressPercent%...\n";
    
    eval {
        # 设置当前帧
        $document->Trajectory->CurrentFrame = $frameIndex;
        
        # 获取帧时间
        my $frameTime = 0;
        eval {
            $frameTime = $document->Trajectory->FrameTime;
        };
        if ($@) {
            print "  注意: 无法获取帧 $frameIndex 的时间信息，使用默认值0\n";
        }
        
        # 计算总体积
        my $cellVolume = $document->Lattice3D->CellVolume;
        
        # 创建溶剂场
        print "  计算可接触溶剂表面...\n";
        
        # 创建溶剂场
        my $fieldSolvent = Tools->AtomVolumesSurfaces->Solvent->Calculate($document, Settings(
            GridInterval => $meshGridSize));
        $fieldSolvent->Style = "None";
        
        # 创建可接触溶剂表面等值面
        my $isosurfaceAccessible = $fieldSolvent->CreateIsosurface([
            "IsoValue" => $isoValue, 
            "IsoSurfaceKind" => "accessible", 
            "HasFlippedNormals" => "No"]);
        
        # 获取可接触溶剂表面的体积和表面积
        my $accessibleVolume = $isosurfaceAccessible->EnclosedVolume;
        my $accessibleArea = $isosurfaceAccessible->SurfaceArea;
        my $accessibleFreeVolume = $cellVolume - $accessibleVolume;
        
        # 获取密度
        my $density = $document->SymmetrySystem->Density;
        
        # 记录数据到研究表 - 确保使用正确的行索引
        $dataSheet->Cell($rowCount, 0) = $frameIndex;
        $dataSheet->Cell($rowCount, 1) = $frameTime;
        $dataSheet->Cell($rowCount, 2) = $cellVolume;
        $dataSheet->Cell($rowCount, 3) = $accessibleVolume;
        $dataSheet->Cell($rowCount, 4) = $accessibleFreeVolume;
        $dataSheet->Cell($rowCount, 5) = $accessibleArea;
        $dataSheet->Cell($rowCount, 6) = $density;
        
        # 立即保存表格，确保每帧数据都被保存
        $resultsTable->Save();
        
        # 详细输出当前帧的计算结果
        print "  帧 $frameIndex 计算结果:\n";
        print "    - 时间: " . sprintf("%.2f", $frameTime) . " ps\n";
        print "    - 晶胞总体积: " . sprintf("%.2f", $cellVolume) . " Å³\n";
        print "    - 可接触溶剂表面 $isoValue:\n";
        print "      - 占据体积: " . sprintf("%.2f", $accessibleVolume) . " Å³\n";
        print "      - 自由体积: " . sprintf("%.2f", $accessibleFreeVolume) . " Å³\n";
        print "      - 表面积: " . sprintf("%.2f", $accessibleArea) . " Å²\n";
        print "    - 密度: " . sprintf("%.4f", $density) . " g/cm³\n";
        
        # 删除场和等值面以释放内存
        $isosurfaceAccessible->Delete();
        $fieldSolvent->Delete();
        
        # 增加行计数
        $rowCount++;
    };
    if ($@) {
        warn "处理帧 $frameIndex 时出错: $@\n";
    }
}

# 最后再次保存研究表确保所有数据都被保存
$resultsTable->Save();

# 打印总结
print "\n==========================================\n";
print "计算完成，结果已保存到 $resultsFile\n";
print "总计处理了 $rowCount 帧\n";
print "==========================================\n"; 