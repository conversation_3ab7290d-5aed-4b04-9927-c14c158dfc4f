import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.ticker import MaxNLocator
from matplotlib import style
from scipy.signal import savgol_filter
from scipy.optimize import curve_fit
import pandas as pd
import os
import seaborn as sns
import tkinter as tk
from tkinter import filedialog, ttk, messagebox, scrolledtext
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.patches import Circle
import sys

# 设置全局绘图样式
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_context("notebook", font_scale=1.1)
plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题

# 修复字体问题 - 替换上标³
def format_volume_unit(with_superscript=False):
    """根据字体支持情况返回体积单位"""
    if with_superscript:
        return "cm³/g"
    else:
        return "cm3/g"

# 设置是否使用上标
USE_SUPERSCRIPT = False
VOLUME_UNIT = format_volume_unit(USE_SUPERSCRIPT)
DENSITY_UNIT = "g/cm3" if not USE_SUPERSCRIPT else "g/cm³"

# 可拖动的Tg点类
class DraggableTg:
    def __init__(self, ax, x, y, temperatures, densities, on_update_callback=None, y_min=None, y_max=None):
        self.ax = ax
        self.temperatures = temperatures
        self.densities = densities
        self.x = x
        self.y = y
        self.y_min = y_min
        self.y_max = y_max
        
        # 不再创建红色Tg点，只使用垂直虚线
        # 使用虚线标记Tg位置
        min_temp, max_temp = min(temperatures), max(temperatures)
        self.tg_line = self.ax.axvline(x=x, color='black', linestyle='--', linewidth=1.0, alpha=0.7, zorder=4)
        
        # 文本标签
        self.text = ax.text(x+5, min(densities) + 0.9*(max(densities)-min(densities)),
                            f'Tg = {x:.0f}K', fontsize=12, color='darkred',
                            bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5'))
        
        self.press = None
        self.background = None
        self.connect()
        self.on_update_callback = on_update_callback
        
        # 保存高温和低温拟合线的引用
        self.high_line = None
        self.low_line = None
        
        # 添加用于拟合线端点拖动的变量
        self.fit_line_points = []  # 用于存储拟合线端点的点对象
        self.active_point = None   # 当前正在拖动的点
        self.high_temp_range = None  # 高温拟合线范围
        self.low_temp_range = None   # 低温拟合线范围
        self.show_control_points = True  # 是否显示控制点
        self.calc_intercept = None  # 计算出的交点位置
        
        # 缓存拟合参数，减少计算频率
        self.cached_high_params = None
        self.cached_low_params = None
        self.last_fit_position = None
        self.drag_threshold = 0.5  # 拖动阈值（温度单位）
        
        # 预先计算拟合参数，减少初始拖动的卡顿
        high_params, low_params = self.fit_two_regions(x)
        self.cached_high_params = high_params
        self.cached_low_params = low_params
        self.last_fit_position = x
    
    def connect(self):
        """连接所有事件"""
        # 改为连接到整个画布而不是点对象
        self.cidpress = self.ax.figure.canvas.mpl_connect('button_press_event', self.on_press)
        self.cidrelease = self.ax.figure.canvas.mpl_connect('button_release_event', self.on_release)
        self.cidmotion = self.ax.figure.canvas.mpl_connect('motion_notify_event', self.on_motion)
    
    def on_press(self, event):
        """当鼠标按下时"""
        if event.inaxes != self.ax:
            return
        
        # 检查鼠标是否在虚线附近(±5个像素)
        # 将事件坐标转换为数据坐标
        mouse_x = event.xdata
        mouse_y = event.ydata
        
        # 检查是否点击了拟合线端点
        for i, point in enumerate(self.fit_line_points):
            if point and abs(mouse_x - point[0]) < 5 and abs(mouse_y - point[1]) < 0.05 * (max(self.densities) - min(self.densities)):
                self.active_point = i
                self.press = (point[0], point[1], event.xdata, event.ydata)
                
                # 鼠标按下时增大点的大小作为反馈
                for artist in self.ax.lines[:]:
                    if hasattr(artist, 'is_control_point') and artist.is_control_point:
                        idx = self.ax.lines.index(artist)
                        if idx == i:
                            artist.set_markersize(10)  # 增大当前控制点
                            self.ax.figure.canvas.draw_idle()
                return
        
        # 如果鼠标在Tg线附近，则捕获拖动事件
        if abs(mouse_x - self.x) < 5:  # 5是像素级误差，可以调整
            self.press = (self.x, event.xdata, event.ydata)
            self.active_point = None
            
            # 更改Tg线样式
            self.tg_line.set_linewidth(2.0)
            self.tg_line.set_alpha(1.0)
            self.ax.figure.canvas.draw_idle()
    
    def on_motion(self, event):
        """当鼠标移动时"""
        if self.press is None:
            return
        if event.inaxes != self.ax:
            return
        
        # 如果正在拖动拟合线端点
        if self.active_point is not None:
            x0, y0, xpress, ypress = self.press
            
            # 计算移动增量
            dx = event.xdata - xpress
            dy = event.ydata - ypress
            
            # 根据拖动的点更新拟合线参数
            if self.high_line and self.low_line:
                min_temp, max_temp = min(self.temperatures), max(self.temperatures)
                
                if self.active_point == 0:  # 高温线左端点
                    # 更新高温线左端点位置
                    new_x = min(max(x0 + dx, min_temp), self.x)  # 限制在数据范围内且不超过Tg点
                    new_y = y0 + dy
                    self.fit_line_points[0] = (new_x, new_y)
                    self.update_fit_params_from_points()
                
                elif self.active_point == 1:  # 高温线右端点
                    # 更新高温线右端点位置
                    new_x = min(max(x0 + dx, self.x), max_temp)  # 限制在数据范围内且不小于Tg点
                    new_y = y0 + dy
                    self.fit_line_points[1] = (new_x, new_y)
                    self.update_fit_params_from_points()
                
                elif self.active_point == 2:  # 低温线左端点
                    # 更新低温线左端点位置
                    new_x = min(max(x0 + dx, min_temp), self.x)  # 限制在数据范围内且不超过Tg点
                    new_y = y0 + dy
                    self.fit_line_points[2] = (new_x, new_y)
                    self.update_fit_params_from_points()
                
                elif self.active_point == 3:  # 低温线右端点
                    # 更新低温线右端点位置
                    new_x = min(max(x0 + dx, self.x), max_temp)  # 限制在数据范围内且不小于Tg点
                    new_y = y0 + dy
                    self.fit_line_points[3] = (new_x, new_y)
                    self.update_fit_params_from_points()
                
                # 更新拟合线
                self.update_fit_lines_from_points()
                
                # 重绘图表
                self.ax.figure.canvas.draw_idle()
                
                # 回调函数，通知外部参数已更新
                if self.on_update_callback:
                    self.on_update_callback(self.x, self.cached_high_params, self.cached_low_params)
            
            return
        
        # 如果正在拖动Tg线
        # 只允许水平移动（X轴方向）
        x0, xpress, ypress = self.press
        dx = event.xdata - xpress
        
        # 更新x坐标（限制在温度范围内）
        min_temp = min(self.temperatures)
        max_temp = max(self.temperatures)
        drag_x = min(max(x0 + dx, min_temp), max_temp)
        
        try:
            # 优化：只有当移动超过阈值时才重新计算拟合参数
            if (self.last_fit_position is None or 
                abs(drag_x - self.last_fit_position) > self.drag_threshold):
                
                # 更新拟合参数
                high_params, low_params = self.fit_two_regions(drag_x)
                self.cached_high_params = high_params
                self.cached_low_params = low_params
                self.last_fit_position = drag_x
            else:
                # 使用缓存的拟合参数
                high_params = self.cached_high_params
                low_params = self.cached_low_params
            
            # 计算两条拟合线的真正交点
            m1, b1 = high_params
            m2, b2 = low_params
            
            # 拖动时直接移动到鼠标位置，提高响应性
            # 只在鼠标释放时计算真正交点
            new_x = drag_x
            
            # 计算鼠标位置处的y值
            high_y = np.polyval(high_params, new_x)
            low_y = np.polyval(low_params, new_x)
            new_y = (high_y + low_y) / 2  # 取两条线在此处的平均值
            
            # 更新位置
            self.x = new_x
            self.y = new_y
            
            # 更新虚线位置
            self.tg_line.set_xdata([new_x, new_x])
            
            # 更新文本位置和内容
            self.text.set_x(new_x + 5)
            self.text.set_text(f'Tg = {new_x:.0f}K')
            
            # 更新拟合线
            self.update_fit_lines(high_params, low_params)
            
            # 重绘图表
            self.ax.figure.canvas.draw_idle()
            
            # 回调函数，通知外部Tg已更新
            if self.on_update_callback:
                self.on_update_callback(new_x, high_params, low_params)
        
        except Exception as e:
            # 出现异常时，简单地使用拖动位置，不更改拟合线
            print(f"处理拖动时出错: {e}")
            self.x = drag_x
            self.tg_line.set_xdata([drag_x, drag_x])
            self.text.set_x(drag_x + 5)
            self.text.set_text(f'Tg = {drag_x:.0f}K')
            self.ax.figure.canvas.draw_idle()
    
    def on_release(self, event):
        """当鼠标释放时，保持Tg在用户释放的位置"""
        if self.press is None:
            return
        
        # 如果正在拖动拟合线端点，则释放
        if self.active_point is not None:
            # 恢复点的正常大小
            for artist in self.ax.lines[:]:
                if hasattr(artist, 'is_control_point') and artist.is_control_point:
                    artist.set_markersize(8)  # 恢复默认大小
            
            self.active_point = None
            self.press = None
            self.ax.figure.canvas.draw()
            return
        
        # 恢复Tg线样式
        self.tg_line.set_linewidth(1.0)
        self.tg_line.set_alpha(0.7)
        
        # 保持Tg在用户拖动的位置（即当前self.x位置），不再重新计算交点
        # 但需要重新计算拟合参数
        high_params, low_params = self.fit_two_regions(self.x)
        self.cached_high_params = high_params
        self.cached_low_params = low_params
        self.last_fit_position = self.x
        
        # 更新拟合线
        self.update_fit_lines(high_params, low_params)
        
        # 重置按压状态
        self.press = None
        self.ax.figure.canvas.draw()
        
        # 回调函数，通知外部Tg已更新
        if self.on_update_callback:
            self.on_update_callback(self.x, high_params, low_params)
    
    def disconnect(self):
        """断开所有事件连接"""
        # 更新为使用ax.figure而不是point.figure
        self.ax.figure.canvas.mpl_disconnect(self.cidpress)
        self.ax.figure.canvas.mpl_disconnect(self.cidrelease)
        self.ax.figure.canvas.mpl_disconnect(self.cidmotion)
    
    def set_fit_lines(self, high_line, low_line):
        """设置拟合线的引用"""
        self.high_line = high_line
        self.low_line = low_line
        
        # 初始化拟合线端点及可拖动点
        self.create_draggable_fit_points()
    
    def fit_two_regions(self, tg):
        """根据当前Tg值拟合两个区域"""
        # 分割数据
        high_temps = [t for t in self.temperatures if t > tg]
        high_dens = [d for t, d in zip(self.temperatures, self.densities) if t > tg]
        
        low_temps = [t for t in self.temperatures if t <= tg]
        low_dens = [d for t, d in zip(self.temperatures, self.densities) if t <= tg]
        
        # 拟合高温区域 - 确保至少有2个点
        if len(high_temps) > 1:
            try:
                high_temp_params = np.polyfit(high_temps, high_dens, 1)
            except:
                # 拟合失败时返回一个默认斜率的线
                high_temp_params = [-0.0001, np.mean(high_dens)]
        else:
            # 如果点太少，使用默认值
            high_temp_params = [-0.0001, np.mean(self.densities) + 0.01]
        
        # 拟合低温区域 - 确保至少有2个点
        if len(low_temps) > 1:
            try:
                low_temp_params = np.polyfit(low_temps, low_dens, 1)
            except:
                # 拟合失败时返回一个默认斜率的线
                low_temp_params = [-0.00005, np.mean(low_dens)]
        else:
            # 如果点太少，使用默认值
            low_temp_params = [-0.00005, np.mean(self.densities) - 0.01]
        
        return high_temp_params, low_temp_params
    
    def update_fit_lines(self, high_params, low_params):
        """更新拟合线"""
        if self.high_line is None or self.low_line is None:
            return
            
        # 温度范围
        t_min, t_max = min(self.temperatures), max(self.temperatures)
        
        # 定义重叠范围，确保两条线在Tg点相交
        overlap_range = (t_max - t_min) * 0.05  # 5%的重叠范围
        
        # 更新高温区拟合线 - 延伸到略低于Tg的点
        high_temp_range = np.linspace(self.x - overlap_range, t_max, 100)
        high_fit = np.polyval(high_params, high_temp_range)
        self.high_line.set_xdata(high_temp_range)
        self.high_line.set_ydata(high_fit)
        
        # 更新低温区拟合线 - 延伸到略高于Tg的点
        low_temp_range = np.linspace(t_min, self.x + overlap_range, 100)
        low_fit = np.polyval(low_params, low_temp_range)
        self.low_line.set_xdata(low_temp_range)
        self.low_line.set_ydata(low_fit)
        
        # 保存温度范围，用于端点拖动功能
        self.high_temp_range = high_temp_range
        self.low_temp_range = low_temp_range
        
        # 更新拟合线端点位置
        self.update_fit_points(high_params, low_params)
    
    def create_draggable_fit_points(self):
        """创建可拖动的拟合线端点"""
        if not self.high_line or not self.low_line:
            return
        
        # 初始化四个点的位置
        t_min, t_max = min(self.temperatures), max(self.temperatures)
        
        # 高温线左端点（靠近Tg处）
        if len(self.high_line.get_xdata()) > 0 and len(self.high_line.get_ydata()) > 0:
            high_x_left = self.high_line.get_xdata()[0]
            high_y_left = self.high_line.get_ydata()[0]
        else:
            high_x_left = self.x
            high_y_left = np.mean(self.densities)
        
        # 高温线右端点
        if len(self.high_line.get_xdata()) > 0 and len(self.high_line.get_ydata()) > 0:
            high_x_right = self.high_line.get_xdata()[-1]
            high_y_right = self.high_line.get_ydata()[-1]
        else:
            high_x_right = t_max
            high_y_right = np.mean(self.densities) - 0.02
        
        # 低温线左端点
        if len(self.low_line.get_xdata()) > 0 and len(self.low_line.get_ydata()) > 0:
            low_x_left = self.low_line.get_xdata()[0]
            low_y_left = self.low_line.get_ydata()[0]
        else:
            low_x_left = t_min
            low_y_left = np.mean(self.densities) + 0.02
        
        # 低温线右端点（靠近Tg处）
        if len(self.low_line.get_xdata()) > 0 and len(self.low_line.get_ydata()) > 0:
            low_x_right = self.low_line.get_xdata()[-1]
            low_y_right = self.low_line.get_ydata()[-1]
        else:
            low_x_right = self.x
            low_y_right = np.mean(self.densities)
        
        # 存储点位置 (x, y)
        self.fit_line_points = [
            (high_x_left, high_y_left),    # 高温线左端点
            (high_x_right, high_y_right),  # 高温线右端点
            (low_x_left, low_y_left),      # 低温线左端点
            (low_x_right, low_y_right)     # 低温线右端点
        ]
        
        # 在图上绘制端点标记
        self.draw_fit_points()
    
    def draw_fit_points(self):
        """在图上绘制拟合线端点标记"""
        # 清除现有的控制点标记
        for artist in self.ax.lines[:]:
            if hasattr(artist, 'is_control_point') and artist.is_control_point:
                artist.remove()
        
        # 如果控制点不可见，直接返回
        if not self.show_control_points:
            return
        
        # 绘制高温线端点
        if len(self.fit_line_points) >= 2:
            # 高温线左端点
            high_left = self.ax.plot(self.fit_line_points[0][0], self.fit_line_points[0][1], 'o', 
                                    color='red', markersize=8, alpha=0.8, zorder=10)[0]
            high_left.is_control_point = True
            
            # 高温线右端点
            high_right = self.ax.plot(self.fit_line_points[1][0], self.fit_line_points[1][1], 'o', 
                                    color='red', markersize=8, alpha=0.8, zorder=10)[0]
            high_right.is_control_point = True
        
        # 绘制低温线端点
        if len(self.fit_line_points) >= 4:
            # 低温线左端点
            low_left = self.ax.plot(self.fit_line_points[2][0], self.fit_line_points[2][1], 'o', 
                                    color='green', markersize=8, alpha=0.8, zorder=10)[0]
            low_left.is_control_point = True
            
            # 低温线右端点
            low_right = self.ax.plot(self.fit_line_points[3][0], self.fit_line_points[3][1], 'o', 
                                    color='green', markersize=8, alpha=0.8, zorder=10)[0]
            low_right.is_control_point = True
    
    def update_fit_points(self, high_params, low_params):
        """根据新的拟合参数更新拟合线端点位置"""
        t_min, t_max = min(self.temperatures), max(self.temperatures)
        
        # 计算新的端点位置
        if self.high_temp_range is not None and len(self.high_temp_range) > 0:
            high_x_left = self.high_temp_range[0]
            high_x_right = self.high_temp_range[-1]
            high_y_left = np.polyval(high_params, high_x_left)
            high_y_right = np.polyval(high_params, high_x_right)
        else:
            high_x_left = self.x
            high_x_right = t_max
            high_y_left = np.polyval(high_params, high_x_left)
            high_y_right = np.polyval(high_params, high_x_right)
        
        if self.low_temp_range is not None and len(self.low_temp_range) > 0:
            low_x_left = self.low_temp_range[0]
            low_x_right = self.low_temp_range[-1]
            low_y_left = np.polyval(low_params, low_x_left)
            low_y_right = np.polyval(low_params, low_x_right)
        else:
            low_x_left = t_min
            low_x_right = self.x
            low_y_left = np.polyval(low_params, low_x_left)
            low_y_right = np.polyval(low_params, low_x_right)
        
        # 更新端点位置
        self.fit_line_points = [
            (high_x_left, high_y_left),
            (high_x_right, high_y_right),
            (low_x_left, low_y_left),
            (low_x_right, low_y_right)
        ]
        
        # 重新绘制控制点
        self.draw_fit_points()
    
    def update_fit_params_from_points(self):
        """根据拖动的端点位置更新拟合参数"""
        # 确保有足够的点
        if len(self.fit_line_points) < 4:
            return
        
        # 从端点计算高温线参数 (slope, intercept)
        high_p1 = self.fit_line_points[0]  # 左端点
        high_p2 = self.fit_line_points[1]  # 右端点
        
        # 检查是否有效
        if high_p1[0] != high_p2[0]:  # 避免除以零
            high_slope = (high_p2[1] - high_p1[1]) / (high_p2[0] - high_p1[0])
            high_intercept = high_p1[1] - high_slope * high_p1[0]
            high_params = [high_slope, high_intercept]
        else:
            # 默认参数
            high_params = self.cached_high_params if self.cached_high_params else [-0.0001, np.mean(self.densities) + 0.01]
        
        # 从端点计算低温线参数
        low_p1 = self.fit_line_points[2]  # 左端点
        low_p2 = self.fit_line_points[3]  # 右端点
        
        # 检查是否有效
        if low_p1[0] != low_p2[0]:  # 避免除以零
            low_slope = (low_p2[1] - low_p1[1]) / (low_p2[0] - low_p1[0])
            low_intercept = low_p1[1] - low_slope * low_p1[0]
            low_params = [low_slope, low_intercept]
        else:
            # 默认参数
            low_params = self.cached_low_params if self.cached_low_params else [-0.00005, np.mean(self.densities) - 0.01]
        
        # 更新缓存的参数
        self.cached_high_params = high_params
        self.cached_low_params = low_params
        
        # 计算两条线的交点（仅显示在文本标签中，不移动Tg线）
        try:
            # 防止除以零错误或斜率接近相等
            if abs(high_params[0] - low_params[0]) > 1e-6:
                # 计算交点
                intercept_x = (low_params[1] - high_params[1]) / (high_params[0] - low_params[0])
                
                # 确保交点在合理范围内
                min_temp = min(self.temperatures)
                max_temp = max(self.temperatures)
                
                if intercept_x >= min_temp and intercept_x <= max_temp:
                    # 交点信息仅作参考，不移动Tg线
                    intercept_y = np.polyval(high_params, intercept_x)
                    
                    # 保存计算出的交点位置
                    self.calc_intercept = intercept_x
                    
                    # 只更新交点信息文本内容，不改变Tg线位置
                    self.text.set_text(f'Tg = {self.x:.0f}K (交点: {intercept_x:.0f}K)')
        except Exception as e:
            print(f"计算交点出错: {e}")
            self.calc_intercept = None
    
    def update_fit_lines_from_points(self):
        """根据拖动的端点位置更新拟合线显示"""
        if self.high_line is None or self.low_line is None:
            return
        
        # 确保有足够的点
        if len(self.fit_line_points) < 4:
            return
        
        # 高温线两个端点
        high_p1 = self.fit_line_points[0]
        high_p2 = self.fit_line_points[1]
        
        # 低温线两个端点
        low_p1 = self.fit_line_points[2]
        low_p2 = self.fit_line_points[3]
        
        # 创建x点集
        high_x = np.linspace(high_p1[0], high_p2[0], 100)
        low_x = np.linspace(low_p1[0], low_p2[0], 100)
        
        # 使用当前参数计算y值
        if self.cached_high_params and self.cached_low_params:
            high_y = np.polyval(self.cached_high_params, high_x)
            low_y = np.polyval(self.cached_low_params, low_x)
            
            # 更新线的数据
            self.high_line.set_xdata(high_x)
            self.high_line.set_ydata(high_y)
            
            self.low_line.set_xdata(low_x)
            self.low_line.set_ydata(low_y)
            
            # 存储温度范围
            self.high_temp_range = high_x
            self.low_temp_range = low_x
    
    def toggle_control_points(self):
        """切换控制点的显示状态"""
        self.show_control_points = not self.show_control_points
        self.draw_fit_points()
        self.ax.figure.canvas.draw_idle()
        return self.show_control_points
    
    def move_to_calculated_intercept(self):
        """将Tg线移动到计算出的交点位置"""
        if self.calc_intercept is None:
            return False
        
        # 确保交点在合理范围内
        min_temp = min(self.temperatures)
        max_temp = max(self.temperatures)
        
        if self.calc_intercept >= min_temp and self.calc_intercept <= max_temp:
            # 更新Tg位置
            self.x = self.calc_intercept
            self.y = np.polyval(self.cached_high_params, self.calc_intercept)
            
            # 更新Tg线位置
            self.tg_line.set_xdata([self.calc_intercept, self.calc_intercept])
            
            # 更新文本位置和内容
            self.text.set_x(self.calc_intercept + 5)
            self.text.set_text(f'Tg = {self.calc_intercept:.0f}K')
            
            # 更新拟合线
            self.update_fit_lines(self.cached_high_params, self.cached_low_params)
            
            # 重绘图表
            self.ax.figure.canvas.draw_idle()
            
            # 通知外部Tg已更新
            if self.on_update_callback:
                self.on_update_callback(self.calc_intercept, self.cached_high_params, self.cached_low_params)
            
            return True
        
        return False

def read_data_from_file(file_path):
    """
    从文件中读取温度和密度数据
    支持CSV和Excel格式
    
    支持的列名格式：
    温度列: 'Temperature', 'Temperature(K)', 'Temperature (K)' 等
    密度列: 'Density', 'Density(g/cm³)', 'Density g/cm3' 等
    
    返回：temperatures列表和densities列表
    """
    file_ext = os.path.splitext(file_path)[1].lower()
    
    if file_ext == '.csv':
        df = pd.read_csv(file_path)
    elif file_ext in ['.xls', '.xlsx']:
        df = pd.read_excel(file_path)
    else:
        raise ValueError(f"不支持的文件格式: {file_ext}，请使用CSV或Excel文件")
    
    # 尝试匹配温度列和密度列
    temp_column = None
    density_column = None
    
    # 常见的温度列名
    temp_patterns = ['Temperature', 'Temp', 'T', '温度']
    # 常见的密度列名
    density_patterns = ['Density', 'ρ', '密度']
    
    # 查找温度列
    for col in df.columns:
        # 检查该列是否匹配任一温度模式
        if any(pattern.lower() in col.lower() for pattern in temp_patterns):
            temp_column = col
            break
    
    # 查找密度列
    for col in df.columns:
        # 检查该列是否匹配任一密度模式
        if any(pattern.lower() in col.lower() for pattern in density_patterns):
            density_column = col
            break
    
    # 如果未找到列，尝试直接使用第一列作为温度，第二列作为密度
    if temp_column is None and density_column is None and len(df.columns) >= 2:
        print("警告：未能识别温度和密度列，使用前两列代替。")
        temp_column = df.columns[0]
        density_column = df.columns[1]
    else:
        # 如果仍未找到列，报错
        if temp_column is None:
            raise ValueError(f"文件缺少必要的温度列。支持的列名包括: {', '.join(temp_patterns)}或类似名称")
        if density_column is None:
            raise ValueError(f"文件缺少必要的密度列。支持的列名包括: {', '.join(density_patterns)}或类似名称")
    
    print(f"使用列: '{temp_column}' 作为温度, '{density_column}' 作为密度")
    temperatures = df[temp_column].tolist()
    densities = df[density_column].tolist()
    
    return temperatures, densities

def find_glass_transition_temperature(temperatures, densities, output_path=None, sample_name=None, interactive=False, y_margin=0.05, y_min=None, y_max=None, language="cn"):
    """
    分析温度和密度数据，寻找玻璃化转变温度(Tg)
    
    参数:
    temperatures - 温度数据列表
    densities - 密度数据列表
    output_path - 输出图像保存路径，默认为当前目录
    sample_name - 样品名称，用于图表标题
    interactive - 是否启用交互式模式(可拖动Tg点)
    y_margin - Y轴边距比例，用于突出显示波动
    y_min - Y轴最小值（手动设置）
    y_max - Y轴最大值（手动设置）
    language - 标签语言选择，"cn"为中文，"en"为英文
    
    返回:
    Tg - 玻璃化转变温度值
    """
    # 确保数据是NumPy数组
    temperatures = np.array(temperatures)
    densities = np.array(densities)
    
    # 语言选择
    if language == "en":
        liquid_region_label = "Liquid Region Fitting"
        glass_region_label = "Glass Region Fitting"
        temperature_label = "Temperature (K)"
        density_label = f"Density ({DENSITY_UNIT})"
        plot_title_format = "Density vs Temperature Plot for {}"
    else:  # 默认中文
        liquid_region_label = "液体区拟合"
        glass_region_label = "玻璃区拟合"
        temperature_label = "温度 (K)"
        density_label = f"密度 ({DENSITY_UNIT})"
        plot_title_format = "{}的密度随温度的变化"
    
    # 初始化变量
    best_error = float('inf')
    best_split = None
    best_params = None
    best_r2_high = 0
    best_r2_low = 0
    
    # 尝试不同的分割点，找出最佳拟合
    # 为了避免过拟合，我们跳过前几个和后几个点
    
    for split_idx in range(3, len(temperatures)-3):
        # 高温区域的线性拟合
        try:
            # 拟合高温区域
            high_temps = temperatures[:split_idx]
            high_dens = densities[:split_idx]
            high_temp_params, high_residuals, _, _, _ = np.polyfit(high_temps, high_dens, 1, full=True)
            
            # 计算高温区域R²值
            high_fit = np.polyval(high_temp_params, high_temps)
            ss_tot_high = np.sum((high_dens - np.mean(high_dens))**2)
            ss_res_high = np.sum((high_dens - high_fit)**2)
            r2_high = 1 - (ss_res_high / ss_tot_high) if ss_tot_high != 0 else 0
            
            # 拟合低温区域
            low_temps = temperatures[split_idx:]
            low_dens = densities[split_idx:]
            low_temp_params, low_residuals, _, _, _ = np.polyfit(low_temps, low_dens, 1, full=True)
            
            # 计算低温区域R²值
            low_fit = np.polyval(low_temp_params, low_temps)
            ss_tot_low = np.sum((low_dens - np.mean(low_dens))**2)
            ss_res_low = np.sum((low_dens - low_fit)**2)
            r2_low = 1 - (ss_res_low / ss_tot_low) if ss_tot_low != 0 else 0
            
            # 计算拟合误差
            high_error = np.sum((high_fit - high_dens)**2)
            low_error = np.sum((low_fit - low_dens)**2)
            total_error = high_error + low_error
            
            if total_error < best_error:
                best_error = total_error
                best_split = split_idx
                best_params = (high_temp_params, low_temp_params)
                best_r2_high = r2_high
                best_r2_low = r2_low
        except:
            continue
    
    if best_params:
        # 找出两条线的交点
        high_params, low_params = best_params
        try:
            # m1*x + b1 = m2*x + b2
            # (m1-m2)*x = b2-b1
            # x = (b2-b1)/(m1-m2)
            m1, b1 = high_params
            m2, b2 = low_params
            
            # 防止除以零错误或斜率接近相等
            if abs(m1 - m2) > 1e-6:
                tg_method2 = (b2-b1)/(m1-m2)
                
                # 检查交点是否在合理范围内
                min_temp, max_temp = min(temperatures), max(temperatures)
                if tg_method2 < min_temp * 0.9 or tg_method2 > max_temp * 1.1:
                    # 若交点不在合理范围，使用中间值
                    tg_method2 = np.mean(temperatures)
            else:
                # 若两线平行，使用中间值
                tg_method2 = np.mean(temperatures)
                
            final_tg = tg_method2
        except:
            # 计算交点出错，使用备选方法
            final_tg = temperatures[best_split]
    else:
        # 如果无法找到交点，使用其他方法
        # 计算斜率(一阶导数)
        slopes = []
        mid_temps = []
        
        for i in range(len(temperatures)-1):
            temp_diff = temperatures[i] - temperatures[i+1]
            density_diff = densities[i+1] - densities[i]
            slope = density_diff / temp_diff
            slopes.append(slope)
            mid_temps.append((temperatures[i] + temperatures[i+1]) / 2)
        
        slopes = np.array(slopes)
        mid_temps = np.array(mid_temps)
        
        # 使用Savitzky-Golay滤波器平滑斜率以减少噪声
        if len(slopes) > 5:  # 该滤波器至少需要5个点
            window_length = min(5, len(slopes) - 2 if len(slopes) % 2 == 0 else len(slopes) - 1)
            smooth_slopes = savgol_filter(slopes, window_length, 2)
        else:
            smooth_slopes = slopes
        
        # 计算二阶导数(斜率的变化率)
        second_deriv = []
        second_mid_temps = []
        
        for i in range(len(smooth_slopes)-1):
            temp_diff = mid_temps[i] - mid_temps[i+1]
            slope_diff = smooth_slopes[i+1] - smooth_slopes[i]
            second_d = slope_diff / temp_diff
            second_deriv.append(second_d)
            second_mid_temps.append((mid_temps[i] + mid_temps[i+1]) / 2)
        
        second_deriv = np.array(second_deriv)
        second_mid_temps = np.array(second_mid_temps)
        
        # 方法1：找出二阶导数绝对值最大的温度点
        tg_method1 = second_mid_temps[np.argmax(np.abs(second_deriv))]
        final_tg = tg_method1
    
    # 创建玻璃化转变温度分析图
    fig = plt.figure(figsize=(8, 6), dpi=100)
    ax = fig.add_subplot(111)
    
    # 设置样品名称
    if sample_name is None:
        sample_name = "样品" if language == "cn" else "Sample"
    
    # 主图 - 温度vs密度
    ax.scatter(temperatures, densities, color='#3182bd', s=40, alpha=0.8)
    
    # 如果提供了手动Y轴范围，应用它
    if y_min is not None and y_max is not None:
        ax.set_ylim(y_min, y_max)
    else:
        # 设置y轴边距比例，使波动范围更加明显
        ax.margins(y=y_margin)
    
    # 使用紧凑布局，让绘图区域最大化
    fig.tight_layout()
    
    # 如果有最佳拟合参数，绘制拟合线
    high_line = None
    low_line = None
    draggable_tg = None
    
    if best_params:
        high_params, low_params = best_params
        
        # 温度范围
        t_min, t_max = min(temperatures), max(temperatures)
        
        # 定义重叠范围，确保两条线在Tg点相交
        overlap_range = (t_max - t_min) * 0.05  # 5%的重叠范围
        
        # 创建均匀分布的温度点进行绘图
        plot_temp_range = np.linspace(t_min, t_max, 1000)
        
        # 高温区域拟合线 - 延伸到略低于Tg的点
        high_mask = plot_temp_range >= (final_tg - overlap_range)
        if np.any(high_mask):
            high_temp_points = plot_temp_range[high_mask]
            high_fit = np.polyval(high_params, high_temp_points)
            high_line, = ax.plot(high_temp_points, high_fit, '-', color='#e41a1c', 
                    linewidth=2, label=f'{liquid_region_label} (R²={best_r2_high:.2f})')
        
        # 低温区域拟合线 - 延伸到略高于Tg的点
        low_mask = plot_temp_range <= (final_tg + overlap_range)
        if np.any(low_mask):
            low_temp_points = plot_temp_range[low_mask]
            low_fit = np.polyval(low_params, low_temp_points)
            low_line, = ax.plot(low_temp_points, low_fit, '-', color='#4daf4a', 
                    linewidth=2, label=f'{glass_region_label} (R²={best_r2_low:.2f})')
        
        # 标记玻璃化转变温度
        if not interactive:
            # 计算交点的y值
            tg_y = np.polyval(high_params, final_tg)
            
            # 不再绘制垂直线
            
            ax.text(final_tg+5, min(densities) + 0.9*(max(densities)-min(densities)), 
                   f'Tg = {final_tg:.0f}K', fontsize=14, color='darkred', 
                   bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5'))
        else:
            # 在交互模式下，创建可拖动的Tg点
            def on_tg_update(new_tg, high_params, low_params):
                nonlocal final_tg
                final_tg = new_tg
                # 在交互模式下更新y轴范围以突出显示波动
                adjust_y_axis(ax, temperatures, densities, high_params, low_params, new_tg, y_margin)
            
            # 计算Tg点的y值（两条线的交点）
            tg_y = np.polyval(high_params, final_tg)
            
            draggable_tg = DraggableTg(ax, final_tg, tg_y, temperatures, densities, 
                                      on_update_callback=on_tg_update, 
                                      y_min=y_min, y_max=y_max)
            draggable_tg.set_fit_lines(high_line, low_line)
    
    # 格式设置
    ax.set_xlabel(temperature_label, fontsize=12)
    ax.set_ylabel(density_label, fontsize=12)
    ax.set_title(plot_title_format.format(sample_name), fontsize=14)
    ax.legend(loc='best')
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 调整布局
    plt.tight_layout()
    
    if not interactive:
        # 确定保存路径
        if output_path is None:
            base_dir = os.path.dirname(os.path.abspath(__file__))
            output_file = os.path.join(base_dir, f'glass_transition_{sample_name}.png')
        else:
            output_file = output_path
        
        # 保存高分辨率图像
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"可视化结果已保存至 {output_file}")
        
        # 显示图表
        plt.show()
        
        # 生成详细分析图 (可选)
        if best_params:
            create_detailed_analysis(temperatures, densities, best_params, final_tg, sample_name, y_margin, y_min, y_max)
        
        # 输出结果
        print(f"玻璃化转变温度 (Tg): {final_tg:.1f}K")
        
        # 返回最终结果
        return final_tg
    else:
        # 在交互模式下，返回图表和可拖动对象
        return final_tg, fig, draggable_tg

def adjust_y_axis(ax, temperatures, densities, high_params, low_params, tg, margin=0.05, y_min=None, y_max=None):
    """根据拟合线和数据调整y轴范围，突出显示波动"""
    # 如果提供了手动设置的范围，直接应用
    if y_min is not None and y_max is not None:
        ax.set_ylim(y_min, y_max)
        return
        
    # 计算拟合线的y值范围
    t_min, t_max = min(temperatures), max(temperatures)
    
    # 生成温度采样点
    temp_range = np.linspace(t_min, t_max, 500)
    
    # 计算高温区域和低温区域拟合线的值
    high_fit = np.polyval(high_params, temp_range[temp_range > tg])
    low_fit = np.polyval(low_params, temp_range[temp_range <= tg]) 
    
    # 组合所有可能的y值
    all_y_values = np.concatenate([densities, high_fit, low_fit])
    
    # 计算最小值和最大值，稍微扩大范围
    y_min = np.min(all_y_values)
    y_max = np.max(all_y_values)
    
    # 计算中心值
    y_center = (y_min + y_max) / 2
    
    # 计算范围的一半
    y_half_range = (y_max - y_min) / 2
    
    # 扩展范围
    expanded_y_min = y_center - y_half_range * (1 + margin)
    expanded_y_max = y_center + y_half_range * (1 + margin)
    
    # 设置轴范围
    ax.set_ylim(expanded_y_min, expanded_y_max)

def create_detailed_analysis(temperatures, densities, best_params, tg, sample_name=None, y_margin=0.05, y_min=None, y_max=None, language="cn", from_interactive=True):
    """
    创建玻璃化转变温度的详细分析图
    
    参数:
    temperatures - 温度数据列表
    densities - 密度数据列表
    best_params - 最佳拟合参数 (high_params, low_params)
    tg - 玻璃化转变温度
    sample_name - 样品名称
    y_margin - Y轴边距
    y_min - Y轴最小值（手动设置）
    y_max - Y轴最大值（手动设置）
    language - 标签语言选择，"cn"为中文，"en"为英文
    from_interactive - 是否来自交互式分析结果
    """
    # 语言选择
    if language == "en":
        title_prefix = "Detailed Analysis"
        main_title = "Glass Transition Temperature Analysis"
        temp_title = "Temperature (K)"
        density_title = f"Density ({DENSITY_UNIT})"
        temp_density_title = "Temperature-Density"
        deriv_title = "First Derivative"
        secderiv_title = "Second Derivative"
        residual_title = "Residual Analysis"
        dataset_label = "Data Points"
        hightemp_label = "Liquid Region Fitting"
        lowtemp_label = "Glass Region Fitting"
        tg_label = f"Tg = {tg:.1f}K"
        residual_label = "Residuals"
        density_diff_title = "Density Difference"
        deviation_from_fit = "Deviation from fitted line"
        error_distribution = "Error Distribution"
        count = "Count"
        method1_text = f"Method 1 (Dual-line): Tg = {tg:.1f}K"
        plot_title_format = "for {}"
        from_interactive_text = "(from Interactive Analysis)" if from_interactive else ""
    else:  # 默认中文
        title_prefix = "详细分析"
        main_title = "玻璃化转变温度分析"
        temp_title = "温度 (K)"
        density_title = f"密度 ({DENSITY_UNIT})"
        temp_density_title = "温度-密度关系"
        deriv_title = "一阶导数"
        secderiv_title = "二阶导数"
        residual_title = "残差分析"
        dataset_label = "数据点"
        hightemp_label = "液体区拟合"
        lowtemp_label = "玻璃区拟合"
        tg_label = f"Tg = {tg:.1f}K"
        residual_label = "残差"
        density_diff_title = "密度差异"
        deviation_from_fit = "与拟合线的偏差"
        error_distribution = "误差分布"
        count = "计数"
        method1_text = f"方法1（双线拟合法）: Tg = {tg:.1f}K"
        plot_title_format = "（{}）"
        from_interactive_text = "(来自交互式分析)" if from_interactive else ""
    
    # 设置样品名称
    if sample_name is None:
        sample_name = "样品" if language == "cn" else "Sample"
    
    # 创建拟合线
    high_params, low_params = best_params
    
    # 创建子图
    plt.figure(figsize=(15, 10), dpi=100)
    plt.suptitle(f"{title_prefix}: {main_title} {from_interactive_text} {plot_title_format.format(sample_name)}", fontsize=16)
    
    # 1. 温度-密度关系图
    ax1 = plt.subplot(2, 2, 1)
    ax1.scatter(temperatures, densities, color='#3182bd', s=40, alpha=0.7, label=dataset_label)
    
    # 拟合线
    t_min, t_max = min(temperatures), max(temperatures)
    temp_range = np.linspace(t_min, t_max, 1000)
    
    # 高温区域拟合线
    high_fit = np.polyval(high_params, temp_range)
    ax1.plot(temp_range, high_fit, '-', color='#e41a1c', 
             linewidth=2, label=hightemp_label)
    
    # 低温区域拟合线
    low_fit = np.polyval(low_params, temp_range)
    ax1.plot(temp_range, low_fit, '-', color='#4daf4a', 
             linewidth=2, label=lowtemp_label)
    
    # 标记玻璃化转变温度
    ax1.axvline(x=tg, color='black', linestyle='--', alpha=0.7)
    ax1.text(tg+5, min(densities) + 0.9*(max(densities)-min(densities)), 
            tg_label, fontsize=12, color='darkred', 
            bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5'))
    
    # 格式设置
    ax1.set_xlabel(temp_title, fontsize=12)
    ax1.set_ylabel(density_title, fontsize=12)
    ax1.set_title(temp_density_title, fontsize=14)
    
    # 如果提供了手动Y轴范围，应用它
    if y_min is not None and y_max is not None:
        ax1.set_ylim(y_min, y_max)
    else:
        # 设置y轴边距比例，使波动范围更加明显
        ax1.margins(y=y_margin)
    
    ax1.legend(loc='best')
    ax1.grid(True, linestyle='--', alpha=0.7)
    
    # 2. 密度差异图
    ax2 = plt.subplot(2, 2, 2)
    
    # 计算拟合密度
    fitted_densities = []
    for temp in temperatures:
        if temp > tg:
            fitted_densities.append(np.polyval(high_params, temp))
        else:
            fitted_densities.append(np.polyval(low_params, temp))
    
    # 计算差异
    density_diff = np.array(densities) - np.array(fitted_densities)
    
    # 绘制差异散点
    ax2.scatter(temperatures, density_diff, color='#ff7f00', s=40, alpha=0.7, label=residual_label)
    ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.5)
    ax2.axvline(x=tg, color='black', linestyle='--', alpha=0.7)
    
    # 格式设置
    ax2.set_xlabel(temp_title, fontsize=12)
    ax2.set_ylabel(deviation_from_fit, fontsize=12)
    ax2.set_title(density_diff_title, fontsize=14)
    ax2.legend(loc='best')
    ax2.grid(True, linestyle='--', alpha=0.7)
    
    # 3. 一阶导数图（密度变化率）
    ax3 = plt.subplot(2, 2, 3)
    
    # 计算导数
    slopes = []
    mid_temps = []
    
    for i in range(len(temperatures)-1):
        temp_diff = temperatures[i] - temperatures[i+1]
        density_diff = densities[i+1] - densities[i]
        slope = density_diff / temp_diff
        slopes.append(slope)
        mid_temps.append((temperatures[i] + temperatures[i+1]) / 2)
    
    slopes = np.array(slopes)
    mid_temps = np.array(mid_temps)
    
    # 使用Savitzky-Golay滤波器平滑斜率以减少噪声
    if len(slopes) > 5:
        window_length = min(5, len(slopes) - 2 if len(slopes) % 2 == 0 else len(slopes) - 1)
        smooth_slopes = savgol_filter(slopes, window_length, 2)
    else:
        smooth_slopes = slopes
    
    # 绘制导数
    ax3.scatter(mid_temps, slopes, color='#b2df8a', s=30, alpha=0.7, label=deviation_from_fit)
    ax3.plot(mid_temps, smooth_slopes, '-', color='#33a02c', linewidth=2, label=deviation_from_fit)
    ax3.axvline(x=tg, color='black', linestyle='--', alpha=0.7)
    
    # 格式设置
    ax3.set_xlabel(temp_title, fontsize=12)
    ax3.set_ylabel(f"d{density_title}/d{temp_title}", fontsize=12)
    ax3.set_title(deriv_title, fontsize=14)
    ax3.grid(True, linestyle='--', alpha=0.7)
    
    # 4. 残差分布直方图
    ax4 = plt.subplot(2, 2, 4)
    
    # 绘制直方图
    ax4.hist(density_diff, bins=15, color='#e41a1c', alpha=0.7)
    
    # 格式设置
    ax4.set_xlabel(deviation_from_fit, fontsize=12)
    ax4.set_ylabel(count, fontsize=12)
    ax4.set_title(error_distribution, fontsize=14)
    ax4.grid(True, linestyle='--', alpha=0.7)
    
    # 显示方法
    plt.figtext(0.5, 0.01, method1_text, 
               ha='center', fontsize=12, 
               bbox=dict(facecolor='#e6f5c9', alpha=0.8, boxstyle='round,pad=0.5'))
    
    # 调整布局
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    
    return plt.gcf()

def save_results_to_file(temperatures, densities, tg, output_path=None, language="cn"):
    """
    将分析结果保存到文件中
    
    参数:
    temperatures - 温度数据列表
    densities - 密度数据列表
    tg - 计算得到的玻璃化转变温度
    output_path - 输出文件路径，默认为当前目录下的 tg_results.csv
    language - 标签语言选择，"cn"为中文，"en"为英文
    """
    if output_path is None:
        base_dir = os.path.dirname(os.path.abspath(__file__))
        output_path = os.path.join(base_dir, 'tg_results.csv')
    
    # 创建结果DataFrame
    results_df = pd.DataFrame({
        'Temperature(K)': temperatures,
        'Density(g/cm3)': densities
    })
    
    # 添加额外的结果信息
    high_temp_mask = temperatures > tg
    low_temp_mask = temperatures <= tg
    
    # 标记区域
    results_df['Region'] = ['液体' if temp > tg else '玻璃' for temp in temperatures]
    
    # 保存结果
    results_df.to_csv(output_path, index=False, encoding='utf-8-sig')
    print(f"分析结果已保存至 {output_path}")

class TgAnalysisApp:
    """
    玻璃化转变温度分析GUI应用
    """
    def __init__(self, root):
        self.root = root
        self.root.title("玻璃化转变温度分析工具")
        self.root.geometry("1000x750")  # 更大的默认窗口尺寸
        self.root.minsize(900, 700)     # 增加最小窗口尺寸
        
        # 设置应用风格
        style = ttk.Style()
        style.theme_use('clam')  # 使用较现代的主题
        
        # 语言设置
        self.language_var = tk.StringVar(value="cn")  # 默认中文
        
        # 创建主框架 - 使用Canvas和Scrollbar支持滚动
        self.main_canvas = tk.Canvas(self.root, borderwidth=0, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=self.main_canvas.yview)
        self.scrollable_frame = ttk.Frame(self.main_canvas, width=900)  # 设置最小宽度
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.main_canvas.configure(
                scrollregion=self.main_canvas.bbox("all")
            )
        )
        
        # 设置Canvas大小随着窗口变化
        self.root.bind("<Configure>", self._on_window_resize)
        
        # 使滚动框架填满Canvas的整个宽度
        self.main_canvas.bind("<Configure>", 
                             lambda e: self.main_canvas.itemconfig(self.frame_id, width=e.width))
        
        # 创建窗口并保存ID以便后续调整大小
        self.frame_id = self.main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.main_canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # 使用grid布局管理器代替pack，使界面填满整个窗口
        self.main_canvas.grid(row=0, column=0, sticky="nsew")
        self.scrollbar.grid(row=0, column=1, sticky="ns")
        
        # 设置网格的权重，使主画布可以扩展
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # 创建顶部框架
        top_header_frame = ttk.Frame(self.scrollable_frame)
        top_header_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        # 创建标题
        title_label = ttk.Label(top_header_frame, text="玻璃化转变温度(Tg)分析程序", font=("SimHei", 16, "bold"))
        title_label.pack(side=tk.LEFT, pady=10)
        
        # 添加语言切换按钮
        self.lang_button = ttk.Button(top_header_frame, text="English", 
                                    command=self.toggle_language, width=10)
        self.lang_button.pack(side=tk.RIGHT, padx=10, pady=10)
        
        # 创建上半部分框架 - 数据输入和控制
        self.top_frame = ttk.Frame(self.scrollable_frame)
        self.top_frame.pack(fill=tk.X, expand=False, pady=5)
        
        # 文件选择区域
        self.file_frame = ttk.LabelFrame(self.top_frame, text="数据文件选择", padding=5)
        self.file_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(self.file_frame, textvariable=self.file_path_var, width=50)
        self.file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        
        self.browse_button = ttk.Button(self.file_frame, text="浏览...", command=self.browse_file)
        self.browse_button.pack(side=tk.LEFT, padx=5)
        
        self.load_button = ttk.Button(self.file_frame, text="加载数据", command=self.load_data)
        self.load_button.pack(side=tk.LEFT, padx=5)
        
        # 样品信息区域
        self.sample_frame = ttk.LabelFrame(self.top_frame, text="样品信息", padding=5)
        self.sample_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        ttk.Label(self.sample_frame, text="样品名称:").pack(side=tk.LEFT, padx=5)
        self.sample_name_var = tk.StringVar(value="样品")
        self.sample_entry = ttk.Entry(self.sample_frame, textvariable=self.sample_name_var, width=20)
        self.sample_entry.pack(side=tk.LEFT, padx=5)
        
        # 数据预览区域 - 减小高度
        self.data_frame = ttk.LabelFrame(self.scrollable_frame, text="数据预览", padding=5)
        self.data_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        # 创建表格
        self.tree_frame = ttk.Frame(self.data_frame)
        self.tree_frame.pack(fill=tk.X, expand=False, padx=5, pady=5)
        
        self.tree_scroll = ttk.Scrollbar(self.tree_frame)
        self.tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置较小高度的Treeview
        self.tree = ttk.Treeview(self.tree_frame, yscrollcommand=self.tree_scroll.set, height=5)
        self.tree.pack(fill=tk.X, expand=False)
        
        self.tree_scroll.config(command=self.tree.yview)
        
        # 设置列
        self.tree["columns"] = ("index", "temperature", "density")
        self.tree.column("#0", width=0, stretch=tk.NO)
        self.tree.column("index", anchor=tk.CENTER, width=50, stretch=tk.NO)
        self.tree.column("temperature", anchor=tk.CENTER, width=120, stretch=tk.YES)
        self.tree.column("density", anchor=tk.CENTER, width=120, stretch=tk.YES)
        
        self.tree.heading("#0", text="", anchor=tk.CENTER)
        self.tree.heading("index", text="序号", anchor=tk.CENTER)
        self.tree.heading("temperature", text="温度 (K)", anchor=tk.CENTER)
        self.tree.heading("density", text=f"密度 ({DENSITY_UNIT})", anchor=tk.CENTER)
        
        # 创建数据可视化预览 - 减小高度
        self.preview_frame = ttk.LabelFrame(self.scrollable_frame, text="数据可视化预览", padding=5)
        self.preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5, ipady=100)  # 修改为BOTH和True
        
        # 分析控制区域
        self.control_frame = ttk.LabelFrame(self.scrollable_frame, text="操作控制", padding=10)
        self.control_frame.pack(fill=tk.X, expand=False, padx=5, pady=10)
        
        # 使用更大的按钮，便于看到
        # 添加交互式分析按钮，放在最左边，作为主要操作按钮
        self.interactive_button = ttk.Button(self.control_frame, text="交互式分析", 
                                            command=self.interactive_analyze,
                                            style='Big.TButton')
        self.interactive_button.pack(side=tk.LEFT, padx=10, pady=10)
        
        self.analyze_button = ttk.Button(self.control_frame, text="确认分析", command=self.analyze_data, 
                                         style='Big.TButton')
        self.analyze_button.pack(side=tk.LEFT, padx=10, pady=10)
        
        self.save_button = ttk.Button(self.control_frame, text="保存结果", command=self.save_results,
                                     style='Big.TButton')
        self.save_button.pack(side=tk.LEFT, padx=10, pady=10)
        
        self.save_data_button = ttk.Button(self.control_frame, text="保存数据", command=self.save_data,
                                     style='Big.TButton')
        self.save_data_button.pack(side=tk.LEFT, padx=10, pady=10)
        
        self.detailed_var = tk.BooleanVar(value=False)
        self.detailed_check = ttk.Checkbutton(self.control_frame, text="生成详细分析", 
                                              variable=self.detailed_var)
        self.detailed_check.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 添加大字体的状态显示
        self.status_frame = ttk.Frame(self.scrollable_frame)
        self.status_frame.pack(fill=tk.X, expand=False, padx=5, pady=10)
        
        self.status_var = tk.StringVar()
        self.status_var.set("准备就绪 - 请选择数据文件")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var, 
                                     font=("SimHei", 12, "bold"))
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 添加交互式图表框架
        self.interactive_frame = ttk.LabelFrame(self.scrollable_frame, text="交互式Tg分析", padding=5)
        # interactive_frame会在interactive_analyze方法中调用时才添加到界面
        
        # 创建大按钮样式
        style.configure('Big.TButton', font=('SimHei', 12))
        
        # 初始化数据
        self.temperatures = []
        self.densities = []
        self.tg_result = None
        self.data_loaded = False
        self.preview_canvas = None
        self.interactive_canvas = None
        self.interactive_fig = None
        self.interactive_draggable = None
        self.toolbar = None
        
        # 绑定鼠标滚轮事件
        if sys.platform.startswith('win'):
            self.root.bind("<MouseWheel>", self._on_mousewheel)
        elif sys.platform == 'darwin':
            self.root.bind("<MouseWheel>", self._on_mousewheel)
        else:
            self.root.bind("<Button-4>", self._on_mousewheel)
            self.root.bind("<Button-5>", self._on_mousewheel)
        
    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件，兼容不同操作系统"""
        if sys.platform.startswith('win'):
            # Windows平台
            self.main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        elif sys.platform == 'darwin':
            # macOS平台
            self.main_canvas.yview_scroll(int(-1*event.delta), "units")
        else:
            # Linux平台
            if event.num == 4:
                self.main_canvas.yview_scroll(-1, "units")
            elif event.num == 5:
                self.main_canvas.yview_scroll(1, "units")
        
    def browse_file(self):
        """浏览并选择数据文件"""
        filetypes = [
            ('CSV文件', '*.csv'),
            ('Excel文件', '*.xlsx *.xls'),
            ('所有文件', '*.*')
        ]
        filepath = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=filetypes
        )
        if filepath:
            self.file_path_var.set(filepath)
            # 从文件名提取样品名称
            base_name = os.path.basename(filepath)
            sample_name = os.path.splitext(base_name)[0]
            self.sample_name_var.set(sample_name)
    
    def load_data(self):
        """加载数据文件"""
        filepath = self.file_path_var.get().strip()
        if not filepath:
            messagebox.showerror("错误", "请先选择数据文件")
            return
            
        try:
            self.status_var.set("正在加载数据...")
            self.root.update()
            
            self.temperatures, self.densities = read_data_from_file(filepath)
            self.data_loaded = True
            
            # 清空现有表格
            for item in self.tree.get_children():
                self.tree.delete(item)
                
            # 填充表格
            for i, (temp, dens) in enumerate(zip(self.temperatures, self.densities)):
                self.tree.insert("", tk.END, values=(i+1, f"{temp:.1f}", f"{dens:.4f}"))
                
            # 更新状态
            self.status_var.set("已加载 {} 个数据点 - 点击'分析数据'继续".format(len(self.temperatures)))
            
            # 生成预览图
            self.create_preview_plot()
            
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败: {str(e)}")
            self.status_var.set("数据加载失败")
    
    def create_preview_plot(self):
        """创建温度-密度数据的预览图"""
        if not self.data_loaded or len(self.temperatures) == 0:
            return
        
        # 清除预览框架中的内容
        for widget in self.preview_frame.winfo_children():
            widget.destroy()
        
        # 创建预览图，使用更大的尺寸
        fig, ax = plt.subplots(figsize=(10, 5), dpi=100)
        
        # 使用充分利用可用宽度的Frame
        canvas_frame = ttk.Frame(self.preview_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # 使用当前语言设置
        current_lang = self.language_var.get()
        if current_lang == "en":
            xlabel = "Temperature (K)"
            ylabel = f"Density ({DENSITY_UNIT})"
            title = "Temperature-Density Preview"
        else:
            xlabel = "温度 (K)"
            ylabel = f"密度 ({DENSITY_UNIT})"
            title = "温度-密度数据预览"
        
        # 绘制散点图
        ax.scatter(self.temperatures, self.densities, color='#3182bd', alpha=0.8)
        ax.set_xlabel(xlabel)
        ax.set_ylabel(ylabel)
        ax.set_title(title)
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 调整图形以填满窗口
        fig.tight_layout()
        
        # 将图表嵌入到Tkinter界面，使用fill=BOTH和expand=True
        canvas = FigureCanvasTkAgg(fig, master=canvas_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 添加工具栏以允许用户与图表交互
        toolbar = NavigationToolbar2Tk(canvas, canvas_frame)
        toolbar.update()
        
        # 保存画布对象供后续使用
        self.preview_canvas = canvas
    
    def analyze_data(self):
        """分析数据并生成玻璃化转变温度结果"""
        if not self.data_loaded or len(self.temperatures) == 0:
            messagebox.showerror("错误" if self.language_var.get() == "cn" else "Error", 
                              "请先加载有效数据" if self.language_var.get() == "cn" else "Please load valid data first")
            return
            
        try:
            # 获取样品名称
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "样品" if self.language_var.get() == "cn" else "Sample"
            
            # 更新状态
            self.status_var.set("正在分析数据..." if self.language_var.get() == "cn" else "Analyzing data...")
            self.root.update()
            
            # 创建分析图表
            plt.figure(figsize=(8, 6), dpi=100)
            
            # 获取Y轴设置（如果有）
            y_margin = self.y_margin_var.get() if hasattr(self, 'y_margin_var') else 0.05
            y_min = None
            y_max = None
            
            if hasattr(self, 'enable_manual_range') and self.enable_manual_range.get():
                try:
                    y_min = float(self.y_min_var.get())
                    y_max = float(self.y_max_var.get())
                except:
                    pass
            
            # 检查是否已有交互式分析结果
            if hasattr(self, 'interactive_draggable') and self.interactive_draggable:
                # 直接使用交互式分析的Tg值
                self.tg_result = self.interactive_draggable.x
                
                # 创建与交互式分析相同参数的分析图表（但不是交互式的）
                plt.figure(figsize=(8, 6), dpi=100)
                find_glass_transition_temperature(
                    self.temperatures, self.densities, 
                    sample_name=sample_name,
                    interactive=False,
                    y_margin=y_margin,
                    y_min=y_min,
                    y_max=y_max,
                    language=self.language_var.get()
                )
                
                # 生成详细分析（如果选中）
                if self.detailed_var.get():
                    # 从交互式分析中获取拟合参数
                    high_params = self.interactive_draggable.cached_high_params
                    low_params = self.interactive_draggable.cached_low_params
                    
                    if high_params and low_params:
                        best_params = (high_params, low_params)
                        # 创建详细分析
                        create_detailed_analysis(
                            self.temperatures, self.densities,
                            best_params, self.tg_result,
                            sample_name=sample_name,
                            y_margin=y_margin,
                            y_min=y_min,
                            y_max=y_max,
                            language=self.language_var.get(),
                            from_interactive=True
                        )
                        plt.figure()  # 创建新图形以显示详细分析
                
                # 显示图表
                plt.show()
            else:
                # 没有交互式分析结果，执行正常分析
                self.tg_result = find_glass_transition_temperature(
                    self.temperatures, self.densities, 
                    sample_name=sample_name,
                    language=self.language_var.get()
                )
            
            # 更新状态
            self.status_var.set(f"分析完成! Tg = {self.tg_result:.1f}K" if self.language_var.get() == "cn" 
                             else f"Analysis complete! Tg = {self.tg_result:.1f}K")
            
            # 显示结果
            messagebox.showinfo(
                "分析结果" if self.language_var.get() == "cn" else "Analysis Result", 
                f"玻璃化转变温度(Tg): {self.tg_result:.1f}K" if self.language_var.get() == "cn" 
                else f"Glass Transition Temperature (Tg): {self.tg_result:.1f}K"
            )
        except Exception as e:
            messagebox.showerror(
                "错误" if self.language_var.get() == "cn" else "Error", 
                f"分析过程中出错: {str(e)}" if self.language_var.get() == "cn" 
                else f"Error during analysis: {str(e)}"
            )

    def interactive_analyze(self):
        """启动交互式分析模式"""
        if not self.data_loaded or len(self.temperatures) == 0:
            messagebox.showerror(
                "错误" if self.language_var.get() == "cn" else "Error", 
                "请先加载有效数据" if self.language_var.get() == "cn" else "Please load valid data first"
            )
            return
            
        try:
            self.status_var.set(
                "正在创建交互式分析..." if self.language_var.get() == "cn" 
                else "Creating interactive analysis..."
            )
            self.root.update()
            
            # 获取样品名称
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "样品" if self.language_var.get() == "cn" else "Sample"
            
            # 确保交互式框架已显示
            if not hasattr(self, 'interactive_frame_visible') or not self.interactive_frame_visible:
                self.interactive_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
                self.interactive_frame_visible = True
            
            # 清空交互式框架
            for widget in self.interactive_frame.winfo_children():
                widget.destroy()
            
            # 创建Y轴控制区域
            y_control_frame = ttk.LabelFrame(
                self.interactive_frame, 
                text="Y轴显示控制" if self.language_var.get() == "cn" else "Y-axis Display Control", 
                padding=5
            )
            y_control_frame.pack(fill=tk.X, pady=5)
            
            # 自动缩放控制区域
            auto_scale_frame = ttk.Frame(y_control_frame)
            auto_scale_frame.pack(fill=tk.X, pady=5)
            
            ttk.Label(
                auto_scale_frame, 
                text="自动缩放边距:" if self.language_var.get() == "cn" else "Auto Scale Margin:"
            ).pack(side=tk.LEFT, padx=5)
            
            # Y轴边距控制滑块
            self.y_margin_var = tk.DoubleVar(value=0.05)
            y_margin_scale = ttk.Scale(auto_scale_frame, from_=0.01, to=0.5, 
                                       variable=self.y_margin_var, length=200, 
                                       orient=tk.HORIZONTAL)
            y_margin_scale.pack(side=tk.LEFT, padx=5)
            
            # 当前值显示
            y_margin_label = ttk.Label(auto_scale_frame, text="0.05")
            y_margin_label.pack(side=tk.LEFT, padx=5)
            
            # 更新滑块标签的函数
            def update_margin_label(*args):
                y_margin_label.config(text=f"{self.y_margin_var.get():.2f}")
            
            # 绑定滑块值变化事件
            self.y_margin_var.trace_add("write", update_margin_label)
            
            # 手动范围控制区域
            manual_range_frame = ttk.Frame(y_control_frame)
            manual_range_frame.pack(fill=tk.X, pady=5)
            
            # 启用手动范围选项
            self.enable_manual_range = tk.BooleanVar(value=False)
            manual_check = ttk.Checkbutton(
                manual_range_frame, 
                text="启用手动Y轴范围" if self.language_var.get() == "cn" else "Enable Manual Y-axis Range", 
                variable=self.enable_manual_range
            )
            manual_check.pack(side=tk.LEFT, padx=5)
            
            # Y轴最小值
            ttk.Label(
                manual_range_frame, 
                text="Y轴最小值:" if self.language_var.get() == "cn" else "Y-axis Min:"
            ).pack(side=tk.LEFT, padx=5)
            self.y_min_var = tk.StringVar()
            y_min_entry = ttk.Entry(manual_range_frame, textvariable=self.y_min_var, width=8)
            y_min_entry.pack(side=tk.LEFT, padx=5)
            
            # Y轴最大值
            ttk.Label(
                manual_range_frame, 
                text="Y轴最大值:" if self.language_var.get() == "cn" else "Y-axis Max:"
            ).pack(side=tk.LEFT, padx=5)
            self.y_max_var = tk.StringVar()
            y_max_entry = ttk.Entry(manual_range_frame, textvariable=self.y_max_var, width=8)
            y_max_entry.pack(side=tk.LEFT, padx=5)
            
            # 获取默认的Y轴范围 (大约最小值和最大值)
            y_min_default = min(self.densities) - 0.1
            y_max_default = max(self.densities) + 0.1
            self.y_min_var.set(f"{y_min_default:.3f}")
            self.y_max_var.set(f"{y_max_default:.3f}")
            
            # 刷新按钮
            refresh_button = ttk.Button(
                y_control_frame, 
                text="应用设置" if self.language_var.get() == "cn" else "Apply Settings", 
                command=self.refresh_interactive_plot
            )
            refresh_button.pack(side=tk.RIGHT, padx=10)
            
            # 创建交互式Tg分析
            self.tg_result, self.interactive_fig, self.interactive_draggable = find_glass_transition_temperature(
                self.temperatures, self.densities, 
                sample_name=sample_name,
                interactive=True,
                y_margin=self.y_margin_var.get(),
                language=self.language_var.get()
            )
            
            # 创建图表容器框架，确保填满整个宽度
            chart_frame = ttk.Frame(self.interactive_frame)
            chart_frame.pack(fill=tk.BOTH, expand=True, pady=5)
            
            # 优化图表布局
            self.interactive_fig.tight_layout()
            
            # 创建新画布
            self.interactive_canvas = FigureCanvasTkAgg(self.interactive_fig, master=chart_frame)
            self.interactive_canvas.draw()
            
            # 确保图表填满整个区域
            self.interactive_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 添加工具栏
            self.toolbar = NavigationToolbar2Tk(self.interactive_canvas, chart_frame)
            self.toolbar.update()
            self.toolbar.pack(fill=tk.X)
            
            # 添加帮助文本
            help_text = ("操作指南：\n1. 拖动黑色虚线可调整Tg值位置\n2. 拖动红色和绿色端点可手动调整拟合线倾斜度\n"
                      "3. 调整Y轴设置可放大显示波动\n4. 勾选手动Y轴范围可精确控制显示范围") if self.language_var.get() == "cn" else (
                      "User Guide:\n1. Drag the black dashed line to adjust Tg position\n2. Drag red and green endpoints to adjust fitting lines\n"
                      "3. Adjust Y-axis settings to zoom in on variations\n4. Check manual Y-axis range for precise control"
                      )
            help_label = ttk.Label(self.interactive_frame, text=help_text, font=("SimHei", 10))
            help_label.pack(pady=5)
            
            # 添加当前Tg值显示
            self.current_tg_var = tk.StringVar()
            self.current_tg_var.set(
                f"当前Tg值: {self.tg_result:.1f}K" if self.language_var.get() == "cn" 
                else f"Current Tg: {self.tg_result:.1f}K"
            )
            
            tg_display_frame = ttk.Frame(self.interactive_frame)
            tg_display_frame.pack(fill=tk.X, expand=False, pady=5)
            
            tg_label = ttk.Label(tg_display_frame, textvariable=self.current_tg_var, 
                                font=("SimHei", 12, "bold"))
            tg_label.pack(side=tk.LEFT, padx=10)
            
            # 添加控制按钮框架
            control_btns_frame = ttk.Frame(tg_display_frame)
            control_btns_frame.pack(side=tk.RIGHT, padx=10)
            
            # 添加切换控制点显示的按钮
            self.show_points_var = tk.BooleanVar(value=True)
            def toggle_control_points():
                if self.interactive_draggable:
                    is_visible = self.interactive_draggable.toggle_control_points()
                    self.show_points_var.set(is_visible)
                    self.toggle_points_btn.config(
                        text="隐藏控制点" if is_visible and self.language_var.get() == "cn"
                        else "显示控制点" if self.language_var.get() == "cn"
                        else "Hide Control Points" if is_visible
                        else "Show Control Points"
                    )
            
            self.toggle_points_btn = ttk.Button(
                control_btns_frame, 
                text="隐藏控制点" if self.language_var.get() == "cn" else "Hide Control Points", 
                command=toggle_control_points
            )
            self.toggle_points_btn.pack(side=tk.LEFT, padx=5)
            
            # 添加跳转到计算交点的按钮
            def move_to_intercept():
                if self.interactive_draggable and hasattr(self.interactive_draggable, 'calc_intercept'):
                    if self.interactive_draggable.calc_intercept is not None:
                        success = self.interactive_draggable.move_to_calculated_intercept()
                        if success:
                            messagebox.showinfo(
                                "成功" if self.language_var.get() == "cn" else "Success", 
                                f"已将Tg线移动到计算出的交点: {self.interactive_draggable.x:.1f}K" if self.language_var.get() == "cn"
                                else f"Moved Tg line to calculated intercept: {self.interactive_draggable.x:.1f}K"
                            )
                            self.current_tg_var.set(
                                f"当前Tg值: {self.interactive_draggable.x:.1f}K" if self.language_var.get() == "cn"
                                else f"Current Tg: {self.interactive_draggable.x:.1f}K"
                            )
                        else:
                            messagebox.showwarning(
                                "警告" if self.language_var.get() == "cn" else "Warning", 
                                "无法移动到交点，可能不在有效范围内" if self.language_var.get() == "cn"
                                else "Cannot move to intercept, it might be outside valid range"
                            )
                    else:
                        messagebox.showinfo(
                            "提示" if self.language_var.get() == "cn" else "Information", 
                            "尚未计算出交点，请先拖动拟合线" if self.language_var.get() == "cn"
                            else "No intercept calculated yet, please drag fitting lines first"
                        )
            
            self.intercept_btn = ttk.Button(
                control_btns_frame, 
                text="跳转到交点" if self.language_var.get() == "cn" else "Jump to Intercept", 
                command=move_to_intercept
            )
            self.intercept_btn.pack(side=tk.LEFT, padx=5)
            
            # 添加保存当前Tg值的按钮
            save_tg_button = ttk.Button(
                control_btns_frame, 
                text="使用此Tg值" if self.language_var.get() == "cn" else "Use This Tg", 
                command=self.use_interactive_tg
            )
            save_tg_button.pack(side=tk.LEFT, padx=5)
            
            # 更新状态
            self.status_var.set(
                "交互式分析已启动，拖动Tg线和拟合线端点可调整参数" if self.language_var.get() == "cn"
                else "Interactive analysis started, drag Tg line and fitting line endpoints to adjust parameters"
            )
            
            # 设置回调函数，在Tg被拖动时更新显示
            def update_tg_display(new_tg, *args):
                self.current_tg_var.set(
                    f"当前Tg值: {new_tg:.1f}K" if self.language_var.get() == "cn"
                    else f"Current Tg: {new_tg:.1f}K"
                )
            
            self.interactive_draggable.on_update_callback = update_tg_display
            
        except Exception as e:
            messagebox.showerror(
                "错误" if self.language_var.get() == "cn" else "Error", 
                f"创建交互式分析失败: {str(e)}" if self.language_var.get() == "cn"
                else f"Failed to create interactive analysis: {str(e)}"
            )
            self.status_var.set(
                "交互式分析启动失败" if self.language_var.get() == "cn"
                else "Interactive analysis failed to start"
            )
    
    def refresh_interactive_plot(self):
        """刷新交互式分析图表，应用新的Y轴设置"""
        if not hasattr(self, 'interactive_fig') or self.interactive_fig is None:
            return
            
        try:
            # 获取当前Tg值
            current_tg = self.interactive_draggable.x if self.interactive_draggable else self.tg_result
            
            # 获取样品名称
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "样品" if self.language_var.get() == "cn" else "Sample"
                
            # 处理手动Y轴范围
            y_min = None
            y_max = None
            
            if self.enable_manual_range.get():
                try:
                    y_min = float(self.y_min_var.get())
                    y_max = float(self.y_max_var.get())
                    
                    # 验证输入值是否有效
                    if y_min >= y_max:
                        messagebox.showerror(
                            "错误" if self.language_var.get() == "cn" else "Error", 
                            "Y轴最小值必须小于最大值" if self.language_var.get() == "cn" 
                            else "Y-axis minimum must be less than maximum"
                        )
                        return
                except ValueError:
                    messagebox.showerror(
                        "错误" if self.language_var.get() == "cn" else "Error", 
                        "请输入有效的Y轴范围值" if self.language_var.get() == "cn"
                        else "Please enter valid Y-axis range values"
                    )
                    return
            
            # 使用新设置创建交互式图
            self.status_var.set(
                "正在刷新交互式分析..." if self.language_var.get() == "cn"
                else "Refreshing interactive analysis..."
            )
            self.root.update()
            
            # 清空画布框架
            chart_frame = None
            for widget in self.interactive_frame.winfo_children():
                if isinstance(widget, ttk.Frame) and not isinstance(widget, ttk.LabelFrame):
                    chart_frame = widget
                    for child in chart_frame.winfo_children():
                        child.destroy()
                    break
            
            if not chart_frame:
                return
            
            # 重新创建分析
            self.tg_result, self.interactive_fig, self.interactive_draggable = find_glass_transition_temperature(
                self.temperatures, self.densities, 
                sample_name=sample_name,
                interactive=True,
                y_margin=self.y_margin_var.get(),
                y_min=y_min,
                y_max=y_max,
                language=self.language_var.get()
            )
            
            # 优化图表布局
            self.interactive_fig.tight_layout()
            
            # 创建新画布
            self.interactive_canvas = FigureCanvasTkAgg(self.interactive_fig, master=chart_frame)
            self.interactive_canvas.draw()
            
            # 确保图表填满整个区域
            self.interactive_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 添加工具栏
            self.toolbar = NavigationToolbar2Tk(self.interactive_canvas, chart_frame)
            self.toolbar.update()
            self.toolbar.pack(fill=tk.X)
            
            # 更新Tg显示
            self.current_tg_var.set(
                f"当前Tg值: {self.tg_result:.1f}K" if self.language_var.get() == "cn"
                else f"Current Tg: {self.tg_result:.1f}K"
            )
            
            # 设置回调函数
            def update_tg_display(new_tg, *args):
                self.current_tg_var.set(
                    f"当前Tg值: {new_tg:.1f}K" if self.language_var.get() == "cn"
                    else f"Current Tg: {new_tg:.1f}K"
                )
            
            self.interactive_draggable.on_update_callback = update_tg_display
            
            # 更新状态
            self.status_var.set(
                "交互式分析已刷新" if self.language_var.get() == "cn"
                else "Interactive analysis refreshed"
            )
        except Exception as e:
            messagebox.showerror(
                "错误" if self.language_var.get() == "cn" else "Error",
                f"刷新交互式分析失败: {str(e)}" if self.language_var.get() == "cn"
                else f"Failed to refresh interactive analysis: {str(e)}"
            )
    
    def use_interactive_tg(self):
        """使用交互式分析的Tg值作为最终结果"""
        if hasattr(self, 'interactive_draggable') and self.interactive_draggable:
            self.tg_result = self.interactive_draggable.x
            messagebox.showinfo(
                "成功" if self.language_var.get() == "cn" else "Success",
                f"已保存Tg值: {self.tg_result:.1f}K" if self.language_var.get() == "cn"
                else f"Tg value saved: {self.tg_result:.1f}K"
            )
            self.status_var.set(
                f"分析完成! Tg = {self.tg_result:.1f}K" if self.language_var.get() == "cn"
                else f"Analysis complete! Tg = {self.tg_result:.1f}K"
            )
        else:
            messagebox.showerror(
                "错误" if self.language_var.get() == "cn" else "Error",
                "未找到有效的交互式分析结果" if self.language_var.get() == "cn"
                else "No valid interactive analysis result found"
            )
    
    def save_results(self):
        """保存分析结果和可视化图表"""
        if not hasattr(self, 'tg_result') or self.tg_result is None:
            messagebox.showerror(
                "错误" if self.language_var.get() == "cn" else "Error", 
                "请先完成Tg分析" if self.language_var.get() == "cn" else "Please complete Tg analysis first"
            )
            return
            
        try:
            # 获取样品名称
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "样品" if self.language_var.get() == "cn" else "Sample"
            
            # 显示保存对话框
            save_path = filedialog.asksaveasfilename(
                title=("选择保存路径" if self.language_var.get() == "cn" else "Select Save Path"),
                defaultextension=".png",
                filetypes=[
                    ("PNG图像", "*.png") if self.language_var.get() == "cn" else ("PNG Images", "*.png"),
                    ("所有文件", "*.*") if self.language_var.get() == "cn" else ("All Files", "*.*"),
                ],
                initialfile=f"glass_transition_{sample_name}"
            )
            
            if not save_path:
                return
                
            # 获取保存目录
            save_dir = os.path.dirname(save_path)
            base_name = os.path.basename(save_path)
            name_without_ext = os.path.splitext(base_name)[0]
            
            # 更新状态
            self.status_var.set(
                "正在保存结果..." if self.language_var.get() == "cn" else "Saving results..."
            )
            self.root.update()
            
            # 生成详细分析图表
            if self.detailed_var.get():
                # 获取当前Y轴设置
                y_margin = self.y_margin_var.get() if hasattr(self, 'y_margin_var') else 0.05
                y_min = None
                y_max = None
                
                if hasattr(self, 'enable_manual_range') and self.enable_manual_range.get():
                    try:
                        y_min = float(self.y_min_var.get())
                        y_max = float(self.y_max_var.get())
                    except:
                        pass
                
                # 生成详细分析图表
                if hasattr(self, 'interactive_draggable') and self.interactive_draggable:
                    # 从交互式分析中获取拟合参数
                    high_params = self.interactive_draggable.cached_high_params
                    low_params = self.interactive_draggable.cached_low_params
                    
                    if high_params and low_params:
                        best_params = (high_params, low_params)
                        # 使用交互式分析的Tg值和拟合参数
                        create_detailed_analysis(
                            self.temperatures, self.densities, 
                            best_params, self.tg_result, 
                            sample_name=sample_name,
                            y_margin=y_margin,
                            y_min=y_min,
                            y_max=y_max,
                            language=self.language_var.get(),
                            from_interactive=True)
                        
                        # 保存详细分析结果
                        detailed_path = os.path.join(save_dir, f"{name_without_ext}_detailed.png")
                        plt.savefig(detailed_path, dpi=300, bbox_inches='tight')
                        plt.close()
                else:
                    # 如果没有交互式分析结果，则进行标准分析生成详细图表
                    # 这里我们需要先进行拟合以获取拟合参数
                    # 创建临时图表进行拟合
                    temp_fig = plt.figure(figsize=(8, 6))
                    temp_ax = temp_fig.add_subplot(111)
                    
                    # 进行拟合
                    tmp_tg, tmp_fig, tmp_draggable = find_glass_transition_temperature(
                        self.temperatures, self.densities,
                        interactive=True,
                        y_margin=y_margin,
                        y_min=y_min,
                        y_max=y_max,
                        language=self.language_var.get()
                    )
                    
                    # 获取拟合参数
                    high_params = tmp_draggable.cached_high_params
                    low_params = tmp_draggable.cached_low_params
                    
                    if high_params and low_params:
                        best_params = (high_params, low_params)
                        # 创建详细分析
                        create_detailed_analysis(
                            self.temperatures, self.densities,
                            best_params, self.tg_result,
                            sample_name=sample_name,
                            y_margin=y_margin,
                            y_min=y_min,
                            y_max=y_max,
                            language=self.language_var.get(),
                            from_interactive=True)
                        
                        # 保存详细分析结果
                        detailed_path = os.path.join(save_dir, f"{name_without_ext}_detailed.png")
                        plt.savefig(detailed_path, dpi=300, bbox_inches='tight')
                        
                        # 关闭临时图表
                        plt.close(temp_fig)
                        plt.close()
            
            # 保存主分析结果
            # 创建标准分析图表
            standard_fig = plt.figure(figsize=(8, 6), dpi=100)
            
            # 使用find_glass_transition_temperature重新生成标准图表
            find_glass_transition_temperature(
                self.temperatures, self.densities, 
                output_path=save_path,
                sample_name=sample_name,
                interactive=False,
                y_margin=0.05,
                language=self.language_var.get()
            )
            
            # 保存数据结果
            csv_path = os.path.join(save_dir, f"{name_without_ext}_data.csv")
            save_results_to_file(self.temperatures, self.densities, self.tg_result, csv_path, language=self.language_var.get())
            
            # 显示成功消息
            messagebox.showinfo(
                "保存成功" if self.language_var.get() == "cn" else "Save Successful", 
                (f"结果已保存至:\n{save_path}\n\n" +
                 (f"详细分析已保存至:\n{detailed_path}\n\n" if self.detailed_var.get() else "") +
                 f"数据已保存至:\n{csv_path}") if self.language_var.get() == "cn" else
                (f"Results saved to:\n{save_path}\n\n" +
                 (f"Detailed analysis saved to:\n{detailed_path}\n\n" if self.detailed_var.get() else "") +
                 f"Data saved to:\n{csv_path}")
            )
            
            # 更新状态
            self.status_var.set(
                "结果已保存" if self.language_var.get() == "cn" else "Results saved"
            )
            
        except Exception as e:
            messagebox.showerror(
                "错误" if self.language_var.get() == "cn" else "Error", 
                f"保存结果失败: {str(e)}" if self.language_var.get() == "cn" else f"Failed to save results: {str(e)}"
            )
    
    def toggle_language(self):
        """切换界面语言和图表标签语言"""
        current_lang = self.language_var.get()
        
        if current_lang == "cn":
            # 切换到英文
            self.language_var.set("en")
            self.lang_button.config(text="中文")
            self.root.title("Glass Transition Temperature Analysis Tool")
            
            # 更新UI文本
            self.file_frame.config(text="Data File Selection")
            self.sample_frame.config(text="Sample Information")
            self.data_frame.config(text="Data Preview")
            self.preview_frame.config(text="Data Visualization Preview")
            self.control_frame.config(text="Operation Controls")
            
            # 更新树状图标题
            self.tree.heading("index", text="Index", anchor=tk.CENTER)
            self.tree.heading("temperature", text="Temperature (K)", anchor=tk.CENTER)
            self.tree.heading("density", text=f"Density ({DENSITY_UNIT})", anchor=tk.CENTER)
            
            # 更新按钮文本
            self.browse_button.config(text="Browse...")
            self.load_button.config(text="Load Data")
            self.analyze_button.config(text="Confirm Analysis")
            self.interactive_button.config(text="Interactive Analysis")
            self.save_button.config(text="Save Results")
            self.save_data_button.config(text="Save Data")
            self.detailed_check.config(text="Generate Detailed Analysis")
            
            # 更新状态
            current_status = self.status_var.get()
            if current_status == "准备就绪 - 请选择数据文件":
                self.status_var.set("Ready - Please select a data file")
            
            # 如果存在交互式框架，更新其文本
            if hasattr(self, 'interactive_frame_visible') and self.interactive_frame_visible:
                self.interactive_frame.config(text="Interactive Tg Analysis")
                
                # 尝试更新交互式框架中的控件
                for widget in self.interactive_frame.winfo_children():
                    if isinstance(widget, ttk.LabelFrame) and widget.cget("text") == "Y轴显示控制":
                        widget.config(text="Y-axis Display Control")
                
                # 如果有控制点切换按钮，更新其文本
                if hasattr(self, 'toggle_points_btn'):
                    if self.show_points_var.get():
                        self.toggle_points_btn.config(text="Hide Control Points")
                    else:
                        self.toggle_points_btn.config(text="Show Control Points")
                
                # 如果有交点跳转按钮，更新其文本
                if hasattr(self, 'intercept_btn'):
                    self.intercept_btn.config(text="Jump to Intercept")
        else:
            # 切换到中文
            self.language_var.set("cn")
            self.lang_button.config(text="English")
            self.root.title("玻璃化转变温度分析工具")
            
            # 更新UI文本
            self.file_frame.config(text="数据文件选择")
            self.sample_frame.config(text="样品信息")
            self.data_frame.config(text="数据预览")
            self.preview_frame.config(text="数据可视化预览")
            self.control_frame.config(text="操作控制")
            
            # 更新树状图标题
            self.tree.heading("index", text="序号", anchor=tk.CENTER)
            self.tree.heading("temperature", text="温度 (K)", anchor=tk.CENTER)
            self.tree.heading("density", text=f"密度 ({DENSITY_UNIT})", anchor=tk.CENTER)
            
            # 更新按钮文本
            self.browse_button.config(text="浏览...")
            self.load_button.config(text="加载数据")
            self.analyze_button.config(text="确认分析")
            self.interactive_button.config(text="交互式分析")
            self.save_button.config(text="保存结果")
            self.save_data_button.config(text="保存数据")
            self.detailed_check.config(text="生成详细分析")
            
            # 更新状态
            current_status = self.status_var.get()
            if current_status == "Ready - Please select a data file":
                self.status_var.set("准备就绪 - 请选择数据文件")
            
            # 如果存在交互式框架，更新其文本
            if hasattr(self, 'interactive_frame_visible') and self.interactive_frame_visible:
                self.interactive_frame.config(text="交互式Tg分析")
                
                # 尝试更新交互式框架中的控件
                for widget in self.interactive_frame.winfo_children():
                    if isinstance(widget, ttk.LabelFrame) and widget.cget("text") == "Y-axis Display Control":
                        widget.config(text="Y轴显示控制")
                
                # 如果有控制点切换按钮，更新其文本
                if hasattr(self, 'toggle_points_btn'):
                    if self.show_points_var.get():
                        self.toggle_points_btn.config(text="隐藏控制点")
                    else:
                        self.toggle_points_btn.config(text="显示控制点")
                
                # 如果有交点跳转按钮，更新其文本
                if hasattr(self, 'intercept_btn'):
                    self.intercept_btn.config(text="跳转到交点")
        
        # 如果有预览图，更新预览图
        self.refresh_preview()
        
        # 如果有交互式图，刷新交互式图
        if hasattr(self, 'interactive_fig') and self.interactive_fig is not None:
            self.refresh_interactive_plot()
            
    def refresh_preview(self):
        """刷新预览图，应用当前语言设置"""
        if hasattr(self, 'preview_canvas') and self.preview_canvas is not None and self.data_loaded:
            # 清除预览框架内容
            for widget in self.preview_frame.winfo_children():
                widget.destroy()
            
            # 使用当前语言创建预览图
            self.create_preview_plot()

    def save_data(self):
        """保存分析数据到CSV和XLSX格式"""
        if not hasattr(self, 'tg_result') or self.tg_result is None:
            messagebox.showerror(
                "错误" if self.language_var.get() == "cn" else "Error", 
                "请先完成Tg分析" if self.language_var.get() == "cn" else "Please complete Tg analysis first"
            )
            return
            
        if not hasattr(self, 'interactive_draggable') or self.interactive_draggable is None:
            messagebox.showerror(
                "错误" if self.language_var.get() == "cn" else "Error",
                "需要先进行交互式分析" if self.language_var.get() == "cn" else "Interactive analysis is required first"
            )
            return
            
        try:
            # 获取样品名称
            sample_name = self.sample_name_var.get().strip()
            if not sample_name:
                sample_name = "样品" if self.language_var.get() == "cn" else "Sample"
            
            # 显示保存对话框
            filetypes = [
                ("CSV文件", "*.csv") if self.language_var.get() == "cn" else ("CSV Files", "*.csv"),
                ("Excel文件", "*.xlsx") if self.language_var.get() == "cn" else ("Excel Files", "*.xlsx")
            ]
            
            save_path = filedialog.asksaveasfilename(
                title=("选择保存路径" if self.language_var.get() == "cn" else "Select Save Path"),
                defaultextension=".csv",
                filetypes=filetypes,
                initialfile=f"glass_transition_data_{sample_name}"
            )
            
            if not save_path:
                return
                
            # 获取温度和密度数据
            temperatures = self.temperatures
            densities = self.densities
            tg = self.tg_result
            
            # 获取拟合参数
            high_params = self.interactive_draggable.cached_high_params
            low_params = self.interactive_draggable.cached_low_params
            
            if not high_params or not low_params:
                messagebox.showerror(
                    "错误" if self.language_var.get() == "cn" else "Error",
                    "无法获取拟合参数" if self.language_var.get() == "cn" else "Cannot get fitting parameters"
                )
                return
            
            # 更新状态
            self.status_var.set(
                "正在保存数据..." if self.language_var.get() == "cn" else "Saving data..."
            )
            self.root.update()
            
            # 创建DataFrame
            data = {
                ('温度 (K)' if self.language_var.get() == "cn" else 'Temperature (K)'): temperatures,
                (f'密度 ({DENSITY_UNIT})' if self.language_var.get() == "cn" else f'Density ({DENSITY_UNIT})'): densities
            }
            
            # 添加区域标签
            regions = []
            for temp in temperatures:
                if temp > tg:
                    regions.append('液体' if self.language_var.get() == "cn" else 'Liquid')
                else:
                    regions.append('玻璃' if self.language_var.get() == "cn" else 'Glass')
            
            data['区域' if self.language_var.get() == "cn" else 'Region'] = regions
            
            # 添加拟合数据
            high_temps = [t for t in temperatures if t > tg]
            low_temps = [t for t in temperatures if t <= tg]
            
            high_fits = []
            low_fits = []
            
            # 计算高温区拟合值
            for t in temperatures:
                if t > tg:
                    high_fits.append(high_params[0] * t + high_params[1])
                else:
                    high_fits.append(None)
            
            # 计算低温区拟合值
            for t in temperatures:
                if t <= tg:
                    low_fits.append(low_params[0] * t + low_params[1])
                else:
                    low_fits.append(None)
            
            data['高温拟合' if self.language_var.get() == "cn" else 'High Temp Fit'] = high_fits
            data['低温拟合' if self.language_var.get() == "cn" else 'Low Temp Fit'] = low_fits
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 添加拟合参数和Tg值到第二个表
            fit_info = {
                '参数' if self.language_var.get() == "cn" else 'Parameter': [
                    '高温斜率' if self.language_var.get() == "cn" else 'High Temp Slope',
                    '高温截距' if self.language_var.get() == "cn" else 'High Temp Intercept',
                    '低温斜率' if self.language_var.get() == "cn" else 'Low Temp Slope',
                    '低温截距' if self.language_var.get() == "cn" else 'Low Temp Intercept',
                    'Tg (K)'
                ],
                '值' if self.language_var.get() == "cn" else 'Value': [
                    high_params[0],
                    high_params[1],
                    low_params[0],
                    low_params[1],
                    tg
                ]
            }
            
            fit_df = pd.DataFrame(fit_info)
            
            # 保存为CSV或XLSX
            file_ext = os.path.splitext(save_path)[1].lower()
            
            if file_ext == '.csv':
                df.to_csv(save_path, index=False, encoding='utf-8-sig')
                
                # 保存拟合参数到另一个CSV文件
                params_path = os.path.splitext(save_path)[0] + '_params.csv'
                fit_df.to_csv(params_path, index=False, encoding='utf-8-sig')
                
                messagebox.showinfo(
                    "保存成功" if self.language_var.get() == "cn" else "Save Successful",
                    (f"数据已保存至:\n{save_path}\n\n参数已保存至:\n{params_path}") 
                    if self.language_var.get() == "cn" else 
                    (f"Data saved to:\n{save_path}\n\nParameters saved to:\n{params_path}")
                )
            elif file_ext == '.xlsx':
                # 创建Excel文件并添加两个表
                with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='数据' if self.language_var.get() == "cn" else 'Data', index=False)
                    fit_df.to_excel(writer, sheet_name='拟合参数' if self.language_var.get() == "cn" else 'Fitting Parameters', index=False)
                
                messagebox.showinfo(
                    "保存成功" if self.language_var.get() == "cn" else "Save Successful",
                    (f"数据和参数已保存至:\n{save_path}") 
                    if self.language_var.get() == "cn" else 
                    (f"Data and parameters saved to:\n{save_path}")
                )
            else:
                # 默认保存为CSV
                csv_path = os.path.splitext(save_path)[0] + '.csv'
                df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                
                params_path = os.path.splitext(csv_path)[0] + '_params.csv'
                fit_df.to_csv(params_path, index=False, encoding='utf-8-sig')
                
                messagebox.showinfo(
                    "保存成功" if self.language_var.get() == "cn" else "Save Successful",
                    (f"数据已保存至:\n{csv_path}\n\n参数已保存至:\n{params_path}") 
                    if self.language_var.get() == "cn" else 
                    (f"Data saved to:\n{csv_path}\n\nParameters saved to:\n{params_path}")
                )
            
            # 更新状态
            self.status_var.set(
                "数据已保存" if self.language_var.get() == "cn" else "Data saved"
            )
            
        except Exception as e:
            messagebox.showerror(
                "错误" if self.language_var.get() == "cn" else "Error", 
                f"保存数据失败: {str(e)}" if self.language_var.get() == "cn" else f"Failed to save data: {str(e)}"
            )

    def _on_window_resize(self, event):
        """窗口大小变化时更新Canvas大小"""
        # 确保event不是由子控件触发的
        if event.widget == self.root:
            # 更新Canvas宽度与窗口大小匹配
            width = event.width - self.scrollbar.winfo_width() - 5  # 减去滚动条宽度和一点填充
            if width > 0:
                self.main_canvas.config(width=width)
                # 调整scrollable_frame宽度
                self.main_canvas.itemconfig(self.frame_id, width=width)

# 主程序入口
if __name__ == "__main__":
    # 检查运行模式
    if len(sys.argv) > 1 and sys.argv[1] == "--cli":
        # 命令行模式
        if len(sys.argv) > 2:
            file_path = sys.argv[2]
        else:
            file_path = input("请输入数据文件路径（CSV或Excel格式）: ")
            
        # 可选参数: 样品名称
        sample_name = None
        if len(sys.argv) > 3:
            sample_name = sys.argv[3]
        
        # 读取数据
        temperatures, densities = read_data_from_file(file_path)
        
        # 打印读取到的数据
        print(f"读取到 {len(temperatures)} 个数据点")
        print("温度(K) | 密度(g/cm3)")
        print("-" * 30)
        for t, d in zip(temperatures, densities):
            print(f"{t:7.1f} | {d:7.4f}")
        
        # 计算玻璃化转变温度并生成可视化
        tg = find_glass_transition_temperature(temperatures, densities, sample_name=sample_name)
        
        # 保存处理结果
        save_results_to_file(temperatures, densities, tg)
    else:
        # GUI模式
        root = tk.Tk()
        app = TgAnalysisApp(root)
        root.mainloop()