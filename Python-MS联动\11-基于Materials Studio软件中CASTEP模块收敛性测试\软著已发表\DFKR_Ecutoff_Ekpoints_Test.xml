<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE REGISTRY SYSTEM "System/Registry.dtd">
<REGISTRY>
	<ITEMS>
		<ITEM NAME="cmdUser17" TEXT="DM-CutOff-K-Test" TOOLTIP="Run user script" STATUS="Perl script for Materials Studio 2024. Tests energy cutoff and k - point grid convergence. Accepts inputs, runs calcs, finds opt. params, reports progress. " CATEGORY="RunScriptOnScopeOnServer" IMAGE="ScriptingPlugin,1" CATEGORYDATA="library://CutOff-K/new-4.pl" ENABLEMENTPROCESSOR="UserMenu:3dAtomisticDocument" DOCUMENTUSAGE="ActiveDocument" HELP="None" TYPE="Command" IMPORTANCE="Medium" ACCELERATOR="" LOCKACCESS="No">
			<OPTIONS>
				<OPTION NAME="Min_Energy_Cutoff" DATATYPE="Integer" DEFAULTVALUE="300"/>
				<OPTION NAME="Max_Energy_Cutoff" DATATYPE="Integer" DEFAULTVALUE="500"/>
				<OPTION NAME="Energy_Cutoff_Step" DATATYPE="Integer" DEFAULTVALUE="10"/>
				<OPTION NAME="Min_KPoint_Grid" DATATYPE="String" DEFAULTVALUE="3 3 3"/>
				<OPTION NAME="Max_KPoint_Grid" DATATYPE="String" DEFAULTVALUE="5 5 5"/>
				<OPTION NAME="Energy_Difference_Tolerance" DATATYPE="Float" DEFAULTVALUE="0.01"/>
				<OPTION NAME="XCFunctional" DATATYPE="String" DEFAULTVALUE="PBE"/>
				<OPTION NAME="Pseudopotentials" DATATYPE="String" DEFAULTVALUE="Ultrasoft"/>
			</OPTIONS>
		</ITEM>
	</ITEMS>
	<MENUS>
		<MENU NAME="menuContainer" OWNER="Container">
			<POPUP NAME="User" LOCATION="Default" POSITIONAL="No">
				<MENUITEM NAME="cmdUser17" LOCATION="Default" POSITIONAL="No"/>
			</POPUP>
		</MENU>
	</MENUS>
	<TOOLBARS/>
	<LOCATIONS>
		<LOCATION NAME="CutOff-K">
			<PATH>
E:\\MS2024\\Perl-zhibo-20250331_Files\\Documents			</PATH>
		</LOCATION>
	</LOCATIONS>
</REGISTRY>
