#!perl

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);
use POSIX qw(strftime);
use constant TRUE  => 1;
use constant FALSE => 0;
use constant DEBUG => 1;  # 调试级别，数值越大输出信息越详细
use constant PICO_TO_FEMTO => 1000;  # 皮秒到飞秒的转换系数

# 硅氧烷与异氰酸酯交联模拟脚本
# 作者: 基于徐敬成和Jason DeJoannis的脚本修改
# 日期: 2023年

# 本脚本用于模拟硅氧烷与异氰酸酯的交联反应，特别是:
# 1. 羟基(-OH)与异氰酸酯(-N=C=O)基团反应形成氨基甲酸酯键(-NH-COO-)
# 2. ABA与固化剂C交联形成体型分子

###### 用户可编辑设置 ######

# 基本设置
my $xsdDocName = "Initial_Structure_m2";  # 初始结构文件名
my $outputBaseName = "Crosslinked_Structure";  # 输出文件基本名称

# 交联参数
my $conversionTarget = 70;  # 目标交联度(%)
my $MinRxnRadius = 4.0;     # 初始近距离接触截断值(Å)
my $StepRxnRadius = 0.5;    # 近距离接触步长(Å)
my $MaxRxnRadius = 8.0;     # 最终近距离接触截断值(Å)
my $IterationsPerRadius = 3;  # 每个反应半径的迭代次数
my $inter_molecule_ratio = 0.8;  # 分子间交联比例(0-1): 0为全部分子内交联，1为全部分子间交联

# 特殊反应原子名称
my $OHName = "R1";    # 羟基氧原子标记名称
my $NCOName = "R2";   # 异氰酸酯碳原子标记名称

# 模拟设置
my $forcefield = "COMPASSIII";  # 力场
my $Quality = "Medium";         # 计算质量：Coarse/Medium/Fine/Ultra Fine
my $chargeMethod = "Atom based"; # 电荷计算方法：Atom based, Group based 或 Ewald
my $timeStep = 1;               # 动力学时间步长(fs)
my $ensemble = "NPT";           # 系综：NPT或NVT
my $xlinkTemperature = 300;     # 主要模拟温度(K)
my $xlinkPressure = 0.0001;     # 主要模拟压力(GPa)
my $equilibration_time = 10;    # 平衡动力学时间(ps)
my $DynamicsTemp = 300;         # 动力学模拟温度(K)

# 温度循环设置
my $UseTempCycle = TRUE;        # 是否使用温度循环
my $lowTemp = 300;              # 最低温度(K)
my $highTemp = 500;             # 最高温度(K)
my $tempStep = 50;              # 温度步长(K)
my $tempDuration = 20;          # 每个温度的持续时间(ps)

# 分析动力学设置
my $analyzeDuration = 10;       # 采样热力学数据的动力学时间(ps)

###### 结束用户设置 ######

# 记录开始时间
my $starttime = time;
my $logdoc = Documents->New("$outputBaseName-Progress.txt");
$logdoc->Append("交联模拟开始于 ".strftime("%Y-%m-%d %H:%M:%S\n", localtime(time)));
$logdoc->Append("#######################################\n");

# 打开初始结构文件
my $originDoc = $Documents{"$xsdDocName.xsd"};
my $doc = Documents->New("$outputBaseName.xsd");
$doc->CopyFrom($originDoc);

# 初始化Forcite模块
my $Forcite = Modules->Forcite;
$Forcite->ChangeSettings([
    CurrentForcefield => $forcefield,
    Quality => $Quality,
    Temperature => $xlinkTemperature,
    Pressure => $xlinkPressure,
    Thermostat => "Andersen",
    Barostat => "Andersen",
    TimeStep => $timeStep,
    TrajectoryFrequency => 5000,
    AppendTrajectory => "No",
    WriteVelocities => "Yes",
    EnergyDeviation => 500000000,
    WriteLevel => "Silent",
    '3DPeriodicElectrostaticSummationMethod' => $chargeMethod,
    '****************************' => "Atom based"
]);

# 创建统计表
my $statTable = Documents->New($outputBaseName."_statistics.std");
my $nstatsheets = 1;

# 创建中间结构表
my $structureTable = Documents->New($outputBaseName."_structures.std");
$structureTable->ColumnHeading(1) = "反应半径(Å)";
$structureTable->ColumnHeading(2) = "迭代次数";
$structureTable->ColumnHeading(3) = "交联度(%)";

# 初始化计数器
my $rowCounter = 0;
my $xlinkCounter = 0;
my $mdcounter = 0;
my $geomoptcounter = 0;
my $conversion = 0;  # 添加全局交联度变量的声明

# 一次性平衡动力学模拟
$logdoc->Append("\n一次性平衡动力学模拟\n");
my $steps = ($equilibration_time * PICO_TO_FEMTO / $timeStep);
my $results = ForciteDynamics($doc, $steps, "NVT");
$results->Trajectory->Delete;
$results = ForciteDynamics($doc, $steps, $ensemble);
$results->Trajectory->Delete;

# 主交联循环
$logdoc->Append("进入主交联循环\n");
$logdoc->Save;

for (my $RxnRadius=$MinRxnRadius; $RxnRadius<=$MaxRxnRadius; $RxnRadius+=$StepRxnRadius) {
    # 基本名称
    my $xsdNameDist = sprintf("%s_R%.2f", $outputBaseName, $RxnRadius);
    
    # 对每个新半径进行平衡(除第一个)
    if ($RxnRadius > $MinRxnRadius) {
        $logdoc->Append("在新半径进行平衡\n");
        $doc->Name = $xsdNameDist . "_init";
        ForciteGeomOpt($doc, 2000);
        my $steps = ($equilibration_time * PICO_TO_FEMTO / $timeStep);
        my $results = ForciteDynamics($doc, $steps, $ensemble);
        $results->Trajectory->Delete;
    }
    
    # 在每个半径上进行多次迭代
    for (my $iteration=1; $iteration<=$IterationsPerRadius; $iteration++) {
        $logdoc->Append("\n\n##########################################################\n");
        $logdoc->Append("###### 反应半径 $RxnRadius, 迭代 $iteration\n");
        $logdoc->Append("##########################################################\n\n");
        $logdoc->Save;
        
        $doc->Name = $xsdNameDist."_".$iteration;
        
        # 创建新键
        my ($numBonds, $conversion) = createNewXlinks($doc, $RxnRadius);
        
        $logdoc->Append(sprintf "交联数= %d \n交联度= %.01F %%\n", $xlinkCounter, $conversion);
        $logdoc->Save;
        
        # 如果没有新键，跳到下一个半径
        if ($numBonds == 0) {
            $logdoc->Append("未创建新键，增加反应半径\n");
            last;
        }
        
        # 优化和扰动以消除可能产生的长键
        optimizeAndPerturb($doc);
        
        # 保存结构到研究表
        $logdoc->Append("保存中间结构到研究表\n");
        $doc->InsertInto($structureTable);
        $structureTable->Cell($rowCounter,1) = $RxnRadius;
        $structureTable->Cell($rowCounter,2) = $iteration;
        $structureTable->Cell($rowCounter,3) = $conversion;
        
        # 短动力学运行以记录热力学性质
        $logdoc->Append("\n\n运行额外的动力学分析\n");
        my $steps = ($analyzeDuration * PICO_TO_FEMTO / $timeStep);
        my $freq = int($steps/20);
        my $results = ForciteDynamics($doc, $steps, $ensemble, (TrajectoryFrequency => $freq));
        getTrajTempAndPressure($results, $rowCounter, $RxnRadius);
        getEnergies($results, $rowCounter);
        $results->Trajectory->Delete;
        $rowCounter++;
        
        # 保存所有文档
        Documents->SaveAll;
        
        # 如果达到目标交联度，退出循环
        last if ($conversion >= $conversionTarget);
    }
    
    # 如果达到目标交联度，退出循环
    last if ($conversion >= $conversionTarget);
}

# 重命名最终文件
$doc->Name = $outputBaseName."_final";

# 计算键分布
analyzeBonds($doc);

# 创建交联原子集合
XlinkSet($doc);

# 结束信息
$logdoc->Append("\n##############################################################\n\n");
$logdoc->Append("计算完成\n");
$logdoc->Append("系统中有 $xlinkCounter 个交联\n");
$logdoc->Append(sprintf "最终交联度 %.1f%%\n", $conversion);
$logdoc->Append("总几何优化步数: $geomoptcounter\n");
$logdoc->Append("总分子动力学步数: $mdcounter\n");

# 报告总时间
my $time_hr = (time-$starttime)/3600;
$logdoc->Append(sprintf("\n总时间 %.2f 小时\n", $time_hr));
$logdoc->Append("\n##############################################################\n");
$logdoc->Save;
Documents->SaveAll;

##########################################################################################################
#
#       子程序
#
##########################################################################################################

# 计算交联度 - 定义为已反应的R1(羟基)原子所占的百分比
sub calculateConversion {
    my $doc1 = shift;
    my $reactedR1Atoms = 0;
    my $totalR1Atoms = 0;
    
    foreach my $atom (@{$doc1->UnitCell->Atoms}) {
        $reactedR1Atoms++ if ($atom->Name =~ /^$OHName-\d/);
        $totalR1Atoms++ if ($atom->Name =~ /^$OHName/);
    }
    
    my $conversion = 0;
    if ($totalR1Atoms > 0) {
        $conversion = 100.0 * $reactedR1Atoms / $totalR1Atoms;
    }
    
    return $conversion;
}

# 创建反应性原子集合
sub createReactiveAtomSets {
    my $doc = shift;
    
    $logdoc->Append("  创建反应性原子集合\n");
    
    my $R1Counter = 0;
    my $R2Counter = 0;
    
    my @notR1;
    my @notR2;
    
    my $atoms = $doc->UnitCell->Atoms;
    foreach my $atom (@$atoms) {
        if (isReactiveR1($atom)) {
            push (@notR2, $atom);
            $R1Counter++;
        } 
        elsif (isReactiveR2($atom)) {
            push (@notR1, $atom);
            $R2Counter++;
        } 
        else {
            push (@notR1, $atom);
            push (@notR2, $atom);
        }
    }
    
    my $notR1Set = $doc->CreateSet("notR1", \@notR1);
    my $notR2Set = $doc->CreateSet("notR2", \@notR2);
    $notR1Set->IsVisible = 0;
    $notR2Set->IsVisible = 0;
    
    $logdoc->Append("    $R1Counter 个反应性羟基原子\n");
    $logdoc->Append("    $R2Counter 个反应性异氰酸酯原子\n\n");
    $logdoc->Save;
    
    return ($doc, $R1Counter, $R2Counter);
}

# 判断是否为R1(羟基)反应性原子
sub isReactiveR1 {
    my $atom = shift;
    return $atom->Name eq "$OHName";
}

# 判断是否为R2(异氰酸酯)反应性原子
sub isReactiveR2 {
    my $atom = shift;
    return $atom->Name eq "$NCOName";
}

# 创建新交联
sub createNewXlinks {
    my $doc1 = shift;
    my $distance = shift;
    
    my $t0 = time;
    $logdoc->Append("创建新交联\n");
    
    # 更新反应性集合
    ($doc1, my $R1Count, my $R2Count) = createReactiveAtomSets($doc1);
    
    # 计算近距离接触
    Tools->BondCalculation->ChangeSettings([
        DistanceCriterionMode => "Absolute",
        ExclusionMode => "Set", 
        MaxAbsoluteDistance => $distance
    ]);
    my $closeContacts = $doc1->CalculateCloseContacts;
    
    # 只保留R1-R2的接触
    $logdoc->Append(sprintf "找到 %d 个近距离接触\n", $closeContacts->Count) if (DEBUG);
    foreach my $closeContact (@$closeContacts) {
        my $atom1 = $closeContact->Atom1;
        my $atom2 = $closeContact->Atom2;
        
        # 删除除非两个原子都是反应性原子且类型不同
        unless ((isReactiveR1($atom1) && isReactiveR2($atom2)) || 
                (isReactiveR1($atom2) && isReactiveR2($atom1))) {
            $closeContact->Delete;
        }
    }
    $logdoc->Append(sprintf "筛选后: %d 个接触对\n", $closeContacts->Count) if (DEBUG);
    
    # 将近距离接触分为分子间和分子内两组
    my @interMolecularContacts;
    my @intraMolecularContacts;
    
    foreach my $closeContact (@$closeContacts) {
        my $atom1 = $closeContact->Atom1;
        my $atom2 = $closeContact->Atom2;
        
        my $isSameMolecule = FALSE;
        eval {
            my $mol1 = $atom1->Ancestors->Molecule->Name;
            my $mol2 = $atom2->Ancestors->Molecule->Name;
            $isSameMolecule = ($mol1 eq $mol2);
        };
        
        if ($isSameMolecule) {
            push(@intraMolecularContacts, $closeContact);
        } else {
            push(@interMolecularContacts, $closeContact);
        }
    }
    
    # 根据交联比例选择接触对
    my $totalContacts = $closeContacts->Count;
    my $interContactsToUse = int($totalContacts * $inter_molecule_ratio);
    my $intraContactsToUse = $totalContacts - $interContactsToUse;
    
    # 随机打乱数组
    fisher_yates_shuffle(\@interMolecularContacts);
    fisher_yates_shuffle(\@intraMolecularContacts);
    
    # 截取所需数量的接触对
    if (scalar(@interMolecularContacts) > $interContactsToUse) {
        splice(@interMolecularContacts, $interContactsToUse);
    }
    
    if (scalar(@intraMolecularContacts) > $intraContactsToUse) {
        splice(@intraMolecularContacts, $intraContactsToUse);
    }
    
    # 合并选择的接触对
    my @selectedContacts = (@interMolecularContacts, @intraMolecularContacts);
    
    # 转换选择的近距离接触为键
    my $newBondCounter = 0;
    foreach my $closeContact (@selectedContacts) {
        my $atom1 = $closeContact->Atom1;
        my $atom2 = $closeContact->Atom2;
        
        # 确保R1是羟基原子，R2是异氰酸酯原子
        if (isReactiveR2($atom1) && isReactiveR1($atom2)) {
            my $temp = $atom1;
            $atom1 = $atom2;
            $atom2 = $temp;
        }
        
        # 创建新键
        $xlinkCounter++;
        my $newBond = $doc1->CreateBond($atom1, $atom2, "Single", ([Name => "xlink-".$xlinkCounter]));
        
        # 设置原子的显示样式
        $atom1->Style = "Ball and stick";
        $atom2->Style = "Ball and stick";
        
        # 添加交联索引到原子名称
        $atom1->Name .= "-".$xlinkCounter;
        $atom2->Name .= "-".$xlinkCounter;
        
        $newBondCounter++;
        $logdoc->Append(sprintf "    在 %s 和 %s 之间创建键\n", $atom1->Name, $atom2->Name) if (DEBUG);
    }
    
    $logdoc->Append("  创建了 $newBondCounter 个交联键\n\n");
    $logdoc->Save;
    
    # 调整氢原子
    $doc1->AdjustHydrogen;
    
    # 删除近距离接触和集合
    $doc1->UnitCell->CloseContacts->Delete;
    $doc1->UnitCell->Sets->Delete;
    
    # 几何优化
    $logdoc->Append("  ");
    ForciteGeomOpt($doc1, 2000);
    
    # 计算交联度
    my $conversion = calculateConversion($doc1);
    
    # 报告时间
    $logdoc->Append(sprintf "\n创建新交联用时 %d 秒\n\n", time-$t0);
    $logdoc->Save;
    
    return ($newBondCounter, $conversion);
}

# Fisher-Yates 洗牌算法
sub fisher_yates_shuffle {
    my $array = shift;
    for (my $i = @$array - 1; $i > 0; $i--) {
        my $j = int(rand($i + 1));
        next if $j == $i;
        @$array[$i, $j] = @$array[$j, $i];
    }
}

# 优化和扰动
sub optimizeAndPerturb {
    $logdoc->Append("\n优化和扰动\n");
    $logdoc->Save;
    my $t0 = time;
    my $mdStepCounter = 0;
    my ($doc1) = @_;
    
    $logdoc->Append("  ");
    ForciteGeomOpt($doc1, 200);
    
    $logdoc->Append("  ");
    my $results = ForciteDynamics($doc1, 1000, "NVT");
    $mdStepCounter += 1000;
    $results->Trajectory->Delete;
    
    return $doc1 if ($UseTempCycle == FALSE);
    
    # 温度循环
    my $steps = ($tempDuration * PICO_TO_FEMTO / $timeStep);
    
    # 升温
    for (my $temperature = $xlinkTemperature; $temperature <= $highTemp; $temperature += $tempStep) {
        $logdoc->Append("  升温，在 $temperature K 运行\n  ");
        $logdoc->Save;
        TemperatureCycleStep($doc1, $steps, $temperature);
        $mdStepCounter += 2*$steps;
    }
    
    # 降温
    for (my $temperature = $highTemp; $temperature >= $lowTemp; $temperature -= $tempStep) {
        $logdoc->Append("  降温，在 $temperature K 运行\n  ");
        $logdoc->Save;
        TemperatureCycleStep($doc1, $steps, $temperature);
        $mdStepCounter += 2*$steps;
    }
    
    ForciteGeomOpt($doc1, 500);
    if (DEBUG) {
        $logdoc->Append(sprintf "优化和扰动总步数 %d，用时 %d 秒\n", 
            $mdStepCounter, time-$t0);
    }
    $logdoc->Save;
}

# 温度循环步骤
sub TemperatureCycleStep {
    my $doc1 = shift;
    my $steps = shift;
    my $temperature = shift;
    
    my $results = ForciteDynamics($doc1, $steps, "NVT", (Temperature => $temperature));
    $results->Trajectory->Delete;
    
    my $results = ForciteDynamics($doc1, $steps, $ensemble, (
        Temperature => $temperature,
        InitialVelocities => "Current"
    ));
    $results->Trajectory->Delete;
}

# 分析键长分布
sub analyzeBonds {
    my $doc1 = shift;
    
    # 计算所有键的分布
    my $bondAnalysis = $Forcite->Analysis->LengthDistribution($doc1, [
        LengthDistributionUseBonds => "Yes"
    ]);
    $bondAnalysis->LengthDistributionChartAsStudyTable->Delete;
    $bondAnalysis->LengthDistributionChart->Name = "AllBonds";
    
    # 创建交联键的距离集合
    my @distances;
    foreach my $bond (@{$doc1->UnitCell->Bonds}) {
        if ($bond->Name =~ /^xlink/) {
            my $distance = $doc1->CreateDistance([$bond->Atom1, $bond->Atom2]);
            push @distances, $distance;
        }
    }
    return unless (scalar(@distances) > 0);
    $doc1->CreateSet("xlink distances", \@distances);
    
    # 分析交联键距离
    my $bondAnalysis = $Forcite->Analysis->LengthDistribution($doc1, [
        LengthDistributionSetA => "xlink distances"
    ]);
    $bondAnalysis->LengthDistributionChartAsStudyTable->Delete;
    $bondAnalysis->LengthDistributionChart->Name = "XlinkBonds";
    $doc1->UnitCell->Distances->Delete;
}

# 创建交联原子集合
sub XlinkSet {
    my $doc = shift;
    
    my @xlinked_atoms;
    foreach my $bond (@{$doc->UnitCell->Bonds}) {
        if ($bond->Name =~ /^xlink/) {
            push @xlinked_atoms, $bond->Atom1;
            push @xlinked_atoms, $bond->Atom2;
            push @xlinked_atoms, $bond;
        }
    }
    return if (scalar(@xlinked_atoms) == 0);
    
    $logdoc->Append("\n创建交联集合\n");
    $doc->CreateSet("Crosslinks", \@xlinked_atoms, [IsVisible => "No", Style => "None"]);
}

# 几何优化
sub ForciteGeomOpt {
    my $t0 = time;
    my $doc1 = shift;
    my $steps = shift;
    
    my $results;
    eval {
        $results = $Forcite->GeometryOptimization->Run($doc1, [MaxIterations => $steps]);
    };
    
    if ($@) {
        $logdoc->Append("ForciteGeomOpt: 几何优化失败\n");
        $logdoc->Append($@);
    }
    
    if (DEBUG) {
        $logdoc->Append(sprintf "ForciteGeomOpt %d 步，%d 秒\n", $steps, time-$t0);
        $logdoc->Save;
    }
    
    $geomoptcounter += $steps;
    return $results;
}

# 分子动力学模拟
sub ForciteDynamics {
    my $t0 = time;
    my $doc1 = shift;
    my $steps = shift;
    my $ensemble = shift;
    
    my @settings = (
        NumberOfSteps => $steps,
        Ensemble3D => $ensemble,
    );
    
    push @settings, @_;
    
    my $results;
    eval {
        $results = $Forcite->Dynamics->Run($doc1, \@settings);
    };
    
    if ($@) {
        $logdoc->Append("错误: ForciteDynamics 失败\n");
        $logdoc->Append($@);
        die "ForciteDynamics 失败\n";
    }
    
    if (DEBUG) {
        $logdoc->Append(sprintf "ForciteDynamics %d 步，%s 系综，%d 秒\n", 
            $steps, $ensemble, time-$t0);
        $logdoc->Save;
    }
    
    $mdcounter += $steps;
    return $results;
}

# 获取轨迹中的温度和压力
sub getTrajTempAndPressure {
    my $dynamicsResults = shift;
    my $row_counter = shift;
    my $distance = shift;
    
    # Forcite分析
    my $T_analysis = Modules->Forcite->Analysis->Temperature($dynamicsResults->Trajectory, 
        [ComputeProfile => "Yes", ComputeBlockAverages => "No"]);
    my $hasPressure = TRUE;
    my $P_analysis;
    eval {
        $P_analysis = Modules->Forcite->Analysis->Pressure($dynamicsResults->Trajectory,
            [ComputeProfile => "Yes", ComputeBlockAverages => "No"]);
    };
    if ($@) { $hasPressure = FALSE; }
    my $T_std = $T_analysis->TemperatureChartAsStudyTable;
    my $P_std = $P_analysis->PressureChartAsStudyTable if ($hasPressure);
    
    # 将数据从研究表推入perl数组
    my @T = ();
    my @P = ();
    for (my $row = 0; $row < $T_std->RowCount; ++$row) {
        push (@T, $T_std->Cell($row, "Temperature"));
        push (@P, $P_std->Cell($row, "Pressure")) if ($hasPressure);
    }
    
    $T_std->Delete;
    $T_analysis->TemperatureChart->Delete;
    $P_std->Delete if ($hasPressure);
    $P_analysis->PressureChart->Delete if ($hasPressure);
    
    # 计算性质的统计数据
    my ($Tavg, $Tsd) = calculateStatistics(@T);
    my ($Pavg, $Psd) = calculateStatistics(@P) if ($hasPressure);
    
    $logdoc->Append("\n                 平均值      标准差\n");
    $logdoc->Append(sprintf "温度(K)    %11.5g %11.5g\n", $Tavg, $Tsd);
    $logdoc->Append(sprintf "压力(GPa)  %11.5g %11.5g\n", $Pavg, $Psd) if ($hasPressure);
    
    # 写入到研究表
    if ($row_counter == 0) {
        my $sheet = $statTable->InsertSheet($nstatsheets,"Thermo");
        $nstatsheets++;
        $statTable->ColumnHeading(0) = "反应半径(Å)";
        $statTable->ColumnHeading(1) = "平均温度(K)";
        $statTable->ColumnHeading(2) = "标准差";
        $statTable->ColumnHeading(3) = "平均压力(GPa)" if ($hasPressure);
        $statTable->ColumnHeading(4) = "标准差" if ($hasPressure);
    }
    my $sheet = $statTable->Sheets("Thermo");
    $sheet->InsertRow;
    $sheet->Cell($row_counter,0) = $distance;
    $sheet->Cell($row_counter,1) = $Tavg;
    $sheet->Cell($row_counter,2) = $Tsd;
    $sheet->Cell($row_counter,3) = $Pavg if ($hasPressure);
    $sheet->Cell($row_counter,4) = $Psd if ($hasPressure);
}

# 获取能量
sub getEnergies {
    my $doc = shift;
    my $row_counter = shift;
    my $trj = $doc->Trajectory;
    my @bond_energies = ();
    my @angle_energies = ();
    my @potential_energies = ();
    
    for (my $frame_counter = 1; $frame_counter <= $trj->NumFrames; ++$frame_counter) {
        $trj->CurrentFrame = $frame_counter;
        
        # 计算这个快照的能量
        Modules->Forcite->Energy->Run($trj);
        my $bond_energy = $trj->BondEnergy;
        my $angle_energy = $trj->AngleEnergy;
        my $potential_energy = $trj->PotentialEnergy;
        
        # 将值推入适当的数组以进行后续分析
        push (@bond_energies, $bond_energy);
        push (@angle_energies, $angle_energy);
        push (@potential_energies, $potential_energy);
    }
    
    # 计算平均值和标准差
    my ($avg_bond, $sd_bond) = calculateStatistics(@bond_energies);
    my ($avg_angle, $sd_angle) = calculateStatistics(@angle_energies);
    my ($avg_pe, $sd_pe) = calculateStatistics(@potential_energies);
    
    $logdoc->Append(sprintf "键能      %11.5g %11.5g\n", $avg_bond, $sd_bond);
    $logdoc->Append(sprintf "角能      %11.5g %11.5g\n", $avg_angle, $sd_angle);
    $logdoc->Append(sprintf "势能      %11.5g %11.5g\n\n", $avg_pe, $sd_pe);
    
    # 写入研究表
    my $sheet = $statTable->Sheets("Thermo");
    if ($row_counter == 0) {
        $sheet->ColumnHeading(5) = "势能(kcal/mol)";
        $sheet->ColumnHeading(6) = "标准差";
        $sheet->ColumnHeading(7) = "键能(kcal/mol)";
        $sheet->ColumnHeading(8) = "标准差";
        $sheet->ColumnHeading(9) = "角能(kcal/mol)";
        $sheet->ColumnHeading(10) = "标准差";
    }
    
    $sheet->Cell($row_counter,5) = $avg_pe;
    $sheet->Cell($row_counter,6) = $sd_pe;
    $sheet->Cell($row_counter,7) = $avg_bond;
    $sheet->Cell($row_counter,8) = $sd_bond;
    $sheet->Cell($row_counter,9) = $avg_angle;
    $sheet->Cell($row_counter,10) = $sd_angle;
}

# 计算平均值和标准差
sub calculateStatistics {
    my @property = @_;
    
    my $numberValues = @property;
    return (0, 0) if ($numberValues <= 0);
    
    # 计算平均值
    my $mean = 0;
    foreach my $value (@property) {
        $mean += $value;
    }
    $mean /= $numberValues;
    
    # 计算标准差
    my $stdDev = 0;
    foreach my $value (@property) {
        my $diffsq = ($value - $mean)**2;
        $stdDev += $diffsq;
    }
    $stdDev = ($numberValues > 1) ? sqrt($stdDev/($numberValues - 1)) : 0;
    
    return ($mean, $stdDev);
}