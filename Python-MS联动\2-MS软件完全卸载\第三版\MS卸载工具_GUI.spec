# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['D:\\Study\\cursor-dmpb\\MS-perl\\Python-MS联动\\2-MS软件完全卸载\\第三版\\MS_Uninstaller_GUI.py'],
    pathex=[],
    binaries=[],
    datas=[('D:\\Study\\cursor-dmpb\\MS-perl\\Python-MS联动\\2-MS软件完全卸载\\第三版\\resources', 'resources')],
    hiddenimports=['cv2', 'PIL', 'PIL._imagingtk', 'PIL._tkinter_finder', 'tkinter', 'automation_utils'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='MS卸载工具_GUI',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['D:\\Study\\cursor-dmpb\\MS-perl\\Python-MS联动\\2-MS软件完全卸载\\第三版\\resources\\icon.ico'],
)
