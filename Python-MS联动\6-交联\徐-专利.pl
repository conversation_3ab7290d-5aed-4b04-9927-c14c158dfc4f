#!perl  # Perl脚本的解释器路径

use strict;  # 启用严格模式，要求变量声明等严格语法检查
use Getopt::Long;  # 导入命令行参数处理模块
use MaterialsScript qw(:all);  # 导入Materials Studio脚本接口，导入所有方法
use POSIX qw(strftime);  # 导入POSIX模块中的时间格式化函数
use List::Util qw(shuffle); 	# 导入随机打乱数组函数，用于随机化近接原子对

# Title: Tri-,Four-merization between R-CN molecules.  # 标题：R-CN分子间的三聚、四聚化反应
# Author: Jingcheng Xu  # 作者：徐敬成
# Date: 2023.9  # 日期：2023年9月

# This script is designed to represent the tri-,four-merization of R-CN molecules, and form the cross-link network.  # 本脚本用于模拟R-CN分子的三聚、四聚化反应，形成交联网络
# Close contacts are calculated between the reactive atoms, bonds are created if RC and RN atoms from different molecule within suitable distance.  # 计算反应性原子之间的近距离接触，如果不同分子的RC和RN原子在合适距离内则创建化学键
# After this, using dynamics to optimize and perturb the system.  # 之后，使用分子动力学优化和扰动系统
###### Begin editable settings ######  # 开始可编辑设置
# General settings  # 通用设置
my $xsdDoc = "model-PN75-100";  # 设置输入文件的基本名称

my $MinReactRadius = 3;			# Initial close contacts cutoff  # 初始近距离接触截断值(Å)
my $StepReactRadius = 1;		# Close contact step size  # 近距离接触步长增量(Å)
my $MaxReactRadius = 5;			# Final close contacts cutoff - hopefully conversion will be reached before this value is reached  # 最终近距离接触截断值(Å) - 希望在达到此值前完成转化
my $IterationsPerRadius = 3;	# Number of iterations per Reactive Radius  # 每个反应半径的迭代次数
my $TimesForIterCounter = 15;   # 迭代计数器的最大次数
my $ReactRatio = 100;			# ratio of react C-N pairs in all reactive pairs  # 所有反应对中C-N对的反应比例(%)
my $inter_in_all = 0.75;			# 0:all intra-molecule links; 1: all inter-molecule links; (0,1): mixture of inter-, intra-molecule links  # 0:全部分子内链接; 1:全部分子间链接; (0,1):混合型链接的比例
						
# Simulation settings  # 模拟设置
my $forcefield 	= "COMPASS";	# Forcefield document  # 力场设置
my $Quality		= "Coarse";		# Coarse/Medium/Fine/Ultra Fine  # 计算质量：粗糙/中等/精细/超精细
my $nonBondMethod = "Ewald";	# Set the non-bond method - applies to both VdW and Coulomb  # 设置非键方法 - 适用于范德华力和库仑力
my $ifMD = "No";			# Yes or No, whether carry out MD  # 是否进行分子动力学模拟
my $ifOPT = "No";          # 是否进行几何优化
my $timeStep = 1;			# Dynamics time step in fs  # 动力学时间步长，单位为飞秒
my $DynamicsTemp = 300;		# Temperature for the dynamics equilibration  # 动力学平衡的温度(K)
my $onetimeMDTime = 10;		# Time for the one-time dynamics (ps)  # 一次性动力学模拟的时间(皮秒)
my $DynamicsTime = 100;		# Time for the equilibration dynamics (ps)  # 平衡动力学模拟的时间(皮秒)

###### End editable settings ######  # 结束可编辑设置
if (($inter_in_all<0) or ($inter_in_all>1)) {die("Error \$inter_in_all: this is ratio of inter-molecule links in all reactions, should in [0,1].");}  # 检查分子间链接比例参数是否在[0,1]范围内
#my $totalCNbonds = 0;
my $starttime = time;		# Record the start time  # 记录开始时间
my $xsdName = "$xsdDoc-xlink";  # 设置输出文件的基本名称
my $logdoc = Documents->New("$xsdName-Progress.txt");		# log file  # 创建日志文件
$logdoc->Append("Start at ".strftime("%Y-%m-%d %H:%M:%S\n", localtime(time)));  # 记录开始时间
$logdoc->Append("#######################################\n");  # 添加分隔线

# Make a copy of the first document to preserve the starting structure  # 复制原始文档以保留起始结构
my $originDoc = $Documents{"$xsdDoc.xsd"};  # 获取原始文档
my $doc = Documents->New("$xsdName.xsd");  # 创建新文档
$doc->CopyFrom($originDoc);  # 从原始文档复制到新文档
$originDoc->Close;  # 关闭原始文档
setAtomLineStyle($doc);  # 设置原子显示样式为线型
#$doc->Save;

my $interRCRN_patName = "InterRCRN";  # 分子间RC-RN模式名称
my $ring_patName = "CNRing";  # CN环模式名称
my $intraRCRN_patName = "IntraRCRN";  # 分子内RC-RN模式名称
my $dimerRCRN_patName = "DimerRCRN";  # 二聚体RC-RN模式名称
my $trimerRCRN_patName = "TrimerRCRN";  # 三聚体RC-RN模式名称
my $polyidlRCRN_patName = "PolyidlRCRN";  # 多聚体RC-RN模式名称
my $Pc_patName = "Pc";  # 酞菁模式名称
my $PcH2_patName = "PcH2-for-Highlight";  # 二氢酞菁高亮显示模式名称

# Initialises Forcite with the basic Dynamics settings  # 初始化Forcite模块，设置基本动力学参数
my $Forcite = Modules->Forcite; 
$Forcite->ChangeSettings([ 	CurrentForcefield => $forcefield,  # 设置当前使用的力场
							Quality => $Quality,  # 设置计算质量
							'****************************' => $nonBondMethod,  # 设置3D周期性范德华力求和方法
							TimeStep => $timeStep,  # 设置时间步长
							TrajectoryFrequency => 1000,  # 设置轨迹记录频率
							WriteLevel => "Silent",  # 设置输出级别为静默
							EnergyDeviation => 1e15  # 设置能量偏差容忍度
						 ]);
#my $TotalCNbonds = CountCNtribondsNum($doc);

### Initialize the atom sets  # 初始化原子集合
my %RCatoms;  # 反应性碳原子哈希表
my %RNatoms;  # 反应性氮原子哈希表
my %CNbonds;  # C-N键哈希表
my %interRCatoms;  # 分子间反应性碳原子哈希表
my %interRNatoms;  # 分子间反应性氮原子哈希表
my %intraRCatoms;  # 分子内反应性碳原子哈希表
my %intraRNatoms;  # 分子内反应性氮原子哈希表
my %dimerRCatoms;  # 二聚体反应性碳原子哈希表
my %dimerRNatoms;  # 二聚体反应性氮原子哈希表
my %trimerRCatoms;  # 三聚体反应性碳原子哈希表
my %trimerRNatoms;  # 三聚体反应性氮原子哈希表
my %polyidlRCatoms;  # 多聚体反应性碳原子哈希表
my %polyidlRNatoms;  # 多聚体反应性氮原子哈希表

# One-time equilibration dynamics  # 一次性平衡动力学模拟
onetimeMD($ifMD,$doc,$timeStep,$onetimeMDTime);  # 调用一次性MD函数

for (my $ReactRadius=$MinReactRadius;$ReactRadius<=$MaxReactRadius;$ReactRadius+=$StepReactRadius) {  # 外层循环：从最小反应半径开始，以步长增加到最大反应半径
for (my $iteration=1;$iteration<=$IterationsPerRadius;$iteration+=1) {  # 内层循环：对每个反应半径进行多次迭代
#for (my $ReactRadius=4;$ReactRadius<=4;$ReactRadius+=$StepReactRadius) {
#for (my $iteration=1;$iteration<=1;$iteration+=1) {
	my $newname = $doc->Name."-$ReactRadius-$iteration";  # 创建当前迭代的新文件名
	$logdoc->Append("\n###### Radius $ReactRadius, Iteration $iteration ######\n");  # 记录当前半径和迭代次数
	ClearHash();  # 清空所有哈希表
	if ($inter_in_all == 0) {  # 如果设置为全部分子内链接
		Find_indexHash_RCRN($doc);  # 查找所有RC-RN原子对
		Find_indexHash_intraRCRN($doc, $intraRCRN_patName);  # 查找分子内RC-RN原子对
		Find_indexHash_dimerRCRN($doc, $dimerRCRN_patName);  # 查找二聚体RC-RN原子对
		Find_indexHash_trimerRCRN($doc, $trimerRCRN_patName);  # 查找三聚体RC-RN原子对
		Find_indexHash_polyidlRCRN($doc, $polyidlRCRN_patName);  # 查找多聚体RC-RN原子对
		my $polyidlRCRN_Num = scalar(keys %polyidlRNatoms);  # 计算多聚体RN原子数量
		my $trimerRCRN_Num = scalar(keys %trimerRNatoms);  # 计算三聚体RN原子数量
		my $dimerRCRN_Num = scalar(keys %dimerRNatoms);  # 计算二聚体RN原子数量
		my $intraRCRN_Num = scalar(keys %intraRNatoms);  # 计算分子内RN原子数量
		my $RCRN_Num = scalar(keys %RNatoms);  # 计算所有RN原子数量
		if ($polyidlRCRN_Num>0) {  # 如果存在多聚体原子
			$logdoc->Append("Create_Pc_link: ");  # 记录创建酞菁链接
			Create_Pc_link($doc,$ReactRatio,$ReactRadius);  # 创建酞菁链接
		}
		my $polyidlIterCounter = 1;  # 多聚体迭代计数器
		while (($trimerRCRN_Num>0) and ($polyidlIterCounter<=$TimesForIterCounter)) {  # 当存在三聚体原子且未超过迭代次数限制时循环
			$logdoc->Append("Create_polyidl_link No.$polyidlIterCounter: ");  # 记录创建多聚体链接及次数
			Create_polyidl_link($doc,$ReactRatio,$ReactRadius);  # 创建多聚体链接
			$polyidlIterCounter += 1;  # 迭代计数器加1
			$trimerRCRN_Num = scalar(keys %trimerRNatoms);  # 更新三聚体RN原子数量
		}	
		my $trimerIterCounter = 1;  # 三聚体迭代计数器
		while (($dimerRCRN_Num>0) and ($trimerIterCounter<=$TimesForIterCounter)) {  # 当存在二聚体原子且未超过迭代次数限制时循环
			$logdoc->Append("Create_trimer_link No.$trimerIterCounter: ");  # 记录创建三聚体链接及次数
			Create_trimer_link($doc,$ReactRatio,$ReactRadius);  # 创建三聚体链接
			$trimerIterCounter += 1;  # 迭代计数器加1
			$dimerRCRN_Num = scalar(keys %dimerRNatoms);  # 更新二聚体RN原子数量
		}
		my $dimerIterCounter = 1;  # 二聚体迭代计数器
		while (($intraRCRN_Num>0) and ($dimerIterCounter<=$TimesForIterCounter)) {  # 当存在分子内原子且未超过迭代次数限制时循环
			$logdoc->Append("Create_dimer_link No.$dimerIterCounter: ");  # 记录创建二聚体链接及次数
			Create_dimer_link($doc,$ReactRatio,$ReactRadius);  # 创建二聚体链接
			$dimerIterCounter += 1;  # 迭代计数器加1
			$intraRCRN_Num = scalar(keys %intraRNatoms);  # 更新分子内RN原子数量
		}
		if ($RCRN_Num>0) {  # 如果仍存在RN原子
			my $ReactiveNum_intra = int(scalar(keys %RNatoms)*(1-$inter_in_all)/2);  # 计算分子内反应数量
			#print("$ReactiveNum_intra\n");
			Create_intraMolecule_link($doc,$ReactRatio,$ReactRadius,$ReactiveNum_intra);  # 创建分子内链接
		}
	} elsif ($inter_in_all == 1) {  # 如果设置为全部分子间链接
		Find_indexHash_RCRN($doc);  # 查找所有RC-RN原子对
		Find_indexHash_interRCRN($doc, $interRCRN_patName);  # 查找分子间RC-RN原子对
		my $interRCRN_Num = scalar(keys %interRNatoms);  # 计算分子间RN原子数量
		my $RCRN_Num = scalar(keys %RNatoms);  # 计算所有RN原子数量
		my $ringlinkcounter = 1;  # 环链接迭代计数器
		while (($interRCRN_Num>0) and ($ringlinkcounter<=$TimesForIterCounter)) {  # 当存在分子间原子且未超过迭代次数限制时循环
			$logdoc->Append("Create_ring_link No.$ringlinkcounter: ");  # 记录创建环链接及次数
			Create_ring_link($doc,$ReactRatio,$ReactRadius);  # 创建环链接
			$ringlinkcounter += 1;  # 迭代计数器加1
			$interRCRN_Num = scalar(keys %interRNatoms);  # 更新分子间RN原子数量
		}
		if ($RCRN_Num>0) {  # 如果仍存在RN原子
			my $ReactiveNum_inter = int(scalar(keys %RNatoms)*$inter_in_all/2);  # 计算分子间反应数量
			#print("$ReactiveNum_inter,$ReactiveNum_intra\n");
			Create_interMolecule_link($doc,$ReactRatio,$ReactRadius,$ReactiveNum_inter);  # 创建分子间链接
		}
	} else {  # 如果设置为混合型链接
		Find_indexHash_RCRN($doc);  # 查找所有RC-RN原子对
		Find_indexHash_interRCRN($doc, $interRCRN_patName);  # 查找分子间RC-RN原子对
		Find_indexHash_intraRCRN($doc, $intraRCRN_patName);  # 查找分子内RC-RN原子对
		Find_indexHash_dimerRCRN($doc, $dimerRCRN_patName);  # 查找二聚体RC-RN原子对
		Find_indexHash_trimerRCRN($doc, $trimerRCRN_patName);  # 查找三聚体RC-RN原子对
		Find_indexHash_polyidlRCRN($doc, $polyidlRCRN_patName);  # 查找多聚体RC-RN原子对
		my $interRCRN_Num = scalar(keys %interRNatoms);  # 计算分子间RN原子数量
		my $polyidlRCRN_Num = scalar(keys %polyidlRNatoms);  # 计算多聚体RN原子数量
		my $trimerRCRN_Num = scalar(keys %trimerRNatoms);  # 计算三聚体RN原子数量
		my $dimerRCRN_Num = scalar(keys %dimerRNatoms);  # 计算二聚体RN原子数量
		my $intraRCRN_Num = scalar(keys %intraRNatoms);  # 计算分子内RN原子数量
		my $RCRN_Num = scalar(keys %RNatoms);  # 计算所有RN原子数量
		if ($polyidlRCRN_Num>0) {  # 如果存在多聚体原子
			$logdoc->Append("Create_Pc_link: ");  # 记录创建酞菁链接
			Create_Pc_link($doc,$ReactRatio,$ReactRadius);  # 创建酞菁链接
		}
		my $polyidlIterCounter = 1;  # 多聚体迭代计数器
		while (($trimerRCRN_Num>0) and ($polyidlIterCounter<=$TimesForIterCounter)) {  # 当存在三聚体原子且未超过迭代次数限制时循环
			$logdoc->Append("Create_polyidl_link No.$polyidlIterCounter: ");  # 记录创建多聚体链接及次数
			Create_polyidl_link($doc,$ReactRatio,$ReactRadius);  # 创建多聚体链接
			$polyidlIterCounter += 1;  # 迭代计数器加1
			$trimerRCRN_Num = scalar(keys %trimerRNatoms);  # 更新三聚体RN原子数量
		}	
		my $trimerIterCounter = 1;  # 三聚体迭代计数器
		while (($dimerRCRN_Num>0) and ($trimerIterCounter<=$TimesForIterCounter)) {  # 当存在二聚体原子且未超过迭代次数限制时循环
			$logdoc->Append("Create_trimer_link No.$trimerIterCounter: ");  # 记录创建三聚体链接及次数
			Create_trimer_link($doc,$ReactRatio,$ReactRadius);  # 创建三聚体链接
			$trimerIterCounter += 1;  # 迭代计数器加1
			$dimerRCRN_Num = scalar(keys %dimerRNatoms);  # 更新二聚体RN原子数量
		}
		my $dimerIterCounter = 1;  # 二聚体迭代计数器
		while (($intraRCRN_Num>0) and ($dimerIterCounter<=$TimesForIterCounter)) {  # 当存在分子内原子且未超过迭代次数限制时循环
			$logdoc->Append("Create_dimer_link No.$dimerIterCounter: ");  # 记录创建二聚体链接及次数
			Create_dimer_link($doc,$ReactRatio,$ReactRadius);  # 创建二聚体链接
			$dimerIterCounter += 1;  # 迭代计数器加1
			$intraRCRN_Num = scalar(keys %intraRNatoms);  # 更新分子内RN原子数量
		}
		my $ringlinkcounter = 1;  # 环链接迭代计数器
		while (($interRCRN_Num>0) and ($ringlinkcounter<=$TimesForIterCounter)) {  # 当存在分子间原子且未超过迭代次数限制时循环
			$logdoc->Append("Create_ring_link No.$ringlinkcounter: ");  # 记录创建环链接及次数
			Create_ring_link($doc,$ReactRatio,$ReactRadius);  # 创建环链接
			$ringlinkcounter += 1;  # 迭代计数器加1
			$interRCRN_Num = scalar(keys %interRNatoms);  # 更新分子间RN原子数量
		}
		if ($RCRN_Num>0) {  # 如果仍存在RN原子
			my $ReactiveNum_inter = int(scalar(keys %RNatoms)*$inter_in_all/2);  # 计算分子间反应数量
			my $ReactiveNum_intra = int(scalar(keys %RNatoms)*(1-$inter_in_all)/2);  # 计算分子内反应数量
			#print("$ReactiveNum_inter,$ReactiveNum_intra\n");
			Create_interMolecule_link($doc,$ReactRatio,$ReactRadius,$ReactiveNum_inter);  # 创建分子间链接
			Create_intraMolecule_link($doc,$ReactRatio,$ReactRadius,$ReactiveNum_intra);  # 创建分子内链接
		}
	}
	onlyOptimize($ifOPT, $doc);  # 如果需要，进行几何优化
	equilibrationMD($ifOPT, $ifMD,$doc,$timeStep,$DynamicsTime);  # 如果需要，进行平衡分子动力学模拟
	$doc->SaveAs("$newname.xsd");  # 保存当前迭代的模型
	xlinkStatistics($doc, $interRCRN_patName, $ring_patName, $intraRCRN_patName, $dimerRCRN_patName, $trimerRCRN_patName, $polyidlRCRN_patName, $Pc_patName);  # 统计交联情况
	HighlightDisplay($doc,$PcH2_patName);  # 高亮显示特定结构
	$logdoc->Save;  # 保存日志文件
	}
}
my $finalname = $doc->Name."-final";  # 创建最终文件名
$doc->SaveAs("$finalname.xsd");  # 保存最终模型
my $endtime = time;		# Record the start time  # 记录结束时间
$logdoc->Append("\n#######################################\n");  # 添加分隔线
$logdoc->Append("End at ".strftime("%Y-%m-%d %H:%M:%S\n", localtime(time)));  # 记录结束时间
FormatTimeOut($endtime-$starttime);  # 格式化并输出总运行时间
$logdoc->Save;  # 保存日志文件
$doc->Delete;  # 删除文档对象

##################################
###### Sub-programs ######  # 子程序部分
sub setAtomLineStyle {  # 设置原子线型显示样式的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $atoms = $doc->UnitCell->Atoms;  # 获取文档中的所有原子
	foreach my $atom (@$atoms) {  # 遍历每个原子
		$atom->Style = "Line";  # 将原子样式设置为"线型"
	}
}

#sub CountCNtribondsNum {  # 计算C-N三键数量的子程序(已注释掉)
#	my $doc = shift;  # 获取传入的文档对象
#	my $bonds = $doc->UnitCell->Bonds;  # 获取文档中的所有键
#	my $count = 0;  # 初始化计数器
#	foreach my $bond (@$bonds) {  # 遍历每个键
#		if ($bond->BondType eq "Triple") {  # 如果是三键
#			if ((($bond->Atom1->ElementSymbol eq "C") and ($bond->Atom2->ElementSymbol eq "N")) or (($bond->Atom1->ElementSymbol eq "N") and ($bond->Atom2->ElementSymbol eq "C"))) {  # 如果是C-N键
#				$count += 1;  # 计数器加1
#			}
#		}
#	}
#	return $count;  # 返回总数
#}

sub ClearHash {  # 清空所有哈希表的子程序
	%RCatoms = ();  # 清空RC原子哈希表
	%RNatoms = ();  # 清空RN原子哈希表
	%CNbonds = ();  # 清空CN键哈希表
	%interRCatoms = ();  # 清空分子间RC原子哈希表
	%interRNatoms = ();  # 清空分子间RN原子哈希表
	%intraRCatoms = ();  # 清空分子内RC原子哈希表
	%intraRNatoms = ();  # 清空分子内RN原子哈希表
	%dimerRCatoms = ();  # 清空二聚体RC原子哈希表
	%dimerRNatoms = ();  # 清空二聚体RN原子哈希表
	%trimerRCatoms = ();  # 清空三聚体RC原子哈希表
	%trimerRNatoms = ();  # 清空三聚体RN原子哈希表
	%polyidlRCatoms = ();  # 清空多聚体RC原子哈希表
	%polyidlRNatoms = ();  # 清空多聚体RN原子哈希表
}

sub Find_indexHash_RCRN {  # 查找所有RC-RN原子对并建立索引哈希表
	my $doc = shift;  # 获取传入的文档对象
	my $bonds = $doc->UnitCell->Bonds;  # 获取文档中的所有键
	my $index = 0;  # 初始化索引计数器
	my $ringindex = 0;  # 初始化环索引计数器
	foreach my $bond (@$bonds) {  # 遍历每个键
		if ($bond->BondType eq "Triple") {  # 如果是三键
			my $RCatom = $bond->Atom1;  # 默认第一个原子为RC
			my $RNatom = $bond->Atom2;  # 默认第二个原子为RN
			if (($bond->Atom1->ElementSymbol eq "N") and ($bond->Atom2->ElementSymbol eq "C")) {  # 如果第一个是N，第二个是C
				$RCatom = $bond->Atom2;  # 交换，使RC为C原子
				$RNatom = $bond->Atom1;  # 交换，使RN为N原子
			}
			$RCatom->Name = "RC-$index";  # 命名RC原子
			$RNatom->Name = "RN-$index";  # 命名RN原子
			$RCatoms{$index} = $RCatom;
			$RNatoms{$index} = $RNatom;
			$CNbonds{$index} = $bond;
			#$RCatom->Style = "Ball and stick";
			#$RNatom->Style = "Ball and stick";
			$index += 1;
			my $RCattachedAtoms = $RCatom->AttachedAtoms;
			foreach my $Cat (@$RCattachedAtoms) {
				if (($Cat->ElementSymbol eq "C") and ($Cat->Name !~ "RingC-")){
						$Cat->Name = "RingC-$ringindex";
						my $CatAttachedatoms = $Cat->AttachedAtoms; 
						foreach my $at (@$CatAttachedatoms) {if ($at->Name !~ "RC-") {$at->Name = "RingC-$ringindex";}}
						$ringindex += 1;
				}
			}
		}
	}
	my @keyRCatoms = keys %RCatoms;
	my @keyRNatoms = keys %RNatoms;
	if (scalar(@keyRCatoms) != scalar(@keyRNatoms)) {$logdoc->Append("WARNING: Find_indexHash_RCRN, the number of RC and RN atoms are NOT equal. Check the structures.\n");} 
	$logdoc->Append("Find_indexHash_RCRN: there are ".scalar(@keyRCatoms)." RC atoms, and ".scalar(@keyRNatoms)." RN atoms\n");
	#$RCatoms{100}->Style = "Ball and stick";
	#$RNatoms{100}->Style = "Ball and stick";
}

sub Find_indexHash_interRCRN {
	my $doc = shift;
	my $interRCRN_patName = shift;
	my $interRCRN_pat = $Documents{"$interRCRN_patName.xsd"};
	my $index = 0;
	my $atoms = $doc->UnitCell->Atoms;
	my $matches = $doc->FindPatterns($interRCRN_pat);
	foreach my $match (@$matches) {
		my $mtatoms = $match->Items;
		foreach my $atom (@$mtatoms) {
			if ($atom->ElementSymbol eq "N") {
				my $Nattachedatoms = $atom->AttachedAtoms;
				foreach my $at (@$Nattachedatoms) {
					if ($at->ElementSymbol eq "H") {
						$atom->Name = "interRN-$index";
						$interRNatoms{$index} = $atom;
						#$atom->Style = "Ball and stick";
					}
				}
			}
			if ($atom->ElementSymbol eq "C") {
				my $Cattachedatoms = $atom->AttachedAtoms;
				foreach my $at (@$Cattachedatoms) {
					if ($at->ElementSymbol eq "H") {
						$atom->Name = "interRC-$index";
						$interRCatoms{$index} = $atom;
						#$atom->Style = "Ball and stick";
					}
				}
			}
		}
		$index += 1;		
	}
	$matches->Delete;
	$interRCRN_pat->Close;
	my @keyinterRCatoms = keys %interRCatoms;
	my @keyinterRNatoms = keys %interRNatoms;
	if (scalar(@keyinterRCatoms) != scalar(@keyinterRNatoms)) {$logdoc->Append("WARNING: Find_indexHash_interRCRN, the number of interRC and interRN atoms are NOT equal. Check the structures.\n");} 
	$logdoc->Append("Find_indexHash_interRCRN: there are ".scalar(@keyinterRCatoms)." interRC atoms, and ".scalar(@keyinterRNatoms)." interRN atoms\n");
	#$interRCatoms{1}->Style = "Ball and stick";
	#$interRNatoms{1}->Style = "Ball and stick";
}

sub Find_indexHash_intraRCRN {  # 查找分子内RC-RN原子对并建立索引哈希表
	my $doc = shift;  # 获取传入的文档对象
	my $intraRCRN_patName = shift;  # 获取传入的模式名称
	my $intraRCRN_pat = $Documents{"$intraRCRN_patName.xsd"};  # 打开相应的模式文档
	my $index = 0;  # 初始化索引计数器
	my $atoms = $doc->UnitCell->Atoms;  # 获取文档中的所有原子
	my $matches = $doc->FindPatterns($intraRCRN_pat);  # 查找匹配指定模式的所有结构
	foreach my $match (@$matches) {  # 遍历每个匹配的结构
		my $mtatoms = $match->Items;  # 获取匹配结构中的所有原子
		foreach my $atom (@$mtatoms) {  # 遍历匹配结构中的每个原子
			if ($atom->ElementSymbol eq "N") {  # 如果是N原子
				my $Nattachedatoms = $atom->AttachedAtoms;  # 获取N原子连接的所有原子
				foreach my $at (@$Nattachedatoms) {  # 遍历N原子连接的每个原子
					if ($at->ElementSymbol eq "H") {  # 如果连接的是H原子(识别可能参与反应的N原子)
						$atom->Name = "intraRN-$index";  # 命名为分子内RN原子
						$intraRNatoms{$index} = $atom;  # 存储到分子内RN原子哈希表
						#$atom->Style = "Ball and stick";  # 设置样式(已注释)
					}
				}
			}
			if ($atom->ElementSymbol eq "C") {  # 如果是C原子
				my $Cattachedatoms = $atom->AttachedAtoms;  # 获取C原子连接的所有原子
				foreach my $at (@$Cattachedatoms) {  # 遍历C原子连接的每个原子
					if ($at->ElementSymbol eq "H") {  # 如果连接的是H原子(识别可能参与反应的C原子)
						$atom->Name = "intraRC-$index";  # 命名为分子内RC原子
						$intraRCatoms{$index} = $atom;  # 存储到分子内RC原子哈希表
						#$atom->Style = "Ball and stick";  # 设置样式(已注释)
					}
				}
			}
		}
		$index += 1;  # 索引计数器加1
	}
	$matches->Delete;  # 删除匹配结果集
	$intraRCRN_pat->Close;  # 关闭模式文档
	my @keyintraRCatoms = keys %intraRCatoms;  # 获取分子内RC原子哈希表的所有键
	my @keyintraRNatoms = keys %intraRNatoms;  # 获取分子内RN原子哈希表的所有键
	if (scalar(@keyintraRCatoms) != scalar(@keyintraRNatoms)) {$logdoc->Append("WARNING: Find_indexHash_intraRCRN, the number of intraRC and intraRN atoms are NOT equal. Check the structures.\n");}  # 检查分子内RC和RN原子数量是否相等
	$logdoc->Append("Find_indexHash_intraRCRN: there are ".scalar(@keyintraRCatoms)." intraRC atoms, and ".scalar(@keyintraRNatoms)." intraRN atoms\n");  # 记录找到的分子内RC和RN原子数量
	#$intraRCatoms{1}->Style = "Ball and stick";  # 设置特定分子内RC原子样式(已注释)
	#$intraRNatoms{1}->Style = "Ball and stick";  # 设置特定分子内RN原子样式(已注释)
}

sub Find_indexHash_dimerRCRN {  # 查找二聚体RC-RN原子对并建立索引哈希表
	my $doc = shift;  # 获取传入的文档对象
	my $dimerRCRN_patName = shift;  # 获取传入的模式名称
	my $dimerRCRN_pat = $Documents{"$dimerRCRN_patName.xsd"};  # 打开相应的模式文档
	my $index = 0;  # 初始化索引计数器
	my $atoms = $doc->UnitCell->Atoms;  # 获取文档中的所有原子
	my $matches = $doc->FindPatterns($dimerRCRN_pat);  # 查找匹配指定模式的所有结构
	foreach my $match (@$matches) {  # 遍历每个匹配的结构
		my $mtatoms = $match->Items;  # 获取匹配结构中的所有原子
		foreach my $atom (@$mtatoms) {  # 遍历匹配结构中的每个原子
			if ($atom->ElementSymbol eq "N") {  # 如果是N原子
				my $Nattachedatoms = $atom->AttachedAtoms;  # 获取N原子连接的所有原子
				foreach my $at (@$Nattachedatoms) {  # 遍历N原子连接的每个原子
					if ($at->ElementSymbol eq "H") {  # 如果连接的是H原子(识别可能参与反应的N原子)
						$atom->Name = "dimerRN-$index";  # 命名为二聚体RN原子
						$dimerRNatoms{$index} = $atom;  # 存储到二聚体RN原子哈希表
						#$atom->Style = "Ball and stick";  # 设置样式(已注释)
					}
				}
			}
			if ($atom->ElementSymbol eq "C") {  # 如果是C原子
				my $Cattachedatoms = $atom->AttachedAtoms;  # 获取C原子连接的所有原子
				foreach my $at (@$Cattachedatoms) {  # 遍历C原子连接的每个原子
					if ($at->ElementSymbol eq "H") {  # 如果连接的是H原子(识别可能参与反应的C原子)
						$atom->Name = "dimerRC-$index";  # 命名为二聚体RC原子
						$dimerRCatoms{$index} = $atom;  # 存储到二聚体RC原子哈希表
						#$atom->Style = "Ball and stick";  # 设置样式(已注释)
					}
				}
			}
		}
		$index += 1;  # 索引计数器加1
	}
	$matches->Delete;  # 删除匹配结果集
	$dimerRCRN_pat->Close;  # 关闭模式文档
	my @keydimerRCatoms = keys %dimerRCatoms;  # 获取二聚体RC原子哈希表的所有键
	my @keydimerRNatoms = keys %dimerRNatoms;  # 获取二聚体RN原子哈希表的所有键
	if (scalar(@keydimerRCatoms) != scalar(@keydimerRNatoms)) {$logdoc->Append("WARNING: Find_indexHash_dimerRCRN, the number of dimerRC and dimerRN atoms are NOT equal. Check the structures.\n");}  # 检查二聚体RC和RN原子数量是否相等
	$logdoc->Append("Find_indexHash_dimerRCRN: there are ".scalar(@keydimerRCatoms)." dimerRC atoms, and ".scalar(@keydimerRNatoms)." dimerRN atoms\n");  # 记录找到的二聚体RC和RN原子数量
	#$dimerRCatoms{1}->Style = "Ball and stick";  # 设置特定二聚体RC原子样式(已注释)
	#$dimerRNatoms{1}->Style = "Ball and stick";  # 设置特定二聚体RN原子样式(已注释)
}

sub Find_indexHash_trimerRCRN {  # 查找三聚体RC-RN原子对并建立索引哈希表
	my $doc = shift;  # 获取传入的文档对象
	my $trimerRCRN_patName = shift;  # 获取传入的模式名称
	my $trimerRCRN_pat = $Documents{"$trimerRCRN_patName.xsd"};  # 打开相应的模式文档
	my $index = 0;  # 初始化索引计数器
	my $atoms = $doc->UnitCell->Atoms;  # 获取文档中的所有原子
	my $matches = $doc->FindPatterns($trimerRCRN_pat);  # 查找匹配指定模式的所有结构
	foreach my $match (@$matches) {  # 遍历每个匹配的结构
		my $mtatoms = $match->Items;  # 获取匹配结构中的所有原子
		foreach my $atom (@$mtatoms) {  # 遍历匹配结构中的每个原子
			if ($atom->ElementSymbol eq "N") {  # 如果是N原子
				my $Nattachedatoms = $atom->AttachedAtoms;  # 获取N原子连接的所有原子
				foreach my $at (@$Nattachedatoms) {  # 遍历N原子连接的每个原子
					if ($at->ElementSymbol eq "H") {  # 如果连接的是H原子(识别可能参与反应的N原子)
						$atom->Name = "trimerRN-$index";  # 命名为三聚体RN原子
						$trimerRNatoms{$index} = $atom;  # 存储到三聚体RN原子哈希表
						#$atom->Style = "Ball and stick";  # 设置样式(已注释)
					}
				}
			}
			if ($atom->ElementSymbol eq "C") {  # 如果是C原子
				my $Cattachedatoms = $atom->AttachedAtoms;  # 获取C原子连接的所有原子
				foreach my $at (@$Cattachedatoms) {  # 遍历C原子连接的每个原子
					if ($at->ElementSymbol eq "H") {  # 如果连接的是H原子(识别可能参与反应的C原子)
						$atom->Name = "trimerRC-$index";  # 命名为三聚体RC原子
						$trimerRCatoms{$index} = $atom;  # 存储到三聚体RC原子哈希表
						#$atom->Style = "Ball and stick";  # 设置样式(已注释)
					}
				}
			}
		}
		$index += 1;  # 索引计数器加1
	}
	$matches->Delete;  # 删除匹配结果集
	$trimerRCRN_pat->Close;  # 关闭模式文档
	my @keytrimerRCatoms = keys %trimerRCatoms;  # 获取三聚体RC原子哈希表的所有键
	my @keytrimerRNatoms = keys %trimerRNatoms;  # 获取三聚体RN原子哈希表的所有键
	if (scalar(@keytrimerRCatoms) != scalar(@keytrimerRNatoms)) {$logdoc->Append("WARNING: Find_indexHash_trimerRCRN, the number of trimerRC and trimerRN atoms are NOT equal. Check the structures.\n");}  # 检查三聚体RC和RN原子数量是否相等
	$logdoc->Append("Find_indexHash_trimerRCRN: there are ".scalar(@keytrimerRCatoms)." trimerRC atoms, and ".scalar(@keytrimerRNatoms)." trimerRN atoms\n");  # 记录找到的三聚体RC和RN原子数量
	#$trimerRCatoms{1}->Style = "Ball and stick";  # 设置特定三聚体RC原子样式(已注释)
	#$trimerRNatoms{1}->Style = "Ball and stick";  # 设置特定三聚体RN原子样式(已注释)
}

sub Find_indexHash_polyidlRCRN {  # 查找多聚体RC-RN原子对并建立索引哈希表
	my $doc = shift;  # 获取传入的文档对象
	my $polyidlRCRN_patName = shift;  # 获取传入的模式名称
	my $polyidlRCRN_pat = $Documents{"$polyidlRCRN_patName.xsd"};  # 打开相应的模式文档
	my $index = 0;  # 初始化索引计数器
	my $atoms = $doc->UnitCell->Atoms;  # 获取文档中的所有原子
	my $matches = $doc->FindPatterns($polyidlRCRN_pat);  # 查找匹配指定模式的所有结构
	foreach my $match (@$matches) {  # 遍历每个匹配的结构
		my $mtatoms = $match->Items;  # 获取匹配结构中的所有原子
		foreach my $atom (@$mtatoms) {  # 遍历匹配结构中的每个原子
			if ($atom->ElementSymbol eq "N") {  # 如果是N原子
				my $Nattachedatoms = $atom->AttachedAtoms;  # 获取N原子连接的所有原子
				foreach my $at (@$Nattachedatoms) {  # 遍历N原子连接的每个原子
					if ($at->ElementSymbol eq "H") {  # 如果连接的是H原子(识别可能参与反应的N原子)
						$atom->Name = "polyidlRN-$index";  # 命名为多聚体RN原子
						$polyidlRNatoms{$index} = $atom;  # 存储到多聚体RN原子哈希表
						#$atom->Style = "Ball and stick";  # 设置样式(已注释)
					}
				}
			}
			if ($atom->ElementSymbol eq "C") {  # 如果是C原子
				my $Cattachedatoms = $atom->AttachedAtoms;  # 获取C原子连接的所有原子
				foreach my $at (@$Cattachedatoms) {  # 遍历C原子连接的每个原子
					if ($at->ElementSymbol eq "H") {  # 如果连接的是H原子(识别可能参与反应的C原子)
						$atom->Name = "polyidlRC-$index";  # 命名为多聚体RC原子
						$polyidlRCatoms{$index} = $atom;  # 存储到多聚体RC原子哈希表
						#$atom->Style = "Ball and stick";  # 设置样式(已注释)
					}
				}
			}
		}
		$index += 1;  # 索引计数器加1
	}
	$matches->Delete;  # 删除匹配结果集
	$polyidlRCRN_pat->Close;  # 关闭模式文档
	my @keypolyidlRCatoms = keys %polyidlRCatoms;  # 获取多聚体RC原子哈希表的所有键
	my @keypolyidlRNatoms = keys %polyidlRNatoms;  # 获取多聚体RN原子哈希表的所有键
	if (scalar(@keypolyidlRCatoms) != scalar(@keypolyidlRNatoms)) {$logdoc->Append("WARNING: Find_indexHash_polyidlRCRN, the number of polyidlRC and polyidlRN atoms are NOT equal. Check the structures.\n");}  # 检查多聚体RC和RN原子数量是否相等
	$logdoc->Append("Find_indexHash_polyidlRCRN: there are ".scalar(@keypolyidlRCatoms)." polyidlRC atoms, and ".scalar(@keypolyidlRNatoms)." polyidlRN atoms\n");  # 记录找到的多聚体RC和RN原子数量
	#$polyidlRCatoms{1}->Style = "Ball and stick";  # 设置特定多聚体RC原子样式(已注释)
	#$polyidlRNatoms{1}->Style = "Ball and stick";  # 设置特定多聚体RN原子样式(已注释)
}

sub Create_ring_link {  # 创建环状链接的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $ReactRatio = shift;  # 获取传入的反应比例
	$ReactRatio = 100;  # 将反应比例设为100%（全部反应）
	my $ReactRadius = shift;  # 获取传入的反应半径
	my $count = 0;  # 初始化创建的环数计数器
	my $ReactiveNum = scalar(keys %RNatoms);  # 计算可用于反应的RN原子数量
	my $ReactNum = int($ReactiveNum*$ReactRatio/100);  # 根据反应比例计算要反应的对数
	$ReactNum = 1 if ($ReactNum < 1);  # 如果计算得到的反应对数小于1，则至少进行1个反应
	Tools->BondCalculation->ChangeSettings([DistanceCriterionMode=> "Absolute", MaxAbsoluteDistance => $ReactRadius, ExclusionMode => "Bonded"]);  # 设置键计算参数，使用绝对距离模式，最大距离为反应半径
	my $closeContacts = $doc->CalculateCloseContacts;  # 计算所有近距离接触对
	my @shuffledCloseContacts = shuffle(@$closeContacts);  # 随机打乱近距离接触对的顺序
	foreach my $closeContact (@shuffledCloseContacts) {  # 遍历每个近距离接触对
		my $atom1name = $closeContact->Atom1->Name;  # 获取第一个原子的名称
		my $atom2name = $closeContact->Atom2->Name;  # 获取第二个原子的名称
		if ((($atom1name =~ "^interRC-") and ($atom2name =~ "^RN-")) or (($atom1name =~ "^interRN-") and ($atom2name =~ "^RC-"))) {  # 如果是分子间RC和RN的接触对
			my $atom1 = $closeContact->Atom1;  # 获取第一个原子
			my $atom2 = $closeContact->Atom2;  # 获取第二个原子
			my $newbond = Create_ring_bond($doc,$atom1,$atom2,$ReactRadius);  # 尝试创建环键
			$count += 1 if ($newbond==1);  # 如果成功创建了环键，计数器加1
		}
		if ((($atom1name =~ "^RC-") and ($atom2name =~ "^interRN-")) or (($atom1name =~ "^RN-") and ($atom2name =~ "^interRC-"))) {  # 如果是RC和分子间RN的接触对
			my $atom1 = $closeContact->Atom1;  # 获取第一个原子
			my $atom2 = $closeContact->Atom2;  # 获取第二个原子
			my $newbond = Create_ring_bond($doc,$atom2,$atom1,$ReactRadius);  # 尝试创建环键，注意原子顺序调整
			$count += 1 if ($newbond==1);  # 如果成功创建了环键，计数器加1
		}
		last if ($count==$ReactNum);  # 如果已创建的环数达到计划的反应对数，结束循环
	}
	$closeContacts->Delete;  # 删除近距离接触对对象
	$doc->AdjustHydrogen;  # 调整氢原子
	$doc->Clean;  # 清理文档
	if ($count>0) {  # 如果创建了环
		$logdoc->Append("There are $count new rings created.\n");  # 记录创建的环数
	} else {$logdoc->Append("No new ring created.\n");}  # 如果没有创建环，记录无新环创建
}

sub Create_ring_bond {  # 创建环键的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $atom1 = shift;		# interRC/interRN  # 第一个原子(分子间RC或RN)
	my $atom2 = shift;		# RC/RN  # 第二个原子(RC或RN)
	my $ReactRadius = shift;  # 获取传入的反应半径
	my $flag = Check_from_same_ring($atom1, $atom2);  # 检查两个原子是否来自同一个环
	if ($flag == 0) {  # 如果不是来自同一个环
		my $atom1index = int((split("-",$atom1->Name))[-1]);  # 从原子名称中提取索引号
		my $atom2index = int((split("-",$atom2->Name))[-1]);  # 从原子名称中提取索引号
		my $atom3;  # 声明第三个原子变量
		my $atom4;  # 声明第四个原子变量
		if ($atom1->ElementSymbol eq "C") {  # 如果第一个原子是C
			$atom3 = $interRNatoms{$atom1index};  # 第三个原子是对应索引的分子间RN
			$atom4 = $RCatoms{$atom2index};  # 第四个原子是对应索引的RC
		} else {  # 如果第一个原子是N
			$atom3 = $interRCatoms{$atom1index};  # 第三个原子是对应索引的分子间RC
			$atom4 = $RNatoms{$atom2index};  # 第四个原子是对应索引的RN
		}
		my $newBond1 = $doc->CreateBond($atom1, $atom2, "Single");  # 创建第一个单键
		my $newBond2 = $doc->CreateBond($atom3, $atom4, "Single");  # 创建第二个单键
		if (($newBond1->Length > 1.5*$ReactRadius) or ($newBond2->Length > 1.5*$ReactRadius)) {  # 如果任一键长超过反应半径的1.5倍
			$newBond1->Delete;  # 删除第一个键
			$newBond2->Delete;  # 删除第二个键
			return 0;  # 返回0表示失败
		} else {  # 如果键长合适
			$atom1->Style = "Ball and stick";  # 设置第一个原子的样式
			$atom2->Style = "Ball and stick";  # 设置第二个原子的样式
			$atom3->Style = "Ball and stick";  # 设置第三个原子的样式
			$atom4->Style = "Ball and stick";  # 设置第四个原子的样式
			$atom1->Name = $atom1->ElementSymbol;  # 将第一个原子重命名为其元素符号
			$atom2->Name = $atom2->ElementSymbol;  # 将第二个原子重命名为其元素符号
			$atom3->Name = $atom3->ElementSymbol;  # 将第三个原子重命名为其元素符号
			$atom4->Name = $atom4->ElementSymbol;  # 将第四个原子重命名为其元素符号
			$CNbonds{$atom2index}->BondType = "Double";  # 将对应的CN键改为双键
			delete($interRCatoms{$atom1index});  # 从分子间RC原子哈希表中删除该原子
			delete($interRNatoms{$atom1index});  # 从分子间RN原子哈希表中删除该原子
			delete($RCatoms{$atom2index});  # 从RC原子哈希表中删除该原子
			delete($RNatoms{$atom2index});  # 从RN原子哈希表中删除该原子
			return 1;  # 返回1表示成功
		}
	}
}

sub Check_from_same_ring {  # 检查原子是否来自同一个环的更复杂子程序
	my $atom1 = shift;		# interRC/interRN  # 第一个原子(分子间RC或RN)
	my $atom2 = shift;		# RC/RN  # 第二个原子(RC或RN)
	my $RCRN_bonded_Ring;  # 声明RC/RN连接的环变量
	my @interRCRN_bonded_Rings;  # 声明分子间RC/RN连接的环数组
	if ($atom1->ElementSymbol eq "C") {  # 如果第一个原子是C
		my $interRCattachedAtoms = $atom1->AttachedAtoms;  # 获取分子间RC连接的所有原子
		foreach my $atom (@$interRCattachedAtoms) {  # 遍历每个连接的原子
			push(@interRCRN_bonded_Rings, $atom->Name) if ($atom->ElementSymbol eq "C");  # 如果是C原子，将其名称添加到环数组
			if ($atom->ElementSymbol eq "N") {  # 如果是N原子
				my $NattachedC = $atom->AttachedAtoms->Item(0);  # 获取N连接的第一个原子
				$NattachedC = $atom->AttachedAtoms->Item(1) if ($atom->AttachedAtoms->Item(0)->Name =~ "inter");  # 如果第一个原子名称包含"inter"，使用第二个原子
				my $CattachedAtoms = $NattachedC->AttachedAtoms;  # 获取C连接的所有原子
				foreach my $at (@$CattachedAtoms) {  # 遍历每个连接的原子
					push(@interRCRN_bonded_Rings, $at->Name) if ($at->ElementSymbol eq "C");  # 如果是C原子，将其名称添加到环数组
				}
			}
		}
	} else {  # 如果第一个原子是N
		my $interRNattachedC = $atom1->AttachedAtoms->Item(0);  # 获取分子间RN连接的第一个原子
		$interRNattachedC = $atom1->AttachedAtoms->Item(1) if ($atom1->AttachedAtoms->Item(1)->ElementSymbol eq "C");  # 如果第二个原子是C，使用它
		my $CattachedAtoms = $interRNattachedC->AttachedAtoms;  # 获取C连接的所有原子
		foreach my $atom (@$CattachedAtoms) {  # 遍历每个连接的原子
			push(@interRCRN_bonded_Rings, $atom->Name) if ($atom->ElementSymbol eq "C");  # 如果是C原子，将其名称添加到环数组
			if (($atom->ElementSymbol eq "N") and ($atom->Name !~ "inter")) {  # 如果是非分子间N原子
				my $NattachedinterRC = $atom->AttachedAtoms->Item(0);  # 获取N连接的第一个原子
				$NattachedinterRC = $atom->AttachedAtoms->Item(1) if ($atom->AttachedAtoms->Item(1)->Name =~ "inter");  # 如果第二个原子名称包含"inter"，使用它
				my $interRCattachedRingC = $NattachedinterRC->AttachedAtoms->Item(0);  # 获取分子间RC连接的第一个原子
				$interRCattachedRingC = $NattachedinterRC->AttachedAtoms->Item(1) if ($NattachedinterRC->AttachedAtoms->Item(1)->ElementSymbol eq "C");  # 如果第二个原子是C，使用它
				push(@interRCRN_bonded_Rings, $interRCattachedRingC->Name);  # 将环C原子名称添加到环数组
			}
		}
	}
	if ($atom2->ElementSymbol eq "C") {  # 如果第二个原子是C
		my $RCattachedRingC = $atom2->AttachedAtoms->Item(0);  # 获取RC连接的第一个原子
		$RCattachedRingC = $atom2->AttachedAtoms->Item(1) if ($atom2->AttachedAtoms->Item(1)->ElementSymbol eq "C");  # 如果第二个原子是C，使用它
		$RCRN_bonded_Ring = $RCattachedRingC->Name;  # 记录RC连接的环原子名称
	} else {  # 如果第二个原子是N
		my $RC = $atom2->AttachedAtoms->Item(0);  # 获取RN连接的第一个原子
		my $RCattachedRingC = $RC->AttachedAtoms->Item(0);  # 获取RC连接的第一个原子
		$RCattachedRingC = $RC->AttachedAtoms->Item(1) if ($RC->AttachedAtoms->Item(1)->ElementSymbol eq "C");  # 如果第二个原子是C，使用它
		$RCRN_bonded_Ring = $RCattachedRingC->Name;  # 记录RN连接的环原子名称
	}
	if (($interRCRN_bonded_Rings[0] eq $RCRN_bonded_Ring) or ($interRCRN_bonded_Rings[1] eq $RCRN_bonded_Ring)) {  # 如果分子间RC/RN连接的环与RC/RN连接的环相同
		return 1;  # 返回1表示来自同一个环
	} else {return 0;}  # 否则返回0，表示不是来自同一个环
}

sub Create_intraMolecule_link {  # 创建分子内链接的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $ReactRatio = shift;  # 获取传入的反应比例
	my $ReactRadius = shift;  # 获取传入的反应半径
	my $ReactiveNum = shift;  # 获取传入的反应原子数
	my $count = 0;  # 初始化创建的链接数计数器
	#my $ReactiveNum = scalar(keys %RNatoms)/2;  # 计算可用于反应的RN原子数量的一半
	my $ReactNum = int($ReactiveNum*$ReactRatio/100);  # 根据反应比例计算要反应的对数
	$ReactNum = 1 if ($ReactNum < 1);  # 如果计算得到的反应对数小于1，则至少进行1个反应
	Tools->BondCalculation->ChangeSettings([DistanceCriterionMode=> "Absolute", MaxAbsoluteDistance => 4.5, ExclusionMode => "Bonded"]);  # 设置键计算参数，使用绝对距离模式，最大距离为4.5埃
	my $closeContacts = $doc->CalculateCloseContacts;  # 计算所有近距离接触对
	my @shuffledCloseContacts = shuffle(@$closeContacts);  # 随机打乱近距离接触对的顺序
	foreach my $closeContact (@shuffledCloseContacts) {  # 遍历每个近距离接触对
			my $atom1name = $closeContact->Atom1->Name;  # 获取第一个原子的名称
			my $atom2name = $closeContact->Atom2->Name;  # 获取第二个原子的名称
			if ((($atom1name =~ "^RC-") and ($atom2name =~ "^RN-")) or (($atom1name =~ "^RN-") and ($atom2name =~ "^RC-"))) {  # 如果是RC和RN的接触对
				my $atom1 = $closeContact->Atom1;  # 获取第一个原子
				my $atom2 = $closeContact->Atom2;  # 获取第二个原子
				my $ifsameRing = Check_from_same_ring_RCRN($atom1, $atom2);  # 检查两个原子是否来自同一个环
				if ($ifsameRing==1) {  # 如果来自同一个环
					my $newbond = Create_intraMolecule_bond($doc,$atom1,$atom2,$ReactRadius);  # 尝试创建分子内键
					$count += 1 if ($newbond==1);  # 如果成功创建了分子内键，计数器加1
					#print("$count,$newbond\n");
				}
			}
		if ($count==$ReactNum) {last;}  # 如果已创建的链接数达到计划的反应对数，结束循环
	}
	$closeContacts->Delete;  # 删除近距离接触对对象
	$doc->AdjustHydrogen;  # 调整氢原子
	$doc->Clean;  # 清理文档
	if ($count>0) {  # 如果创建了链接
		$logdoc->Append("Create_intraMolecule_link: There are $count new selfRings created.\n");  # 记录创建的自环数
	} else {$logdoc->Append("Create_intraMolecule_link: No new selfRing created.\n");}  # 如果没有创建链接，记录无新自环创建
}

sub Create_intraMolecule_bond {  # 创建分子内键的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $atom1 = shift;  # 获取传入的第一个原子
	my $atom2 = shift;  # 获取传入的第二个原子
	my $ReactRadius = shift;  # 获取传入的反应半径
	my $newBond = $doc->CreateBond($atom1, $atom2, "Single");  # 创建单键
	if ($newBond->Length > 8) {  # 如果键长超过8埃
		$newBond->Delete;  # 删除该键
		return 0;  # 返回0表示失败
	} else {  # 如果键长合适
		$atom1->Style = "Ball and stick";  # 设置第一个原子的样式
		$atom2->Style = "Ball and stick";  # 设置第二个原子的样式
		my $atom1index = int((split("-",$atom1->Name))[-1]);  # 从原子名称中提取索引号
		my $atom2index = int((split("-",$atom2->Name))[-1]);  # 从原子名称中提取索引号
		$CNbonds{$atom1index}->BondType = "Double";  # 将第一个原子对应的CN键改为双键
		$CNbonds{$atom2index}->BondType = "Double";  # 将第二个原子对应的CN键改为双键
		$RCatoms{$atom1index}->Name = $RCatoms{$atom1index}->ElementSymbol;  # 将第一个RC原子重命名为其元素符号
		$RCatoms{$atom2index}->Name = $RCatoms{$atom2index}->ElementSymbol;  # 将第二个RC原子重命名为其元素符号
		$RNatoms{$atom1index}->Name = $RNatoms{$atom1index}->ElementSymbol;  # 将第一个RN原子重命名为其元素符号
		$RNatoms{$atom2index}->Name = $RNatoms{$atom2index}->ElementSymbol;  # 将第二个RN原子重命名为其元素符号
		return 1;  # 返回1表示成功
	}
}

sub Create_interMolecule_link {  # 创建分子间链接的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $ReactRatio = shift;  # 获取传入的反应比例
	my $ReactRadius = shift;  # 获取传入的反应半径
	my $ReactiveNum = shift;  # 获取传入的反应原子数
	my $count = 0;  # 初始化创建的链接数计数器
	#my $ReactiveNum = scalar(keys %RNatoms);  # 计算可用于反应的RN原子数量
	my $ReactNum = int($ReactiveNum*$ReactRatio/100);  # 根据反应比例计算要反应的对数
	$ReactNum = 1 if ($ReactNum < 1);  # 如果计算得到的反应对数小于1，则至少进行1个反应
	Tools->BondCalculation->ChangeSettings([DistanceCriterionMode=> "Absolute", MaxAbsoluteDistance => $ReactRadius, ExclusionMode => "Bonded"]);  # 设置键计算参数，使用绝对距离模式，最大距离为反应半径
	my $closeContacts = $doc->CalculateCloseContacts;  # 计算所有近距离接触对
	my @shuffledCloseContacts = shuffle(@$closeContacts);  # 随机打乱近距离接触对的顺序
	foreach my $closeContact (@shuffledCloseContacts) {  # 遍历每个近距离接触对
		my $atom1name = $closeContact->Atom1->Name;  # 获取第一个原子的名称
		my $atom2name = $closeContact->Atom2->Name;  # 获取第二个原子的名称
		if ((($atom1name =~ "^RC-") and ($atom2name =~ "^RN-")) or (($atom1name =~ "^RN-") and ($atom2name =~ "^RC-"))) {  # 如果是RC和RN的接触对
			my $atom1 = $closeContact->Atom1;  # 获取第一个原子
			my $atom2 = $closeContact->Atom2;  # 获取第二个原子
			my $ifsameRing = Check_from_same_ring_RCRN($atom1, $atom2);  # 检查两个原子是否来自同一个环
			if ($ifsameRing!=1) {  # 如果不是来自同一个环
				my $newbond = Create_interMolecule_bond($doc,$atom1,$atom2,$ReactRadius);  # 尝试创建分子间键
				$count += 1 if ($newbond==1);  # 如果成功创建了分子间键，计数器加1
				#print("$count,$newbond\n");
			}
		}
		last if ($count==$ReactNum);  # 如果已创建的链接数达到计划的反应对数，结束循环
	}
	$closeContacts->Delete;  # 删除近距离接触对对象
	$doc->AdjustHydrogen;  # 调整氢原子
	$doc->Clean;  # 清理文档
	if ($count>0) {  # 如果创建了链接
		$logdoc->Append("Create_interMolecule_link: There are $count new C-N bonds created.\n");  # 记录创建的C-N键数
	} else {$logdoc->Append("Create_interMolecule_link: No new bond created.\n");}  # 如果没有创建链接，记录无新键创建
}

sub Create_interMolecule_bond {  # 创建分子间键的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $atom1 = shift;  # 获取传入的第一个原子
	my $atom2 = shift;  # 获取传入的第二个原子
	my $ReactRadius = shift;  # 获取传入的反应半径
	my $newBond = $doc->CreateBond($atom1, $atom2, "Single");  # 创建单键
	if ($newBond->Length > 2*$ReactRadius) {  # 如果键长超过反应半径的2倍
		$newBond->Delete;  # 删除该键
		return 0;  # 返回0表示失败
	} else {  # 如果键长合适
		$atom1->Style = "Ball and stick";  # 设置第一个原子的样式
		$atom2->Style = "Ball and stick";  # 设置第二个原子的样式
		my $atom1index = int((split("-",$atom1->Name))[-1]);  # 从原子名称中提取索引号
		my $atom2index = int((split("-",$atom2->Name))[-1]);  # 从原子名称中提取索引号
		$CNbonds{$atom1index}->BondType = "Double";  # 将第一个原子对应的CN键改为双键
		$CNbonds{$atom2index}->BondType = "Double";  # 将第二个原子对应的CN键改为双键
		$RCatoms{$atom1index}->Name = $RCatoms{$atom1index}->ElementSymbol;  # 将第一个RC原子重命名为其元素符号
		$RCatoms{$atom2index}->Name = $RCatoms{$atom2index}->ElementSymbol;  # 将第二个RC原子重命名为其元素符号
		$RNatoms{$atom1index}->Name = $RNatoms{$atom1index}->ElementSymbol;  # 将第一个RN原子重命名为其元素符号
		$RNatoms{$atom2index}->Name = $RNatoms{$atom2index}->ElementSymbol;  # 将第二个RN原子重命名为其元素符号
		return 1;  # 返回1表示成功
	}
}

sub Create_dimer_link {  # 创建二聚体链接的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $ReactRatio = shift;  # 获取传入的反应比例
	$ReactRatio = 100;  # 将反应比例设为100%（全部反应）
	my $ReactRadius = shift;  # 获取传入的反应半径
	my $count = 0;  # 初始化创建的二聚体数计数器
	my $ReactiveNum = scalar(keys %intraRNatoms);  # 计算可用于反应的分子内RN原子数量
		last if ($count==$ReactNum);
	}
	$closeContacts->Delete;
	$doc->AdjustHydrogen;
	$doc->Clean;
	if ($count>0) {
		$logdoc->Append("Create_interMolecule_link: There are $count new C-N bonds created.\n");
	} else {$logdoc->Append("Create_interMolecule_link: No new bond created.\n");}
}

sub Create_interMolecule_bond {
	my $doc = shift;
	my $atom1 = shift;
	my $atom2 = shift;
	my $ReactRadius = shift;
	my $newBond = $doc->CreateBond($atom1, $atom2, "Single");
	if ($newBond->Length > 2*$ReactRadius) {
		$newBond->Delete;
		return 0;
	} else {
		$atom1->Style = "Ball and stick";  
		$atom2->Style = "Ball and stick";
		my $atom1index = int((split("-",$atom1->Name))[-1]);
		my $atom2index = int((split("-",$atom2->Name))[-1]);
		$CNbonds{$atom1index}->BondType = "Double";
		$CNbonds{$atom2index}->BondType = "Double";
		$RCatoms{$atom1index}->Name = $RCatoms{$atom1index}->ElementSymbol;
		$RCatoms{$atom2index}->Name = $RCatoms{$atom2index}->ElementSymbol;
		$RNatoms{$atom1index}->Name = $RNatoms{$atom1index}->ElementSymbol;
		$RNatoms{$atom2index}->Name = $RNatoms{$atom2index}->ElementSymbol;
		return 1;
	}
}

sub Create_dimer_link {
	my $doc = shift;  
	my $ReactRatio = shift;
	$ReactRatio = 100;
	my $ReactRadius = shift;
	my $count = 0;
	my $ReactiveNum = scalar(keys %intraRNatoms);
	my $ReactNum = int($ReactiveNum*$ReactRatio/100);
	$ReactNum = 1 if ($ReactNum < 1);
	Tools->BondCalculation->ChangeSettings([DistanceCriterionMode=> "Absolute", MaxAbsoluteDistance => $ReactRadius, ExclusionMode => "Bonded"]);
	my $closeContacts = $doc->CalculateCloseContacts;
	my @shuffledCloseContacts = shuffle(@$closeContacts);
	foreach my $closeContact (@shuffledCloseContacts) {
		my $atom1name = $closeContact->Atom1->Name;
		my $atom2name = $closeContact->Atom2->Name;
		if ((($atom1name =~ "^intraRC-") and ($atom2name =~ "^intraRN-")) or (($atom1name =~ "^intraRN-") and ($atom2name =~ "^intraRC-"))) {
			my $atom1 = $closeContact->Atom1;
			my $atom2 = $closeContact->Atom2;
			#my $ifsameRing = Check_from_same_ring_RCRN($atom1, $atom2);
			#if ($ifsameRing==1) {
			my $newbond = Create_dimer_bond($doc,$atom1,$atom2,$ReactRadius);
			$count += 1 if ($newbond==1);
			#}
		}
		if ($count==$ReactNum) {last;}
	}
	$closeContacts->Delete;
	$doc->AdjustHydrogen;
	$doc->Clean;
	if ($count>0) {
		$logdoc->Append("There are $count new dimers created.\n");
	} else {$logdoc->Append("No new dimer created.\n");}
}

sub Create_dimer_bond {
	my $doc = shift;
	my $atom1 = shift;
	my $atom2 = shift;
	my $ReactRadius = shift;
	my $newBond = $doc->CreateBond($atom1, $atom2, "Single");
	if ($newBond->Length > 2*$ReactRadius) {
		$newBond->Delete;
		return 0;
	} else {
		$atom1->Style = "Ball and stick";  
		$atom2->Style = "Ball and stick";
		my $atom1index = int((split("-",$atom1->Name))[-1]);
		my $atom2index = int((split("-",$atom2->Name))[-1]);
		#$CNbonds{$atom1index}->BondType = "Double";
		#$CNbonds{$atom2index}->BondType = "Double";
		$intraRCatoms{$atom1index}->Name = $intraRCatoms{$atom1index}->ElementSymbol;
		$intraRCatoms{$atom2index}->Name = $intraRCatoms{$atom2index}->ElementSymbol;
		$intraRNatoms{$atom1index}->Name = $intraRNatoms{$atom1index}->ElementSymbol;
		$intraRNatoms{$atom2index}->Name = $intraRNatoms{$atom2index}->ElementSymbol;
		if ($atom1->ElementSymbol eq "C") {
			delete($intraRCatoms{$atom1index});
			delete($intraRNatoms{$atom2index});
		} else {
			delete($intraRCatoms{$atom2index});
			delete($intraRNatoms{$atom1index});
		}
		return 1;
	}
}

sub Create_trimer_link {  # 创建三聚体链接的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $ReactRatio = shift;  # 获取传入的反应比例
	$ReactRatio = 100;  # 将反应比例设为100%（全部反应）
	my $ReactRadius = shift;  # 获取传入的反应半径
	my $count = 0;  # 初始化创建的三聚体数计数器
	my $ReactiveNum = scalar(keys %dimerRNatoms);  # 计算可用于反应的二聚体RN原子数量
	my $ReactNum = int($ReactiveNum*$ReactRatio/100);  # 根据反应比例计算要反应的对数
	$ReactNum = 1 if ($ReactNum < 1);  # 如果计算得到的反应对数小于1，则至少进行1个反应
	Tools->BondCalculation->ChangeSettings([DistanceCriterionMode=> "Absolute", MaxAbsoluteDistance => $ReactRadius, ExclusionMode => "Bonded"]);  # 设置键计算参数，使用绝对距离模式，最大距离为反应半径
	my $closeContacts = $doc->CalculateCloseContacts;  # 计算所有近距离接触对
	my @shuffledCloseContacts = shuffle(@$closeContacts);  # 随机打乱近距离接触对的顺序
	foreach my $closeContact (@shuffledCloseContacts) {  # 遍历每个近距离接触对
		my $atom1name = $closeContact->Atom1->Name;  # 获取第一个原子的名称
		my $atom2name = $closeContact->Atom2->Name;  # 获取第二个原子的名称
		if ((($atom1name =~ "^dimerRC-") and ($atom2name =~ "^intraRN-")) or (($atom1name =~ "^dimerRN-") and ($atom2name =~ "^intraRC-")) or (($atom1name =~ "^intraRC-") and ($atom2name =~ "^dimerRN-")) or (($atom1name =~ "^intraRN-") and ($atom2name =~ "^dimerRC-"))) {  # 如果是二聚体RC/RN和分子内RN/RC的接触对
			my $atom1 = $closeContact->Atom1;  # 获取第一个原子
			my $atom2 = $closeContact->Atom2;  # 获取第二个原子
			#my $ifsameRing = Check_from_same_ring_RCRN($atom1, $atom2);  # 检查两个原子是否来自同一个环(已注释)
			#if ($ifsameRing==1) {  # 如果来自同一个环(已注释)
			my $newbond = Create_trimer_bond($doc,$atom1,$atom2,$ReactRadius);  # 尝试创建三聚体键
			$count += 1 if ($newbond==1);  # 如果成功创建了三聚体键，计数器加1
			#}
		}
		if ($count==$ReactNum) {last;}  # 如果已创建的三聚体数达到计划的反应对数，结束循环
	}
	$closeContacts->Delete;  # 删除近距离接触对对象
	$doc->AdjustHydrogen;  # 调整氢原子
	$doc->Clean;  # 清理文档
	if ($count>0) {  # 如果创建了三聚体
		$logdoc->Append("There are $count new trimers created.\n");  # 记录创建的三聚体数
	} else {$logdoc->Append("No new trimer created.\n");}  # 如果没有创建三聚体，记录无新三聚体创建
}

sub Create_trimer_bond {  # 创建三聚体键的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $atom1 = shift;  # 获取传入的第一个原子
	my $atom2 = shift;  # 获取传入的第二个原子
	my $ReactRadius = shift;  # 获取传入的反应半径
	my $newBond = $doc->CreateBond($atom1, $atom2, "Single");  # 创建单键
	if ($newBond->Length > 2*$ReactRadius) {  # 如果键长超过反应半径的2倍
		$newBond->Delete;  # 删除该键
		return 0;  # 返回0表示失败
	} else {  # 如果键长合适
		$atom1->Style = "Ball and stick";  # 设置第一个原子的样式
		$atom2->Style = "Ball and stick";  # 设置第二个原子的样式
		my $atom1name = $atom1->Name;  # 获取第一个原子的名称
		my $atom2name = $atom2->Name;  # 获取第二个原子的名称
		my $atom1index = int((split("-",$atom1name))[-1]);  # 从原子名称中提取索引号
		my $atom2index = int((split("-",$atom2name))[-1]);  # 从原子名称中提取索引号
		if (($atom1name =~ "^dimer") and ($atom2name =~ "^intra")) {  # 如果第一个原子是二聚体原子，第二个原子是分子内原子
			$dimerRCatoms{$atom1index}->Name = $dimerRCatoms{$atom1index}->ElementSymbol;  # 将二聚体RC原子重命名为其元素符号
			$dimerRNatoms{$atom1index}->Name = $dimerRNatoms{$atom1index}->ElementSymbol;  # 将二聚体RN原子重命名为其元素符号
			$intraRCatoms{$atom2index}->Name = $intraRCatoms{$atom2index}->ElementSymbol;  # 将分子内RC原子重命名为其元素符号
			$intraRNatoms{$atom2index}->Name = $intraRNatoms{$atom2index}->ElementSymbol;  # 将分子内RN原子重命名为其元素符号
			delete($dimerRCatoms{$atom1index});  # 从二聚体RC原子哈希表中删除该原子
			delete($dimerRNatoms{$atom1index});  # 从二聚体RN原子哈希表中删除该原子
			delete($intraRCatoms{$atom2index});  # 从分子内RC原子哈希表中删除该原子
			delete($intraRNatoms{$atom2index});  # 从分子内RN原子哈希表中删除该原子
		} else {  # 如果第一个原子是分子内原子，第二个原子是二聚体原子
			$dimerRCatoms{$atom2index}->Name = $dimerRCatoms{$atom2index}->ElementSymbol;  # 将二聚体RC原子重命名为其元素符号
			$dimerRNatoms{$atom2index}->Name = $dimerRNatoms{$atom2index}->ElementSymbol;  # 将二聚体RN原子重命名为其元素符号
			$intraRCatoms{$atom1index}->Name = $intraRCatoms{$atom1index}->ElementSymbol;  # 将分子内RC原子重命名为其元素符号
			$intraRNatoms{$atom1index}->Name = $intraRNatoms{$atom1index}->ElementSymbol;  # 将分子内RN原子重命名为其元素符号
			delete($dimerRCatoms{$atom2index});  # 从二聚体RC原子哈希表中删除该原子
			delete($dimerRNatoms{$atom2index});  # 从二聚体RN原子哈希表中删除该原子
			delete($intraRCatoms{$atom1index});  # 从分子内RC原子哈希表中删除该原子
			delete($intraRNatoms{$atom1index});  # 从分子内RN原子哈希表中删除该原子
		}
		return 1;  # 返回1表示成功
	}
}

sub Create_polyidl_link {  # 创建多聚体链接的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $ReactRatio = shift;  # 获取传入的反应比例
	$ReactRatio = 100;  # 将反应比例设为100%（全部反应）
	my $ReactRadius = shift;  # 获取传入的反应半径
	my $count = 0;  # 初始化创建的多聚体数计数器
	my $ReactiveNum = scalar(keys %trimerRNatoms);  # 计算可用于反应的三聚体RN原子数量
	my $ReactNum = int($ReactiveNum*$ReactRatio/100);  # 根据反应比例计算要反应的对数
	$ReactNum = 1 if ($ReactNum < 1);  # 如果计算得到的反应对数小于1，则至少进行1个反应
	Tools->BondCalculation->ChangeSettings([DistanceCriterionMode=> "Absolute", MaxAbsoluteDistance => $ReactRadius, ExclusionMode => "Bonded"]);  # 设置键计算参数，使用绝对距离模式，最大距离为反应半径
	my $closeContacts = $doc->CalculateCloseContacts;  # 计算所有近距离接触对
	my @shuffledCloseContacts = shuffle(@$closeContacts);  # 随机打乱近距离接触对的顺序
	foreach my $closeContact (@shuffledCloseContacts) {  # 遍历每个近距离接触对
		my $atom1name = $closeContact->Atom1->Name;  # 获取第一个原子的名称
		my $atom2name = $closeContact->Atom2->Name;  # 获取第二个原子的名称
		if ((($atom1name =~ "^trimerRC-") and ($atom2name =~ "^intraRN-")) or (($atom1name =~ "^trimerRN-") and ($atom2name =~ "^intraRC-")) or (($atom1name =~ "^intraRC-") and ($atom2name =~ "^trimerRN-")) or (($atom1name =~ "^intraRN-") and ($atom2name =~ "^trimerRC-"))) {
			my $atom1 = $closeContact->Atom1;
			my $atom2 = $closeContact->Atom2;
			#my $ifsameRing = Check_from_same_ring_RCRN($atom1, $atom2);
			#if ($ifsameRing==1) {
			my $newbond = Create_polyidl_bond($doc,$atom1,$atom2,$ReactRadius);
			$count += 1 if ($newbond==1);
			#}
		}
		if ($count==$ReactNum) {last;}
	}
	$closeContacts->Delete;
	$doc->AdjustHydrogen;
	$doc->Clean;
	if ($count>0) {
		$logdoc->Append("There are $count new polyidls created.\n");
	} else {$logdoc->Append("No new polyidl created.\n");}
}

sub Create_polyidl_bond {
	my $doc = shift;
	my $atom1 = shift;
	my $atom2 = shift;
	my $ReactRadius = shift;
	my $newBond = $doc->CreateBond($atom1, $atom2, "Single");
	if ($newBond->Length > 2*$ReactRadius) {
		$newBond->Delete;
		return 0;
	} else {
		$atom1->Style = "Ball and stick";  
		$atom2->Style = "Ball and stick";
		my $atom1name = $atom1->Name;
		my $atom2name = $atom2->Name;
		my $atom1index = int((split("-",$atom1name))[-1]);
		my $atom2index = int((split("-",$atom2name))[-1]);
		if (($atom1name =~ "^trimer") and ($atom2name =~ "^intra")) {
			$trimerRCatoms{$atom1index}->Name = $trimerRCatoms{$atom1index}->ElementSymbol;
			$trimerRNatoms{$atom1index}->Name = $trimerRNatoms{$atom1index}->ElementSymbol;
			$intraRCatoms{$atom2index}->Name = $intraRCatoms{$atom2index}->ElementSymbol;
			$intraRNatoms{$atom2index}->Name = $intraRNatoms{$atom2index}->ElementSymbol;
			delete($trimerRCatoms{$atom1index});
			delete($trimerRNatoms{$atom1index});
			delete($intraRCatoms{$atom2index});
			delete($intraRNatoms{$atom2index});
		} else {
			$trimerRCatoms{$atom2index}->Name = $trimerRCatoms{$atom2index}->ElementSymbol;
			$trimerRNatoms{$atom2index}->Name = $trimerRNatoms{$atom2index}->ElementSymbol;
			$intraRCatoms{$atom1index}->Name = $intraRCatoms{$atom1index}->ElementSymbol;
			$intraRNatoms{$atom1index}->Name = $intraRNatoms{$atom1index}->ElementSymbol;
			delete($trimerRCatoms{$atom2index});
			delete($trimerRNatoms{$atom2index});
			delete($intraRCatoms{$atom1index});
			delete($intraRNatoms{$atom1index});
		}
		return 1;
	}
}

sub Create_Pc_link {
	my $doc = shift;  
	my $ReactRatio = shift;
	$ReactRatio = 100;
	my $ReactRadius = shift;
	my $count = 0;
	my $ReactiveNum = scalar(keys %polyidlRNatoms);
	my $ReactNum = int($ReactiveNum*$ReactRatio/100);
	$ReactNum = 1 if ($ReactNum < 1);	
	my @polyidlRCRNkeys = keys %polyidlRNatoms;
	foreach my $key (@polyidlRCRNkeys) {
		my $atom1 = $polyidlRCatoms{int($key)};
		my $atom2 = $polyidlRNatoms{int($key)};
		my $newbond = Create_Pc_bond($doc,$atom1,$atom2,$ReactRadius);
		$count += 1 if ($newbond == 1);
	}
	$doc->AdjustHydrogen;
	$doc->Clean;
	if ($count>0) {
		$logdoc->Append("There are $count new Pcs created.\n");
	} else {$logdoc->Append("No new Pc created.\n");}
}

sub Create_Pc_bond {
	my $doc = shift;
	my $atom1 = shift;
	my $atom2 = shift;
	my $ReactRadius = shift;
	my $newBond = $doc->CreateBond($atom1, $atom2, "Single");
	if ($newBond->Length > 5*$ReactRadius) {
		$newBond->Delete;
		return 0;
	} else {
		#$atom1->Style = "Ball and stick";  
		#$atom2->Style = "Ball and stick";
		my $atom1name = $atom1->Name;
		my $atom1index = int((split("-",$atom1name))[-1]);
		my $polyRC = $atom1;
		$polyRC = $atom2 if ($atom2->ElementSymbol eq "C");
		Pc_AddH2($polyRC);
		$atom1->Name = $atom1->ElementSymbol;
		$atom2->Name = $atom2->ElementSymbol;	
		delete($polyidlRCatoms{$atom1index});
		delete($polyidlRNatoms{$atom1index});
		return 1;
	}
}

sub Pc_AddH2 {
	my $polyidlRC = shift;
	my $AddHN1;
	my $polyidlRCAttachedatoms = $polyidlRC->AttachedAtoms; 
	foreach my $at (@$polyidlRCAttachedatoms) {
		if (($at->ElementSymbol eq "N") and ($at->Name !~ "polyidlRN")) {
			$AddHN1 = $at;
			$AddHN1->Name = "AddHN1";
		}
	}
	my $polyidlRC_bonds = $polyidlRC->Bonds;
	foreach my $bond (@$polyidlRC_bonds) {
		if (($bond->Atom1->Name eq "AddHN1") or ($bond->Atom2->Name eq "AddHN1")) {$bond->BondType = "Single";}
		if (($bond->Atom1->ElementSymbol eq "C") and ($bond->Atom2->ElementSymbol eq "C")) {$bond->BondType = "Double";}
	}
	my $AddHN1Attachedatoms = $AddHN1->AttachedAtoms; 
	my $C1;
	foreach my $at (@$AddHN1Attachedatoms) {
		if (($at->ElementSymbol eq "C") and ($at->Name !~ "polyidlRC")) {
			$C1 = $at;
			$C1->Name = "C1";
		}
	}
	my $C1Attachedatoms = $C1->AttachedAtoms; 
	my $N1;
	foreach my $at (@$C1Attachedatoms) {
		if (($at->ElementSymbol eq "N") and ($at->Name ne "AddHN1")) {
			$N1 = $at;
			$N1->Name = "N1";
		}
	}
	my $C1_bonds = $C1->Bonds;
	my $bond_C1_N1;
	foreach my $bond (@$C1_bonds) {
		if (($bond->Atom1->Name eq "N1") or ($bond->Atom2->Name eq "N1")) {$bond_C1_N1 = $bond;}
		if (($bond->Atom1->ElementSymbol eq "C") and ($bond->Atom2->ElementSymbol eq "C")) {$bond->BondType = "Double";}
	}
	my $bond_N1_C2 = $N1->Bonds->Item(0);
	$bond_N1_C2 = $N1->Bonds->Item(1) if ($N1->Bonds->Item(1)->BondType eq "Single");
	$bond_C1_N1->BondType = "Single";
	$bond_N1_C2->BondType = "Double";
	my $C2 = $bond_N1_C2->Atom1;
	$C2 = $bond_N1_C2->Atom2 if ($bond_N1_C2->Atom2->ElementSymbol eq "C");
	my $C2Attachedatoms = $C2->AttachedAtoms; 
	my $N2;
	foreach my $at (@$C2Attachedatoms) {
		if (($at->ElementSymbol eq "N") and ($at->Name ne "N1")) {
			$N2 = $at;
			$N2->Name = "N2";
		}
	}
	my $bond_C2_N2 = $N2->Bonds->Item(0);
	my $bond_N2_C3 = $N2->Bonds->Item(1);
	if ($N2->Bonds->Item(1)->BondType eq "Double") {
		$bond_C2_N2 = $N2->Bonds->Item(1);
		$bond_N2_C3 = $N2->Bonds->Item(0);
	}
	$bond_C2_N2->BondType = "Single";
	$bond_N2_C3->BondType = "Double";
	my $C3 = $bond_N2_C3->Atom1;
	$C3 = $bond_N2_C3->Atom2 if ($bond_N2_C3->Atom2->ElementSymbol eq "C");
	$C3->Name = "C3";
	my $C3Attachedatoms = $C3->AttachedAtoms; 
	my $N3;
	foreach my $at (@$C3Attachedatoms) {
		if (($at->ElementSymbol eq "N") and ($at->Name ne "N2")) {
			$N3 = $at;
			$N3->Name = "N3";
		}
	}
	my $bond_C3_N3 = $N3->Bonds->Item(0);
	my $bond_N3_C4 = $N3->Bonds->Item(1);
	if ($N3->Bonds->Item(1)->BondType eq "Double") {
		$bond_C3_N3 = $N3->Bonds->Item(1);
		$bond_N3_C4 = $N3->Bonds->Item(0);
	}
	$bond_C3_N3->BondType = "single";
	$bond_N3_C4->BondType = "Double";
	my $C4 = $bond_N3_C4->Atom1;
	$C4 = $bond_N3_C4->Atom2 if ($bond_N3_C4->Atom2->ElementSymbol eq "C");
	$C4->Name = "C4";
	my $C4Attachedatoms = $C4->AttachedAtoms; 
	my $AddHN2;
	foreach my $at (@$C4Attachedatoms) {
		if (($at->ElementSymbol eq "N") and ($at->Name ne "N3")) {
			$AddHN2 = $at;
			$AddHN2->Name = "AddHN2";
		}
	}
	my $bond_AddHN2_C4 = $AddHN2->Bonds->Item(0);
	$bond_AddHN2_C4 = $AddHN2->Bonds->Item(1) if ($AddHN2->Bonds->Item(1)->BondType eq "Double");
	$bond_AddHN2_C4->BondType = "Single";
	$AddHN1->Name = $AddHN1->ElementSymbol;
	$AddHN2->Name = $AddHN2->ElementSymbol;
	$C1->Name = $C1->ElementSymbol;
	$C2->Name = $C2->ElementSymbol;
	$C3->Name = $C3->ElementSymbol;
	$C4->Name = $C4->ElementSymbol;
	$N1->Name = $N1->ElementSymbol;
	$N2->Name = $N2->ElementSymbol;
	$N3->Name = $N3->ElementSymbol;
}

sub HighlightDisplay {  # 高亮显示特定结构的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $PcH2_patName = shift;  # 获取传入的二氢酞菁模式名称
	#setAtomLineStyle($doc);  # 设置所有原子为线型样式(已注释)
	my $PcH2_pat = $Documents{"$PcH2_patName.xsd"};  # 打开二氢酞菁模式文档
	my $PcH2_matches = $doc->FindPatterns($PcH2_pat);  # 查找匹配二氢酞菁模式的所有结构
	foreach my $match (@$PcH2_matches) {  # 遍历每个匹配的结构
		my $mtatoms = $match->Items;  # 获取匹配结构中的所有原子
		foreach my $atom (@$mtatoms) {  # 遍历匹配结构中的每个原子
			$atom->Style = "Ball and stick";  # 将原子样式设置为"球棍模型"，以高亮显示
			#if (not ($atom->IsInRing)) {$atom->Style = "Ball and stick";}  # 如果原子不在环中，设置为球棍模型(已注释)
		}
	}
	$PcH2_pat->Close;  # 关闭二氢酞菁模式文档
	$PcH2_matches->Delete;  # 删除匹配结果集
}

sub xlinkStatistics {  # 统计交联结构数量的子程序
	my $doc = shift;  # 获取传入的文档对象
	my $interRCRN_patName = shift;  # 获取传入的分子间RC-RN模式名称
	my $ring_patName = shift;  # 获取传入的环模式名称
	my $intraRCRN_patName = shift;  # 获取传入的分子内RC-RN模式名称
	my $dimerRCRN_patName = shift;  # 获取传入的二聚体RC-RN模式名称
	my $trimerRCRN_patName = shift;  # 获取传入的三聚体RC-RN模式名称
	my $polyidlRCRN_patName = shift;  # 获取传入的多聚体RC-RN模式名称
	my $Pc_patName = shift;  # 获取传入的酞菁模式名称
	my $interRCRN_pat = $Documents{"$interRCRN_patName.xsd"};  # 打开分子间RC-RN模式文档
	my $ring_pat = $Documents{"$ring_patName.xsd"};  # 打开环模式文档
	my $intraRCRN_pat = $Documents{"$intraRCRN_patName.xsd"};  # 打开分子内RC-RN模式文档
	my $dimerRCRN_pat = $Documents{"$dimerRCRN_patName.xsd"};  # 打开二聚体RC-RN模式文档
	my $trimerRCRN_pat = $Documents{"$trimerRCRN_patName.xsd"};  # 打开三聚体RC-RN模式文档
	my $polyidlRCRN_pat = $Documents{"$polyidlRCRN_patName.xsd"};  # 打开多聚体RC-RN模式文档
	my $Pc_pat = $Documents{"$Pc_patName.xsd"};  # 打开酞菁模式文档
	my $interRCRN_matches = $doc->FindPatterns($interRCRN_pat);  # 查找匹配分子间RC-RN模式的所有结构
	my $ring_matches = $doc->FindPatterns($ring_pat);  # 查找匹配环模式的所有结构
	my $intraRCRN_matches = $doc->FindPatterns($intraRCRN_pat);  # 查找匹配分子内RC-RN模式的所有结构
	my $dimerRCRN_matches = $doc->FindPatterns($dimerRCRN_pat);  # 查找匹配二聚体RC-RN模式的所有结构
	my $trimerRCRN_matches = $doc->FindPatterns($trimerRCRN_pat);  # 查找匹配三聚体RC-RN模式的所有结构
	my $polyidlRCRN_matches = $doc->FindPatterns($polyidlRCRN_pat);  # 查找匹配多聚体RC-RN模式的所有结构
	my $Pc_matches = $doc->FindPatterns($Pc_pat);  # 查找匹配酞菁模式的所有结构
	my $interRCRN_count = $interRCRN_matches->Count;  # 统计分子间RC-RN结构数量
	my $ring_count = $ring_matches->Count;  # 统计环结构数量
	my $intraRCRN_count = $intraRCRN_matches->Count;  # 统计分子内RC-RN结构数量
	my $dimerRCRN_count = $dimerRCRN_matches->Count;  # 统计二聚体RC-RN结构数量
	my $trimerRCRN_count = $trimerRCRN_matches->Count;  # 统计三聚体RC-RN结构数量
	my $polyidlRCRN_count = $polyidlRCRN_matches->Count;  # 统计多聚体RC-RN结构数量
	my $Pc_count = $Pc_matches->Count;  # 统计酞菁结构数量
	$interRCRN_pat->Close;  # 关闭分子间RC-RN模式文档
	$ring_pat->Close;  # 关闭环模式文档
	$intraRCRN_pat->Close;  # 关闭分子内RC-RN模式文档
	$dimerRCRN_pat->Close;  # 关闭二聚体RC-RN模式文档
	$trimerRCRN_pat->Close;  # 关闭三聚体RC-RN模式文档
	$polyidlRCRN_pat->Close;  # 关闭多聚体RC-RN模式文档
	$Pc_pat->Close;  # 关闭酞菁模式文档
	$interRCRN_matches->Delete;  # 删除分子间RC-RN匹配结果集
	$ring_matches->Delete;  # 删除环匹配结果集
	$intraRCRN_matches->Delete;  # 删除分子内RC-RN匹配结果集
	$dimerRCRN_matches->Delete;  # 删除二聚体RC-RN匹配结果集
	$trimerRCRN_matches->Delete;  # 删除三聚体RC-RN匹配结果集
	$polyidlRCRN_matches->Delete;  # 删除多聚体RC-RN匹配结果集
	$Pc_matches->Delete;  # 删除酞菁匹配结果集
	my $ring_conversion = $ring_count;  # 环转化数量等于环结构数量
	my $interRCRN_conversion = ($interRCRN_count-$intraRCRN_count);  # 分子间RC-RN转化数量等于分子间RC-RN结构数量减去分子内RC-RN结构数量
	my $intraRCRN_conversion = $intraRCRN_count;  # 分子内RC-RN转化数量等于分子内RC-RN结构数量
	my $dimerRCRN_conversion = $dimerRCRN_count;  # 二聚体RC-RN转化数量等于二聚体RC-RN结构数量
	my $trimerRCRN_conversion = $trimerRCRN_count;  # 三聚体RC-RN转化数量等于三聚体RC-RN结构数量
	my $polyidlRCRN_conversion = $polyidlRCRN_count;  # 多聚体RC-RN转化数量等于多聚体RC-RN结构数量
	my $Pc_conversion = $Pc_count;  # 酞菁转化数量等于酞菁结构数量
	$logdoc->Append("\nCurrent Conversion: C-N tri-bonds formed $ring_conversion Triazines(Tas), $Pc_conversion Phthalocyanines(Pcs), $interRCRN_conversion inter-molecule links,  $intraRCRN_conversion self-5-Rings, $dimerRCRN_conversion dimers, $trimerRCRN_conversion trimers, $polyidlRCRN_conversion polyidls.\n");  # 记录当前转化情况，包括三嗪环、酞菁、分子间链接、自环、二聚体、三聚体和多聚体的数量
}

sub onlyOptimize {  # 仅进行几何优化的子程序
	my $ifOPT = shift;  # 获取传入的优化标志
	my $doc= shift;  # 获取传入的文档对象
	my $steps = 100000;  # 设置最大迭代步数为10万步
	my $optResults;  # 声明优化结果变量
	if (($ifOPT eq "Yes") or ($ifOPT eq "yes") or ($ifOPT eq "Y") or ($ifOPT eq "y")) {  # 如果优化标志为是
		eval {$optResults = Modules->Forcite->GeometryOptimization->Run($doc, ([MaxIterations =>$steps]));};  # 尝试运行几何优化
		if ($@) {  # 如果发生错误
			$logdoc->Append("\nThere is a problem with the geometry optimization:\n");  # 记录错误信息
			$logdoc->Append($@);  # 记录详细错误内容
		} else {  # 如果优化成功
			$doc = $optResults->Structure;  # 更新文档为优化后的结构
			#$optResults->Report->Delete;  # 删除优化报告(已注释)
			#$optResults->ConvergenceChart->Delete;  # 删除收敛图表(已注释)
			#$Documents{"Status.txt"}->Delete;  # 删除状态文件(已注释)
			#$Documents{"$xsdName Energies.xcd"}->Delete;  # 删除能量文件(已注释)
			$logdoc->Append ("Optimize structure.\n");  # 记录结构已优化
		}
	}
}

sub onetimeMD {  # 一次性分子动力学模拟子程序
	my $ifMD = shift;  # 获取传入的MD标志
	my $doc = shift;  # 获取传入的文档对象
	my $timeStep = shift;  # 获取传入的时间步长
	my $DynamicsTime = shift;  # 获取传入的动力学模拟时间
	if (($ifMD eq "Yes") or ($ifMD eq "yes") or ($ifMD eq "Y") or ($ifMD eq "y")) {  # 如果MD标志为是
		my $steps = $DynamicsTime*1000/$timeStep;  # 计算模拟步数（模拟时间(ps)*1000/时间步长(fs)）
		Modules->Forcite->ChangeSettings([NumberOfSteps => $steps, Ensemble3D=>"NVT", Temperature=>$DynamicsTemp]);  # 设置Forcite模块参数，包括步数、系综(NVT)和温度
		Modules->Forcite->GeometryOptimization->Run($doc, [MaxIterations => 100000]);  # 先进行几何优化
		my $results = Modules->Forcite->Dynamics->Run($doc);  # 运行分子动力学模拟
		#$results->Trajectory->Delete;  # 删除轨迹文件(已注释)
		#$results->Report->Delete;  # 删除报告文件(已注释)
		#$Documents{"Status.txt"}->Delete;  # 删除状态文件(已注释)
		#$Documents{"$xsdName Energies.xcd"}->Delete;  # 删除能量文件(已注释)
		#$Documents{"$xsdName Temperature.xcd"}->Delete;  # 删除温度文件(已注释)
		$logdoc->Append ("Run one-time NVT-MD, $DynamicsTime ps.\n");  # 记录已运行一次NVT分子动力学模拟
	}
}

sub equilibrationMD {  # 平衡分子动力学模拟子程序
	my $ifOPT = shift;  # 获取传入的优化标志
	my $ifMD = shift;  # 获取传入的MD标志
	my $doc = shift;  # 获取传入的文档对象
	my $timeStep = shift;  # 获取传入的时间步长
	my $DynamicsTime = shift;  # 获取传入的动力学模拟时间
	if (($ifMD eq "Yes") or ($ifMD eq "yes") or ($ifMD eq "Y") or ($ifMD eq "y")) {  # 如果MD标志为是
		my $steps = $DynamicsTime*1000/$timeStep;  # 计算模拟步数（模拟时间(ps)*1000/时间步长(fs)）
		Modules->Forcite->ChangeSettings([NumberOfSteps => $steps, Ensemble3D=>"NVT", Temperature=>$DynamicsTemp]);  # 设置Forcite模块参数，包括步数、系综(NVT)和温度
		Modules->Forcite->GeometryOptimization->Run($doc, [MaxIterations => 100000]) if (($ifOPT ne "Yes") or ($ifOPT ne "yes") or ($ifOPT ne "Y") or ($ifOPT ne "y"));  # 如果未进行过几何优化，先进行几何优化
		my $results = Modules->Forcite->Dynamics->Run($doc);  # 运行分子动力学模拟
		#$results->Trajectory->Delete;  # 删除轨迹文件(已注释)
		#$results->Report->Delete;  # 删除报告文件(已注释)
		#$Documents{"Status.txt"}->Delete;  # 删除状态文件(已注释)
		#$Documents{"$xsdName Energies.xcd"}->Delete;  # 删除能量文件(已注释)
sub setAtomLineStyle {
	my $doc = shift;
	my $atoms = $doc->UnitCell->Atoms;
	foreach my $atom (@$atoms) {
		$atom->Style = "Line";
	}
}

sub HighlightDisplay {
	my $doc = shift;
	my $PcH2_patName = shift;
	#setAtomLineStyle($doc);
	my $PcH2_pat = $Documents{"$PcH2_patName.xsd"};
	my $PcH2_matches = $doc->FindPatterns($PcH2_pat);
	foreach my $match (@$PcH2_matches) {
		my $mtatoms = $match->Items;
		foreach my $atom (@$mtatoms) {
			$atom->Style = "Ball and stick";
			#if (not ($atom->IsInRing)) {$atom->Style = "Ball and stick";}
		}
	}
	$PcH2_pat->Close;
	$PcH2_matches->Delete;
}

sub xlinkStatistics {
	my $doc = shift;
	my $interRCRN_patName = shift;
	my $ring_patName = shift;
	my $intraRCRN_patName = shift;
	my $dimerRCRN_patName = shift;
	my $trimerRCRN_patName = shift;
	my $polyidlRCRN_patName = shift;
	my $Pc_patName = shift;
	my $interRCRN_pat = $Documents{"$interRCRN_patName.xsd"};
	my $ring_pat = $Documents{"$ring_patName.xsd"};
	my $intraRCRN_pat = $Documents{"$intraRCRN_patName.xsd"};
	my $dimerRCRN_pat = $Documents{"$dimerRCRN_patName.xsd"};
	my $trimerRCRN_pat = $Documents{"$trimerRCRN_patName.xsd"};
	my $polyidlRCRN_pat = $Documents{"$polyidlRCRN_patName.xsd"};
	my $Pc_pat = $Documents{"$Pc_patName.xsd"};
	my $interRCRN_matches = $doc->FindPatterns($interRCRN_pat);
	my $ring_matches = $doc->FindPatterns($ring_pat);
	my $intraRCRN_matches = $doc->FindPatterns($intraRCRN_pat);
	my $dimerRCRN_matches = $doc->FindPatterns($dimerRCRN_pat);
	my $trimerRCRN_matches = $doc->FindPatterns($trimerRCRN_pat);
	my $polyidlRCRN_matches = $doc->FindPatterns($polyidlRCRN_pat);
	my $Pc_matches = $doc->FindPatterns($Pc_pat);
	my $interRCRN_count = $interRCRN_matches->Count;
	my $ring_count = $ring_matches->Count;
	my $intraRCRN_count = $intraRCRN_matches->Count;
	my $dimerRCRN_count = $dimerRCRN_matches->Count;
	my $trimerRCRN_count = $trimerRCRN_matches->Count;
	my $polyidlRCRN_count = $polyidlRCRN_matches->Count;
	my $Pc_count = $Pc_matches->Count;
	$interRCRN_pat->Close;
	$ring_pat->Close;
	$intraRCRN_pat->Close;
	$dimerRCRN_pat->Close;
	$trimerRCRN_pat->Close;
	$polyidlRCRN_pat->Close;
	$Pc_pat->Close;
	$interRCRN_matches->Delete;
	$ring_matches->Delete;
	$intraRCRN_matches->Delete;
	$dimerRCRN_matches->Delete;
	$trimerRCRN_matches->Delete;
	$polyidlRCRN_matches->Delete;
	$Pc_matches->Delete;
	my $ring_conversion = $ring_count;
	my $interRCRN_conversion = ($interRCRN_count-$intraRCRN_count);
	my $intraRCRN_conversion = $intraRCRN_count;
	my $dimerRCRN_conversion = $dimerRCRN_count;
	my $trimerRCRN_conversion = $trimerRCRN_count;
	my $polyidlRCRN_conversion = $polyidlRCRN_count;
	my $Pc_conversion = $Pc_count;
	$logdoc->Append("\nCurrent Conversion: C-N tri-bonds formed $ring_conversion Triazines(Tas), $Pc_conversion Phthalocyanines(Pcs), $interRCRN_conversion inter-molecule links,  $intraRCRN_conversion self-5-Rings, $dimerRCRN_conversion dimers, $trimerRCRN_conversion trimers, $polyidlRCRN_conversion polyidls.\n");	
}

sub onlyOptimize {
	my $ifOPT = shift;
	my $doc= shift;
	my $steps = 100000;
	my $optResults;
	if (($ifOPT eq "Yes") or ($ifOPT eq "yes") or ($ifOPT eq "Y") or ($ifOPT eq "y")) {
		eval {$optResults = Modules->Forcite->GeometryOptimization->Run($doc, ([MaxIterations =>$steps]));};
		if ($@) {
			$logdoc->Append("\nThere is a problem with the geometry optimization:\n");
			$logdoc->Append($@);
		} else {
			$doc = $optResults->Structure;
			#$optResults->Report->Delete;
			#$optResults->ConvergenceChart->Delete;
			#$Documents{"Status.txt"}->Delete;
			#$Documents{"$xsdName Energies.xcd"}->Delete;
			$logdoc->Append ("Optimize structure.\n");
		}
	}
}

sub onetimeMD {
	my $ifMD = shift;
	my $doc = shift;
	my $timeStep = shift;
	my $DynamicsTime = shift;
	if (($ifMD eq "Yes") or ($ifMD eq "yes") or ($ifMD eq "Y") or ($ifMD eq "y")) {
		my $steps = $DynamicsTime*1000/$timeStep;
		Modules->Forcite->ChangeSettings([NumberOfSteps => $steps, Ensemble3D=>"NVT", Temperature=>$DynamicsTemp]);
		Modules->Forcite->GeometryOptimization->Run($doc, [MaxIterations => 100000]);
		my $results = Modules->Forcite->Dynamics->Run($doc);
		#$results->Trajectory->Delete;
		#$results->Report->Delete;
		#$Documents{"Status.txt"}->Delete;
		#$Documents{"$xsdName Energies.xcd"}->Delete;
		#$Documents{"$xsdName Temperature.xcd"}->Delete;
		$logdoc->Append ("Run one-time NVT-MD, $DynamicsTime ps.\n");
	}
}

sub equilibrationMD {
	my $ifOPT = shift;
	my $ifMD = shift;
	my $doc = shift;
	my $timeStep = shift;
	my $DynamicsTime = shift;
	if (($ifMD eq "Yes") or ($ifMD eq "yes") or ($ifMD eq "Y") or ($ifMD eq "y")) {
		my $steps = $DynamicsTime*1000/$timeStep;
		Modules->Forcite->ChangeSettings([NumberOfSteps => $steps, Ensemble3D=>"NVT", Temperature=>$DynamicsTemp]);
		Modules->Forcite->GeometryOptimization->Run($doc, [MaxIterations => 100000]) if (($ifOPT ne "Yes") or ($ifOPT ne "yes") or ($ifOPT ne "Y") or ($ifOPT ne "y"));
		my $results = Modules->Forcite->Dynamics->Run($doc);
		#$results->Trajectory->Delete;
		#$results->Report->Delete;
		#$Documents{"Status.txt"}->Delete;
		#$Documents{"$xsdName Energies.xcd"}->Delete;
		#$Documents{"$xsdName Temperature.xcd"}->Delete;
		$logdoc->Append ("Run equilibration NVT-MD, $DynamicsTime ps.\n");
	}
}

sub FormatTimeOut {
	my $secs = shift;
	if ($secs > 3600) {
		my $h = int($secs/3600);
		my $m = int(($secs-3600*int($secs/3600))/60);
		my $s = $secs-3600*int($secs/3600)-60*int(($secs-3600*int($secs/3600))/60);
		$logdoc->Append("Time Used: $h hours $m minutes $s seconds");
	} elsif ($secs == 3600) {
		$logdoc->Append("Time Used: 1 hour");
	} elsif ($secs > 60) {
		my $m = int($secs/60);
		my $s = $secs-60*int($secs/60);
		$logdoc->Append("Time Used: $m minutes $s seconds");
	} elsif ($secs == 60) {
		$logdoc->Append("Time Used: 1 minute");
	} else {
		$logdoc->Append("Time Used: $secs seconds");
	}
}