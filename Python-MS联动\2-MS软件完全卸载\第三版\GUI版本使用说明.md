# BIOVIA Materials Studio 卸载工具 (GUI版本)

这是一个用于彻底卸载BIOVIA Materials Studio及其组件的工具，通过图形用户界面(GUI)引导您完成整个卸载流程，包括程序卸载、注册表清理和残留文件删除。

## 功能特点

- 美观的图形用户界面，操作简单直观
- 自动搜索并找到所有BIOVIA相关程序
- 支持多个版本识别（2020、2021、2022等）
- 强制卸载程序，包括注册表清理
- 自动搜索并删除C盘中的残留文件
- 验证卸载结果，确保完全卸载
- 详细的日志输出，方便追踪问题

## 系统要求

- Windows 10/11 操作系统
- 管理员权限
- 如果直接运行Python脚本:
  - Python 3.6或更高版本
  - 依赖库: tkinter, pyautogui, opencv-python, pillow

## 准备工作

1. **管理员权限**：必须使用管理员权限运行本程序
2. **备份数据**：建议在卸载前备份重要数据
3. **关闭相关程序**：确保所有BIOVIA相关程序已关闭

## 安装方法

### 使用可执行文件（推荐）

1. 下载`MS卸载工具_GUI.exe`可执行文件
2. 将Geek Uninstaller软件(`geek.exe`)放在同一目录下
3. 右键点击`MS卸载工具_GUI.exe`，选择"以管理员身份运行"

### 从源代码运行

1. 确保已安装Python及必要的库(tkinter, pyautogui, opencv-python, pillow)
2. 右键点击`启动MS卸载工具.bat`，选择"以管理员身份运行"

### 编译为可执行文件

如果您想自己编译可执行文件:

1. 运行`make_gui_exe.py`脚本
2. 编译完成后的可执行文件将位于`dist`目录下

## 使用流程

### 1. 欢迎页面

- 显示程序功能介绍
- 点击"开始卸载流程"按钮进入下一步

### 2. 扫描页面

- 点击"开始扫描"按钮，程序将自动:
  - 查找并启动Geek Uninstaller软件
  - 搜索BIOVIA相关程序
  - 显示搜索结果
- 扫描完成后，点击"下一步"继续

### 3. 卸载页面

- 在列表中选择要卸载的程序
- 点击"开始卸载"按钮
- 程序将逐一卸载选中的程序
- 卸载完成后，点击"下一步"继续

### 4. 清理页面

- 点击"开始清理"按钮，程序将:
  - 清理注册表中的BIOVIA相关条目
  - 搜索并删除C盘中的残留文件
- 清理完成后，点击"下一步"继续

### 5. 验证页面

- 点击"开始验证"按钮
- 程序将再次检查BIOVIA程序是否已完全卸载
- 验证完成后，点击"完成"退出程序

## 界面元素说明

- **进度条**：显示当前操作的进度
- **日志区域**：显示详细的操作日志
- **按钮**：
  - 返回：返回上一步
  - 下一步：进入下一步
  - 开始按钮：开始当前步骤的操作
  - 完成：退出程序

## 问题排查

- **如果程序无法启动**：
  - 确保以管理员权限运行
  - 检查是否已安装所需的Python库

- **如果无法找到BIOVIA程序**：
  - 确认您的系统上是否安装了BIOVIA程序
  - 检查Geek Uninstaller软件是否正常工作

- **如果卸载过程中断**：
  - 检查日志区域，了解中断原因
  - 尝试重新运行程序

- **如果文件无法删除**：
  - 可能是因为文件被其他程序占用
  - 记下文件路径，稍后手动删除

## 其他说明

- 程序运行过程中会保存屏幕截图，用于调试和验证
- 截图保存在`resources`目录下
- 卸载完成后，可以手动删除这些截图 