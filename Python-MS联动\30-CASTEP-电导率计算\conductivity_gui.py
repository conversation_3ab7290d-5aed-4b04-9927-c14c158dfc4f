#!/usr/bin/env python3
"""
CASTEP电导率计算GUI界面
基于Streamlit的Web界面

运行方式: streamlit run conductivity_gui.py
"""

import streamlit as st
import pandas as pd
import numpy as np
import altair as alt
import io
import sys
import os
from typing import Optional, Tuple

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.io.file_reader import CastepFileReader
    from src.core.conductivity_calculator import ConductivityCalculator
    from src.io.result_exporter import ResultExporter
    from src.utils.constants import DEFAULT_FIT_POINTS, DEFAULT_ENERGY_THRESHOLD
except ImportError as e:
    st.error(f"导入模块失败: {e}")
    st.stop()

# 页面配置
st.set_page_config(
    page_title="CASTEP电导率计算器",
    page_icon="⚡",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 2rem;
}
.sub-header {
    font-size: 1.5rem;
    color: #ff7f0e;
    margin-top: 2rem;
    margin-bottom: 1rem;
}
.result-box {
    background-color: #f0f2f6;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 5px solid #1f77b4;
}
</style>
""", unsafe_allow_html=True)

class ConductivityGUI:
    """电导率计算GUI类"""
    
    def __init__(self):
        self.reader = CastepFileReader()
        self.calculator = ConductivityCalculator()
        self.exporter = ResultExporter()
        
        # 初始化session state
        if 'data_loaded' not in st.session_state:
            st.session_state.data_loaded = False
        if 'calculation_done' not in st.session_state:
            st.session_state.calculation_done = False
    
    def load_data_from_upload(self, uploaded_file) -> Optional[Tuple[np.ndarray, np.ndarray, np.ndarray]]:
        """从上传的文件加载数据"""
        try:
            # 保存上传的文件到临时位置
            temp_path = f"temp_{uploaded_file.name}"
            with open(temp_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            
            # 读取数据
            energy, epsilon_1, epsilon_2 = self.reader.read_epsilon_file(temp_path)
            
            # 清理临时文件
            os.remove(temp_path)
            
            return energy, epsilon_1, epsilon_2
            
        except Exception as e:
            st.error(f"文件读取失败: {str(e)}")
            return None
    
    def create_conductivity_chart(self, energy: np.ndarray, sigma_omega: np.ndarray) -> alt.Chart:
        """创建光学导率图表"""
        # 准备数据
        df = pd.DataFrame({
            'Energy (eV)': energy,
            'Conductivity (S/m)': sigma_omega,
            'Log Conductivity': np.log10(sigma_omega + 1e-20)  # 避免log(0)
        })
        
        # 创建对数坐标图表
        chart = alt.Chart(df).mark_line(
            color='#ff7f0e',
            strokeWidth=2
        ).add_selection(
            alt.selection_interval(bind='scales')
        ).encode(
            x=alt.X('Energy (eV):Q', scale=alt.Scale(type='log')),
            y=alt.Y('Log Conductivity:Q', title='log₁₀[σ(ω)] (S/m)'),
            tooltip=['Energy (eV):Q', 'Conductivity (S/m):Q']
        ).properties(
            width=600,
            height=400,
            title='光学导率谱 (对数坐标)'
        )
        
        return chart
    
    def create_epsilon_chart(self, energy: np.ndarray, epsilon_1: np.ndarray, epsilon_2: np.ndarray) -> alt.Chart:
        """创建介电函数图表"""
        # 准备数据
        df = pd.DataFrame({
            'Energy (eV)': np.concatenate([energy, energy]),
            'Epsilon': np.concatenate([epsilon_1, epsilon_2]),
            'Component': ['ε₁'] * len(energy) + ['ε₂'] * len(energy)
        })
        
        # 创建图表
        chart = alt.Chart(df).mark_line(
            strokeWidth=2
        ).add_selection(
            alt.selection_interval(bind='scales')
        ).encode(
            x='Energy (eV):Q',
            y='Epsilon:Q',
            color=alt.Color('Component:N', scale=alt.Scale(range=['#1f77b4', '#ff7f0e'])),
            tooltip=['Energy (eV):Q', 'Epsilon:Q', 'Component:N']
        ).properties(
            width=600,
            height=400,
            title='介电函数谱'
        )
        
        return chart
    
    def run(self):
        """运行GUI应用"""
        # 主标题
        st.markdown('<h1 class="main-header">⚡ CASTEP电导率计算器</h1>', unsafe_allow_html=True)
        
        # 侧边栏
        with st.sidebar:
            st.header("📁 数据输入")
            
            # 文件上传
            uploaded_file = st.file_uploader(
                "选择CASTEP介电数据文件",
                type=['epsilon', 'csv', 'txt', 'xlsx', 'xls'],
                help="支持.epsilon, .csv, .txt, .xlsx格式"
            )
            
            if uploaded_file is not None:
                st.success(f"已选择文件: {uploaded_file.name}")
                
                # 加载数据按钮
                if st.button("📊 加载数据", type="primary"):
                    with st.spinner("正在读取文件..."):
                        result = self.load_data_from_upload(uploaded_file)
                        
                        if result is not None:
                            energy, epsilon_1, epsilon_2 = result
                            
                            # 存储到session state
                            st.session_state.energy = energy
                            st.session_state.epsilon_1 = epsilon_1
                            st.session_state.epsilon_2 = epsilon_2
                            st.session_state.data_loaded = True
                            
                            st.success(f"成功加载 {len(energy)} 个数据点!")
                            st.rerun()
            
            # 计算参数
            if st.session_state.data_loaded:
                st.header("⚙️ 计算参数")
                
                fit_points = st.slider(
                    "拟合点数",
                    min_value=2,
                    max_value=20,
                    value=DEFAULT_FIT_POINTS,
                    help="用于直流电导率外推的低能点数"
                )
                
                energy_threshold = st.number_input(
                    "能量阈值 (eV)",
                    min_value=0.001,
                    max_value=1.0,
                    value=DEFAULT_ENERGY_THRESHOLD,
                    step=0.01,
                    help="低能区域的能量上限"
                )
                
                method = st.selectbox(
                    "拟合方法",
                    ['linear', 'polynomial', 'exponential'],
                    index=0,
                    help="直流电导率外推的拟合方法"
                )
                
                # 开始计算按钮
                if st.button("🚀 开始计算", type="primary"):
                    with st.spinner("正在计算..."):
                        try:
                            # 计算光学导率
                            omega, sigma_omega = self.calculator.calculate_optical_conductivity(
                                st.session_state.energy, st.session_state.epsilon_2
                            )
                            
                            # 计算直流电导率
                            fit_result = self.calculator.extrapolate_dc_conductivity(
                                st.session_state.energy, sigma_omega, 
                                fit_points, energy_threshold, method
                            )
                            
                            # 存储结果
                            st.session_state.omega = omega
                            st.session_state.sigma_omega = sigma_omega
                            st.session_state.fit_result = fit_result
                            st.session_state.calculation_done = True
                            
                            st.success("计算完成!")
                            st.rerun()
                            
                        except Exception as e:
                            st.error(f"计算失败: {str(e)}")
        
        # 主内容区域
        if not st.session_state.data_loaded:
            st.info("👆 请在左侧上传CASTEP介电数据文件开始分析")
            
            # 显示使用说明
            with st.expander("📖 使用说明", expanded=True):
                st.markdown("""
                ### 支持的文件格式
                - **CASTEP .epsilon文件**: CASTEP光学性质计算的直接输出
                - **CSV/TXT文件**: 包含Energy, ε₁, ε₂三列的文本文件
                - **Excel文件**: .xlsx或.xls格式的电子表格
                
                ### 数据要求
                - 至少包含能量(eV)和介电函数虚部ε₂两列
                - 能量数据应为正值且单调递增
                - 建议数据点数 > 50 以获得更好的拟合效果
                
                ### 计算原理
                光学导率计算公式: **σ(ω) = ω·ε₂(ω)·ε₀**
                
                其中:
                - ω: 角频率 (rad/s)
                - ε₂(ω): 介电函数虚部
                - ε₀: 真空介电常数
                """)
        
        else:
            # 显示数据信息
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric(
                    "数据点数",
                    len(st.session_state.energy)
                )
            
            with col2:
                st.metric(
                    "能量范围 (eV)",
                    f"{st.session_state.energy[0]:.3f} - {st.session_state.energy[-1]:.1f}"
                )
            
            with col3:
                st.metric(
                    "ε₂范围",
                    f"{np.min(st.session_state.epsilon_2):.2e} - {np.max(st.session_state.epsilon_2):.2e}"
                )
            
            # 显示介电函数图表
            st.markdown('<h2 class="sub-header">📈 介电函数谱</h2>', unsafe_allow_html=True)
            epsilon_chart = self.create_epsilon_chart(
                st.session_state.energy, 
                st.session_state.epsilon_1, 
                st.session_state.epsilon_2
            )
            st.altair_chart(epsilon_chart, use_container_width=True)
            
            # 如果计算完成，显示结果
            if st.session_state.calculation_done:
                # 显示光学导率图表
                st.markdown('<h2 class="sub-header">⚡ 光学导率谱</h2>', unsafe_allow_html=True)
                conductivity_chart = self.create_conductivity_chart(
                    st.session_state.energy, 
                    st.session_state.sigma_omega
                )
                st.altair_chart(conductivity_chart, use_container_width=True)
                
                # 显示计算结果
                st.markdown('<h2 class="sub-header">📊 计算结果</h2>', unsafe_allow_html=True)
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.markdown('<div class="result-box">', unsafe_allow_html=True)
                    st.markdown("### 直流电导率")
                    sigma_dc = st.session_state.fit_result['sigma_dc']
                    st.markdown(f"**σ_dc = {sigma_dc:.6e} S/m**")
                    
                    r_squared = st.session_state.fit_result['r_squared']
                    st.markdown(f"拟合质量 R² = {r_squared:.6f}")
                    
                    method = st.session_state.fit_result['method']
                    st.markdown(f"拟合方法: {method}")
                    st.markdown('</div>', unsafe_allow_html=True)
                
                with col2:
                    st.markdown('<div class="result-box">', unsafe_allow_html=True)
                    st.markdown("### 光学导率统计")
                    sigma_min = np.min(st.session_state.sigma_omega)
                    sigma_max = np.max(st.session_state.sigma_omega)
                    sigma_mean = np.mean(st.session_state.sigma_omega)
                    
                    st.markdown(f"最小值: {sigma_min:.6e} S/m")
                    st.markdown(f"最大值: {sigma_max:.6e} S/m")
                    st.markdown(f"平均值: {sigma_mean:.6e} S/m")
                    st.markdown('</div>', unsafe_allow_html=True)
                
                # 结果下载
                st.markdown('<h2 class="sub-header">💾 结果下载</h2>', unsafe_allow_html=True)
                
                col1, col2 = st.columns(2)
                
                with col1:
                    # CSV下载
                    csv_data = self._prepare_csv_data()
                    st.download_button(
                        label="📄 下载CSV结果",
                        data=csv_data,
                        file_name="conductivity_results.csv",
                        mime="text/csv"
                    )
                
                with col2:
                    # 摘要报告下载
                    summary_data = self._prepare_summary_report()
                    st.download_button(
                        label="📋 下载摘要报告",
                        data=summary_data,
                        file_name="conductivity_summary.txt",
                        mime="text/plain"
                    )
    
    def _prepare_csv_data(self) -> str:
        """准备CSV数据"""
        df = pd.DataFrame({
            'Energy_eV': st.session_state.energy,
            'Angular_Frequency_rad_per_s': st.session_state.omega,
            'Frequency_Hz': st.session_state.omega / (2 * np.pi),
            'Optical_Conductivity_S_per_m': st.session_state.sigma_omega,
            'Epsilon_1_Real': st.session_state.epsilon_1,
            'Epsilon_2_Imaginary': st.session_state.epsilon_2
        })
        
        return df.to_csv(index=False)
    
    def _prepare_summary_report(self) -> str:
        """准备摘要报告"""
        report = f"""CASTEP电导率计算摘要报告
{'='*50}

基本信息:
数据点数: {len(st.session_state.energy)}
能量范围: {st.session_state.energy[0]:.6f} - {st.session_state.energy[-1]:.3f} eV

光学导率统计:
最小值: {np.min(st.session_state.sigma_omega):.6e} S/m
最大值: {np.max(st.session_state.sigma_omega):.6e} S/m
平均值: {np.mean(st.session_state.sigma_omega):.6e} S/m

直流电导率拟合结果:
拟合方法: {st.session_state.fit_result['method']}
拟合点数: {st.session_state.fit_result['fit_points']}
直流电导率 σ_dc: {st.session_state.fit_result['sigma_dc']:.6e} S/m
拟合质量 R²: {st.session_state.fit_result['r_squared']:.6f}

计算公式: σ(ω) = ω·ε₂(ω)·ε₀
"""
        return report

def main():
    """主函数"""
    gui = ConductivityGUI()
    gui.run()

if __name__ == "__main__":
    main()
