import os
import sys
import re
import time
import json
import uuid
import socket
import random
import string
import ctypes
import subprocess
import threading
import traceback
import webbrowser
import shutil
import psutil
from datetime import datetime
from tkinter import *
from tkinter import ttk, messagebox, filedialog, simpledialog
import tkinter as tk
from functools import partial

# 懒加载PyWinAuto和PyAutoGUI (这两个模块导入时间较长)
PYWINAUTO_AVAILABLE = False
try:
    # 这里不立即导入，延迟到实际需要时
    # 不要放在全局导入中，否则会延长启动时间
    PYWINAUTO_AVAILABLE = True
except:
    pass

# 程序初始化 - 禁用浏览器相关
def disable_web_browser():
    """禁用程序中所有可能打开浏览器的功能"""
    # 替换webbrowser.open为空函数
    def dummy_open(*args, **kwargs):
        return False
    
    webbrowser.open = dummy_open
    webbrowser.open_new = dummy_open
    webbrowser.open_new_tab = dummy_open
    
    # 如果已导入pyautogui，替换其热键功能，防止打开浏览器
    if 'pyautogui' in sys.modules:
        import pyautogui
        
        # 存储原始的热键函数
        original_hotkey = pyautogui.hotkey
        
        # 定义安全热键函数
        def safe_hotkey(*args, **kwargs):
            # 阻止可能打开网页的组合键
            blocked_keys = ['winleft', 'winright', 'win', 'command']
            if any(key in args for key in blocked_keys) and ('r' in args or 'e' in args):
                return  # 阻止Win+R, Win+E等
            
            # 安全的热键可以通过
            return original_hotkey(*args, **kwargs)
        
        # 替换pyautogui的热键函数
        pyautogui.hotkey = safe_hotkey

# 确保调用禁用web browser
disable_web_browser()

# 检查管理员权限
def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin() != 0
    except:
        return False

# 导入延迟加载的模块 - 仅在需要时调用此函数
def import_automation_modules():
    global PYWINAUTO_AVAILABLE
    
    if not PYWINAUTO_AVAILABLE:
        return False
        
    try:
        # 现在才真正导入这些模块
        global pywinauto, Application, find_windows, pyautogui
        
        # 设置导入超时，避免卡住
        import_timeout = threading.Timer(10, lambda: sys.exit(1))
        import_timeout.daemon = True
        import_timeout.start()
        
        import pyautogui
        from pywinauto.application import Application
        from pywinauto.findwindows import find_windows
        
        import_timeout.cancel()
        
        # 设置pyautogui的默认行为
        pyautogui.PAUSE = 0.1  # 设置操作间隔
        pyautogui.FAILSAFE = False  # 关闭故障保护
        
        return True
    except Exception as e:
        print(f"无法导入自动化模块: {str(e)}")
        PYWINAUTO_AVAILABLE = False
        return False

# 禁用自动打开网页功能
def disable_web_browser():
    """禁用网页浏览器自动打开功能"""
    # 重写webbrowser.open和webbrowser.open_new方法，防止自动打开网页
    def dummy_open(*args, **kwargs):
        print(f"[网页打开已阻止] 尝试打开: {args[0] if args else ''}")
        return True
    
    webbrowser.open = dummy_open
    webbrowser.open_new = dummy_open
    webbrowser.open_new_tab = dummy_open

# 立即禁用网页浏览器
disable_web_browser()

# 禁用pyautogui可能的网页打开
try:
    import pyautogui
    original_hotkey = pyautogui.hotkey
    def safe_hotkey(*args, **kwargs):
        # 阻止可能打开网页的组合键
        if len(args) >= 2 and args[0].lower() in ['win', 'command', 'ctrl'] and args[1].lower() == 'r':
            print("[阻止] 阻止了可能打开网页的快捷键")
            return
        return original_hotkey(*args, **kwargs)
    
    pyautogui.hotkey = safe_hotkey
except ImportError:
    pass

# 尝试导入pywinauto和pyautogui用于自动处理对话框
try:
    print("正在尝试导入自动化模块...")
    from pywinauto import Application, timings
    from pywinauto.findwindows import find_windows
    import pyautogui
    print("自动化模块导入成功")
    PYWINAUTO_AVAILABLE = True
except ImportError as e:
    print(f"自动化模块导入失败: {str(e)}")
    PYWINAUTO_AVAILABLE = False
    print("将使用有限功能继续")
except Exception as e:
    print(f"导入自动化模块时发生其他错误: {str(e)}")
    PYWINAUTO_AVAILABLE = False
    print("将使用有限功能继续")

# 确保程序以管理员权限运行
def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

if not is_admin():
    # 如果没有管理员权限，则请求管理员权限重新运行
    ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, " ".join(sys.argv), None, 1)
    sys.exit()

# 配置文件存储位置
def get_config_path():
    app_data = os.getenv('APPDATA')
    config_dir = os.path.join(app_data, 'MS_Uninstaller')
    if not os.path.exists(config_dir):
        os.makedirs(config_dir)
    return os.path.join(config_dir, 'config.json')

# 验证程序是否已使用过
def check_if_used():
    config_path = get_config_path()
    if os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
                if config.get('used', False):
                    return True
        except:
            pass
    return False

# 重置程序使用状态
def reset_used_state(reset_code):
    # 基于机器ID和固定盐值生成重置码
    machine_id = get_machine_id()
    expected_reset_code = generate_reset_code(machine_id)
    
    # 添加调试日志
    print(f"输入的重置码: '{reset_code}'")
    print(f"预期的重置码: '{expected_reset_code}'")
    print(f"机器ID: '{machine_id}'")
    
    # 确保两个码都转换为小写进行比较
    if reset_code.lower().strip() == expected_reset_code.lower().strip():
        config_path = get_config_path()
        if os.path.exists(config_path):
            try:
                os.remove(config_path)
                print("成功删除配置文件，重置完成")
                return True
            except Exception as e:
                print(f"删除配置文件失败: {str(e)}")
        else:
            print("配置文件不存在，创建空配置")
            # 如果配置文件不存在，则创建一个空的配置
            try:
                config_dir = os.path.dirname(config_path)
                if not os.path.exists(config_dir):
                    os.makedirs(config_dir)
                with open(config_path, 'w') as f:
                    json.dump({}, f)
                os.remove(config_path)
                print("成功创建并删除空配置，重置完成")
                return True
            except Exception as e:
                print(f"创建空配置失败: {str(e)}")
    else:
        print("重置码不匹配")
    return False

# 生成重置码 - 只有开发者才需要调用此函数
def generate_reset_code(machine_id):
    """生成重置码"""
    if not machine_id or len(machine_id) < 10:
        return "无效的机器码"
    
    # 清理输入，只保留字母和数字
    machine_id = ''.join(c for c in machine_id if c in string.ascii_letters + string.digits)
    
    # 使用确定性种子，确保生成的激活码可重复
    random.seed(f"MS_RESET_{machine_id}")
    
    # 生成10位重置码
    reset_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))
    return reset_code

# 标记程序已被使用
def mark_as_used():
    config_path = get_config_path()
    config = {'used': True, 'timestamp': datetime.now().isoformat()}
    with open(config_path, 'w') as f:
        json.dump(config, f)

# 获取计算机唯一标识符
def get_machine_id():
    # 使用更多硬件特征来生成稳定ID
    machine_id = str(uuid.getnode())
    disk_serial = ''
    try:
        c_drive = 'C:\\'
        if os.path.exists(c_drive):
            volume_info = subprocess.check_output('vol C:', shell=True).decode('utf-8')
            for line in volume_info.split('\n'):
                if 'Volume Serial Number' in line:
                    disk_serial = line.split(':')[1].strip()
                    break
    except:
        pass
    
    return machine_id + disk_serial

# 验证激活码是否有效
def validate_activation_code(code, machine_id):
    """验证激活码是否与当前机器匹配"""
    if not code or not machine_id:
        print("错误: 激活码或机器码为空")
        return False
    
    # 调试信息
    print("\n---------- 激活码验证调试信息 ----------")
    print(f"原始机器码: '{machine_id}'")
    
    # 清理输入
    cleaned_machine_id = ''.join(c for c in machine_id if c in string.ascii_letters + string.digits)
    print(f"清理后机器码: '{cleaned_machine_id}'")
    
    original_code = code
    code = code.strip().upper()
    print(f"原始激活码: '{original_code}'")
    print(f"处理后激活码: '{code}'")
    
    # 1. 检查特殊激活码
    special_codes = ["M3A7E5R1I9A2L8S6", "B5I9O3V7I1A4_2023"]
    if code in special_codes:
        print(f"✓ 使用特殊激活码: {code}")
        return True
    
    # 2. 检查管理员激活码
    admin_codes = ["MSADMIN2023UNLOCK", "MS2023RESETCODE16"]
    if code in admin_codes:
        print("✓ 使用管理员激活码")
        return True
    
    # 3. 使用确定性方法生成激活码进行验证
    seed_string = f"MS_ACTIVATION_{cleaned_machine_id}"
    print(f"随机数种子: '{seed_string}'")
    
    random.seed(seed_string)
    expected_code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=16))
    print(f"期望的激活码: '{expected_code}'")
    print(f"输入的激活码: '{code}'")
    print(f"验证结果: {'✓ 成功' if code == expected_code else '✗ 失败'}")
    print("----------------------------------------\n")
    
    return code == expected_code

# 主应用类
class MSUninstallerApp:
    def __init__(self, root):
        self.root = root
        root.protocol("WM_DELETE_WINDOW", self.on_close)
        
        # 设置应用程序图标
        # self.set_app_icon()
        
        # 初始化变量
        self.current_frame = None
        self.welcome_frame = None
        self.progress_frame = None
        self.complete_frame = None
        
        self.machine_id = get_machine_id()[:12]  # 截断显示给用户的机器码
        self.full_machine_id = get_machine_id()  # 存储完整的机器码用于验证
        self.used_status = check_if_used()
        self.admin_mode = False  # 初始化管理员模式为关闭状态
        
        # 加载管理员密码
        self.admin_password = self.load_admin_password()
        print(f"加载管理员密码: {self.admin_password}")
        
        # 设置样式
        self.setup_style()
        
        # 显示免责声明对话框
        disclaimer_dialog = DisclaimerDialog(self.root)
        if not disclaimer_dialog.result:
            # 用户不同意，退出程序
            sys.exit(0)
        
        # 显示安装指南对话框
        installation_guide = InstallationGuideDialog(self.root)
        if not installation_guide.result:
            # 用户取消，退出程序
            sys.exit(0)
        
        # 创建主框架
        self.setup_ui()
        
        # 检查是否已经使用过
        if self.used_status and not self.is_reset_mode():
            self.show_reset_dialog()
        else:
            self.show_welcome_page()
    
    def setup_style(self):
        # 设置样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.style.configure('TButton', font=('微软雅黑', 11), background='#3498db', foreground='white')
        self.style.map('TButton', background=[('active', '#2980b9')])
        self.style.configure('TLabel', font=('微软雅黑', 12))
        self.style.configure('TEntry', font=('微软雅黑', 11))
        self.style.configure('TFrame', background='#f0f0f0')
        
        # 添加强调按钮样式
        self.style.configure('Accent.TButton', font=('微软雅黑', 12, 'bold'), 
                          background='#e74c3c', foreground='white')
        self.style.map('Accent.TButton', background=[('active', '#c0392b')])
        
        # 添加警告框样式
        self.style.configure('Alert.TFrame', background='#ffeb3b')
    
    def setup_ui(self):
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=BOTH, expand=True)
        
        # 创建不同的页面
        self.create_welcome_page()
        
        # 初始状态
        if check_if_used():
            self.show_already_used_message()
        else:
            self.show_welcome_page()
    
    def create_welcome_page(self):
        self.welcome_frame = ttk.Frame(self.main_frame)
        
        # 创建滚动容器
        canvas = Canvas(self.welcome_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.welcome_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 顶部装饰
        top_frame = ttk.Frame(scrollable_frame)
        top_frame.pack(fill=X, pady=20)
        
        title_label = ttk.Label(top_frame, text="Materials Studio 卸载助手", 
                               font=('微软雅黑', 24, 'bold'))
        title_label.pack(pady=10)
        
        subtitle_label = ttk.Label(top_frame, text="一次性使用，彻底卸载Materials Studio及相关组件", 
                                 font=('微软雅黑', 14))
        subtitle_label.pack(pady=5)
        
        # 添加管理员模式按钮（隐藏在右上角）
        admin_button = ttk.Button(top_frame, text="⚙", width=3, 
                               command=self.toggle_admin_mode)
        admin_button.place(relx=1.0, rely=0.0, anchor=NE)
        
        # 中间内容
        content_frame = ttk.Frame(scrollable_frame)
        content_frame.pack(fill=BOTH, expand=True, padx=50, pady=20)
        
        info_label = ttk.Label(content_frame, 
                             text="欢迎使用 Materials Studio 卸载助手。此工具将帮助您完全移除 Materials Studio 和相关组件。\n\n"
                                  "使用前请确保:\n"
                                  "1. 已关闭所有 Materials Studio 相关程序\n"
                                  "2. 此程序已放置于 Materials Studio 安装目录\n"
                                  "3. 您已备份所有重要数据\n"
                                  "4. 本助手由公众号：材料模拟路漫漫 提供，如有其他需求可以后台留言\n\n"
                                  "请输入激活序列码继续:",
                             font=('微软雅黑', 11), wraplength=600, justify=LEFT)
        info_label.pack(fill=X, pady=20, anchor=W)
        
        # 显示机器码，方便用户获取新激活码
        machine_id_frame = ttk.Frame(content_frame)
        machine_id_frame.pack(fill=X, pady=10)
        
        # 获取完整机器码
        full_machine_id = get_machine_id()
        # 截断机器码用于显示 - 重要：激活码验证也必须使用这个截断版本
        self.machine_id = full_machine_id[:12]
        
        machine_id_label = ttk.Label(machine_id_frame, text="您的机器码:", font=('微软雅黑', 11))
        machine_id_label.pack(side=LEFT, padx=(0, 10))
        
        machine_id_value = ttk.Label(machine_id_frame, text=self.machine_id, 
                                   font=('Consolas', 12, 'bold'))
        machine_id_value.pack(side=LEFT)
        
        # 添加复制按钮
        copy_button = ttk.Button(machine_id_frame, text="复制", width=8, 
                               command=lambda: self.copy_to_clipboard(self.machine_id))
        copy_button.pack(side=LEFT, padx=10)
        
        # 序列码输入框架
        code_frame = ttk.Frame(content_frame)
        code_frame.pack(fill=X, pady=10)
        
        self.activation_code = StringVar()
        code_label = ttk.Label(code_frame, text="激活序列码:", font=('微软雅黑', 11))
        code_label.pack(side=LEFT, padx=(0, 10))
        
        code_entry = ttk.Entry(code_frame, textvariable=self.activation_code, width=25, font=('Consolas', 12))
        code_entry.pack(side=LEFT, fill=X, expand=True)
        
        # 粘贴按钮
        paste_button = ttk.Button(code_frame, text="粘贴", width=8, 
                                command=self.paste_from_clipboard)
        paste_button.pack(side=LEFT, padx=10)
        
        # 管理员模式指示器 (初始隐藏)
        self.admin_indicator = ttk.Label(content_frame, text="管理员模式已启用", 
                                       font=('微软雅黑', 10, 'bold'), foreground='#e74c3c')
        if self.admin_mode:
            self.admin_indicator.pack(pady=5)
        
        # 添加高级清理选项
        options_frame = ttk.LabelFrame(content_frame, text="高级卸载选项")
        options_frame.pack(fill=X, pady=10)
        
        # 清理选项
        self.clean_temp = BooleanVar(value=True)
        self.clean_reg = BooleanVar(value=True)
        self.clean_user_data = BooleanVar(value=True)  
        self.save_log = BooleanVar(value=False)  # 隐藏但仍保留变量
        
        # 临时文件清理选项
        temp_check = ttk.Checkbutton(options_frame, text="清理临时文件和缓存", 
                                  variable=self.clean_temp)
        temp_check.pack(anchor=W, padx=20, pady=5)
        
        # 注册表清理选项
        reg_check = ttk.Checkbutton(options_frame, text="清理注册表项", 
                                 variable=self.clean_reg)
        reg_check.pack(anchor=W, padx=20, pady=5)
        
        # 用户数据清理选项
        data_check = ttk.Checkbutton(options_frame, text="清理用户数据和配置文件（可能包含您的自定义设置）", 
                                  variable=self.clean_user_data)
        data_check.pack(anchor=W, padx=20, pady=5)
        
        # 日志保存选项 - 已移除
        # log_check = ttk.Checkbutton(options_frame, text="保存卸载日志到桌面", 
        #                          variable=self.save_log)
        # log_check.pack(anchor=W, padx=20, pady=5)
        
        # 帮助说明
        option_tip = ttk.Label(options_frame, text="提示: 为确保完全卸载，建议启用所有清理选项", 
                             font=('微软雅黑', 9), foreground='#7f8c8d')
        option_tip.pack(padx=20, pady=5, anchor=W)
        
        note_label = ttk.Label(content_frame, 
                             text="注意：此卸载工具为一次性使用，卸载完成后需要使用重置码才能再次使用。",
                             font=('微软雅黑', 10), foreground='#7f8c8d')
        note_label.pack(pady=10)
        
        # 按钮区域
        button_frame = ttk.Frame(content_frame)
        button_frame.pack(fill=X, pady=20)
        
        submit_button = ttk.Button(button_frame, text="开始卸载", 
                                 command=self.validate_and_start, style='Accent.TButton')
        submit_button.pack(side=RIGHT)
        
        self.canvas = canvas  # 存储canvas便于后续使用
    
    def copy_to_clipboard(self, text):
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        self.root.update()  # 立即更新剪贴板
        messagebox.showinfo("复制成功", "已复制到剪贴板")
    
    def is_reset_mode(self):
        """检查是否为重置模式"""
        # 获取命令行参数，检查是否包含reset参数
        return len(sys.argv) > 1 and "reset" in sys.argv[1].lower()
    
    def paste_from_clipboard(self):
        try:
            clipboard_text = self.root.clipboard_get()
            self.activation_code.set(clipboard_text)
        except:
            messagebox.showerror("错误", "剪贴板中没有文本内容")
    
    def create_progress_page(self):
        self.progress_frame = ttk.Frame(self.main_frame)
        
        # 顶部标题
        title_label = ttk.Label(self.progress_frame, text="正在卸载 Materials Studio",
                               font=('微软雅黑', 18, 'bold'))
        title_label.pack(pady=20)
        
        # 进度显示区域 - 仅在管理员模式下显示
        if self.admin_mode:
            self.progress_area = Text(self.progress_frame, wrap=WORD, height=15, width=70,
                                   font=('Consolas', 10), state=DISABLED)
            self.progress_area.pack(fill=BOTH, expand=True, padx=50, pady=10)
        else:
            # 在非管理员模式下使用隐藏的Text对象接收日志，但不显示
            self.progress_area = Text(self.progress_frame, wrap=WORD, height=1, width=1,
                                   font=('Consolas', 10), state=DISABLED)
            self.progress_area.pack_forget()  # 不显示给用户
            
            # 添加一个图片或提示信息代替详细过程
            info_label = ttk.Label(self.progress_frame, 
                                 text="正在卸载，请耐心等待...\n\n卸载过程中可能会弹出多个对话框，程序会自动处理。",
                                 font=('微软雅黑', 12), wraplength=600, justify=CENTER)
            info_label.pack(fill=BOTH, expand=True, padx=50, pady=50)
        
        # 进度条
        progress_label = ttk.Label(self.progress_frame, text="卸载进度:")
        progress_label.pack(padx=50, anchor=W, pady=(20, 5))
        
        self.progress_var = DoubleVar()
        self.progress_bar = ttk.Progressbar(self.progress_frame, variable=self.progress_var, 
                                         length=700, mode='determinate')
        self.progress_bar.pack(padx=50, pady=5)
        
        self.progress_text = StringVar(value="准备卸载...")
        progress_status = ttk.Label(self.progress_frame, textvariable=self.progress_text)
        progress_status.pack(padx=50, anchor=W, pady=5)
        
        # 小提示
        tip_label = ttk.Label(self.progress_frame, 
                            text="卸载过程需要一些时间，请勿关闭此窗口或操作电脑。",
                            font=('微软雅黑', 10), foreground='#e74c3c')
        tip_label.pack(pady=20)
    
    def create_completion_page(self):
        self.completion_frame = ttk.Frame(self.main_frame)
        
        # 成功图标/动画可以在这里添加
        
        title_label = ttk.Label(self.completion_frame, text="卸载完成!",
                               font=('微软雅黑', 22, 'bold'), foreground='#27ae60')
        title_label.pack(pady=30)
        
        message_label = ttk.Label(self.completion_frame, 
                                text="Materials Studio 及其相关组件已成功卸载。\n您的系统现在已清理完毕。",
                                font=('微软雅黑', 12), wraplength=600, justify=CENTER)
        message_label.pack(pady=20)
        
        # 详细信息框
        details_frame = ttk.LabelFrame(self.completion_frame, text="卸载详情")
        details_frame.pack(fill=BOTH, expand=True, padx=50, pady=20)
        
        self.details_text = Text(details_frame, wrap=WORD, height=12, width=70,
                              font=('Consolas', 10), state=DISABLED)
        self.details_text.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # 完成按钮
        finish_button = ttk.Button(self.completion_frame, text="完成并退出", 
                                 command=self.on_close)
        finish_button.pack(pady=20)
    
    def show_welcome_page(self):
        self.hide_all_frames()
        self.welcome_frame.pack(fill=BOTH, expand=True)
    
    def show_progress_page(self):
        self.hide_all_frames()
        if not hasattr(self, 'progress_frame'):
            self.create_progress_page()
        self.progress_frame.pack(fill=BOTH, expand=True)
    
    def show_completion_page(self):
        self.hide_all_frames()
        if not hasattr(self, 'completion_frame'):
            self.create_completion_page()
        self.completion_frame.pack(fill=BOTH, expand=True)
    
    def hide_all_frames(self):
        # 安全地隐藏所有frame
        if hasattr(self, 'welcome_frame') and self.welcome_frame is not None:
            self.welcome_frame.pack_forget()
        
        if hasattr(self, 'progress_frame') and self.progress_frame is not None:
            self.progress_frame.pack_forget()
        
        if hasattr(self, 'completion_frame') and self.completion_frame is not None:
            self.completion_frame.pack_forget()
    
    def validate_and_start(self):
        try:
            code = self.activation_code.get().strip()
            if not code:
                messagebox.showerror("错误", "请输入激活序列码")
                return
            
            # 使用截断的机器码进行验证！这是关键改动
            # 使用显示给用户的相同机器码（截断版本）
            machine_id = self.machine_id  # 使用已截断的机器码
            
            # 调试信息
            print(f"验证使用的机器码: {machine_id}（截断版本，与界面显示一致）")
            
            # 使用更简化的验证方法
            if not validate_activation_code(code, machine_id):
                messagebox.showerror("错误", "激活序列码无效，请检查后重试")
                return
            
            # 确认是否继续
            if not messagebox.askyesno("确认操作", 
                                   "即将开始卸载 Materials Studio 及相关组件。\n此操作不可撤销！确定继续吗？"):
                return
            
            # 确保进度页面已创建
            if not hasattr(self, 'progress_frame') or self.progress_frame is None:
                print("创建进度页面...")
                self.create_progress_page()
            
            # 记录开始卸载时间
            self.uninstall_start_time = datetime.now()
            print(f"开始卸载时间: {self.uninstall_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 先显示进度页面，再启动线程
            print("显示进度页面...")
            self.show_progress_page()
            
            # 确保界面更新
            self.root.update()
            
            print("启动卸载线程...")
            # 开始卸载流程
            self.uninstall_thread = threading.Thread(target=self.perform_uninstallation)
            self.uninstall_thread.daemon = True
            self.uninstall_thread.start()
            print("卸载线程已启动")
            
        except Exception as e:
            error_msg = f"启动卸载过程时出错: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            messagebox.showerror("错误", error_msg)
    
    def perform_uninstallation(self):
        print("开始执行卸载过程...")
        try:
            # 先尝试写一条日志，验证日志系统是否正常工作
            try:
                self.log_progress(f"开始卸载时间: {self.uninstall_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
                print("日志系统正常工作")
            except Exception as log_error:
                print(f"日志系统出现问题: {str(log_error)}")
                # 如果日志系统不工作，尝试重新初始化progress_area
                try:
                    if hasattr(self, 'progress_area'):
                        self.progress_area.config(state=NORMAL)
                        self.progress_area.delete(1.0, END)
                        self.progress_area.insert(END, "正在初始化日志系统...\n")
                        self.progress_area.config(state=DISABLED)
                    print("重新初始化日志系统")
                except:
                    print("无法重新初始化日志系统")
            
            # 标记为已使用
            print("标记程序为已使用...")
            mark_as_used()
            
            # 设置超时保护
            print("设置超时保护...")
            self.uninstall_timeout = threading.Timer(30 * 60, self.handle_timeout)  # 30分钟超时
            self.uninstall_timeout.daemon = True
            self.uninstall_timeout.start()
            
            # 导入自动化模块 - 延迟初始化
            print("尝试导入自动化模块...")
            automation_available = import_automation_modules()
            print(f"自动化模块可用: {automation_available}")
            
            # 启动增强的对话框自动响应线程
            if automation_available:
                self.log_progress("正在初始化自动化模块...")
                
                # 主对话框处理线程
                print("启动对话框处理线程...")
                dialog_handler_thread = threading.Thread(target=self.auto_handle_dialogs)
                dialog_handler_thread.daemon = True
                dialog_handler_thread.start()
                
                # InstallShield专用处理线程
                print("启动InstallShield处理线程...")
                installshield_handler = threading.Thread(target=self.handle_installshield_errors)
                installshield_handler.daemon = True
                installshield_handler.start()
                
                # 浏览器窗口监控线程
                print("启动浏览器监控线程...")
                browser_monitor = threading.Thread(target=self.monitor_and_close_browsers)
                browser_monitor.daemon = True
                browser_monitor.start()
                
                self.log_progress("已启动自动对话框处理，卸载过程将自动进行")
            else:
                self.log_progress("警告: 未能导入自动化模块，无法自动处理对话框。如有弹窗请手动点击。")
                print("警告: 无法使用自动化模块，将使用有限功能继续")
            
            # 执行卸载步骤
            print("更新进度条: 0%")
            self.update_progress(0, "开始卸载过程...")
            self.log_progress("开始卸载 Materials Studio 及相关组件")
            time.sleep(1)
            
            # 1. 查找并运行 setup.exe 进行卸载
            self.update_progress(5, "正在查找卸载程序...")
            self.log_progress("搜索 setup.exe 卸载程序...")
            
            # 获取当前目录(应该是MS安装目录)
            current_dir = os.getcwd()
            setup_path = None
            
            # 尝试查找setup.exe
            for root, dirs, files in os.walk(current_dir):
                if "setup.exe" in files:
                    setup_path = os.path.join(root, "setup.exe")
                    break
            
            if not setup_path:
                self.log_progress("错误: 未找到 setup.exe，请确保程序放置在 Materials Studio 安装目录")
                self.update_progress(100, "卸载失败")
                self.show_failure()
                return
            
            self.log_progress(f"找到卸载程序: {setup_path}")
            self.update_progress(10, "开始卸载主程序...")
            
            # 运行setup.exe进行卸载
            self.log_progress("启动 Materials Studio 卸载向导")
            self.update_progress(15, "正在卸载主程序...")
            
            # 执行实际的卸载命令
            try:
                # 运行setup.exe卸载程序 - 使用静默卸载参数避免交互
                uninstall_cmd = f'"{setup_path}" /uninstall /silent'
                self.log_progress(f"执行卸载命令: {uninstall_cmd}")
                
                # 执行卸载命令
                subprocess.call(uninstall_cmd, shell=True)
                self.log_progress("主程序卸载命令执行完成")
            except Exception as e:
                self.log_progress(f"执行卸载命令出错: {str(e)}")
                
            # 特别处理：使用msiexec强制卸载
            try:
                # 尝试查找产品代码
                product_code_path = os.path.join(os.path.dirname(setup_path), "ProductCode.txt")
                if os.path.exists(product_code_path):
                    with open(product_code_path, 'r') as f:
                        product_code = f.read().strip()
                        if product_code and product_code.startswith('{') and product_code.endswith('}'):
                            self.log_progress(f"找到产品代码: {product_code}，尝试强制卸载")
                            msiexec_cmd = f'msiexec /x {product_code} /qn'
                            subprocess.call(msiexec_cmd, shell=True)
            except Exception as e:
                self.log_progress(f"尝试使用msiexec卸载时出错: {str(e)}")
            
            # 2. 从控制面板卸载所有BIOVIA和Materials Studio相关程序
            self.update_progress(40, "正在卸载所有BIOVIA和Materials Studio相关组件...")
            self.log_progress("从控制面板卸载所有相关组件")
            
            try:
                # 使用wmic命令查找所有相关产品
                search_terms = ['BIOVIA', 'Materials Studio', 'Accelrys']
                installed_products = []
                
                for term in search_terms:
                    self.log_progress(f"搜索安装的 {term} 相关产品...")
                    wmic_cmd = f'wmic product where "name like \'%{term}%\'" get name,IdentifyingNumber'
                    try:
                        wmic_result = subprocess.check_output(wmic_cmd, shell=True).decode('utf-8', errors='ignore')
                        self.log_progress(f"找到以下 {term} 相关产品:\n{wmic_result}")
                        
                        # 解析结果获取产品ID
                        for line in wmic_result.splitlines():
                            if '{' in line:  # 包含GUID
                                parts = line.split()
                                if parts and parts[0].startswith('{'):
                                    product_id = parts[0].strip()
                                    product_name = ' '.join(parts[1:])
                                    installed_products.append((product_id, product_name))
                    except Exception as e:
                        self.log_progress(f"搜索 {term} 相关产品时出错: {str(e)}")
                
                # 卸载找到的所有产品
                if installed_products:
                    self.log_progress(f"开始卸载 {len(installed_products)} 个相关软件组件...")
                    for product_id, product_name in installed_products:
                        try:
                            self.log_progress(f"正在卸载: {product_name}")
                            uninstall_cmd = f'msiexec /x {product_id} /quiet /norestart'
                            self.log_progress(f"执行卸载命令: {uninstall_cmd}")
                            subprocess.call(uninstall_cmd, shell=True)
                            self.log_progress(f"卸载完成: {product_name}")
                        except Exception as e:
                            self.log_progress(f"卸载产品出错 {product_name}: {str(e)}")
                else:
                    self.log_progress("未找到需要卸载的组件")
            except Exception as e:
                self.log_progress(f"控制面板卸载程序出错: {str(e)}")
            
            # 3. 停止并删除相关Windows服务
            self.update_progress(60, "正在停止并删除相关服务...")
            self.log_progress("检查相关Windows服务")
            
            try:
                services_to_check = ['Materials', 'BIOVIA', 'Accelrys']
                for service_name in services_to_check:
                    try:
                        # 查找含有相关关键词的服务
                        cmd = f'sc query state= all | findstr /i "{service_name}"'
                        result = subprocess.check_output(cmd, shell=True).decode('utf-8', errors='ignore')
                        
                        for line in result.splitlines():
                            if 'SERVICE_NAME' in line:
                                srv_name = line.split(':')[1].strip()
                                self.log_progress(f"发现服务: {srv_name}，正在停止并删除...")
                                
                                # 停止服务
                                try:
                                    subprocess.call(f'sc stop "{srv_name}"', shell=True)
                                    self.log_progress(f"服务 {srv_name} 停止成功")
                                except:
                                    self.log_progress(f"停止服务 {srv_name} 时出错")
                                    
                                # 删除服务
                                try:
                                    subprocess.call(f'sc delete "{srv_name}"', shell=True)
                                    self.log_progress(f"服务 {srv_name} 删除成功")
                                except:
                                    self.log_progress(f"删除服务 {srv_name} 时出错")
                    except:
                        self.log_progress(f"未找到 {service_name} 相关服务")
            except Exception as e:
                self.log_progress(f"处理服务时出错: {str(e)}")
            
            # 4. 清理所有文件夹
            self.update_progress(70, "正在清理所有相关文件夹...")
            
            # 搜索和删除C盘中的所有相关文件夹
            search_paths = ["C:\\", "C:\\Program Files", "C:\\Program Files (x86)", "C:\\ProgramData", 
                           "C:\\Users", "C:\\Windows\\Temp"]
            folder_keywords = ["BIOVIA", "Accelrys", "Materials Studio", "materials", "biovia", "accelrys", "discovery"]
            
            self.log_progress("开始搜索和删除所有相关文件夹...")
            
            # 1. 首先尝试使用dir命令搜索所有匹配的文件夹（更快更全面）
            try:
                for keyword in folder_keywords:
                    try:
                        self.log_progress(f"使用系统命令搜索包含 {keyword} 的文件夹...")
                        # 使用dir命令递归查找文件夹
                        dir_cmd = f'dir "C:\\*{keyword}*" /s /b /ad'
                        dir_result = subprocess.check_output(dir_cmd, shell=True).decode('utf-8', errors='ignore')
                        
                        # 处理搜索结果
                        for line in dir_result.splitlines():
                            folder_path = line.strip()
                            if os.path.exists(folder_path):
                                try:
                                    self.log_progress(f"发现文件夹: {folder_path}")
                                    # 尝试强制删除文件夹
                                    shutil.rmtree(folder_path)
                                    self.log_progress(f"已删除文件夹: {folder_path}")
                                except Exception as e:
                                    self.log_progress(f"删除文件夹失败: {folder_path}, 错误: {str(e)}")
                    except Exception as e:
                        self.log_progress(f"搜索 {keyword} 文件夹时出错: {str(e)}")
            except Exception as e:
                self.log_progress(f"使用dir命令搜索文件夹时出错: {str(e)}")
            
            # 2. 备用方法：手动遍历目录
            for base_path in search_paths:
                if os.path.exists(base_path):
                    self.log_progress(f"手动搜索路径: {base_path}")
                    
                    try:
                        for root, dirs, files in os.walk(base_path):
                            # 检查当前遍历的目录是否匹配关键词
                            current_dir = os.path.basename(root).lower()
                            for keyword in folder_keywords:
                                if keyword.lower() in current_dir:
                                    try:
                                        self.log_progress(f"发现匹配文件夹: {root}")
                                        # 尝试删除文件夹
                                        try:
                                            shutil.rmtree(root)
                                            self.log_progress(f"已删除文件夹: {root}")
                                        except Exception as e1:
                                            self.log_progress(f"使用shutil删除失败: {str(e1)}")
                                            try:
                                                subprocess.call(f'rmdir /s /q "{root}"', shell=True)
                                                self.log_progress(f"使用系统命令删除文件夹: {root}")
                                            except Exception as e2:
                                                self.log_progress(f"使用系统命令删除也失败: {str(e2)}")
                                        break  # 已找到匹配的关键词，跳出内循环
                                    except Exception as e:
                                        self.log_progress(f"处理文件夹时出错 {root}: {str(e)}")
                    except Exception as e:
                        self.log_progress(f"遍历路径出错 {base_path}: {str(e)}")
                        continue  # 继续下一个基础路径
            
            # 3. 搜索和删除单个文件
            self.log_progress("开始搜索和删除所有相关文件...")
            try:
                for keyword in folder_keywords:
                    try:
                        self.log_progress(f"使用系统命令搜索包含 {keyword} 的文件...")
                        # 使用dir命令递归查找文件
                        dir_cmd = f'dir "C:\\*{keyword}*.*" /s /b /a-d'
                        dir_result = subprocess.check_output(dir_cmd, shell=True, stderr=subprocess.STDOUT).decode('utf-8', errors='ignore')
                        
                        # 处理搜索结果
                        for line in dir_result.splitlines():
                            file_path = line.strip()
                            if os.path.exists(file_path) and os.path.isfile(file_path):
                                try:
                                    self.log_progress(f"发现文件: {file_path}")
                                    # 尝试删除文件
                                    os.remove(file_path)
                                    self.log_progress(f"已删除文件: {file_path}")
                                except Exception as e:
                                    self.log_progress(f"删除文件失败: {file_path}, 错误: {str(e)}")
                                    try:
                                        # 使用系统命令强制删除
                                        subprocess.call(f'del /f /q "{file_path}"', shell=True)
                                        self.log_progress(f"使用系统命令删除文件: {file_path}")
                                    except:
                                        pass
                    except Exception as e:
                        self.log_progress(f"搜索 {keyword} 文件时出错: {str(e)}")
            except Exception as e:
                self.log_progress(f"使用dir命令搜索文件时出错: {str(e)}")
            
            # 4. 特殊处理一些已知的常见路径
            special_paths = [
                "C:\\BIOVIA", 
                "C:\\Accelrys", 
                "C:\\Program Files\\BIOVIA", 
                "C:\\Program Files\\Accelrys",
                "C:\\Program Files (x86)\\BIOVIA", 
                "C:\\Program Files (x86)\\Accelrys",
                "C:\\ProgramData\\BIOVIA", 
                "C:\\ProgramData\\Accelrys"
            ]
            
            for path in special_paths:
                if os.path.exists(path):
                    self.log_progress(f"处理特殊路径: {path}")
                    try:
                        # 使用takeown获取所有权
                        subprocess.call(f'takeown /f "{path}" /r /d y', shell=True)
                        subprocess.call(f'icacls "{path}" /grant administrators:F /t', shell=True)
                        
                        # 强制删除
                        time.sleep(1)  # 等待权限生效
                        subprocess.call(f'rmdir /s /q "{path}"', shell=True)
                        self.log_progress(f"已删除特殊路径: {path}")
                    except Exception as e:
                        self.log_progress(f"删除特殊路径失败: {path}, 错误: {str(e)}")
            
            # 5. 清理注册表
            self.update_progress(85, "正在清理注册表...")
            self.log_progress("清理所有相关注册表项")
            
            try:
                # 清理各种位置下的相关注册表项
                registry_keys = [
                    # HKLM下的项
                    r"SOFTWARE\BIOVIA",
                    r"SOFTWARE\Accelrys",
                    r"SOFTWARE\Materials Studio",
                    r"SOFTWARE\WOW6432Node\BIOVIA",
                    r"SOFTWARE\WOW6432Node\Accelrys",
                    r"SOFTWARE\WOW6432Node\Materials Studio",
                    # HKCU下的项
                    r"SOFTWARE\BIOVIA",
                    r"SOFTWARE\Accelrys",
                    r"SOFTWARE\Materials Studio",
                ]
                
                # 清理HKLM下的项
                for key in registry_keys[:6]:  # 前6个是HKLM项
                    try:
                        subprocess.call(f'reg delete "HKLM\\{key}" /f', shell=True)
                        self.log_progress(f"已删除注册表项: HKLM\\{key}")
                    except:
                        self.log_progress(f"删除注册表项失败或不存在: HKLM\\{key}")
                
                # 清理HKCU下的项
                for key in registry_keys[6:]:  # 后3个是HKCU项
                    try:
                        subprocess.call(f'reg delete "HKCU\\{key}" /f', shell=True)
                        self.log_progress(f"已删除注册表项: HKCU\\{key}")
                    except:
                        self.log_progress(f"删除注册表项失败或不存在: HKCU\\{key}")
                
                # 查找并删除含有相关关键词的注册表项
                reg_keywords = ["BIOVIA", "Accelrys", "Materials Studio"]
                for keyword in reg_keywords:
                    try:
                        # 查找并删除含关键词的HKLM\SOFTWARE下的项
                        search_cmd = f'reg query "HKLM\\SOFTWARE" /s /f "{keyword}" /k'
                        reg_result = subprocess.check_output(search_cmd, shell=True, stderr=subprocess.STDOUT).decode('utf-8', errors='ignore')
                        
                        for line in reg_result.splitlines():
                            if 'HKEY_LOCAL_MACHINE' in line:
                                try:
                                    key_path = line.strip()
                                    self.log_progress(f"删除注册表项: {key_path}")
                                    subprocess.call(f'reg delete "{key_path}" /f', shell=True)
                                except:
                                    pass
                    except:
                        self.log_progress(f"未找到包含 {keyword} 的HKLM注册表项")
            except Exception as e:
                self.log_progress(f"清理注册表时出错: {str(e)}")
            
            # 6. 清理环境变量
            self.update_progress(95, "正在清理环境变量...")
            self.log_progress("检查和清理相关环境变量")
            
            try:
                # 获取系统环境变量PATH
                cmd = 'reg query "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment" /v PATH'
                path_result = subprocess.check_output(cmd, shell=True).decode('utf-8', errors='ignore')
                
                if 'PATH' in path_result:
                    path_value = path_result.split('REG_EXPAND_SZ')[1].strip() if 'REG_EXPAND_SZ' in path_result else path_result.split('REG_SZ')[1].strip()
                    path_entries = path_value.split(';')
                    new_path_entries = []
                    
                    # 筛选出不包含关键词的路径
                    for entry in path_entries:
                        contains_keyword = False
                        for keyword in folder_keywords:
                            if keyword.lower() in entry.lower():
                                contains_keyword = True
                                self.log_progress(f"从PATH环境变量中移除: {entry}")
                                break
                        if not contains_keyword:
                            new_path_entries.append(entry)
                    
                    # 如果有变化，更新环境变量
                    if len(new_path_entries) < len(path_entries):
                        new_path = ';'.join(new_path_entries)
                        set_cmd = f'setx PATH "{new_path}" /M'
                        subprocess.call(set_cmd, shell=True)
                        self.log_progress("已更新系统PATH环境变量")
            except Exception as e:
                self.log_progress(f"清理环境变量时出错: {str(e)}")
            
            # 清理临时文件和缓存
            if hasattr(self, 'clean_temp') and self.clean_temp.get():
                self.update_progress(75, "正在清理临时文件和缓存...")
                self.log_progress("开始清理临时文件和应用缓存")
                self.clean_temporary_files()
            
            # 在成功卸载后保存日志
            if hasattr(self, 'save_log') and self.save_log.get():
                log_path = self.save_log_to_file()
                if log_path:
                    self.log_progress(f"卸载日志已保存到: {log_path}")
            
            # 卸载完成后计算耗时
            self.uninstall_end_time = datetime.now()
            elapsed_time = self.uninstall_end_time - self.uninstall_start_time
            
            # 计算小时、分钟和秒
            hours, remainder = divmod(elapsed_time.total_seconds(), 3600)
            minutes, seconds = divmod(remainder, 60)
            
            if hours > 0:
                elapsed_str = f"{int(hours)}小时{int(minutes)}分钟{int(seconds)}秒"
            elif minutes > 0:
                elapsed_str = f"{int(minutes)}分钟{int(seconds)}秒"
            else:
                elapsed_str = f"{int(seconds)}秒"
                
            self.elapsed_time_str = elapsed_str
            self.log_progress(f"卸载总耗时: {elapsed_str}")
            
            # 取消超时计时器
            if hasattr(self, 'uninstall_timeout') and self.uninstall_timeout.is_alive():
                self.uninstall_timeout.cancel()
            
            # 完成卸载
            self.update_progress(100, "卸载完成!")
            self.log_progress("Materials Studio 彻底卸载完成!")
            
            # 更新完成页面的详情
            self.update_completion_details()
            
            # 显示完成页面
            self.root.after(1000, self.show_completion_page)
            
        except Exception as e:
            self.log_progress(f"卸载过程中出错: {str(e)}")
            self.update_progress(100, "卸载失败")
            messagebox.showerror("错误", f"卸载过程中发生错误:\n{str(e)}")
    
    def update_progress(self, value, text):
        def _update():
            try:
                self.progress_var.set(value)
                self.progress_text.set(text)
                # 确保进度条可见
                if hasattr(self, 'progress_bar'):
                    self.progress_bar.update()
            except Exception as e:
                print(f"更新进度条时出错: {str(e)}")
        try:
            self.root.after(0, _update)
        except Exception as e:
            print(f"调度进度条更新时出错: {str(e)}")
    
    def log_progress(self, message):
        def _log():
            try:
                if hasattr(self, 'progress_area') and self.progress_area is not None:
                    self.progress_area.config(state=NORMAL)
                    self.progress_area.insert(END, f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")
                    self.progress_area.see(END)
                    self.progress_area.config(state=DISABLED)
                else:
                    print(f"日志: {message}")
            except Exception as e:
                print(f"写入日志时出错: {str(e)}")
                print(f"日志内容: {message}")
        try:
            self.root.after(0, _log)
        except Exception as e:
            print(f"调度日志更新时出错: {str(e)}")
            print(f"日志内容: {message}")
    
    def update_completion_details(self):
        def _update():
            if hasattr(self, 'details_text'):
                self.details_text.config(state=NORMAL)
                self.details_text.insert(END, "卸载详情摘要:\n\n")
                self.details_text.insert(END, "✓ Materials Studio 主程序已卸载\n")
                self.details_text.insert(END, "✓ 控制面板中所有相关组件已卸载\n")
                self.details_text.insert(END, "✓ 相关Windows服务已停止并删除\n")
                self.details_text.insert(END, "✓ C盘中所有相关文件夹已清理\n")
                self.details_text.insert(END, "✓ 注册表项已清理\n")
                self.details_text.insert(END, "✓ 环境变量已清理\n\n")
                self.details_text.insert(END, f"卸载完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                self.details_text.insert(END, f"\n卸载总耗时: {self.elapsed_time_str}")
                self.details_text.config(state=DISABLED)
        self.root.after(0, _update)
    
    def show_already_used_message(self):
        result = messagebox.askyesno("程序已使用", 
                          "此卸载程序已被使用过。\n"
                          "您是否有重置码，想要重置使用状态？")
        if result:
            self.show_reset_dialog()
        else:
            self.root.destroy()
    
    def show_reset_dialog(self):
        # 创建重置对话框
        reset_dialog = Toplevel(self.root)
        reset_dialog.title("重置程序使用状态")
        reset_dialog.geometry("600x400")  # 增大初始尺寸
        reset_dialog.resizable(True, True)  # 允许调整大小
        reset_dialog.transient(self.root)
        reset_dialog.grab_set()
        
        # 创建带滚动条的容器
        canvas = Canvas(reset_dialog, highlightthickness=0)
        scrollbar = ttk.Scrollbar(reset_dialog, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 顶部标题
        ttk.Label(scrollable_frame, text="重置程序使用状态", 
                 font=('微软雅黑', 16, 'bold')).pack(pady=20)
        
        ttk.Label(scrollable_frame, text="请输入管理员提供的重置码:", 
                 font=('微软雅黑', 11)).pack(pady=5)
        
        # 显示机器码
        machine_id = get_machine_id()
        machine_id_frame = ttk.Frame(scrollable_frame)
        machine_id_frame.pack(fill=X, pady=10, padx=50)
        
        ttk.Label(machine_id_frame, text="您的机器码:", 
                font=('微软雅黑', 11)).pack(side=LEFT, padx=(0, 10))
        
        machine_id_value = ttk.Label(machine_id_frame, text=machine_id[:12], 
                                   font=('Consolas', 12, 'bold'))
        machine_id_value.pack(side=LEFT)
        
        # 添加复制按钮
        copy_button = ttk.Button(machine_id_frame, text="复制", width=8, 
                              command=lambda: self.copy_to_clipboard(machine_id))
        copy_button.pack(side=LEFT, padx=10)
        
        # 重置码输入
        reset_code_var = StringVar()
        reset_code_frame = ttk.Frame(scrollable_frame)
        reset_code_frame.pack(fill=X, pady=10, padx=50)
        
        ttk.Label(reset_code_frame, text="重置码:", 
                font=('微软雅黑', 11)).pack(side=LEFT, padx=(0, 10))
        
        reset_entry = ttk.Entry(reset_code_frame, textvariable=reset_code_var, 
                             width=20, font=('Consolas', 12))
        reset_entry.pack(side=LEFT, fill=X, expand=True)
        
        # 粘贴按钮
        paste_button = ttk.Button(reset_code_frame, text="粘贴", width=8, 
                               command=lambda: reset_code_var.set(self.root.clipboard_get()))
        paste_button.pack(side=LEFT, padx=10)
        
        # 操作说明
        ttk.Label(scrollable_frame, text="请联系管理员并提供您的机器码，获取重置码。", 
                font=('微软雅黑', 10), foreground='#e74c3c').pack(pady=10)
        
        # 调试信息区域
        debug_frame = ttk.Frame(scrollable_frame)
        debug_frame.pack(fill=X, pady=10, padx=50)
        debug_var = StringVar()
        debug_var.set("重置状态: 等待输入...")
        debug_label = ttk.Label(debug_frame, textvariable=debug_var, 
                              font=('Consolas', 10), foreground='#2980b9')
        debug_label.pack(fill=X)
        
        # 按钮框架
        button_frame = ttk.Frame(scrollable_frame)
        button_frame.pack(fill=X, pady=20, padx=50)
        
        # 重置按钮
        def attempt_reset():
            code = reset_code_var.get().strip()
            if not code:
                messagebox.showerror("错误", "请输入重置码")
                debug_var.set("重置状态: 未输入重置码")
                return
            
            # 显示正在处理
            debug_var.set(f"正在验证: '{code}'...")
            reset_dialog.update()
            
            # 预先计算预期重置码用于调试
            expected = generate_reset_code(machine_id)
            debug_var.set(f"验证中... 输入: {code}, 预期: {expected}")
            reset_dialog.update()
            
            if reset_used_state(code):
                debug_var.set("重置成功! 将关闭程序")
                reset_dialog.update()
                messagebox.showinfo("成功", "程序已成功重置，现在可以再次使用。")
                reset_dialog.destroy()
                self.root.destroy()  # 关闭主窗口，用户需要重新启动
            else:
                debug_var.set(f"重置失败! 输入码不匹配")
                messagebox.showerror("错误", "重置码无效，请检查后重试或联系管理员。")
        
        reset_button = ttk.Button(button_frame, text="重置", command=attempt_reset)
        reset_button.pack(side=RIGHT, padx=5)
        
        cancel_button = ttk.Button(button_frame, text="取消", 
                                command=lambda: [reset_dialog.destroy(), self.root.destroy()])
        cancel_button.pack(side=RIGHT, padx=5)
        
        # 添加额外的空间，确保所有内容可见
        ttk.Frame(scrollable_frame).pack(pady=30)
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    def show_failure(self):
        messagebox.showerror("卸载失败", 
                          "卸载过程中发生错误。\n请确保程序放置在正确的位置并以管理员身份运行。")
    
    def on_close(self):
        if hasattr(self, 'uninstall_thread') and self.uninstall_thread.is_alive():
            # 添加日志输出，记录on_close被调用
            print("警告: 检测到卸载过程中的退出请求")
            if not messagebox.askyesno("确认", "卸载过程正在进行中，确定要退出吗？"):
                return
        self.root.destroy()

    # 添加滚轮滚动支持
    def _on_mousewheel(self, event):
        if hasattr(self, 'canvas'):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def auto_handle_dialogs(self):
        """自动检测并处理弹出的对话框，点击"确定"、"是"等按钮"""
        if not PYWINAUTO_AVAILABLE:
            return
            
        # 创建已处理窗口集合，避免重复处理
        self._processed_windows = set()
        
        # 主程序窗口标题，用于排除
        main_window_titles = ["Materials Studio 卸载助手", "MS卸载助手", "卸载助手", "MS Uninstaller"]
        
        # 不处理的对话框标题，这些对话框可能是敏感操作或主程序自己的对话框
        skip_dialog_titles = ["确认", "确认操作", "MS卸载助手 - 确认", "Confirm", "Confirmation"]
        
        # 不处理的对话框内容，如果对话框文本包含以下内容，则跳过
        skip_dialog_contents = [
            "卸载过程正在进行中",
            "确定要退出",
            "进行中",
            "是否继续",
            "是否终止",
            "确认退出",
            "取消操作"
        ]
            
        # 扩展对话框标题列表，覆盖更多可能的对话框
        dialog_titles = [
            "确认", "卸载", "提示", "警告", "确定", "完成", "安装", "移除", "删除", "Microsoft", 
            "Setup", "Uninstall", "Confirmation", "Warning", "Information", "Question",
            "Error", "材料", "Materials", "BIOVIA", "Accelrys", "系统", "System", "UAC", 
            "User Account Control", "安全", "Security", "控制", "Control", "Install",
            "Remove", "Delete", "管理员", "Administrator", "要求", "服务", "Service", 
            "许可", "License", "Windows", "程序", "Program", "通知", "Notification", "继续", 
            "Continue", "权限", "Permission", "注册表", "Registry", "Windows 安全中心",
            "InstallShield", "Installation", "安装向导", "Wizard", "进度", "Progress", 
            # 添加MSN相关对话框和更多通用对话框
            "MSN", "Microsoft Edge", "Edge", "Internet Explorer", "浏览器", "Browser", "网页", "Web",
            # 添加更多可能的通用标题
            "选择", "Select", "选项", "Options", "应用", "Application", "软件", "Software", 
            "关闭", "Close", "退出", "Exit", "停止", "Stop", "终止", "Terminate",
            "更新", "Update", "下载", "Download", "配置", "Configuration", "重启", "Restart",
            "对话框", "Dialog", "窗口", "Window", "界面", "Interface", "问题", "Problem", 
            "初始化", "Initialize", "执行", "Execute", "运行", "Run", "完毕", "Done",
            # 添加"Finish"相关对话框
            "Finish", "完成", "Completed", "安装完成", "卸载完成", "Installation Complete", "Uninstallation Complete"
        ]
        
        # 优先处理的按钮文本列表 - 包括"Finish"按钮
        priority_buttons = [
            "Finish", "完成", "Next", "下一步", "继续", "Continue", "Done", "完毕", "Close", "关闭"
        ]
        
        # 扩展按钮文本列表
        button_texts = [
            "确定", "是", "OK", "Yes", "继续", "完成", "接受", "确认", "允许", "运行", "下一步", 
            "Next", "Finish", "Close", "关闭", "同意", "Agree", "I Agree", "我同意", "不再提示", 
            "忽略", "Ignore", "跳过", "Skip", "完成", "完毕", "确认删除", "确认卸载", "移除", 
            "卸载", "删除", "Remove", "Uninstall", "Delete", "授权", "Authorize", "同意", 
            "始终", "Always", "全部", "All", "安装", "Install", "执行", "Execute", "&OK",
            "&Yes", "是(&Y)", "确定(&O)", "继续(&C)", "允许(&A)", "运行(&R)",
            # 添加取消和拒绝相关按钮 - 某些情况下需要点击这些
            "不", "No", "拒绝", "Decline", "退出", "Exit", "取消", "不同意", "Disagree",
            "重试", "Retry", "重新尝试", "Try Again", "放弃", "Abort"
        ]
        
        # 动态按钮映射，适应不同语言环境
        button_ids = [
            "Button1", "Button2", "Button3", "Button4", "Button5",  # 通用按钮ID
            "IDOK", "IDYES", "IDNO", "IDCANCEL", "IDCONTINUE", "IDIGNORE", "IDRETRY", # 标准ID
            "&Yes", "&No", "&OK", "&Cancel", "&Continue", "&Ignore", "&Retry", # 带快捷键的按钮
            "1", "2", "3", "4", "5", "6", "OK", "Yes", "No", "Cancel" # 数字ID和简单ID
        ]
        
        self.log_progress("增强对话框自动处理已启动，将自动点击各种确认按钮")
        
        # 持续运行45分钟，足够完成卸载过程
        end_time = time.time() + 2700  # 45分钟
        
        # 窗口类型和标题的映射
        window_types = {
            "UAC": ["用户账户控制", "User Account Control", "UAC", "权限提升"],
            "InstallShield": ["InstallShield", "安装向导", "Wizard", "Setup"],
            "Error": ["错误", "Error", "警告", "Warning", "失败", "Failed"],
            "Browser": ["MSN", "Microsoft Edge", "Edge", "Internet Explorer", "Chrome", "Firefox"],
            "Finish": ["Finish", "完成", "Completed", "Done", "安装完成", "卸载完成"],  # 添加完成类型窗口
            "MainProgram": main_window_titles  # 添加主程序窗口类型
        }
        
        # 定义窗口处理优先级（数字越小越优先处理）
        window_priority = {"UAC": 1, "Error": 2, "Finish": 3, "InstallShield": 4, "Browser": 5}  # 完成对话框高优先级
        
        # 记录上次处理的窗口，避免重复频繁处理同一窗口
        last_processed = {"handle": None, "time": 0}
        
        while time.time() < end_time:
            try:
                # 收集所有窗口并按优先级排序
                all_windows = []
                
                # 1. 收集各类型窗口
                for win_type, keywords in window_types.items():
                    # 跳过主程序窗口类型的处理
                    if win_type == "MainProgram":
                        continue
                        
                    pattern = "|".join(f".*{kw}.*" for kw in keywords)
                    try:
                        windows = find_windows(title_re=pattern)
                        for win in windows:
                            # 跳过已处理的窗口或主程序窗口
                            if win in self._processed_windows:
                                continue
                                
                            try:
                                app = Application().connect(handle=win)
                                window = app.window(handle=win)
                                window_text = window.window_text()
                                
                                # 检查是否为主程序窗口
                                is_main_window = False
                                for main_title in main_window_titles:
                                    if main_title in window_text:
                                        is_main_window = True
                                        break
                                
                                # 检查是否是需要跳过的对话框标题
                                should_skip = is_main_window
                                if not should_skip:
                                    # 检查标题
                                    for skip_title in skip_dialog_titles:
                                        if skip_title in window_text:
                                            should_skip = True
                                            self.log_progress(f"跳过敏感对话框: {window_text}")
                                            break
                                
                                # 检查内容
                                if not should_skip:
                                    dialog_content = ""
                                    try:
                                        # 尝试获取窗口的文本内容
                                        for child in window.children():
                                            try:
                                                if hasattr(child, 'window_text'):
                                                    dialog_content += child.window_text() + " "
                                            except:
                                                pass
                                        
                                        # 检查是否包含需要跳过的内容
                                        for skip_content in skip_dialog_contents:
                                            if skip_content in dialog_content:
                                                should_skip = True
                                                self.log_progress(f"跳过敏感内容对话框: \"{dialog_content}\"")
                                                break
                                    except:
                                        pass
                                
                                if should_skip:
                                    continue
                                
                                all_windows.append({
                                    "type": win_type,
                                    "handle": win,
                                    "window": window,
                                    "text": window_text,
                                    "priority": window_priority.get(win_type, 99)  # 默认低优先级
                                })
                            except Exception as e:
                                self.log_progress(f"连接到窗口失败: {str(e)}")
                    except Exception as e:
                        self.log_progress(f"查找窗口类型 {win_type} 失败: {str(e)}")
                
                # 2. 收集所有通用对话框
                try:
                    generic_dialogs = find_windows(control_type="Dialog")
                    for win in generic_dialogs:
                        if win in self._processed_windows:
                            continue
                            
                        if win == last_processed["handle"] and time.time() - last_processed["time"] < 1:
                            continue  # 避免频繁处理同一窗口
                            
                        try:
                            app = Application().connect(handle=win)
                            window = app.window(handle=win)
                            window_text = window.window_text()
                            
                            # 检查是否为主程序窗口
                            is_main_window = False
                            for main_title in main_window_titles:
                                if main_title in window_text:
                                    is_main_window = True
                                    break
                            
                            if is_main_window:
                                self.log_progress(f"跳过主程序对话框: {window_text}")
                                continue
                                
                            # 确定窗口类型
                            window_type = "Generic"
                            for type_name, keywords in window_types.items():
                                if type_name != "MainProgram" and any(kw.lower() in window_text.lower() for kw in keywords):
                                    window_type = type_name
                                    break
                                    
                            # 检查是否有"Finish"或"完成"字样，优先处理
                            has_finish = "finish" in window_text.lower() or "完成" in window_text
                            if has_finish:
                                window_type = "Finish"  # 覆盖窗口类型
                                
                            all_windows.append({
                                "type": window_type,
                                "handle": win,
                                "window": window,
                                "text": window_text,
                                "priority": window_priority.get(window_type, 50),  # 通用对话框优先级中等
                                "has_finish": has_finish
                            })
                        except Exception as e:
                            continue  # 连接失败的窗口跳过
                except Exception as e:
                    self.log_progress(f"查找通用对话框失败: {str(e)}")
                
                # 3. 按优先级排序
                all_windows.sort(key=lambda x: x["priority"])
                
                # 处理找到的窗口
                if all_windows:
                    # 处理最高优先级的窗口
                    window_info = all_windows[0]
                    window = window_info["window"]
                    window_text = window_info["text"]
                    window_type = window_info["type"]
                    
                    # 检查是否是敏感对话框 - 判断标题
                    is_sensitive_dialog = False
                    for skip_title in skip_dialog_titles:
                        if skip_title in window_text:
                            is_sensitive_dialog = True
                            self.log_progress(f"跳过敏感对话框: {window_text}")
                            break
                    
                    # 检查对话框内容是否包含敏感词
                    if not is_sensitive_dialog:
                        try:
                            dialog_content = ""
                            # 尝试获取所有子控件的文本
                            for child in window.children():
                                try:
                                    if hasattr(child, 'window_text'):
                                        child_text = child.window_text()
                                        dialog_content += child_text + " "
                                        # 检查子控件文本是否包含敏感内容
                                        for skip_content in skip_dialog_contents:
                                            if skip_content in child_text:
                                                is_sensitive_dialog = True
                                                self.log_progress(f"在子控件中发现敏感内容: {skip_content}")
                                                break
                                except:
                                    pass
                                    
                            # 检查整个对话框内容
                            if not is_sensitive_dialog:
                                for skip_content in skip_dialog_contents:
                                    if skip_content in dialog_content:
                                        is_sensitive_dialog = True
                                        self.log_progress(f"检测到敏感对话框内容: {skip_content}")
                                        break
                        except Exception as e:
                            self.log_progress(f"检查对话框内容时出错: {str(e)}")
                    
                    # 如果是敏感对话框，跳过处理
                    if is_sensitive_dialog:
                        continue
                    
                    # 处理非敏感对话框
                    self.log_progress(f"处理 {window_type} 窗口: {window_text}")
                    last_processed["handle"] = window_info["handle"]
                    last_processed["time"] = time.time()
                    
                    # 根据窗口类型调用不同的处理策略
                    handled = False
                    
                    # 特殊处理Finish类型窗口
                    if window_type == "Finish":
                        self.log_progress("发现完成对话框，尝试点击Finish/完成按钮")
                        try:
                            # 优先尝试找到并点击"Finish"/"完成"按钮
                            for finish_btn in priority_buttons:
                                try:
                                    if window[finish_btn].exists():
                                        window[finish_btn].click()
                                        self.log_progress(f"在完成对话框中点击了 {finish_btn} 按钮")
                                        handled = True
                                        break
                                except:
                                    continue
                                    
                            if not handled:
                                # 回退到通用按钮处理
                                for btn_text in button_texts:
                                    try:
                                        if window[btn_text].exists():
                                            window[btn_text].click()
                                            self.log_progress(f"在完成对话框中点击了 {btn_text} 按钮")
                                            handled = True
                                            break
                                    except:
                                        continue
                                
                            if not handled:
                                # 尝试通过快捷键操作
                                window.set_focus()
                                pyautogui.press('enter')  # 通常回车键会选择默认按钮
                                self.log_progress("在完成对话框中按下了回车键")
                                handled = True
                        except Exception as e:
                            self.log_progress(f"处理完成对话框时出错: {str(e)}")
                    
                    # UAC 对话框特殊处理
                    elif window_type == "UAC":
                        try:
                            # 尝试多种方式点击"是"
                            for yes_btn in ["是(&Y)", "是", "Yes", "&Yes", "允许", "确认"]:
                                try:
                                    if window[yes_btn].exists():
                                        window[yes_btn].click()
                                        self.log_progress(f"点击了UAC对话框的 {yes_btn} 按钮")
                                        handled = True
                                        break
                                except:
                                    pass
                                    
                            if not handled:
                                # 使用左方向键+回车的组合（通常选择"是"）
                                window.set_focus()
                                pyautogui.press('left')
                                time.sleep(0.2)
                                pyautogui.press('enter')
                                self.log_progress("使用左方向键+回车处理UAC对话框")
                                handled = True
                        except Exception as e:
                            self.log_progress(f"处理UAC对话框时出错: {str(e)}")
                    
                    # InstallShield 对话框处理
                    elif window_type == "InstallShield":
                        try:
                            # 尝试点击常见按钮
                            for btn in ["下一步", "Next", "确定", "OK", "是", "Yes", "完成", "Finish"]:
                                try:
                                    if window[btn].exists():
                                        window[btn].click()
                                        self.log_progress(f"点击了InstallShield对话框的 {btn} 按钮")
                                        handled = True
                                        break
                                except:
                                    pass
                                    
                            if not handled:
                                # 尝试通过位置点击
                                rect = window.rectangle()
                                # 下一步按钮通常在右下角
                                right_bottom_x = rect.right - 70
                                right_bottom_y = rect.bottom - 30
                                pyautogui.click(right_bottom_x, right_bottom_y)
                                self.log_progress("点击InstallShield对话框右下角(通常是下一步按钮)")
                                handled = True
                        except Exception as e:
                            self.log_progress(f"处理InstallShield对话框时出错: {str(e)}")
                    
                    # 错误对话框处理
                    elif window_type == "Error":
                        try:
                            # 尝试点击确定按钮
                            for btn in ["确定", "OK", "重试", "Retry", "忽略", "Ignore", "关闭", "Close"]:
                                try:
                                    if window[btn].exists():
                                        window[btn].click()
                                        self.log_progress(f"点击了错误对话框的 {btn} 按钮")
                                        handled = True
                                        break
                                except:
                                    pass
                                    
                            if not handled:
                                # 尝试发送回车键
                                window.set_focus()
                                pyautogui.press('enter')
                                self.log_progress("使用回车键处理错误对话框")
                                handled = True
                        except Exception as e:
                            self.log_progress(f"处理错误对话框时出错: {str(e)}")
                    
                    # 浏览器窗口处理
                    elif window_type == "Browser":
                        try:
                            # 尝试关闭窗口
                            window.close()
                            self.log_progress("关闭了浏览器窗口")
                            handled = True
                        except:
                            try:
                                # 使用Alt+F4关闭
                                window.set_focus()
                                pyautogui.hotkey('alt', 'f4')
                                self.log_progress("使用Alt+F4关闭浏览器窗口")
                                handled = True
                            except Exception as e:
                                self.log_progress(f"关闭浏览器窗口时出错: {str(e)}")
                    
                    # 通用对话框处理
                    if not handled:
                        try:
                            # 尝试按钮文本
                            for btn_text in button_texts:
                                try:
                                    if window[btn_text].exists():
                                        window[btn_text].click()
                                        self.log_progress(f"点击了按钮: {btn_text}")
                                        handled = True
                                        break
                                except:
                                    pass
                            
                            # 尝试按钮ID
                            if not handled:
                                for btn_id in button_ids:
                                    try:
                                        if window[btn_id].exists():
                                            window[btn_id].click()
                                            self.log_progress(f"点击了按钮ID: {btn_id}")
                                            handled = True
                                            break
                                    except:
                                        pass
                            
                            # 尝试键盘操作
                            if not handled:
                                try:
                                    # 设置焦点
                                    window.set_focus()
                                    
                                    # 尝试发送常用快捷键
                                    window.type_keys("{ENTER}")
                                    time.sleep(0.2)
                                    window.type_keys("%y")  # Alt+Y (是)
                                    time.sleep(0.2)
                                    window.type_keys("%o")  # Alt+O (确定)
                                    
                                    # 使用Tab移动到按钮位置再按回车
                                    window.type_keys("{TAB}")
                                    time.sleep(0.1)
                                    window.type_keys("{ENTER}")
                                    
                                    self.log_progress("使用键盘快捷键处理对话框")
                                    handled = True
                                except Exception as e:
                                    self.log_progress(f"使用键盘处理对话框时出错: {str(e)}")
                            
                            # 最后尝试点击对话框下部中间位置（通常是按钮区域）
                            if not handled:
                                try:
                                    rect = window.rectangle()
                                    center_x = rect.left + (rect.right - rect.left) // 2
                                    
                                    # 尝试不同高度的点击
                                    for offset in [30, 40, 50, 60]:
                                        button_y = rect.bottom - offset
                                        pyautogui.click(center_x, button_y)
                                        self.log_progress(f"点击对话框位置 ({center_x}, {button_y})")
                                        time.sleep(0.2)
                                    
                                    handled = True
                                except Exception as e:
                                    self.log_progress(f"点击对话框位置时出错: {str(e)}")
                        except Exception as e:
                            self.log_progress(f"处理对话框时出错: {str(e)}")
                    
                    # 标记为已处理
                    if handled:
                        self._processed_windows.add(window_info["handle"])
                        
                        # 特殊窗口处理成功后短暂暂停，让系统有时间响应
                        if window_type in ["UAC", "InstallShield", "Error"]:
                            time.sleep(0.5)
                
                # 使用屏幕截图搜索通用确认按钮 - 这是最后的备份方法
                try:
                    # 使用pyautogui截图并寻找常见按钮
                    screenshot = pyautogui.screenshot()
                    
                    # 搜索常见按钮，如"确定"、"是"等
                    # 注意：这里需要提前准备按钮图片模板，存储在程序目录下
                    button_templates = [
                        "ok_button.png", "yes_button.png", "next_button.png", 
                        "finish_button.png", "close_button.png"
                    ]
                    
                    for template in button_templates:
                        # 跳过不存在的模板
                        template_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), template)
                        if not os.path.exists(template_path):
                            continue
                            
                        try:
                            # 使用pyautogui定位按钮
                            button_pos = pyautogui.locateOnScreen(template_path, confidence=0.7)
                            if button_pos:
                                button_center = pyautogui.center(button_pos)
                                pyautogui.click(button_center)
                                self.log_progress(f"通过图像识别点击了 {template} 按钮")
                                time.sleep(0.5)  # 等待响应
                        except Exception as e:
                            # 忽略图像搜索错误
                            pass
                except Exception as e:
                    # 忽略截图相关错误
                    pass
                
                # 休眠一段时间再检查
                time.sleep(0.5)  # 更频繁地检查
            except Exception as e:
                # 出现错误继续循环，不中断监控
                self.log_progress(f"对话框处理过程中出错: {str(e)}")
                time.sleep(1)
        
        self.log_progress("对话框自动处理已停止")

    def handle_installshield_errors(self):
        """专门处理InstallShield错误对话框以及Materials Studio卸载向导的线程"""
        if not PYWINAUTO_AVAILABLE:
            return
            
        self.log_progress("启动InstallShield和Materials Studio卸载向导处理线程")
        
        # 定义匹配关键词
        installshield_keywords = [
            "InstallShield", "Wizard", "安装向导", "Setup", "Accelrys", "BIOVIA", 
            "Materials Studio", "材料工作室", "安装程序", "卸载程序", "Uninstall",
            "安装", "卸载", "Install", "错误", "Error", "警告", "Warning",
            "Materials", "Studio", "MS", "向导", "Installer"
        ]
        
        # 定义可能的按钮文本
        button_texts = [
            "确定", "是", "OK", "Yes", "Next", "下一步", "继续", "完成", "取消",
            "Cancel", "Finish", "Close", "关闭", "重试", "Retry", "忽略", "Ignore",
            "Remove", "删除", "移除", "卸载", "Uninstall", "Install", "安装",
            "Back", "上一步", "返回", "Exit", "退出", "Abort", "终止", "Skip", "跳过"
        ]
        
        # 卸载过程中的关键步骤 - 根据卸载思路文档提供的步骤
        ms_uninstall_steps = [
            {
                "description": "点击Next开始卸载过程",
                "button": "Next",
                "alt_buttons": ["下一步", "继续", "OK", "确定"],
                "radio_options": [],
                "completed": False
            },
            {
                "description": "选择Remove选项而非Repair",
                "button": "Next",
                "alt_buttons": ["下一步", "继续", "OK", "确定"],
                "radio_options": ["Remove", "删除", "移除", "卸载", "Uninstall"],
                "radio_avoid": ["Repair", "修复", "Modify", "修改"],
                "click_positions": [
                    {"x_ratio": 0.3, "y_ratio": 0.4},  # 尝试点击左侧的Remove选项位置
                    {"x_ratio": 0.3, "y_ratio": 0.5},
                    {"x_ratio": 0.3, "y_ratio": 0.6}
                ],
                "completed": False
            },
            {
                "description": "再次点击Next继续",
                "button": "Next",
                "alt_buttons": ["下一步", "继续", "OK", "确定"],
                "radio_options": [],
                "completed": False
            },
            {
                "description": "点击Remove开始卸载",
                "button": "Remove",
                "alt_buttons": ["删除", "移除", "卸载", "Uninstall"],
                "radio_options": [],
                "completed": False
            },
            {
                "description": "卸载完成后点击Finish",
                "button": "Finish",
                "alt_buttons": ["完成", "结束", "Close", "关闭"],
                "radio_options": [],
                "completed": False
            }
        ]
        
        # 当前处理到的卸载步骤索引
        current_step_index = 0
        
        # InstallShield特有的按钮ID列表
        button_ids = [
            "IDOK", "IDYES", "IDCANCEL", "IDRETRY", "IDIGNORE", "IDCONTINUE",
            "Button1", "Button2", "Button3", "NextButton", "OkButton", "YesButton",
            "12324", "12325", "12326", "12327", "1", "2", "3", "4", "5", "6", "7"  # InstallShield常用的按钮ID
        ]
        
        # 持续运行30分钟
        end_time = time.time() + 1800
        
        # 记录已处理的窗口句柄
        processed_handles = set()
        
        # 上次处理时间
        last_action_time = 0
        
        # 卸载步骤完成标志
        all_steps_completed = False
        
        while time.time() < end_time:
            try:
                # 获取当前时间
                current_time = time.time()
                
                # 每隔一定时间强制执行下一个步骤，防止卡住
                if current_time - last_action_time > 30 and not all_steps_completed:  # 30秒超时
                    self.log_progress(f"已超过30秒未检测到操作，尝试执行下一步: {ms_uninstall_steps[current_step_index]['description']}")
                    current_step = ms_uninstall_steps[current_step_index]
                    current_step['completed'] = True
                    current_step_index = min(current_step_index + 1, len(ms_uninstall_steps) - 1)
                    last_action_time = current_time
                    self.log_progress(f"现在执行步骤 {current_step_index+1}: {ms_uninstall_steps[current_step_index]['description']}")
                
                # 检查是否所有步骤都已完成
                if current_step_index >= len(ms_uninstall_steps) - 1 and ms_uninstall_steps[-1]['completed']:
                    if not all_steps_completed:
                        self.log_progress("所有Materials Studio卸载向导步骤已完成!")
                        all_steps_completed = True
                
                # 捕获所有可能的InstallShield窗口和对话框
                pattern = "|".join(f".*{kw}.*" for kw in installshield_keywords)
                try:
                    windows = find_windows(title_re=pattern)
                    for win in windows:
                        # 跳过在短时间内已处理的窗口
                        if win in processed_handles and current_time - last_action_time < 5:
                            continue
                            
                        try:
                            app = Application().connect(handle=win)
                            window = app.window(handle=win)
                            window_text = window.window_text()
                            
                            # 跳过主程序窗口
                            if "Materials Studio 卸载助手" in window_text:
                                continue
                                
                            self.log_progress(f"发现对话框: {window_text}")
                            
                            # 截图当前窗口以便日志记录
                            try:
                                rect = window.rectangle()
                                # 使用pyautogui截图
                                screenshot = pyautogui.screenshot(region=(rect.left, rect.top, rect.right-rect.left, rect.bottom-rect.top))
                                # 可以保存到临时目录作为日志，这里暂不实现
                            except:
                                pass
                            
                            # 1. 判断窗口类型 - 卸载向导或错误对话框
                            is_uninstall_wizard = any(kw.lower() in window_text.lower() for kw in ["wizard", "向导", "材料", "studio", "卸载", "安装"])
                            is_error_dialog = any(kw.lower() in window_text.lower() for kw in ["error", "错误", "warning", "警告", "failed", "失败"])
                            
                            # 2. 获取窗口中的所有控件文本，帮助判断当前步骤
                            window_controls = []
                            try:
                                for child in window.children():
                                    try:
                                        if hasattr(child, 'window_text'):
                                            child_text = child.window_text()
                                            if child_text and len(child_text) > 1:  # 忽略空文本
                                                window_controls.append(child_text)
                                                self.log_progress(f"控件文本: {child_text}")
                                    except:
                                        pass
                            except:
                                pass
                            
                            # 当前处理步骤
                            if not all_steps_completed:
                                current_step = ms_uninstall_steps[current_step_index]
                                self.log_progress(f"当前处理步骤 {current_step_index+1}: {current_step['description']}")
                            
                            # 3. 处理卸载向导
                            if is_uninstall_wizard and not all_steps_completed:
                                handled = False
                                
                                # 根据当前步骤查找并点击相应按钮
                                current_step = ms_uninstall_steps[current_step_index]
                                step_button = current_step["button"]
                                all_buttons = [step_button] + current_step["alt_buttons"]
                                
                                # 调试输出当前窗口文本和控件
                                self.log_progress(f"当前窗口文本: '{window_text}'")
                                for text in window_controls:
                                    self.log_progress(f"控件: '{text}'")
                                
                                # 检查是否需要选择radio选项
                                if current_step.get("radio_options"):
                                    option_selected = False
                                    radio_options = current_step["radio_options"]
                                    radio_avoid = current_step.get("radio_avoid", [])
                                    
                                    # 1. 首先尝试识别并点击特定文本的单选框或其相关标签
                                    for child in window.children():
                                        try:
                                            if hasattr(child, 'window_text'):
                                                child_text = child.window_text()
                                                # 检查是否匹配目标选项
                                                if child_text and any(opt.lower() in child_text.lower() for opt in radio_options):
                                                    # 点击前先确认不是要避免的选项
                                                    if not any(avoid.lower() in child_text.lower() for avoid in radio_avoid):
                                                        # 尝试点击选项
                                                        child.click()
                                                        self.log_progress(f"点击了单选选项: '{child_text}'")
                                                        time.sleep(0.5)
                                                        option_selected = True
                                                        break
                                        except Exception as e:
                                            self.log_progress(f"点击单选选项失败: {str(e)}")
                                    
                                    # 2. 如果未能找到并点击选项，尝试查找RadioButton控件
                                    if not option_selected:
                                        try:
                                            # 查找所有单选按钮
                                            radio_buttons = window.children(control_type="RadioButton")
                                            if radio_buttons:
                                                # 查找与选项文本匹配的按钮
                                                for radio in radio_buttons:
                                                    try:
                                                        radio_text = radio.window_text()
                                                        if any(opt.lower() in radio_text.lower() for opt in radio_options):
                                                            radio.click()
                                                            self.log_progress(f"点击了RadioButton: '{radio_text}'")
                                                            time.sleep(0.5)
                                                            option_selected = True
                                                            break
                                                    except:
                                                        pass
                                                
                                                # 如果未找到匹配文本的单选按钮，尝试点击第一个
                                                if not option_selected and len(radio_buttons) > 0:
                                                    try:
                                                        radio_buttons[0].click()
                                                        self.log_progress(f"点击了第一个RadioButton")
                                                        time.sleep(0.5)
                                                        option_selected = True
                                                    except:
                                                        pass
                                        except:
                                            pass
                                    
                                    # 3. 尝试通过点击指定位置选择选项
                                    if not option_selected and "click_positions" in current_step:
                                        rect = window.rectangle()
                                        window_width = rect.right - rect.left
                                        window_height = rect.bottom - rect.top
                                        
                                        for pos in current_step["click_positions"]:
                                            try:
                                                click_x = rect.left + int(window_width * pos["x_ratio"])
                                                click_y = rect.top + int(window_height * pos["y_ratio"])
                                                pyautogui.click(click_x, click_y)
                                                self.log_progress(f"点击窗口中的位置({click_x}, {click_y})选择选项")
                                                time.sleep(0.5)
                                                option_selected = True
                                            except Exception as e:
                                                self.log_progress(f"点击位置选择选项失败: {str(e)}")
                                    
                                    # 尝试在点击Remove单选框后截图查看结果
                                    if option_selected:
                                        try:
                                            # 给UI时间更新
                                            time.sleep(0.2)
                                            screenshot = pyautogui.screenshot()
                                            # 日志记录已点击
                                            self.log_progress("已截图查看单选选项点击后的状态")
                                        except:
                                            pass
                                
                                # 尝试点击步骤对应的按钮
                                for btn in all_buttons:
                                    try:
                                        if window[btn].exists():
                                            window[btn].click()
                                            self.log_progress(f"点击了 {btn} 按钮，完成步骤: {current_step['description']}")
                                            current_step["completed"] = True
                                            current_step_index = min(current_step_index + 1, len(ms_uninstall_steps) - 1)
                                            handled = True
                                            processed_handles.add(win)
                                            last_action_time = current_time
                                            break
                                    except:
                                        pass
                                
                                # 尝试使用按钮ID
                                if not handled:
                                    for btn_id in button_ids:
                                        try:
                                            button = window[btn_id]
                                            if button.exists():
                                                # 检查按钮文本是否匹配当前步骤
                                                button_text = button.window_text()
                                                self.log_progress(f"发现按钮(ID {btn_id}): '{button_text}'")
                                                if any(btn.lower() in button_text.lower() for btn in all_buttons):
                                                    button.click()
                                                    self.log_progress(f"通过ID点击了按钮: {button_text}，完成步骤: {current_step['description']}")
                                                    current_step["completed"] = True
                                                    current_step_index = min(current_step_index + 1, len(ms_uninstall_steps) - 1)
                                                    handled = True
                                                    processed_handles.add(win)
                                                    last_action_time = current_time
                                                    break
                                        except Exception as e:
                                            pass
                                    
                                # 新增部分：尝试识别没有明确文本标识的按钮
                                if not handled:
                                    try:
                                        # 获取所有按钮类型的控件
                                        all_button_controls = window.children(control_type="Button")
                                        for btn_ctrl in all_button_controls:
                                            try:
                                                btn_text = btn_ctrl.window_text()
                                                self.log_progress(f"发现按钮控件: '{btn_text}'")
                                                
                                                # 检查是否匹配当前步骤的按钮
                                                if any(btn.lower() in btn_text.lower() for btn in all_buttons):
                                                    btn_ctrl.click()
                                                    self.log_progress(f"点击了按钮控件: {btn_text}，完成步骤: {current_step['description']}")
                                                    current_step["completed"] = True
                                                    current_step_index = min(current_step_index + 1, len(ms_uninstall_steps) - 1)
                                                    handled = True
                                                    processed_handles.add(win)
                                                    last_action_time = current_time
                                                    break
                                            except:
                                                pass
                                    except:
                                        pass

                                # 如果无法通过按钮文本或ID处理，尝试点击特定位置
                                if not handled:
                                    rect = window.rectangle()
                                    window_width = rect.right - rect.left
                                    window_height = rect.bottom - rect.top
                                    
                                    # 获取按钮的可能位置
                                    if current_step["button"] in ["Next", "下一步"]:
                                        # Next按钮通常在右下角
                                        button_positions = [
                                            {"x_ratio": 0.85, "y_ratio": 0.9},  # 右下角
                                            {"x_ratio": 0.9, "y_ratio": 0.85},  # 右下角变种
                                            {"x_ratio": 0.8, "y_ratio": 0.9},   # 稍微左一点
                                        ]
                                    elif current_step["button"] in ["Remove", "删除", "移除"]:
                                        # Remove按钮可能在中间位置(如果是单选项)或右下角(如果是按钮)
                                        button_positions = [
                                            {"x_ratio": 0.85, "y_ratio": 0.9},  # 右下角
                                            {"x_ratio": 0.8, "y_ratio": 0.9},   # 稍微左一点
                                            {"x_ratio": 0.85, "y_ratio": 0.85}   # 稍微上一点
                                        ]
                                    elif current_step["button"] in ["Finish", "完成"]:
                                        # Finish按钮通常在右下角
                                        button_positions = [
                                            {"x_ratio": 0.85, "y_ratio": 0.9},  # 右下角
                                            {"x_ratio": 0.9, "y_ratio": 0.85},  # 右下角变种
                                            {"x_ratio": 0.8, "y_ratio": 0.9},   # 稍微左一点
                                        ]
                                    else:
                                        # 默认位置
                                        button_positions = [
                                            {"x_ratio": 0.85, "y_ratio": 0.9},  # 右下角
                                        ]
                                    
                                    # 尝试点击各个位置
                                    for pos in button_positions:
                                        button_x = rect.left + int(window_width * pos["x_ratio"])
                                        button_y = rect.top + int(window_height * pos["y_ratio"])
                                        # 点击位置
                                        pyautogui.click(button_x, button_y)
                                        self.log_progress(f"点击位置 ({button_x}, {button_y}) 尝试完成步骤: {current_step['description']}")
                                        time.sleep(0.3)
                                    
                                    # 额外尝试Enter键
                                    window.set_focus()
                                    pyautogui.press('enter')
                                    
                                    # 标记步骤完成
                                    current_step["completed"] = True
                                    current_step_index = min(current_step_index + 1, len(ms_uninstall_steps) - 1)
                                    processed_handles.add(win)
                                    last_action_time = current_time
                                    self.log_progress(f"已尝试完成步骤，现在进入下一步骤: {ms_uninstall_steps[current_step_index]['description']}")
                            
                            # 4. 处理错误对话框
                            elif is_error_dialog:
                                self.log_progress(f"检测到错误对话框: {window_text}")
                                
                                # 尝试点击确定按钮
                                button_handled = False
                                for button_text in ["确定", "OK", "重试", "Retry", "忽略", "Ignore", "关闭", "Close", "是", "Yes"]:
                                    try:
                                        if window[button_text].exists():
                                            window[button_text].click()
                                            self.log_progress(f"点击了错误对话框按钮: {button_text}")
                                            button_handled = True
                                            processed_handles.add(win)
                                            last_action_time = current_time
                                            break
                                    except:
                                        pass
                                
                                # 如果没有找到标准按钮，尝试通用处理方法
                                if not button_handled:
                                    # 尝试通过ID
                                    for button_id in ["IDOK", "IDYES", "IDRETRY", "IDIGNORE", "IDCONTINUE", "1", "2"]:
                                        try:
                                            if window[button_id].exists():
                                                window[button_id].click()
                                                self.log_progress(f"通过ID点击了错误对话框按钮: {button_id}")
                                                button_handled = True
                                                processed_handles.add(win)
                                                last_action_time = current_time
                                                break
                                        except:
                                            pass
                                
                                # 尝试键盘按键
                                if not button_handled:
                                    window.set_focus()
                                    pyautogui.press('enter')
                                    time.sleep(0.2)
                                    pyautogui.press('space')
                                    self.log_progress("使用键盘处理错误对话框")
                                    processed_handles.add(win)
                                    last_action_time = current_time
                            
                            # 5. 通用处理 - 尝试匹配任何按钮
                            else:
                                button_handled = False
                                # 尝试所有可能的按钮文本
                                for button_text in button_texts:
                                    try:
                                        if window[button_text].exists():
                                            window[button_text].click()
                                            self.log_progress(f"点击了通用按钮: {button_text}")
                                            button_handled = True
                                            processed_handles.add(win)
                                            last_action_time = current_time
                                            break
                                    except:
                                        pass
                                
                                # 如果没有找到标准按钮，尝试通用处理方法
                                if not button_handled:
                                    # 尝试点击对话框右下角位置（通常是确定/下一步按钮）
                                    rect = window.rectangle()
                                    button_x = rect.right - 70
                                    button_y = rect.bottom - 30
                                    pyautogui.click(button_x, button_y)
                                    self.log_progress(f"点击对话框位置 ({button_x}, {button_y})")
                                    processed_handles.add(win)
                                    last_action_time = current_time
                        except Exception as e:
                            self.log_progress(f"处理对话框时出错: {str(e)}")
                except Exception as e:
                    self.log_progress(f"查找对话框时出错: {str(e)}")
                
                # 特别搜索特定类型的对话框 - 包括可能没有明确标题的
                try:
                    all_dialogs = find_windows(control_type="Dialog")
                    for win in all_dialogs:
                        if win in processed_handles and current_time - last_action_time < 5:
                            continue
                            
                        try:
                            app = Application().connect(handle=win)
                            window = app.window(handle=win)
                            window_text = window.window_text()
                            
                            # 跳过主程序窗口
                            if "Materials Studio 卸载助手" in window_text:
                                continue
                                
                            # 尝试获取窗口中的所有文本内容
                            dialog_texts = []
                            for child in window.children():
                                try:
                                    if hasattr(child, 'window_text'):
                                        child_text = child.window_text()
                                        if child_text and len(child_text) > 1:
                                            dialog_texts.append(child_text)
                                except:
                                    pass
                            
                            # 构建窗口中所有文本的组合字符串，用于匹配
                            combined_text = window_text + " " + " ".join(dialog_texts)
                            combined_text = combined_text.lower()
                            
                            # 检查是否为MS卸载相关对话框
                            is_related = any(kw.lower() in combined_text for kw in installshield_keywords)
                            
                            if is_related:
                                self.log_progress(f"发现相关对话框: {window_text}")
                                
                                # 根据对话框文本判断当前步骤
                                if not all_steps_completed:
                                    current_step = ms_uninstall_steps[current_step_index]
                                    
                                    # 尝试点击步骤对应的按钮
                                    button_handled = False
                                    all_buttons = [current_step["button"]] + current_step["alt_buttons"]
                                    
                                    for btn in all_buttons:
                                        try:
                                            if window[btn].exists():
                                                window[btn].click()
                                                self.log_progress(f"点击了 {btn} 按钮，完成步骤: {current_step['description']}")
                                                current_step["completed"] = True
                                                current_step_index = min(current_step_index + 1, len(ms_uninstall_steps) - 1)
                                                button_handled = True
                                                processed_handles.add(win)
                                                last_action_time = current_time
                                                break
                                        except:
                                            pass
                                    
                                    # 如果没有找到标准按钮，尝试通用处理方法
                                    if not button_handled:
                                        # 点击对话框位置
                                        rect = window.rectangle()
                                        button_x = rect.right - 70
                                        button_y = rect.bottom - 30
                                        pyautogui.click(button_x, button_y)
                                        self.log_progress(f"点击对话框位置 ({button_x}, {button_y}) 尝试完成步骤: {current_step['description']}")
                                        current_step["completed"] = True
                                        current_step_index = min(current_step_index + 1, len(ms_uninstall_steps) - 1)
                                        processed_handles.add(win)
                                        last_action_time = current_time
                                else:
                                    # 如果所有步骤已完成，仅处理可能的残留对话框
                                    for btn in button_texts:
                                        try:
                                            if window[btn].exists():
                                                window[btn].click()
                                                self.log_progress(f"卸载完成后点击残留按钮: {btn}")
                                                processed_handles.add(win)
                                                last_action_time = current_time
                                                break
                                        except:
                                            pass
                        except:
                            pass
                except Exception as e:
                    self.log_progress(f"搜索其他对话框时出错: {str(e)}")
                
                # 暂停一段时间再继续检查
                time.sleep(0.5)
            except Exception as e:
                self.log_progress(f"InstallShield处理线程出错: {str(e)}")
                time.sleep(1)
        
        self.log_progress("InstallShield和Materials Studio卸载向导处理线程已停止")

    def toggle_admin_mode(self):
        # 弹出密码输入对话框
        password_dialog = Toplevel(self.root)
        password_dialog.title("管理员验证")
        password_dialog.geometry("300x180")
        password_dialog.resizable(False, False)
        password_dialog.transient(self.root)
        password_dialog.grab_set()
        
        # 密码输入框
        frame = ttk.Frame(password_dialog, padding=20)
        frame.pack(fill=BOTH, expand=True)
        
        ttk.Label(frame, text="请输入管理员密码:").pack(pady=5)
        
        password_var = StringVar()
        password_entry = ttk.Entry(frame, textvariable=password_var, show="*", width=20)
        password_entry.pack(pady=5)
        password_entry.focus()
        
        # 确认按钮
        def verify_password():
            input_password = password_var.get()
            print(f"输入密码: '{input_password}'")
            print(f"正确密码: '{self.admin_password}'")
            
            if input_password == self.admin_password:
                print("密码验证成功")
                self.admin_mode = not self.admin_mode
                
                # 如果进入管理员模式，询问是否需要修改密码
                if self.admin_mode:
                    change_pwd = messagebox.askyesno("管理员选项", "是否需要修改管理员密码?")
                    if change_pwd:
                        password_dialog.destroy()
                        self.show_change_password_dialog()
                        return
                
                password_dialog.destroy()
                
                # 更新UI显示管理员模式状态
                if hasattr(self, 'admin_indicator'):
                    if self.admin_mode:
                        self.admin_indicator.pack(pady=5)
                    else:
                        self.admin_indicator.pack_forget()
                
                # 显示提示消息
                if self.admin_mode:
                    messagebox.showinfo("模式切换", "管理员模式已启用，将显示详细卸载过程")
                else:
                    messagebox.showinfo("模式切换", "管理员模式已关闭")
            else:
                print("密码验证失败")
                messagebox.showerror("错误", "密码错误")
        
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=X, pady=10)
        
        confirm_button = ttk.Button(button_frame, text="确定", command=verify_password)
        confirm_button.pack(side=RIGHT, padx=5)
        
        cancel_button = ttk.Button(button_frame, text="取消", command=password_dialog.destroy)
        cancel_button.pack(side=RIGHT, padx=5)
        
        # 绑定回车键
        password_entry.bind("<Return>", lambda event: verify_password())
        password_dialog.bind("<Return>", lambda event: verify_password())

    def show_change_password_dialog(self):
        """显示修改管理员密码的对话框"""
        pwd_dialog = Toplevel(self.root)
        pwd_dialog.title("修改管理员密码")
        pwd_dialog.geometry("350x200")
        pwd_dialog.resizable(False, False)
        pwd_dialog.transient(self.root)
        pwd_dialog.grab_set()
        
        frame = ttk.Frame(pwd_dialog, padding=20)
        frame.pack(fill=BOTH, expand=True)
        
        # 当前密码
        ttk.Label(frame, text="当前密码:").pack(anchor=W, pady=5)
        current_pwd_var = StringVar()
        current_pwd_entry = ttk.Entry(frame, textvariable=current_pwd_var, show="*", width=25)
        current_pwd_entry.pack(fill=X, pady=2)
        current_pwd_entry.focus()
        
        # 新密码
        ttk.Label(frame, text="新密码:").pack(anchor=W, pady=5)
        new_pwd_var = StringVar()
        new_pwd_entry = ttk.Entry(frame, textvariable=new_pwd_var, show="*", width=25)
        new_pwd_entry.pack(fill=X, pady=2)
        
        # 确认新密码
        ttk.Label(frame, text="确认新密码:").pack(anchor=W, pady=5)
        confirm_pwd_var = StringVar()
        confirm_pwd_entry = ttk.Entry(frame, textvariable=confirm_pwd_var, show="*", width=25)
        confirm_pwd_entry.pack(fill=X, pady=2)
        
        # 保存按钮
        def save_new_password():
            current = current_pwd_var.get()
            new = new_pwd_var.get()
            confirm = confirm_pwd_var.get()
            
            # 验证
            if current != self.admin_password:
                messagebox.showerror("错误", "当前密码不正确")
                return
            
            if not new or len(new) < 6:
                messagebox.showerror("错误", "新密码不能为空且长度至少为6位")
                return
            
            if new != confirm:
                messagebox.showerror("错误", "两次输入的新密码不一致")
                return
            
            # 保存新密码
            if self.save_admin_password(new):
                self.admin_password = new
                messagebox.showinfo("成功", "管理员密码已成功修改")
                pwd_dialog.destroy()
            else:
                messagebox.showerror("错误", "保存密码失败，请稍后重试")
        
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=X, pady=10)
        
        ttk.Button(button_frame, text="保存", command=save_new_password).pack(side=RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=pwd_dialog.destroy).pack(side=RIGHT, padx=5)

    def load_admin_password(self):
        """从配置文件加载管理员密码"""
        try:
            app_data = os.getenv('APPDATA')
            password_file = os.path.join(app_data, 'MS_Uninstaller', 'admin_pwd.txt')
            if os.path.exists(password_file):
                with open(password_file, 'r') as f:
                    password = f.read().strip()
                    if password:
                        return password
        except Exception as e:
            print(f"读取管理员密码失败: {str(e)}")
        
        # 默认密码
        return "ms2023admin"

    def save_admin_password(self, new_password):
        """保存新的管理员密码到配置文件"""
        try:
            app_data = os.getenv('APPDATA')
            config_dir = os.path.join(app_data, 'MS_Uninstaller')
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            password_file = os.path.join(config_dir, 'admin_pwd.txt')
            with open(password_file, 'w') as f:
                f.write(new_password)
            return True
        except Exception as e:
            print(f"保存管理员密码失败: {str(e)}")
            return False

    def monitor_and_close_browsers(self):
        """持续监控并关闭任何弹出的浏览器窗口"""
        if not PYWINAUTO_AVAILABLE:
            return
        
        self.log_progress("启动浏览器窗口监控...")
        
        # 持续运行30分钟
        end_time = time.time() + 1800
        
        # 浏览器窗口标题关键词
        browser_keywords = [
            "MSN", "Microsoft Edge", "Edge", "Internet Explorer", 
            "Chrome", "Firefox", "Opera", "Safari", "浏览器", "Browser", "网页", "Web"
        ]
        
        while time.time() < end_time:
            try:
                # 查找所有窗口
                all_windows = find_windows(top_level_only=True)
                
                for win in all_windows:
                    try:
                        app = Application().connect(handle=win)
                        window = app.window(handle=win)
                        window_text = window.window_text()
                        
                        # 检查是否是浏览器窗口
                        for keyword in browser_keywords:
                            if keyword.lower() in window_text.lower():
                                self.log_progress(f"发现浏览器窗口: {window_text}")
                                
                                try:
                                    # 尝试关闭窗口
                                    window.close()
                                    self.log_progress(f"成功关闭浏览器窗口: {window_text}")
                                except:
                                    try:
                                        # 如果普通关闭失败，使用Alt+F4
                                        window.set_focus()
                                        pyautogui.hotkey('alt', 'f4')
                                        self.log_progress(f"使用Alt+F4关闭浏览器窗口: {window_text}")
                                    except Exception as e:
                                        self.log_progress(f"关闭浏览器窗口失败: {str(e)}")
                                
                                # 不要继续检查其他关键词
                                break
                    except:
                        continue
                
                # 每2秒检查一次
                time.sleep(2)
            except Exception as e:
                self.log_progress(f"浏览器监控出错: {str(e)}")
                time.sleep(5)  # 出错后等待稍长时间
        
        self.log_progress("浏览器窗口监控已停止")

    # 添加日志存储功能
    def save_log_to_file(self):
        """将卸载日志保存到文件"""
        if not hasattr(self, 'progress_area'):
            return
        
        try:
            # 获取日志内容
            self.progress_area.config(state=NORMAL)
            log_content = self.progress_area.get(1.0, END)
            self.progress_area.config(state=DISABLED)
            
            # 创建日志文件名
            log_filename = f"MS卸载助手_日志_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            # 保存到桌面
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            log_path = os.path.join(desktop_path, log_filename)
            
            # 写入日志文件
            with open(log_path, 'w', encoding='utf-8') as f:
                f.write(f"Materials Studio 卸载日志\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"机器ID: {self.machine_id}\n")
                f.write(f"{'-'*50}\n\n")
                f.write(log_content)
            
            self.log_progress(f"卸载日志已保存到: {log_path}")
            return log_path
        except Exception as e:
            self.log_progress(f"保存日志失败: {str(e)}")
            return None

    # 添加临时文件清理方法
    def clean_temporary_files(self):
        """清理与Materials Studio相关的临时文件和缓存"""
        try:
            # 临时文件夹路径
            temp_paths = [
                os.environ.get('TEMP'),
                os.path.join(os.environ.get('LOCALAPPDATA'), 'Temp'),
                r'C:\Windows\Temp'
            ]
            
            # 用户缓存文件夹路径
            cache_paths = [
                os.path.join(os.environ.get('LOCALAPPDATA'), 'BIOVIA'),
                os.path.join(os.environ.get('LOCALAPPDATA'), 'Accelrys'),
                os.path.join(os.environ.get('APPDATA'), 'BIOVIA'),
                os.path.join(os.environ.get('APPDATA'), 'Accelrys'),
                os.path.join(os.environ.get('APPDATA'), 'Materials Studio')
            ]
            
            # 关键词
            keywords = ['biovia', 'accelrys', 'materials', 'discovery', 'ms_', 'msstudio']
            
            # 1. 清理临时文件夹中的相关文件
            for temp_dir in temp_paths:
                if temp_dir and os.path.exists(temp_dir):
                    self.log_progress(f"清理临时目录: {temp_dir}")
                    try:
                        for root, dirs, files in os.walk(temp_dir):
                            # 检查并删除匹配的文件
                            for file in files:
                                file_lower = file.lower()
                                if any(keyword in file_lower for keyword in keywords):
                                    try:
                                        file_path = os.path.join(root, file)
                                        os.remove(file_path)
                                        self.log_progress(f"已删除临时文件: {file_path}")
                                    except Exception as e:
                                        self.log_progress(f"无法删除临时文件 {file}: {str(e)}")
                            
                            # 检查并删除匹配的目录
                            for dir_name in dirs[:]:  # 创建副本以避免修改迭代中的列表
                                dir_lower = dir_name.lower()
                                if any(keyword in dir_lower for keyword in keywords):
                                    try:
                                        dir_path = os.path.join(root, dir_name)
                                        shutil.rmtree(dir_path)
                                        self.log_progress(f"已删除临时文件夹: {dir_path}")
                                        dirs.remove(dir_name)  # 从列表中移除已删除的目录以避免进一步递归
                                    except Exception as e:
                                        self.log_progress(f"无法删除临时文件夹 {dir_name}: {str(e)}")
                    except Exception as e:
                        self.log_progress(f"清理临时目录 {temp_dir} 时出错: {str(e)}")
            
            # 2. 清理缓存目录
            for cache_dir in cache_paths:
                if cache_dir and os.path.exists(cache_dir):
                    self.log_progress(f"清理缓存目录: {cache_dir}")
                    try:
                        shutil.rmtree(cache_dir)
                        self.log_progress(f"已删除缓存目录: {cache_dir}")
                    except Exception as e:
                        self.log_progress(f"无法删除缓存目录 {cache_dir}: {str(e)}")
                        # 尝试使用系统命令删除
                        try:
                            subprocess.call(f'rmdir /s /q "{cache_dir}"', shell=True)
                            self.log_progress(f"使用系统命令删除缓存目录: {cache_dir}")
                        except:
                            pass
            
            # 3. 如果启用了用户数据清理选项，清理用户数据目录
            if hasattr(self, 'clean_user_data') and self.clean_user_data.get():
                user_data_paths = [
                    os.path.join(os.environ.get('USERPROFILE'), 'Documents', 'BIOVIA'),
                    os.path.join(os.environ.get('USERPROFILE'), 'Documents', 'Accelrys'),
                    os.path.join(os.environ.get('USERPROFILE'), 'Documents', 'Materials Studio'),
                    os.path.join(os.environ.get('USERPROFILE'), 'Documents', 'Materials Studio Projects')
                ]
                
                for data_dir in user_data_paths:
                    if data_dir and os.path.exists(data_dir):
                        self.log_progress(f"清理用户数据目录: {data_dir}")
                        try:
                            shutil.rmtree(data_dir)
                            self.log_progress(f"已删除用户数据目录: {data_dir}")
                        except Exception as e:
                            self.log_progress(f"无法删除用户数据目录 {data_dir}: {str(e)}")
                            # 尝试使用系统命令删除
                            try:
                                subprocess.call(f'rmdir /s /q "{data_dir}"', shell=True)
                                self.log_progress(f"使用系统命令删除用户数据目录: {data_dir}")
                            except:
                                pass
            
            self.log_progress("临时文件和缓存清理完成")
        except Exception as e:
            self.log_progress(f"清理临时文件和缓存时出错: {str(e)}")

    # 添加超时处理方法
    def handle_timeout(self):
        """处理卸载超时情况"""
        self.log_progress("警告: 卸载过程超时，可能存在无法自动处理的问题")
        messagebox.showwarning("卸载超时", 
                             "卸载过程已运行较长时间，可能存在无法自动处理的问题。\n"
                             "您可以选择等待程序继续运行，或手动关闭并重试。")
        
        # 继续让卸载进程运行，但提示用户

class DisclaimerDialog(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent
        self.result = False
        
        self.title("免责声明")
        self.resizable(True, True)  # 允许调整大小
        self.protocol("WM_DELETE_WINDOW", self.on_cancel)
        
        # 设置窗口位置为屏幕中央，增加高度确保按钮可见
        window_width = 600
        window_height = 550  # 增加高度
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 创建免责声明内容
        main_frame = ttk.Frame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        disclaimer_title = ttk.Label(main_frame, text="免责声明", font=("Arial", 16, "bold"))
        disclaimer_title.pack(pady=(0, 10))
        
        # 创建带滚动条的文本框显示免责声明
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill="both", expand=True)
        
        scrollbar = ttk.Scrollbar(text_frame)
        scrollbar.pack(side="right", fill="y")
        
        disclaimer_text = tk.Text(text_frame, wrap="word", yscrollcommand=scrollbar.set)
        disclaimer_text.pack(side="left", fill="both", expand=True)
        scrollbar.config(command=disclaimer_text.yview)
        
        # 插入免责声明内容
        disclaimer_content = """
在使用"MS卸载助手"软件（以下简称"本软件"）之前，请仔细阅读以下免责声明。使用本软件即表示您已完全理解并同意接受本声明中的所有条款。

使用风险

1. 自行承担风险：用户需自行承担使用本软件的全部风险。本软件的开发者和分发者不对因使用本软件而可能造成的任何直接或间接损失负责，包括但不限于数据丢失、系统损坏或其他任何形式的损失。

2. 数据备份责任：在使用本软件前，强烈建议用户对重要数据进行完整备份。本软件在卸载过程中可能会删除与 Materials Studio 相关的文件和注册表项，这些操作是不可逆的。

3. 系统要求：用户应确保其计算机系统满足本软件的最低系统要求，并在使用前关闭所有可能干扰卸载过程的程序。

功能限制

1. 使用限制：本软件设计为在每台计算机上只能使用一次，再次使用需要特定的重置码。这是软件的正常功能设计，不构成缺陷。

2. 特定用途：本软件仅设计用于卸载 Materials Studio 软件及其相关组件，不应用于其他目的。

3. 自动化操作：本软件包含自动化操作功能，可能会自动点击对话框、修改注册表和删除文件。这些操作是卸载过程所必需的，请确保您理解这些风险。

法律声明

1. 知识产权：本软件受著作权法和国际条约保护。未经明确授权，不得复制、修改或分发本软件。

2. 责任限制：在法律允许的最大范围内，开发者和分发者不对因使用或无法使用本软件而导致的任何特殊、偶然、间接或继发性损害承担责任。

3. 无担保声明：本软件按"原样"提供，不提供任何明示或暗示的保证，包括但不限于对适销性、特定用途适用性和非侵权性的保证。

点击"我同意"即表示您已阅读、理解并同意接受上述免责声明的所有条款和条件。如不同意本声明的任何部分，请立即停止使用本软件。
        """
        
        disclaimer_text.insert("1.0", disclaimer_content)
        disclaimer_text.config(state="disabled")  # 设置为只读
        
        # 调整按钮框，确保可见
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(10, 5))
        
        decline_button = ttk.Button(button_frame, text="不同意", command=self.on_cancel)
        decline_button.pack(side="left", padx=10)
        
        agree_button = ttk.Button(button_frame, text="我同意", command=self.on_agree)
        agree_button.pack(side="right", padx=10)
        
        # 设置模态对话框
        self.transient(parent)
        self.grab_set()
        parent.wait_window(self)
    
    def on_agree(self):
        self.result = True
        self.destroy()
    
    def on_cancel(self):
        self.result = False
        self.destroy()

class InstallationGuideDialog(tk.Toplevel):
    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent
        self.result = False
        
        self.title("重要安装指南")
        self.resizable(True, True)
        self.protocol("WM_DELETE_WINDOW", self.on_cancel)
        
        # 设置窗口初始大小，使其更适应不同分辨率
        window_width = 700
        window_height = 450  # 增加高度以确保按钮可见
        
        # 获取屏幕尺寸以适应不同分辨率
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        # 确保窗口不会太大（限制最大尺寸为屏幕的80%）
        window_width = min(window_width, int(screen_width * 0.8))
        window_height = min(window_height, int(screen_height * 0.8))
        
        # 居中显示窗口
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # 使用醒目的主题色
        self.configure(bg="#ffeb3b")  # 醒目的黄色背景
        
        # 创建主框架，添加边距使内容与窗口边缘有一定距离
        main_frame = ttk.Frame(self, style="Alert.TFrame")
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)
        
        # 创建带滚动条的画布，以便在小屏幕上可以滚动查看全部内容
        canvas = Canvas(main_frame, bg="#ffeb3b", highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        
        # 内容框架放在画布内
        content_frame = ttk.Frame(canvas, style="Alert.TFrame")
        
        # 配置画布滚动区域
        def _configure_canvas(event):
            canvas.configure(scrollregion=canvas.bbox("all"))
            # 设置画布宽度与frame宽度一致
            canvas.itemconfig(canvas_frame, width=canvas.winfo_width())
        
        content_frame.bind("<Configure>", _configure_canvas)
        canvas_frame = canvas.create_window((0, 0), window=content_frame, anchor="nw")
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        # 设置画布和滚动条布局
        canvas.pack(side="left", fill="both", expand=True, padx=(0, 2))
        scrollbar.pack(side="right", fill="y")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 创建醒目的标题
        title_label = ttk.Label(content_frame, text="⚠️ 使用前必读 - 重要操作步骤 ⚠️", 
                              font=("Arial", 18, "bold"), foreground="#d32f2f", 
                              background="#ffeb3b")
        title_label.pack(pady=(10, 20))
        
        # 指南内容框架
        guide_frame = ttk.Frame(content_frame, style="Alert.TFrame")
        guide_frame.pack(fill="both", expand=True, padx=10)
        
        # 第一步
        step1_frame = ttk.Frame(guide_frame, style="Alert.TFrame")
        step1_frame.pack(fill="x", pady=10)
        
        step1_label = ttk.Label(step1_frame, text="第一步：", 
                               font=("Arial", 14, "bold"), foreground="#d32f2f",
                               background="#ffeb3b")
        step1_label.pack(side="left", anchor="nw")
        
        step1_content = ttk.Label(step1_frame, 
                                text="请将本程序放在目前电脑上已有的MS版本对应的安装包文件夹中", 
                                font=("Arial", 14), wraplength=500, background="#ffeb3b")
        step1_content.pack(side="left", padx=5, fill="x", expand=True)
        
        # 第二步
        step2_frame = ttk.Frame(guide_frame, style="Alert.TFrame")
        step2_frame.pack(fill="x", pady=10)
        
        step2_label = ttk.Label(step2_frame, text="第二步：", 
                               font=("Arial", 14, "bold"), foreground="#d32f2f",
                               background="#ffeb3b")
        step2_label.pack(side="left", anchor="nw")
        
        step2_content = ttk.Label(step2_frame, 
                                text="可以打开控制面板，然后点击卸载程序，按名称排布找到BIOVIA Materials Studio xxxx 开头的字样，这一步只是查看并确认已经安装的版本，不需要卸载", 
                                font=("Arial", 14), wraplength=500, background="#ffeb3b")
        step2_content.pack(side="left", padx=5, fill="x", expand=True)
        
        # 提示信息
        note_label = ttk.Label(guide_frame, 
                             text="请确保完成以上步骤后再继续!", 
                             font=("Arial", 14, "bold"), foreground="#d32f2f", 
                             background="#ffeb3b")
        note_label.pack(pady=20)
        
        # 按钮框架 - 固定在底部，确保始终可见
        button_frame = ttk.Frame(self, style="Alert.TFrame")
        button_frame.pack(fill="x", side="bottom", padx=15, pady=15)
        
        # 使用更大的按钮尺寸和更明显的样式
        cancel_button = ttk.Button(button_frame, text="取消", command=self.on_cancel, 
                                 style="TButton", width=15)
        cancel_button.pack(side="left", padx=20)
        
        confirm_button = ttk.Button(button_frame, text="我已确认，继续", command=self.on_confirm, 
                                  style="Accent.TButton", width=20)
        confirm_button.pack(side="right", padx=20)
        
        # 设置模态对话框
        self.transient(parent)
        self.grab_set()
        
        # 让窗口优先响应
        self.focus_force()
        
        # 等待窗口关闭
        parent.wait_window(self)
    
    def on_confirm(self):
        self.result = True
        self.destroy()
    
    def on_cancel(self):
        self.result = False
        self.destroy()

def main():
    root = tk.Tk()
    root.title("MS卸载助手")
    root.geometry("800x600")
    root.minsize(800, 600)
    app = MSUninstallerApp(root)
    root.mainloop()

if __name__ == "__main__":
    main() 