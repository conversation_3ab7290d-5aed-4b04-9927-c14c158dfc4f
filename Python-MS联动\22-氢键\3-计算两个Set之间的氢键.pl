##################################################################################################################
# perl                                                                                                           #
#                                                                                                                #
# Author: IMATSOFT.DM                                                                                            #
# Version: 1.2                                                                                                   #
# Tested on: Materials Studio 2020                                                                               #
#                                                                                                                #
# Required modules: Materials Visualizer                                                                         #
# This script provides comprehensive hydrogen bond analysis between two sets in a molecular structure.           #
# It analyzes trajectory files and identifies hydrogen bonds where the donor atom is in one set and the          #
# acceptor atom is in another set. For each frame, the script calculates:                                        #
# 1. Number of hydrogen bonds between specified sets                                                             #
# 2. Total number of hydrogen bonds in the structure                                                             #
# 3. Geometric features (H-A distances and D-H-A angles)                                                         #
# 4. Average hydrogen bond distance and angle across all frames                                                  #
#                                                                                                                #
# The results are presented in organized tables with detailed statistics, providing a complete picture           #
# of hydrogen bonding interactions between different components in molecular systems.                            #
#                                                                                                                #
# Date: 2023-05-15                                                                                               #
#                                                                                                                #
#                                                                                                                #
##################################################################################################################

use strict;
use warnings;
use MaterialsScript qw(:all);
use Math::Trig;
use Getopt::Long;
use Cwd;


################################
#      USER INPUT SECTION      #
################################

# 定义参数哈希表用于接收GUI传入的参数
my %Args;
GetOptions(\%Args, "Start_Frame=i", "End_Frame=i", "First_Set_Name=s", "Second_Set_Name=s", 
           "Max_H_A_Distance=f", "Min_D_H_A_Angle=f", "Output_Geometry_Table=s");

# 获取当前工作目录
my $cwd = getcwd;

# 创建一个输出日志文件
open my $out, '>', "HBond_Sets_process.txt";

# Input document - use active document instead of hardcoded filename
my $inputDocument = Documents->ActiveDocument;
print {$out} "使用活动文档: " . $inputDocument->Name . "\n";

# Trajectory frame range - from GUI or defaults
my $startFrame = $Args{Start_Frame};      # Start frame 
my $endFrame = $Args{End_Frame};     # End frame

# Names of the two sets to analyze - from GUI or defaults
my $firstSetName = $Args{First_Set_Name};
my $secondSetName = $Args{Second_Set_Name};

# Hydrogen bond parameters - from GUI or defaults
my $maxHydrogenAcceptorDistance = $Args{Max_H_A_Distance}; # Maximum hydrogen-acceptor distance in Angstroms
my $minDonorHydrogenAcceptorAngle = $Args{Min_D_H_A_Angle}; # Minimum angle for hydrogen bonds (D-H...A) in degrees

# 是否输出几何表格 - 从GUI或使用默认值
my $outputGeometryTable = $Args{Output_Geometry_Table};

# Elements that can participate in hydrogen bonds
my @hydrogenBondElements = ("N", "O", "Cl", "F", "S");

# 设置默认值（如果GUI没有传入参数）
if (!defined $startFrame) {
    $startFrame = 1;
}
if (!defined $endFrame) {
    $endFrame = 1000;
}
if (!defined $firstSetName) {
    $firstSetName = "Target1";
}
if (!defined $secondSetName) {
    $secondSetName = "Target2";
}
if (!defined $maxHydrogenAcceptorDistance) {
    $maxHydrogenAcceptorDistance = 2.5;
}
if (!defined $minDonorHydrogenAcceptorAngle) {
    $minDonorHydrogenAcceptorAngle = 90;
}
if (!defined $outputGeometryTable) {
    $outputGeometryTable = "Yes";
}

# 输出初始信息到日志文件
print {$out} "开始执行两个Set之间的氢键分析脚本...\n";
print {$out} "==================================================\n";
print {$out} "起始帧: $startFrame\n";
print {$out} "终止帧: $endFrame\n";
print {$out} "第一组原子集: $firstSetName\n";
print {$out} "第二组原子集: $secondSetName\n";
print {$out} "最大氢-受体距离: $maxHydrogenAcceptorDistance Å\n";
print {$out} "最小供体-氢-受体角度: $minDonorHydrogenAcceptorAngle 度\n";
print {$out} "输出几何表格: $outputGeometryTable\n";
print {$out} "可参与氢键的元素: " . join(", ", @hydrogenBondElements) . "\n";
print {$out} "==================================================\n";

# Create results table with fixed filename
my $resultsTable = Documents->New("DM-HBondResults.std");
$resultsTable->ColumnHeading(0) = "Frame";
$resultsTable->ColumnHeading(1) = "FrameTime (ps)";
$resultsTable->ColumnHeading(2) = "H-Bonds Between Sets";
$resultsTable->ColumnHeading(3) = "Total H-Bonds";
print {$out} "已创建结果数据表: DM-HBondResults.std\n";

# Create geometry features table with fixed filename if requested
my $geometryTable;
if ($outputGeometryTable eq "Yes") {
    $geometryTable = Documents->New("DM-HBondGeometry.std");
    $geometryTable->ColumnHeading(0) = "Frame";
    $geometryTable->ColumnHeading(1) = "FrameTime (ps)";
    $geometryTable->ColumnHeading(2) = "HBond ID";
    $geometryTable->ColumnHeading(3) = "H-A Distance (A)";
    $geometryTable->ColumnHeading(4) = "D-H-A Angle (Du)";
    $geometryTable->ColumnHeading(5) = "Donor Set";
    $geometryTable->ColumnHeading(6) = "Acceptor Set";
    print {$out} "已创建氢键几何特征数据表: DM-HBondGeometry.std\n";
} else {
    print {$out} "用户已选择不输出几何表格\n";
}

# Function to get atom IDs belonging to a specific set
sub GetAtomsInSet {
    my ($document, $setName) = @_;
    my %atomIDMap = ();
    
    # Get all atoms in the specified set
    my $setAtoms = $document->UnitCell->Sets($setName)->Atoms;
    foreach my $atom (@$setAtoms) {
        $atomIDMap{$atom->ID} = 1;
    }
    
    return %atomIDMap;
}

# Function to calculate distance between two atoms
sub CalculateDistance {
    my ($atom1, $atom2) = @_;
    my $dx = $atom1->X - $atom2->X;
    my $dy = $atom1->Y - $atom2->Y;
    my $dz = $atom1->Z - $atom2->Z;
    
    return sqrt($dx*$dx + $dy*$dy + $dz*$dz);
}

# Function to calculate angle between three atoms (in degrees)
sub CalculateAngle {
    my ($atom1, $atom2, $atom3) = @_;
    
    # Vector from atom2 to atom1 (D-H)
    my $v1x = $atom1->X - $atom2->X;
    my $v1y = $atom1->Y - $atom2->Y;
    my $v1z = $atom1->Z - $atom2->Z;
    
    # Vector from atom2 to atom3 (H-A)
    my $v2x = $atom3->X - $atom2->X;
    my $v2y = $atom3->Y - $atom2->Y;
    my $v2z = $atom3->Z - $atom2->Z;
    
    # Dot product
    my $dotProduct = $v1x*$v2x + $v1y*$v2y + $v1z*$v2z;
    
    # Magnitude of vectors
    my $mag1 = sqrt($v1x*$v1x + $v1y*$v1y + $v1z*$v1z);
    my $mag2 = sqrt($v2x*$v2x + $v2y*$v2y + $v2z*$v2z);
    
    # Avoid division by zero
    if ($mag1 < 0.0001 || $mag2 < 0.0001) {
        return 0;
    }
    
    # Calculate angle in degrees
    my $cosTheta = $dotProduct / ($mag1 * $mag2);
    
    # Handle numerical precision issues
    if ($cosTheta > 1.0) { $cosTheta = 1.0; }
    if ($cosTheta < -1.0) { $cosTheta = -1.0; }
    
    my $angleRad = acos($cosTheta);
    my $angleDeg = $angleRad * 180.0 / 3.14159265358979;
    
    return $angleDeg;
}

################################
#       MAIN CALCULATION       #
################################

# Process each frame in the trajectory
my $totalFrames = $inputDocument->Trajectory->NumFrames;
print {$out} "轨迹总帧数: $totalFrames\n";

# Adjust end frame if it exceeds the actual number of frames
if ($endFrame > $totalFrames) {
    $endFrame = $totalFrames;
    print {$out} "终止帧调整为实际轨迹最大帧数: $endFrame\n";
}
# Validate start frame
if ($startFrame < 1) {
    $startFrame = 1;
    print {$out} "起始帧调整为: 1\n";
}
if ($startFrame > $endFrame) {
    $startFrame = 1;
    print {$out} "起始帧大于终止帧，已重置为: 1\n";
}

my $totalGeometryRows = 0;  # Total counter for all rows across all frames

# Variables to calculate average values
my $totalHADistance = 0.0;
my $totalDHAAngle = 0.0;
my $totalHBondCount = 0;

print {$out} "==================================================\n";
print {$out} "开始逐帧分析...\n";

for (my $frameIndex = $startFrame; $frameIndex <= $endFrame; ++$frameIndex) {
    print {$out} "正在处理帧: $frameIndex\n";
    $inputDocument->Trajectory->CurrentFrame = $frameIndex;
    
    # Get current frame time directly from trajectory
    my $frameTime = $inputDocument->Trajectory->FrameTime;
    
    # Clear and configure hydrogen bond calculation parameters
    Tools->BondCalculation->HBonds->ClearDonors;
    Tools->BondCalculation->HBonds->ClearAcceptors;
    
    # Set elements that can participate in hydrogen bonds
    foreach my $element (@hydrogenBondElements) {
        Tools->BondCalculation->HBonds->AddDonor($element);
        Tools->BondCalculation->HBonds->AddAcceptor($element);
    }
    
    # Calculate hydrogen bonds for the current frame
    Tools->BondCalculation->HBonds->Calculate($inputDocument, 
        Settings(
            MaxHydrogenAcceptorDistance => $maxHydrogenAcceptorDistance,
            MinDonorHydrogenAcceptorAngle => $minDonorHydrogenAcceptorAngle
        ));
    print {$out} "  - 已计算氢键\n";
    
    # Get all hydrogen bonds
    my $hydrogenBonds = $inputDocument->UnitCell->HydrogenBonds;
    
    # Get total number of hydrogen bonds in this frame
    my $frameTotalHBondCount = $hydrogenBonds->Count;
    print {$out} "  - 该帧总氢键数量: $frameTotalHBondCount\n";
    
    # Get atom IDs for both sets
    my %firstSetAtoms = GetAtomsInSet($inputDocument, $firstSetName);
    my %secondSetAtoms = GetAtomsInSet($inputDocument, $secondSetName);
    
    # Count hydrogen bonds between the two sets
    my $intersetHBondCount = 0;
    my $frameGeometryRowCount = 0;  # Counter for geometry rows in this frame
    
    foreach my $hBond (@$hydrogenBonds) {
        # Get donor and acceptor atoms for this hydrogen bond
        my $donorAtom = $hBond->Donor;
        my $acceptorAtom = $hBond->Acceptor;
        
        # Check which set each atom belongs to
        my $donorInFirstSet = exists $firstSetAtoms{$donorAtom->ID};
        my $donorInSecondSet = exists $secondSetAtoms{$donorAtom->ID};
        my $acceptorInFirstSet = exists $firstSetAtoms{$acceptorAtom->ID};
        my $acceptorInSecondSet = exists $secondSetAtoms{$acceptorAtom->ID};
        
        # Count bond if donor and acceptor are in different sets
        if (($donorInFirstSet && $acceptorInSecondSet) || 
            ($donorInSecondSet && $acceptorInFirstSet)) {
            $intersetHBondCount++;
            
            # Get hydrogen atom (needed for geometric features)
            my $hydrogenAtom = $hBond->Hydrogen;
            
            # Calculate H-A distance directly from atom coordinates
            my $haDistance = CalculateDistance($hydrogenAtom, $acceptorAtom);
            
            # Calculate D-H-A angle directly from atom coordinates
            my $dhaAngle = CalculateAngle($donorAtom, $hydrogenAtom, $acceptorAtom);
            
            # Add to totals for average calculation
            $totalHADistance += $haDistance;
            $totalDHAAngle += $dhaAngle;
            $totalHBondCount++;
            
            # Only record geometry data if requested
            if ($outputGeometryTable eq "Yes") {
                # Determine which set contains the donor and acceptor
                my $donorSetName = $donorInFirstSet ? $firstSetName : $secondSetName;
                my $acceptorSetName = $acceptorInFirstSet ? $firstSetName : $secondSetName;
                
                # Record geometry features in the table
                $geometryTable->Cell($totalGeometryRows, 0) = $frameIndex;
                $geometryTable->Cell($totalGeometryRows, 1) = $frameTime;
                $geometryTable->Cell($totalGeometryRows, 2) = $frameGeometryRowCount + 1;  # HBond ID within frame
                $geometryTable->Cell($totalGeometryRows, 3) = $haDistance;
                $geometryTable->Cell($totalGeometryRows, 4) = $dhaAngle;
                $geometryTable->Cell($totalGeometryRows, 5) = $donorSetName;
                $geometryTable->Cell($totalGeometryRows, 6) = $acceptorSetName;
                
                # Increment row counter for geometry table
                $totalGeometryRows++;
            }
            
            # Increment counter for bonds in this frame
            $frameGeometryRowCount++;
        }
    }
    
    # Save results to table
    $resultsTable->Cell($frameIndex-1, 0) = $frameIndex;
    $resultsTable->Cell($frameIndex-1, 1) = $frameTime;
    $resultsTable->Cell($frameIndex-1, 2) = $intersetHBondCount;
    $resultsTable->Cell($frameIndex-1, 3) = $frameTotalHBondCount;
    
    # Display progress to log file
    my $progressPercent = int(($frameIndex / $endFrame) * 100);
    print {$out} "  - 进度: $progressPercent\%, 两组间氢键数量: $intersetHBondCount\n";
}

# Calculate averages if there were any hydrogen bonds
my $avgHADistance = 0.0;
my $avgDHAAngle = 0.0;

if ($totalHBondCount > 0) {
    $avgHADistance = $totalHADistance / $totalHBondCount;
    $avgDHAAngle = $totalDHAAngle / $totalHBondCount;
}

# Add a row for averages at the end of the geometry table if requested
if ($outputGeometryTable eq "Yes" && $totalHBondCount > 0) {
    $geometryTable->Cell($totalGeometryRows, 0) = "Average";
    $geometryTable->Cell($totalGeometryRows, 1) = "-";
    $geometryTable->Cell($totalGeometryRows, 2) = "-";
    $geometryTable->Cell($totalGeometryRows, 3) = $avgHADistance;
    $geometryTable->Cell($totalGeometryRows, 4) = $avgDHAAngle;
    $geometryTable->Cell($totalGeometryRows, 5) = "-";
    $geometryTable->Cell($totalGeometryRows, 6) = "-";
}

print {$out} "==================================================\n";
print {$out} "氢键分析完成！\n";
print {$out} "结果已保存到 DM-HBondResults.std";
if ($outputGeometryTable eq "Yes") {
    print {$out} " 和 DM-HBondGeometry.std";
}
print {$out} "\n";
print {$out} "两组间总氢键数量: $totalHBondCount\n";
print {$out} "平均氢-受体距离: $avgHADistance Å\n";
print {$out} "平均供体-氢-受体角度: $avgDHAAngle 度\n";
print {$out} "已分析从 $startFrame 到 $endFrame 帧之间 '$firstSetName' 和 '$secondSetName' 的氢键\n";
print {$out} "==================================================\n";

# 关闭输出文件
close($out);

# 导入日志文件到 Materials Studio
Documents->Import("$cwd/HBond_Sets_process.txt"); 