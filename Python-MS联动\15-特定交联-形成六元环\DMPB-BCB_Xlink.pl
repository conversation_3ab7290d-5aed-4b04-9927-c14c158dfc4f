# BCB交联脚本 (DMPB-bcb_crosslink.pl)

###############################################################################
# Copyright (c) 2023-2024 DuanMu_Pengbo (DMPB)
# All rights reserved.
# 
# 本脚本由DuanMu_Pengbo(DMPB)开发，未经授权不得使用、复制或分发。
# This script was developed by Peng<PERSON> Duanmu (DMPB) and is protected
# by copyright law. Unauthorized use, reproduction or distribution
# is prohibited.
# 
# 联系方式/Contact: <EMAIL>
###############################################################################

#!perl

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);
use constant TRUE 	=> 1;
use constant FALSE 	=> 0;
use constant DEBUG	=> 2; # larger is more verbose
use constant PICO_TO_FEMTO => 1000;
# 计算因子
use constant D_FACTOR => 4;
use constant M_FACTOR => 13;
use constant P_FACTOR => 16; 
use constant B_FACTOR => 2;

# Purpose: 	Crosslink BCB (benzocyclobutene) molecules using atomistic models
# Modules:	Forcite
# Author:   DuanMu_Pengbo (DMPB), 2022
# Based on:      Original crosslinking script by Jason DeJoannis, Stephen Todd & James Wescott

# Disclaimer
# This script is distributed as-is on an unsupported basis. The authors do not make any
# guarantees it will work or that it is accurate. 

# Creates crosslinks in a system containing BCB molecules
# Atoms on the BCB units are designated as reactive atoms by assigning 
# special names (R1, R2, R3, R4). Close-contacts are calculated between 
# the reactive units, bonds R1-R2 and R3-R4 are broken, and new bonds R2-R3 and R1-R4
# are created to form the eight-membered ring structure.

# Executes these steps:
#   1. One-time equilibration dynamics
#   2. Update reaction radius
#   3. Create new crosslinks (createNewBCBXlinks)
#   4. Relax with optimization and dynamics
#   5. Adjust hydrogens
#   6. Recalculate charge groups (optional)
#   7. Anneal xlinked structure using temperature cycle (optional)
#   8. Write xlink data to study tables
#   9. Repeat steps 2-8 until target conversion or max cutoff is reached

# Input documents:
# (1) An atomistic xsd file with reactive atoms named as R1, R2, R3, and R4

# Output documents:
# (1) bcb_xlink_final.xsd - Final xlinked structure 
# (2) bcb_xlink_statistics.std - Table of xlinking and thermo data
# (3) bcb_xlink_structures.std - Table of intermediate structures at lower conversion levels
# (4) BCBXlinkBonds.xcd - Distribution of bond lengths to check for stretched bonds
# (5) Progress.txt - Log file updated continuously during the run
# (6) Timings.txt - Log file of cpu time

################################################################################################
#
# User settings - either from GUI or edit here
#
################################################################################################

# These will be used if there is no UI
my $xsdDocName			= "Original";	# Name of xsd file with BCB units
my $conversionTarget 		= 100;		# Percent of BCB units that react
my $MinRxnRadius 		= 4;		# Initial close contacts cutoff
my $StepRxnRadius 		= 0.5;		# Close contact step size
my $MaxRxnRadius 		= 20;		# Final close contacts cutoff - hopefully conversion
							# is reached before this value is reached
my $IterationsPerRadius		= 2;		# Max number of xlink tries at each radius
						
# We do not use separate molecule objects for BCB since each BCB unit is part of a polymer chain
my $noMol			= TRUE;

# Simulation settings
my $forcefield		 	= "COMPASSIII";
my $timeStep			= 1;		# Dynamics time step in fs
my $chargeMethod 		= "Atom based";	# Atom based, Group based or Ewald
my $Quality			= "Medium";	# Coarse/Medium/Fine/Ultra Fine
my $thermostat			= "Andersen";	# Andersen, Nose, Velocity Scale, Berendsen or NHL
my $xlinkTemperature		= 300;		# Main temperature throughout
my $xlinkPressure		= 0.0001;	# Main pressure throughout
my $ensemble			= "NPT";	# Normally NPT, but use NVT if eg liquid slab with vacuum layer
							# if non-periodic NVT will be used in any case

my $one_time_equilibration	= 10;		# ps of dynamics for initial equilibration

# Options for the perturbations -  Temperature cycle settings
my $UseTempCycle		= FALSE;
my $startTemp			= $xlinkTemperature;	# Starting temperature
my $lowTemp			= $xlinkTemperature;	# Ending temperature
my $highTemp			= $xlinkTemperature + 200;	# High temperature
my $tempStep			= 50;			# Temperature step
my $tempDuration		= 20;			# Time at each temperature (ps)

# Analysis dynamics settings
my $analyzeDuration		= 10;		# ps of dynamics for sampling thermo data

# dynamics before crosslinking at each new distance
my $straightDynamicsTime	= 10;		# Time in ps

# Option for calculating the largest bond energy to help assess network strain
my $UseMaxBondEnergy		= TRUE;

######################################################
# GUI? - attempt to open the active document
######################################################
my $xsdDoc;
eval{ $xsdDoc = Documents->ActiveDocument; };

if ($@) # no active doc - use parameters defined above
{
	$xsdDoc	= $Documents{"$xsdDocName.xsd"};
}
else
{
	# Get the user supplied options
	my %Arg;
	GetOptions(\%Arg,
		"Forcefield=s", 
		"Conversion=i", 
		"MinRxnRadius=f", 
		"MaxRxnRadius=f", 
		"StepRxnRadius=f",
		"Iterations_per_Radius=i", 
		"ChargeMethod=s", 
		"Temperature=f",
		"TemperatureCycle=s",
		"Temperature_Step=f",
		"High_Temperature=f",
		"ps_per_Temperature=f",
		"Initial_Equil_ps=f",
		"Equil_ps=f",
		"Ensemble=s"
	);

	$conversionTarget 	= $Arg{Conversion}	if (exists $Arg{Conversion});
	$MinRxnRadius 		= $Arg{MinRxnRadius}	if (exists $Arg{MinRxnRadius});
	$StepRxnRadius 		= $Arg{StepRxnRadius}	if (exists $Arg{StepRxnRadius});
	$MaxRxnRadius 		= $Arg{MaxRxnRadius}	if (exists $Arg{MaxRxnRadius});
	$IterationsPerRadius	= $Arg{Iterations_per_Radius} if (exists $Arg{Iterations_per_Radius});
	$forcefield	 	= $Arg{Forcefield}	if (exists $Arg{Forcefield});
	$chargeMethod 		= $Arg{ChargeMethod}	if (exists $Arg{ChargeMethod});
	$xlinkTemperature	= $Arg{Temperature}	if (exists $Arg{Temperature});
	$one_time_equilibration	= $Arg{Initial_Equil_ps} if (exists $Arg{Initial_Equil_ps});
	$straightDynamicsTime   = $Arg{Equil_ps}	if (exists $Arg{Equil_ps});
	$UseTempCycle		= TRUE  if (exists $Arg{TemperatureCycle} and $Arg{TemperatureCycle} eq "Yes");
	$UseTempCycle		= FALSE if (exists $Arg{TemperatureCycle} and $Arg{TemperatureCycle} eq "No");
	$highTemp		= $Arg{High_Temperature}	if (exists $Arg{High_Temperature});
	$tempStep		= $Arg{Temperature_Step}	if (exists $Arg{Temperature_Step});
	$tempDuration		= $Arg{ps_per_Temperature}	if (exists $Arg{ps_per_Temperature});
	$ensemble		= $Arg{Ensemble}	if (exists $Arg{Ensemble} and $Arg{Ensemble} ne "");

	# Write to text file options specified by user in the GUI
	my $textDoc = Documents->New("GUI_inputs.txt");
	$textDoc->Append("USER OPTIONS\n============================\n");
	while ( my ($key, $value) = each(%Arg) ) 
	{
		$textDoc->Append(sprintf "$key => $value\n");
	}
	$textDoc->Append("============================\n\n");
	$textDoc->Close;
}

###########################################################################################
#
# End user settings
#
###########################################################################################

# Import some server files with info on hostname, mpi, arguments
eval 
{
	Documents->Import("fromdsd.txt");
	Documents->Import("mpd.hosts");
};

# File for reporting run progress
my $textDoc = Documents->New("Progress.txt");

# 文件完整性检查
if (!_d_m_p_b_() || !_check_integrity()) {
    # 格式化警告代码
    my $warn_code = sprintf("W%s%s%s%s", 
                           chr(65 + D_FACTOR), 
                           chr(65 + M_FACTOR % 26),
                           chr(65 + P_FACTOR % 26),
                           chr(65 + B_FACTOR));
    $textDoc->Append("$warn_code: 请确认脚本来源的可靠性。\n");
    $textDoc->Append("File integrity cannot be fully verified.\n");
    $textDoc->Save;
}

# File for reporting time taken by each cycle 
my $timeDoc = Documents->New("Timings.txt");
$timeDoc->Append("Distance Iteration Elapsted_Time(hr) Segment_Time(hr) Conversion(%)\n");
my $segtime = time; # Time in seconds for a segment of the run

# Make a copy of the first document to preserve the starting structure
my $rootName = "bcb_xlink";
my $doc = Documents->New("$rootName.xsd");
$doc->CopyFrom($xsdDoc);

# Initialize Forcite with settings to be used globally
my $Forcite = Modules->Forcite;
$Forcite->ChangeSettings([
	CurrentForcefield	=> $forcefield,
	Quality			=> $Quality,
	Temperature		=> $xlinkTemperature,
	Pressure		=> $xlinkPressure,
	Thermostat		=> $thermostat,
	Barostat		=> "Andersen",
	TimeStep		=> $timeStep,
	TrajectoryFrequency	=> 5000,
	AppendTrajectory	=> "No",
	WriteVelocities		=> "Yes",
	EnergyDeviation		=> 500000000,
	WriteLevel		=> "Silent"
]);

# Periodicity: 0 and 3 are supported. 2 might work
my $Periodicity;
if (($doc->SymmetrySystems->Count == 0) || ($doc->SymmetrySystem->SymmetryDefinition->Periodicity == 0))
{
	$Periodicity = 0;
	die "Cannot use Ewald for nonperiodic systems\n" if ($chargeMethod eq "Ewald");
	$Forcite->ChangeSettings([
		NonPeriodicElectrostaticSummationMethod		=> $chargeMethod,
		NonPeriodicvdWSummationMethod			=> "Atom based"
	]);
}
else
{
	$Periodicity = $doc->SymmetrySystem->SymmetryDefinition->Periodicity;
	$Forcite->ChangeSettings([
	    	'3DPeriodicElectrostaticSummationMethod'	=> $chargeMethod,
		'****************************' 			=> "Atom based",
		'2DPeriodicElectrostaticSummationMethod'	=> $chargeMethod,
		'2DPeriodicvdWSummationMethod'			=> "Atom based",
	]);
} 
warn "2D periodicity has not been tested\n" if ($Periodicity == 2);

# Prevent filename conflict for restart runs
$xsdDoc->Name = "initial" if ($xsdDoc->Name =~ /^bcb_xlink_/);
$xsdDoc->Close;

# Create a study table to hold the statistics
my $statTable = Documents->New($rootName."_statistics.std");
my $nstatsheets = 1;

# Create a study table to hold the intermediate structures at the end of each distance cycle
my $structureTable = Documents->New($rootName."_structures.std");
$structureTable->ColumnHeading(1) = "Distance (A)";
$structureTable->ColumnHeading(2) = "Iteration";
$structureTable->ColumnHeading(3) = "Percent Conversion";

###########################################################################################
# Initialize the crosslinking stuff

# Count the total BCB units and those that have already reacted
my $totalR1R2Units = 0;
my $totalR3R4Units = 0;
my $reactedR1R2Units = 0;
my $reactedR3R4Units = 0;
my %r1r2Units;  # Hash to store R1-R2 unit information
my %r3r4Units;  # Hash to store R3-R4 unit information

# Identify R1-R2 units
foreach my $atom (@{$doc->UnitCell->Atoms})
{
    if ($atom->Name eq "R1")
    {
        $totalR1R2Units++;
        my $unitID = "R1R2_" . $totalR1R2Units;
        $r1r2Units{$unitID} = {
            R1 => $atom,
            R2 => undef,
            isReacted => FALSE
        };
    }
}
$textDoc->Append(sprintf "Found %d R1 atoms\n", $totalR1R2Units);

# Identify R3-R4 units
foreach my $atom (@{$doc->UnitCell->Atoms})
{
    if ($atom->Name eq "R3")
    {
        $totalR3R4Units++;
        my $unitID = "R3R4_" . $totalR3R4Units;
        $r3r4Units{$unitID} = {
            R3 => $atom,
            R4 => undef,
            isReacted => FALSE
        };
    }
}
$textDoc->Append(sprintf "Found %d R3 atoms\n", $totalR3R4Units);

# Count all atom types for debugging
my $countR1 = $totalR1R2Units;
my $countR2 = 0;
my $countR3 = $totalR3R4Units;
my $countR4 = 0;

# Link R2 atoms to their R1 units
foreach my $atom (@{$doc->UnitCell->Atoms})
{
    if ($atom->Name eq "R2")
    {
        $countR2++;
        # Find which R1R2 unit this belongs to by checking nearby R1 atoms
        my $minDistance = 999.0;
        my $closestR1Unit = "";
        
        foreach my $unitID (keys %r1r2Units)
        {
            if ($r1r2Units{$unitID}->{R1} && !$r1r2Units{$unitID}->{R2})
            {
                my $distance = calcDistance($r1r2Units{$unitID}->{R1}, $atom);
                if ($distance < $minDistance)
                {
                    $minDistance = $distance;
                    $closestR1Unit = $unitID;
                }
            }
        }
        
        # If this is close to a R1R2 unit, assign it
        if ($closestR1Unit && $minDistance < 5.0) # 5Å threshold for belonging to same unit
        {
            $r1r2Units{$closestR1Unit}->{R2} = $atom;
        }
    }
}

# Link R4 atoms to their R3 units
foreach my $atom (@{$doc->UnitCell->Atoms})
{
    if ($atom->Name eq "R4")
    {
        $countR4++;
        # Find which R3R4 unit this belongs to by checking nearby R3 atoms
        my $minDistance = 999.0;
        my $closestR3Unit = "";
        
        foreach my $unitID (keys %r3r4Units)
        {
            if ($r3r4Units{$unitID}->{R3} && !$r3r4Units{$unitID}->{R4})
            {
                my $distance = calcDistance($r3r4Units{$unitID}->{R3}, $atom);
                if ($distance < $minDistance)
                {
                    $minDistance = $distance;
                    $closestR3Unit = $unitID;
                }
            }
        }
        
        # If this is close to a R3R4 unit, assign it
        if ($closestR3Unit && $minDistance < 5.0) # 5Å threshold for belonging to same unit
        {
            $r3r4Units{$closestR3Unit}->{R4} = $atom;
        }
    }
}

$textDoc->Append(sprintf "Found atoms: %d R1, %d R2, %d R3, %d R4\n", $countR1, $countR2, $countR3, $countR4);

# Validate units have all needed atoms
my $validR1R2Units = 0;
my $validR3R4Units = 0;

foreach my $unitID (keys %r1r2Units)
{
    if ($r1r2Units{$unitID}->{R1} && $r1r2Units{$unitID}->{R2})
    {
        $validR1R2Units++;
    }
    else
    {
        delete $r1r2Units{$unitID}; # Remove invalid units
    }
}

foreach my $unitID (keys %r3r4Units)
{
    if ($r3r4Units{$unitID}->{R3} && $r3r4Units{$unitID}->{R4})
    {
        $validR3R4Units++;
    }
    else
    {
        delete $r3r4Units{$unitID}; # Remove invalid units
    }
}

$textDoc->Append(sprintf "Valid units: %d R1-R2 units, %d R3-R4 units\n", $validR1R2Units, $validR3R4Units);

# Check if units already have bonds
foreach my $unitID (keys %r1r2Units)
{
    my $hasBond = FALSE;
    foreach my $bond (@{$r1r2Units{$unitID}->{R1}->Bonds})
    {
        if ((AtomsEqual($bond->Atom1, $r1r2Units{$unitID}->{R1}) && AtomsEqual($bond->Atom2, $r1r2Units{$unitID}->{R2})) ||
            (AtomsEqual($bond->Atom1, $r1r2Units{$unitID}->{R2}) && AtomsEqual($bond->Atom2, $r1r2Units{$unitID}->{R1})))
        {
            $hasBond = TRUE;
            last;
        }
    }
    $r1r2Units{$unitID}->{hasR1R2Bond} = $hasBond;
}

foreach my $unitID (keys %r3r4Units)
{
    my $hasBond = FALSE;
    foreach my $bond (@{$r3r4Units{$unitID}->{R3}->Bonds})
    {
        if ((AtomsEqual($bond->Atom1, $r3r4Units{$unitID}->{R3}) && AtomsEqual($bond->Atom2, $r3r4Units{$unitID}->{R4})) ||
            (AtomsEqual($bond->Atom1, $r3r4Units{$unitID}->{R4}) && AtomsEqual($bond->Atom2, $r3r4Units{$unitID}->{R3})))
        {
            $hasBond = TRUE;
            last;
        }
    }
    $r3r4Units{$unitID}->{hasR3R4Bond} = $hasBond;
}

# Count units with bonds
my $r1r2WithBonds = 0;
my $r3r4WithBonds = 0;
foreach my $unitID (keys %r1r2Units)
{
    $r1r2WithBonds++ if ($r1r2Units{$unitID}->{hasR1R2Bond});
}
foreach my $unitID (keys %r3r4Units)
{
    $r3r4WithBonds++ if ($r3r4Units{$unitID}->{hasR3R4Bond});
}
$textDoc->Append(sprintf "Units with bonds: %d R1-R2 units, %d R3-R4 units\n", $r1r2WithBonds, $r3r4WithBonds);

# Count xlinks formed during previous runs
my $xlinkCounter = 0;
foreach my $bond (@{$doc->UnitCell->Bonds})
{
    $xlinkCounter++ if ($bond->Name =~ /^bcb_xlink/);
}

# Total valid units is the minimum of valid R1-R2 and R3-R4 units
my $validBCBUnits = ($validR1R2Units < $validR3R4Units) ? $validR1R2Units : $validR3R4Units;

my $conversion = 0;
my $totalReacted = $reactedR1R2Units;  # 假设R1R2和R3R4单元一一对应

if ($validBCBUnits > 0)
{
    $conversion = 100.0 * $totalReacted / $validBCBUnits;
}
my $rowCounter = 0;  # Current row in the study tables

# Check required number of BCB units are consistent with conversion
my $targetBCBUnits = $validBCBUnits * ($conversionTarget / 100);
$textDoc->Append("Conversion = percent of BCB pairs that have reacted\n");
$textDoc->Append("Conversion target = $conversionTarget, Current conversion = $conversion\n");
$textDoc->Append("Total BCB pairs possible: $validBCBUnits\n");
$textDoc->Append("Target number of BCB pairs to react: $targetBCBUnits\n");
$textDoc->Save;

# Counters for optimization and dynamics steps
my $mdcounter = 0;
my $geomoptcounter = 0;

###########################################################################################
# One time equilibration

if ($one_time_equilibration > 0)
{
    my $steps = ($one_time_equilibration * PICO_TO_FEMTO / $timeStep);
    $textDoc->Append("\nOne-time equilibration\n");
    my $results = ForciteDynamics($doc, $steps, "NVT");
    $results->Trajectory->Delete;
    my $results = ForciteDynamics($doc, $steps, $ensemble);
    $results->Trajectory->Delete;
}

###########################################################################################
# Main crosslinking loop
# Increment reaction radius
$textDoc->Append("Entering main crosslinking loop\n");
$textDoc->Save;

for (my $RxnRadius = $MinRxnRadius; $RxnRadius <= $MaxRxnRadius; $RxnRadius += $StepRxnRadius) 
{
    # Base name for the structure	
    my $xsdNameDist = sprintf("%s_R%.2f", $rootName, $RxnRadius);

    # Equilibrate for each new radius (except first)
    if ($RxnRadius > $MinRxnRadius)  
    {
        $textDoc->Append("Equilibrating at new radius\n");
        $doc->Name = $xsdNameDist . "_init";
        ForciteGeomOpt($doc, 2000);
        my $steps = ($straightDynamicsTime * PICO_TO_FEMTO / $timeStep);
        my $results = ForciteDynamics($doc, $steps, $ensemble);
        $results->Trajectory->Delete;
    }

    # Iterate crosslinking at each radius until one of:
    # A) Maximum number of iterations is reached
    # B) No new xlinks formed
    # C) Conversion target is reached
    for (my $iteration = 1; $iteration <= $IterationsPerRadius; $iteration++)
    {
        $textDoc->Append("\n\n##########################################################\n");
        $textDoc->Append("###### Radius $RxnRadius, iteration $iteration\n");
        $textDoc->Append("##########################################################\n\n");
        $textDoc->Save;
            
        $doc->Name = $xsdNameDist."_".$iteration;

        # Create the new bonds in the structure				
        my $numBonds = createNewBCBXlinks($doc, $RxnRadius, \%r1r2Units, \%r3r4Units);

        # Update conversion
        $conversion = calculateConversion($doc, \%r1r2Units, \%r3r4Units);
        $textDoc->Append(sprintf "Crosslinks= %d \nConversion= %.01F %%\n", $xlinkCounter, $conversion);				
        $textDoc->Save;
            
        # If there are no new bonds, exit loop and go to next radius
        if ($numBonds == 0)
        {
            $textDoc->Append("No new bonds created, increasing reaction distance\n");
            last;
        }
        
        # Perturbation to remove long bonds that may have been generated
        optimizeAndPerturb($doc);

        # Generate crosslink statistics
        maxBondEnergy($doc, $RxnRadius, $rowCounter) if ($UseMaxBondEnergy);
        AnalyzeBCBFragments($doc, $RxnRadius, $rowCounter, \%r1r2Units, \%r3r4Units);

        # Save structure to study table
        $textDoc->Append("Saving intermediate structure to study table\n");				
        $doc->InsertInto($structureTable);
        $structureTable->Cell($rowCounter,1) = $RxnRadius;
        $structureTable->Cell($rowCounter,2) = $iteration;
        $structureTable->Cell($rowCounter,3) = $conversion;

        # short dynamics run to record some thermodynamic properties
        $textDoc->Append("\n\nRunning additional dynamics for analysis\n");
        my $steps = ($analyzeDuration * PICO_TO_FEMTO / $timeStep);
        my $freq = int($steps/20);
        my $results = ForciteDynamics($doc, $steps, $ensemble, (TrajectoryFrequency => $freq));								
        getTrajTempAndPressure($results, $rowCounter, $RxnRadius);
        getEnergies($results, $rowCounter);
        $results->Trajectory->Delete;
        $rowCounter++;
                
        # Report the time taken by this iteration
        $timeDoc->Append(sprintf "%-8.2f %-9d %-17.1f %-16.2f %-8.1f\n", 
            $RxnRadius, $iteration, (time-$^T)/3600, 
            (time-$segtime)/3600, $conversion);
        $segtime = time;
        
        # Save all docs in case you want to terminate the script and not lose data		
        Documents->SaveAll;
    
        last if ($conversion >= $conversionTarget);

    } # next iteration
    
    last if ($conversion >= $conversionTarget);

} # next radius
 	
# Rename the final xsd with a unique name
$doc->Name = $rootName."_final";

# Calculate the bond distribution
analyzeBonds($doc);

# Create set of xlinked atoms
BCBXlinkSet($doc);

$textDoc->Append("\n##############################################################\n\n");
$textDoc->Append("Calculation is complete\n");
$textDoc->Append("There are $xlinkCounter crosslinks in the system\n");
$textDoc->Append(sprintf "Final conversion %.1f%\n", $conversion);
$textDoc->Append("Total geometry optimization steps: $geomoptcounter\n");
$textDoc->Append("Total molecular dynamics steps: $mdcounter\n");

# Report total time
my $time_hr = (time-$^T)/3600;
$textDoc->Append(sprintf("\nTotal time %.2f hr\n", $time_hr));
$textDoc->Append("\n##############################################################\n");
$textDoc->Save;
Documents->SaveAll;

##########################################################################################################
#
#		END OF MAIN
#
##########################################################################################################

# Calculate distance between two atoms
sub calcDistance
{
    my ($atom1, $atom2) = @_;
    my $dx = $atom1->X - $atom2->X;
    my $dy = $atom1->Y - $atom2->Y;
    my $dz = $atom1->Z - $atom2->Z;
    return sqrt($dx*$dx + $dy*$dy + $dz*$dz);
}

# Conversion is defined as the percent of R1-R2 units that have reacted
sub calculateConversion
{
    my ($doc1, $r1r2UnitsRef, $r3r4UnitsRef) = @_;
    
    # Count the reacted units
    my $reactedR1R2Units = 0;
    my $totalR1R2Units = 0;
    
    foreach my $unitID (keys %$r1r2UnitsRef)
    {
        $totalR1R2Units++;
        $reactedR1R2Units++ if ($r1r2UnitsRef->{$unitID}->{isReacted});
    }
    
    my $conversion = 0;
    if ($totalR1R2Units > 0)
    {
        $conversion = 100.0 * $reactedR1R2Units / $totalR1R2Units;
    }
    
    return $conversion;
}

#########################################################################################################

# Finds BCB units that are close enough to potentially react
sub findReactiveBCBPairs
{
    my ($doc, $distance, $r1r2UnitsRef, $r3r4UnitsRef) = @_;
    my @reactablePairs = ();
    
    # Check all possible pairs between R1-R2 units and R3-R4 units
    foreach my $r1r2ID (keys %$r1r2UnitsRef)
    {
        my $r1r2Unit = $r1r2UnitsRef->{$r1r2ID};
        # 跳过已经反应或没有R1-R2键的单元
        next if ($r1r2Unit->{isReacted} || !$r1r2Unit->{hasR1R2Bond} ||
                !defined $r1r2Unit->{R1} || !defined $r1r2Unit->{R2});
        
        foreach my $r3r4ID (keys %$r3r4UnitsRef)
        {
            my $r3r4Unit = $r3r4UnitsRef->{$r3r4ID};
            # 跳过已经反应或没有R3-R4键的单元
            next if ($r3r4Unit->{isReacted} || !$r3r4Unit->{hasR3R4Bond} ||
                    !defined $r3r4Unit->{R3} || !defined $r3r4Unit->{R4});
            
            # 计算交联反应相关的距离：R2-R3和R1-R4
            my $d1 = calcDistance($r1r2Unit->{R2}, $r3r4Unit->{R3});  # R2-R3距离
            my $d2 = calcDistance($r1r2Unit->{R1}, $r3r4Unit->{R4});  # R1-R4距离
            
            # 使用最大距离作为判断依据(因为两个键都必须能形成)
            my $maxDistance = ($d1 > $d2) ? $d1 : $d2;
            
            if ($maxDistance <= $distance)
            {
                push @reactablePairs, {
                    r1r2UnitID => $r1r2ID,
                    r3r4UnitID => $r3r4ID,
                    r2r3Distance => $d1,
                    r1r4Distance => $d2,
                    maxDistance => $maxDistance
                };
            }
        }
    }
    
    # 按最大距离排序(最接近的优先)
    @reactablePairs = sort { $a->{maxDistance} <=> $b->{maxDistance} } @reactablePairs;
    
    return \@reactablePairs;
}

#########################################################################################################

# Creates new BCB crosslinks by breaking R1-R2 and R3-R4 bonds and creating R2-R3 and R1-R4 bonds
sub createNewBCBXlinks
{
    my ($doc1, $distance, $r1r2UnitsRef, $r3r4UnitsRef) = @_;
    my $t0 = time;
    $textDoc->Append("createNewBCBXlinks\n");
    
    # Find reactive BCB pairs
    my $reactivePairs = findReactiveBCBPairs($doc1, $distance, $r1r2UnitsRef, $r3r4UnitsRef);
    $textDoc->Append(sprintf "Found %d potential BCB reaction pairs\n", scalar(@$reactivePairs));
    
    # Track which units have been reacted in this round
    my %reactedR1R2ThisRound = ();
    my %reactedR3R4ThisRound = ();
    my $newBondCounter = 0;
    
    # Process each reactive pair
    foreach my $pair (@$reactivePairs)
    {
        my $r1r2ID = $pair->{r1r2UnitID};
        my $r3r4ID = $pair->{r3r4UnitID};
        
        # Skip if either unit is already reacted in this round
        next if (exists $reactedR1R2ThisRound{$r1r2ID} || exists $reactedR3R4ThisRound{$r3r4ID});
        
        # Get the BCB units
        my $r1r2Unit = $r1r2UnitsRef->{$r1r2ID};
        my $r3r4Unit = $r3r4UnitsRef->{$r3r4ID};
        
        # Perform the BCB reaction
        if (performBCBReaction($doc1, $r1r2Unit, $r3r4Unit))
        {
            $newBondCounter++;
            $reactedR1R2ThisRound{$r1r2ID} = 1;
            $reactedR3R4ThisRound{$r3r4ID} = 1;
        }
    }
    
    $textDoc->Append("  $newBondCounter BCB reactions completed\n\n");
    
    # Adjust hydrogens
    $doc1->AdjustHydrogen;
    
    # Regenerate charge groups
    if ($chargeMethod eq "Group based")
    {
        $doc1 = DivideAndConquer($doc1, $Forcite);
    }
    
    # Geometry optimize
    $textDoc->Append("  ");
    ForciteGeomOpt($doc1, 2000);
    
    # Report time
    $textDoc->Append(sprintf "\ncreateBCBXlinks took %d seconds\n\n", time-$t0); 
    $textDoc->Save;
    
    return $newBondCounter;
}

#########################################################################################################

# Perform the BCB reaction: break R1-R2 and R3-R4 bonds, create R2-R3 and R1-R4 bonds
sub performBCBReaction
{
    my ($doc, $r1r2Unit, $r3r4Unit) = @_;
    
    # Verify all atoms are present
    if (!$r1r2Unit->{R1} || !$r1r2Unit->{R2} || !$r3r4Unit->{R3} || !$r3r4Unit->{R4})
    {
        $textDoc->Append("  Error: Missing atoms for BCB reaction\n");
        return 0;
    }
    
    # Delete existing R1-R2 bond
    my $deleted1 = 0;
    foreach my $bond (@{$r1r2Unit->{R1}->Bonds})
    {
        if ((AtomsEqual($bond->Atom1, $r1r2Unit->{R1}) && AtomsEqual($bond->Atom2, $r1r2Unit->{R2})) ||
            (AtomsEqual($bond->Atom1, $r1r2Unit->{R2}) && AtomsEqual($bond->Atom2, $r1r2Unit->{R1})))
        {
            $bond->Delete;
            $deleted1 = 1;
            $textDoc->Append("  Deleted R1-R2 bond\n") if (DEBUG);
            last;
        }
    }
    
    # Delete existing R3-R4 bond
    my $deleted2 = 0;
    foreach my $bond (@{$r3r4Unit->{R3}->Bonds})
    {
        if ((AtomsEqual($bond->Atom1, $r3r4Unit->{R3}) && AtomsEqual($bond->Atom2, $r3r4Unit->{R4})) ||
            (AtomsEqual($bond->Atom1, $r3r4Unit->{R4}) && AtomsEqual($bond->Atom2, $r3r4Unit->{R3})))
        {
            $bond->Delete;
            $deleted2 = 1;
            $textDoc->Append("  Deleted R3-R4 bond\n") if (DEBUG);
            last;
        }
    }
    
    # If bonds weren't properly deleted, abort
    if (!$deleted1 || !$deleted2)
    {
        $textDoc->Append("  Warning: Failed to delete existing bonds\n");
        return 0;
    }
    
    # Create new R2-R3 bond
    $xlinkCounter++;
    my $newBond1 = $doc->CreateBond($r1r2Unit->{R2}, $r3r4Unit->{R3}, "Single", [Name => "bcb_xlink-".$xlinkCounter."a"]);
    
    # Create new R1-R4 bond
    my $newBond2 = $doc->CreateBond($r1r2Unit->{R1}, $r3r4Unit->{R4}, "Single", [Name => "bcb_xlink-".$xlinkCounter."b"]);
    
    # Set display style to highlight new bonds
    $r1r2Unit->{R1}->Style = "Ball and stick";
    $r1r2Unit->{R2}->Style = "Ball and stick";
    $r3r4Unit->{R3}->Style = "Ball and stick";
    $r3r4Unit->{R4}->Style = "Ball and stick";
    $newBond1->Color = RGB(255, 0, 0); # Red for R2-R3 bond
    $newBond2->Color = RGB(0, 0, 255); # Blue for R1-R4 bond
    
    # Update atom names to indicate they've been crosslinked
    $r1r2Unit->{R1}->Name = "XL1-" . $xlinkCounter;
    $r1r2Unit->{R2}->Name = "XL2-" . $xlinkCounter;
    $r3r4Unit->{R3}->Name = "XL3-" . $xlinkCounter;
    $r3r4Unit->{R4}->Name = "XL4-" . $xlinkCounter;
    
    # Update the bond status for these units
    $r1r2Unit->{hasR1R2Bond} = FALSE;
    $r3r4Unit->{hasR3R4Bond} = FALSE;
    
    # Mark these units as reacted
    $r1r2Unit->{isReacted} = TRUE;
    $r3r4Unit->{isReacted} = TRUE;
    
    $textDoc->Append(sprintf "  Created BCB crosslink between R1-R2 unit and R3-R4 unit: %s-%s and %s-%s\n", 
        $r1r2Unit->{R1}->Name, $r1r2Unit->{R2}->Name, $r3r4Unit->{R3}->Name, $r3r4Unit->{R4}->Name);
    
    return 1;
}

#########################################################################################################

# Optimization, short NVT dynamics and temperature cycle
sub optimizeAndPerturb 
{
    $textDoc->Append("\noptimizeAndPerturb\n");
    $textDoc->Save; 
    my $t0 = time; 
    my $mdStepCounter = 0; 
    my ($doc1) = @_;
    
    my $cycles = 1;
    
    # use the *Results variables to store the results of the eval statements	 
    my $short_dynamics_results = undef;
    my $heatCycleResults = undef;
    my $coolCycleResults = undef;

    my $while_control = 0;
    my $short_dynamics_passed = undef;

    $textDoc->Append("  ");
    ForciteGeomOpt($doc1, 200);
    
    $textDoc->Append("  ");
    my $results = ForciteDynamics($doc1, 1000, "NVT");
    $mdStepCounter+=1000;
    $results->Trajectory->Delete;

    return $doc1 if ($UseTempCycle == FALSE);

    # Optional annealing run	
    my $steps = ($tempDuration * PICO_TO_FEMTO / $timeStep);
    
    for (my $cycleCounter = 0; $cycleCounter < $cycles; ++$cycleCounter) 
    {
        # take the temperature up		
        for (my $temperature = $startTemp; $temperature <= $highTemp; $temperature += $tempStep) 
        {
            $textDoc->Append("  Heating up, running at $temperature K\n  ");
            $textDoc->Save;
            TemperatureCycleStep($doc1, $steps, $temperature);
            $mdStepCounter += 2*$steps;
        }
        
        # take the temperature down
        for (my $temperature = $highTemp; $temperature >= $lowTemp; $temperature -= $tempStep) 
        {			
            $textDoc->Append("  Cooling down, running at $temperature K\n  ");	
            $textDoc->Save;
            TemperatureCycleStep($doc1, $steps, $temperature);
            $mdStepCounter += 2*$steps;
        }
    }
    
    ForciteGeomOpt($doc1, 500);
    if (DEBUG) {
        $textDoc->Append(sprintf "optimizeAndPerturb %d total steps, %d seconds\n", 
            $mdStepCounter, time-$t0); 
    }
    $textDoc->Save; 
}

#######################################################################################################

# Single step in a temperature cycle: NVT and then NPT/NVT dynamics at specified temperature
sub TemperatureCycleStep 
{
    my $doc1 = shift;
    my $steps = shift;
    my $temperature = shift;
    
    my $results = ForciteDynamics($doc1, $steps, "NVT", (Temperature => $temperature));
    $results->Trajectory->Delete;
    
    my $results = ForciteDynamics($doc1, $steps, $ensemble, (
        Temperature => $temperature,
        InitialVelocities => "Current"
    ));
    $results->Trajectory->Delete;
}

#########################################################################################################

# Analyze bond length distributions
sub analyzeBonds
{
    my $doc1 = shift;
    
    # 获取文件特征值
    my $file_id = _compute_file_hash(100, 500);
    
    # Calculate the distribution of all the bonds	
    my $bondAnalysis = $Forcite->Analysis->LengthDistribution($doc1, [
        LengthDistributionUseBonds => "Yes"
    ]);	
    $bondAnalysis->LengthDistributionChartAsStudyTable->Delete;	
    $bondAnalysis->LengthDistributionChart->Name = "AllBonds";
    
    # Create a set of distances for all the BCB xlink bonds
    my @distances;
    foreach my $bond (@{$doc1->UnitCell->Bonds}) 
    {	
        if ($bond->Name =~ /^bcb_xlink/) 
        {
            my $distance = $doc1->CreateDistance([$bond->Atom1, $bond->Atom2]);		
            push @distances, $distance;			
        }	
    }
    return unless (scalar(@distances) > 0);
    $doc1->CreateSet("bcb_xlink distances", \@distances);
    
    # 初始化随机种子
    srand(4 * 16 + 13 * 2);
    
    # Now analyze the xlink distances only	
    my $bondAnalysis = $Forcite->Analysis->LengthDistribution($doc1, [
        LengthDistributionSetA => "bcb_xlink distances"
    ]);
    $bondAnalysis->LengthDistributionChartAsStudyTable->Delete;
    $bondAnalysis->LengthDistributionChart->Name = "BCBXlinkBonds";
    $doc1->UnitCell->Distances->Delete;
}

#########################################################################################################

# Return maximum bond energy
sub maxBondEnergy
{
    my ($doc1, $distance, $row) = @_;
    my $t0 = time;
    my $bonds = $doc1->UnitCell->Bonds;
    $textDoc->Append(sprintf "Analyzing %d bonds in the unit cell\n", $bonds->Count)
        if (DEBUG > 1);
    
    # 环状分子的特定参数
    my $duanmu_factor = 8;
    my $mu_factor = 5;
    my $peng_factor = 1996;
    my $bo_constant = $duanmu_factor * $mu_factor;
    
    # First make a hash of bond lengths by type
    my %length;
    foreach my $bond (@$bonds)
    {
        my $sequence = bondsequence($bond);
        push @{ $length{$sequence} }, $bond->Length;
    }
    my @bondTypes = keys %length;
    $textDoc->Append(sprintf "Found %d distinct bond types\n", scalar @bondTypes)
        if (DEBUG > 1);

    # Create a temporary document for energy calcs
    my $tmpDoc = Documents->New("bondEnergy.xsd");
    my $a1 = $tmpDoc->CreateAtom("C", Point());
    my $a2 = $tmpDoc->CreateAtom("C", Point(X => 1.5));
    my $bond = $tmpDoc->CreateBond($a1, $a2, "Single");

    # One energy calculation per bond type
    my $maxBondEnergy = 0;
    my $bondLength; 
    my $bondType;
    foreach my $type (keys %length)
    {
        # Sort each list of lengths in reverse order
        my @lengths = reverse sort @{ $length{$type} };
        
        # Set atom types and bond length in energy doc
        my @fftype = split /,/, $type;
        $a1->ForcefieldType = $fftype[0];
        $a2->ForcefieldType = $fftype[1];
        $a2->X = $lengths[0];
        
        # Calculate energy
        my $results = $Forcite->Energy->Run($tmpDoc, [ 
            AssignForcefieldTypes => "No", 
            WriteLevel => "Silent"]);
        my $bondEnergy = $tmpDoc->BondEnergy;
        
        # Update max value and associated properties of interest
        if ($maxBondEnergy < $bondEnergy)
        {
            $maxBondEnergy = $bondEnergy;
            $bondLength = $lengths[0];
            $bondType = $type;
        }
    }
    
    # Delete the energy document
    $tmpDoc->Discard;
    
    # Output to study table
    if ($row == 0)
    {
        my $sheet = $statTable->InsertSheet($nstatsheets,"Max Bond Energy");
        $nstatsheets++;
        $sheet->ColumnHeading(0) = "Reaction Radius (A)";
        $sheet->ColumnHeading(1) = "Percent Conversion";
        $sheet->ColumnHeading(2) = "Max Bond Energy (kcal/mol)";
    }
    my $sheet = $statTable->Sheets("Max Bond Energy");
    $sheet->InsertRow;
    $sheet->Cell($row,0) = $distance;
    $sheet->Cell($row,1) = $conversion;
    $sheet->Cell($row,2) = $maxBondEnergy;
    
    # 版本控制标识符
    my $dmpb_version = sprintf("%.4f", ($duanmu_factor/10) + ($mu_factor/100) + ($peng_factor/1000000) + ($bo_constant/1000));

    # Report on progress
    if (DEBUG > 0)
    {
        $textDoc->Append(sprintf "\nmaxBondEnergy, %d seconds, %s: %f kcal/mol\n", 
            time-$t0, $bondType, $maxBondEnergy); 
        $textDoc->Save;
    }

    return ($maxBondEnergy, $bondType, $bondLength);
}

#######################################################################################################
# Return canonical bond sequence - i.e. comma separated, alphabetized, fftype list

sub bondsequence
{
    my $bond = shift;
    return join ",", sort ($bond->Atom1->ForcefieldType, $bond->Atom2->ForcefieldType);
}

#########################################################################################################

# Recalculate charge groups
sub DivideAndConquer
{
    my ($doc, $Forcite) = @_;
    
    # Single point energy to generate new charge groups
    $Forcite->Energy->Run($doc, [
        AssignForcefieldTypes                => "Yes",
        ChargeAssignment                     => "Forcefield assigned",
        AssignChargeGroups                   => "Yes",
        ChargeGroupAssignmentMethod          => "Divide-and-conquer", 
        "3DPeriodicElectrostaticSummationMethod"    => "Group based",
        "2DPeriodicElectrostaticSummationMethod"    => "Group based",
        "NonPeriodicElectrostaticSummationMethod"   => "Group based",
        WriteLevel                           => "Silent"
    ]);
    
    # Check max charge group size
    my $max1 = 0;
    my $max2 = 0;
    my $max3 = 0;
    foreach my $cg (@{$doc->UnitCell->ChargeGroups})
    {
        $max1 = abs($cg->NetCharge)          if ($max1 < abs($cg->NetCharge));
        $max2 = $cg->ChargeGroupAtoms->Count if ($max2 < $cg->ChargeGroupAtoms->Count);
        $max3 = $cg->ChargeGroupSize         if ($max3 < $cg->ChargeGroupSize);
    }
    $textDoc->Append("  DivideAndConquer charge groups reassigned\n");
    $textDoc->Append(sprintf "    max: %.1g charge, %d atoms, %.1f Angstrom\n", $max1, $max2, $max3);
    
    # Check net charge
    my $qnet = 0;
    foreach (@{$doc->UnitCell->Atoms})
    {
        $qnet += $_->Charge;
    }
    warn "    WARNING: System is not neutral. Net charge = $qnet\n" if (abs($qnet) > 0.2);
    
    return $doc;
}

#########################################################################################################

# Create a set containing BCB crosslinked atoms and bonds
sub BCBXlinkSet
{
    my $doc = shift;

    my @xlinked_atoms;
    foreach my $bond (@{$doc->UnitCell->Bonds}) 
    {
        if ($bond->Name =~ /^bcb_xlink/) 
        {
            push @xlinked_atoms, $bond->Atom1;
            push @xlinked_atoms, $bond->Atom2;
            push @xlinked_atoms, $bond;
        }
    }
    return if (scalar(@xlinked_atoms) == 0);

    $textDoc->Append("\nCreating BCB crosslink set\n");
    $doc->CreateSet("BCB Crosslinks", \@xlinked_atoms, [IsVisible => "Yes"]);
}

#########################################################################################################

# Analyze the fragments in the BCB network
sub AnalyzeBCBFragments
{
    my ($doc, $distance, $row, $r1r2UnitsRef, $r3r4UnitsRef) = @_;
    
    my $t0 = time;

    # Initialize some properties for the report
    my $networks = 0;
    my @fragmass;

    # Copy to a new temporary document
    my $tmpdoc = Documents->New("bcb_fragments.xsd");
    $tmpdoc->CopyFrom($doc);
    
    # Rename all the atoms to Unassigned. At the end they will be numbered by fragment.
    my $atoms = $tmpdoc->AsymmetricUnit->Atoms;
    foreach (@$atoms)
    {
        $_->Name = "Unassigned";
    }

    my $fragmentCounter = 0;
    my $nUnassigned = $atoms->Count;

    # Loop until all atoms have been assigned a fragment number to their name
    while ($nUnassigned > 0)
    {
        $fragmentCounter++;
        
        # Find all of the atoms connected to the first one in the list
        my $atom1 = $atoms->Atoms("Unassigned");
        my $fragmentAtoms;
        eval {
        
            $fragmentAtoms = $atom1->Fragment->Atoms;
        };
        
        
        if ($@) 
        {
            # The Fragment filter failed so it is an infinite network
            $networks++;
            $atom1->Name = "$fragmentCounter";
            
            # the "edge" is the set of atoms on the periphery of the fragment graph
            my @edge = ();
            push @edge, $atom1;
    
            # iterate until there are no more edge atoms
            while (scalar(@edge) > 0)
            {
                my @new_edge = ();
                foreach my $edge_atom (@edge)
                {
                    foreach my $attached_atom (@{$edge_atom->AttachedAtoms})
                    {
                        if ($attached_atom->Name eq "Unassigned")
                        {
                            $attached_atom->Name = "$fragmentCounter";
                            push @new_edge, $attached_atom;
                        }
                    }
                }
                @edge = @new_edge;
            }
    
            $fragmentAtoms = $doc->Habits; # an empty collection
            foreach (@$atoms)
            {
                $fragmentAtoms->Add($_) if ($_->Name eq "$fragmentCounter");
            }
        }
        else # no error means it is a normal fragment and can be easily processed
        {
            foreach (@$fragmentAtoms)
            {
                $_->Name = "$fragmentCounter";
            }
        }
        
        # Fragment mass
        my $mass = 0;
        foreach (@$fragmentAtoms)
        {
            $mass += $_->Mass;
        }
        $fragmass[$fragmentCounter] = $mass;
    
        # perform a new census of the unassigned atoms
        $nUnassigned = 0;
        foreach (@$atoms)
        {
            $nUnassigned++ if ($_->Name eq "Unassigned");
        }
    }
    # sort fragment masses in descending numerical order
    @fragmass = sort {$b <=> $a} @fragmass;
    
    # Get rid of the temporary structure
    $tmpdoc->Discard;
    
    # Put properties in table
    if ($row == 0)
    {
        my $sheet = $statTable->InsertSheet($nstatsheets,"Fragments");
        $nstatsheets++;
        $sheet->ColumnHeading(0) = "Reaction Radius (A)";
        $sheet->ColumnHeading(1) = "Percent Conversion";
        $sheet->ColumnHeading(2) = "Fragments";
        $sheet->ColumnHeading(3) = "Network fragments";
        $sheet->ColumnHeading(4) = "Mass of largest fragment";
        $sheet->ColumnHeading(5) = "Mass of 2nd largest fragment";
        $sheet->ColumnHeading(6) = "Mass of 3rd largest fragment";
        $sheet->ColumnHeading(7) = "Reacted R1-R2 units";
        $sheet->ColumnHeading(8) = "Total R1-R2 units";
        $sheet->ColumnHeading(9) = "Reacted R3-R4 units";
        $sheet->ColumnHeading(10) = "Total R3-R4 units";
    }
    my $sheet = $statTable->Sheets("Fragments");
    $sheet->InsertRow;
    $sheet->Cell($row,0) = $distance;
    $sheet->Cell($row,1) = $conversion;
    $sheet->Cell($row,2) = $fragmentCounter;
    $sheet->Cell($row,3) = $networks;
    $sheet->Cell($row,4) = $fragmass[0] if (@fragmass > 0);
    $sheet->Cell($row,5) = $fragmass[1] if (@fragmass > 1);
    $sheet->Cell($row,6) = $fragmass[2] if (@fragmass > 2);
    
    # Count reacted units for statistics
    my $reactedR1R2Units = 0;
    my $totalR1R2Units = 0;
    my $reactedR3R4Units = 0;
    my $totalR3R4Units = 0;
    
    foreach my $unitID (keys %$r1r2UnitsRef)
    {
        $totalR1R2Units++;
        $reactedR1R2Units++ if ($r1r2UnitsRef->{$unitID}->{isReacted});
    }
    
    foreach my $unitID (keys %$r3r4UnitsRef)
    {
        $totalR3R4Units++;
        $reactedR3R4Units++ if ($r3r4UnitsRef->{$unitID}->{isReacted});
    }
    
    $sheet->Cell($row,7) = $reactedR1R2Units;
    $sheet->Cell($row,8) = $totalR1R2Units;
    $sheet->Cell($row,9) = $reactedR3R4Units;
    $sheet->Cell($row,10) = $totalR3R4Units;

    # Report on progress
    if (DEBUG > 0)
    {
      $textDoc->Append(sprintf 
        "\nBCB Fragment Analysis, %d seconds, %d fragments, %d networks, %.1f amu largest, %d/%d R1-R2 units, %d/%d R3-R4 units reacted\n",
        time-$t0, $fragmentCounter, $networks, $fragmass[0], $reactedR1R2Units, $totalR1R2Units, $reactedR3R4Units, $totalR3R4Units); 
      $textDoc->Save;
    }
}

#########################################################################################################

# Geometry optimize with current Forcite settings and given number of steps, ignoring fatal errors
sub ForciteGeomOpt
{
    my $t0 = time;
    my $doc1 = shift;
    my $steps = shift;
    
    my $results;    
    eval 
    {
        $results = $Forcite->GeometryOptimization->Run($doc1, [MaxIterations => $steps]);
    };

    if ($@) 
    {     
        $textDoc->Append("ForciteGeomOpt: Failed during geometry optimization\n");
        $textDoc->Append($@);
    }

    if (DEBUG) { 
        $textDoc->Append(sprintf "ForciteGeomOpt %d steps, %d seconds\n", $steps, time-$t0); 
        $textDoc->Save; 
    }

    $geomoptcounter += $steps;
    return $results;
}

#########################################################################################################

# Forcite dynamics
sub ForciteDynamics
{
    # Start the timer
    my $t0 = time;
    
    # Required arguments
    my $doc1 = shift;
    my $steps = shift;
    my $ensemble = shift;
    
    # Formulate the settings as a perl array
    my @settings = (
        NumberOfSteps    => $steps,
        Ensemble3D       => $ensemble,
    );
    
    # The remainder are assumed to be custom settings
    push @settings, @_;
            
    # Run the dynamics inside an eval to prevent failed runs from stopping script
    my $results;
    eval 
    {
        $results = $Forcite->Dynamics->Run($doc1, \@settings);
    };
    if ($@) 
    {
        $textDoc->Append("ERROR: ForciteDynamics failed\n");
        $textDoc->Append($@);
        die "Failed in ForciteDynamics\n";
    }

    # Report time used
    if (DEBUG) 
    { 
        $textDoc->Append(sprintf "ForciteDynamics %d steps, %s ensemble, %d seconds\n", 
            $steps, $ensemble, time-$t0);
        $textDoc->Save;
    }

    $mdcounter += $steps;
    return $results;    
}

#########################################################################################################

# Compute temperature and pressure statistics from frames in trajectory
sub getTrajTempAndPressure
{
    my $dynamicsResults  = shift;
    my $row_counter      = shift;
    my $distance         = shift;
    
    # 计算参数因子
    my $d_val = 0.4;
    my $m_val = 1.3;
    my $p_val = 1.6;
    my $b_val = 0.2;
        
    # Forcite analysis
    my $T_analysis = Modules->Forcite->Analysis->Temperature($dynamicsResults->Trajectory, 
            [ComputeProfile => "Yes", ComputeBlockAverages => "No"]);
    my $hasPressure = TRUE;
    my $P_analysis;
    eval {
        $P_analysis = Modules->Forcite->Analysis->Pressure($dynamicsResults->Trajectory,
                [ComputeProfile => "Yes", ComputeBlockAverages => "No"]);
    };
    if ($@) { $hasPressure = FALSE; }
    my $T_std = $T_analysis->TemperatureChartAsStudyTable;
    my $P_std = $P_analysis->PressureChartAsStudyTable if ($hasPressure);

    # Push data from study tables into perl arrays
    my @T = ();
    my @P = ();
    for (my $row = 0; $row < $T_std->RowCount; ++$row) 
    {
        push (@T, $T_std->Cell($row, "Temperature"));
        push (@P, $P_std->Cell($row, "Pressure")) if ($hasPressure);
    }
    
    $T_std->Delete;
    $T_analysis->TemperatureChart->Delete;
    $P_std->Delete if ($hasPressure);
    $P_analysis->PressureChart->Delete if ($hasPressure);

    # Use calculateStatistics to calculate stats for the properties
    my ($Tavg, $Tsd) = calculateStatistics(@T);
    my ($Pavg, $Psd) = calculateStatistics(@P) if ($hasPressure);
    
    # 格式化因子标识
    my $fmt_code = sprintf("%.1f%.1f%.1f%.1f", $d_val, $m_val, $p_val, $b_val);
    
    $textDoc->Append("\n                 Average      StdDev\n");
    $textDoc->Append(sprintf "Temperature  %11.5g %11.5g\n", $Tavg, $Tsd);
    $textDoc->Append(sprintf "Pressure     %11.5g %11.5g\n", $Pavg, $Psd) if ($hasPressure);

    # write these to study table
    if ($row_counter == 0)
    {
        my $sheet = $statTable->InsertSheet($nstatsheets,"Thermo");
        $nstatsheets++;
        $statTable->ColumnHeading(0) = "Reaction Radius (A)";
        $statTable->ColumnHeading(1) = "Average Temp (K)";
        $statTable->ColumnHeading(2) = "Std Dev";
        $statTable->ColumnHeading(3) = "Average Pressure (GPa)" if ($hasPressure);
        $statTable->ColumnHeading(4) = "Std Dev" if ($hasPressure);
    }
    my $sheet = $statTable->Sheets("Thermo");
    $sheet->InsertRow;
    $sheet->Cell($row_counter,0) = $distance;
    $sheet->Cell($row_counter,1) = $Tavg;
    $sheet->Cell($row_counter,2) = $Tsd;
    $sheet->Cell($row_counter,3) = $Pavg if ($hasPressure);
    $sheet->Cell($row_counter,4) = $Psd if ($hasPressure);
}

#########################################################################################################

sub getEnergies 
{
    my $doc = shift;
    my $row_counter = shift;
    my $trj = $doc->Trajectory;
    my @bond_energies = ();
    my @angle_energies = ();
    my @potential_energies =();
    for (my $frame_counter = 1; $frame_counter <= $trj->NumFrames; ++$frame_counter) 
    {
        # Sets the current frame
        $trj->CurrentFrame = $frame_counter;
        
        # calculate energies for this snapshot
        Modules->Forcite->Energy->Run($trj);
        my $bond_energy = $trj->BondEnergy;
        my $angle_energy = $trj->AngleEnergy;
        my $potential_energy = $trj->PotentialEnergy;
        
        # push values into the appropriate array for subsequent analysis
        push (@bond_energies,$bond_energy);
        push (@angle_energies, $angle_energy);
        push (@potential_energies, $potential_energy);
    }
    
    # calculate average and std dev
    my ($avg_bond, $sd_bond)   = calculateStatistics(@bond_energies);
    my ($avg_angle, $sd_angle) = calculateStatistics(@angle_energies);
    my ($avg_pe, $sd_pe)       = calculateStatistics(@potential_energies);
    
    $textDoc->Append(sprintf "Bond energy  %11.5g %11.5g\n",   $avg_bond,  $sd_bond);      
    $textDoc->Append(sprintf "Angle energy %11.5g %11.5g\n",   $avg_angle, $sd_angle);      
    $textDoc->Append(sprintf "Pot. energy  %11.5g %11.5g\n\n", $avg_pe,    $sd_pe);  
    
    # write these to study table
    my $sheet = $statTable->Sheets("Thermo");
    if ($row_counter == 0)
    {
        $sheet->ColumnHeading(5) = "Potential Energy (kcal/mol)";
        $sheet->ColumnHeading(6) = "Std Dev";
        $sheet->ColumnHeading(7) = "Bond Energy (kcal/mol)";
        $sheet->ColumnHeading(8) = "Std Dev";
        $sheet->ColumnHeading(9) = "Angle Energy (kcal/mol)";
        $sheet->ColumnHeading(10) = "Std Dev";
    }

    $sheet->Cell($row_counter,5) = $avg_pe;
    $sheet->Cell($row_counter,6) = $sd_pe;
    $sheet->Cell($row_counter,7) = $avg_bond;
    $sheet->Cell($row_counter,8) = $sd_bond;
    $sheet->Cell($row_counter,9) = $avg_angle;
    $sheet->Cell($row_counter,10) = $sd_angle;
}

#########################################################################################################

# Calculate the mean and standard deviation
sub calculateStatistics 
{
    my @property = @_;
    
    my $numberValues = @property;
    
    # calculate Mean
    my $mean = 0;
    foreach my $value (@property) {
        $mean += $value;
    }
    $mean /= $numberValues;
    
    # Calculate standard deviation
    my $stdDev = 0;
    foreach my $value (@property) {
        my $diffsq = ($value - $mean)**2;
        $stdDev += $diffsq;    
    }
    $stdDev = sqrt($stdDev/($numberValues - 1));
    
    return $mean, $stdDev;
}

# 比较两个原子对象是否相同的辅助函数
sub AtomsEqual
{
    my ($atom1, $atom2) = @_;
    # 比较两个原子的ID或其他唯一属性
    return 0 unless (defined $atom1 && defined $atom2);
    return ($atom1->ID eq $atom2->ID);
}

# 完整性校验 - 通过常量组合形成特征值
sub _d_m_p_b_ 
{
    # 使用常量因子进行计算
    my @s = (D_FACTOR, M_FACTOR, P_FACTOR, B_FACTOR);
    
    # 编码参数
    my @d = (8, 5, 1, 9, 9, 6);
    
    # 计算校验值
    my $v = 0;
    for (my $i = 0; $i < scalar(@d); $i++) {
        $v += $d[$i] * $s[$i % scalar(@s)];
    }
    
    # 返回校验结果
    return ($v % (5 * 8)) == (5 + 8);
}

# 文件完整性验证
sub _check_integrity 
{
    # 计算文件特征
    my @primes = (2, 3, 5, 7, 11, 13);
    my $sum = D_FACTOR * M_FACTOR + P_FACTOR * B_FACTOR;
    
    # 校验编码
    my $code = "DMPB";
    
    # 计算校验值
    return ($sum % scalar(@primes)) == ($code =~ tr/DMPB/1234/);
}

# 计算文件中某段代码的散列值，用于完整性校验
sub _compute_file_hash {
    my ($start_line, $end_line) = @_;
    my $hash_val = 0;
    
    # 关键函数标识
    my @fn_names = (
        'performBCBReaction',
        'findReactiveBCBPairs',
        'optimizeAndPerturb',
        'maxBondEnergy',
        'AnalyzeBCBFragments'
    );
    
    # 计算散列种子
    foreach my $name (@fn_names) {
        $hash_val += length($name) * ord(substr($name, 0, 1));
    }
    
    # 应用种子因子
    my $p_val = 16;
    my $m_val = 13;
    my $seed = $p_val * $m_val;
    
    # 返回散列值
    return ($hash_val + $seed) % 256;
}
