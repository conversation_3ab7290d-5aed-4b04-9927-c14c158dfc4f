 目的: 	适用于苯并环丁烯（BCB）的交联脚本


# 本脚本用于实现苯并环丁烯(BCB)的交联反应
# 核心反应为：苯并四元环上外侧的两个碳原子R1和R2之间的单键打开，
# 与另一个打开的苯并四元环上的R1和R2进行交联，形成苯并八元环结构

# 执行以下步骤:
#   1. 一次性平衡动力学
#   2. 更新反应半径
#   3. 创建新的交联键 (createNewXlinks)
#   4. 通过优化和动力学松弛结构
#   5. 断开苯并四元环上的R1-R2键
#   6. 调整氢原子
#   7. 重新计算电荷组(可选)
#   8. 进行温度循环退火(可选)
#   9. 将交联数据写入研究表
#  10. 重复步骤2-9直至达到目标转化率或最大半径

# 输入文件:
# (1) 含有标记为R1和R2的反应原子的原子级xsd文件

# 输出文件:
# (1) xlink_final.xsd - 最终交联结构
# (2) xlink_statistics.std - 交联和热力学数据表
# (3) xlink_structures.std - 不同转化水平的中间结构表
# (4) XlinkBonds.xcd - 键长分布，用于检查拉伸键
# (5) Progress.txt - 连续更新的日志文件
