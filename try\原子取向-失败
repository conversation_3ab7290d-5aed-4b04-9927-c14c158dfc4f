#!perl

use strict;
use warnings;
use Math::Trig;
use MaterialsScript qw(:all);

# 打开当前文档
my $doc = $Documents{"Current.xsd"};
die "没有打开的文档!" unless $doc;

# 初始化参数
my $timeStep = 0.001;        # 时间步长，单位：ps
my $totalTime = 100.0;       # 总模拟时间，单位：ps
my $interval = 0.5;          # 每次模拟间隔，单位：ps
my $period = 5.0;            # 超声波周期，单位：ps
my $forceAmplitude = 0.5;    # 力振幅，单位：kcal/mol/Å
my $breakBondLength = 1.76;  # 断键长度阈值，单位：Å
my $temperature = 413.15;    # 模拟温度，单位：K

# 分子动力学模拟循环
my $currentTime = 0.0;
my $step = 0;

while ($currentTime < $totalTime) {
    $step++;
    my $time = $currentTime;
    
    # 尝试删除可能存在的轨迹文件，以避免文件冲突
    eval {
        if (exists $Documents{"Current.xtd"}) {
            $Documents{"Current.xtd"}->Delete();
            print "已删除之前的轨迹文件\n";
        }
    };
    
    # 计算当前时间点的力
    my $force = $forceAmplitude * sin($time / $period * 2 * pi * 20000);
    my $externalForceZ = $force > 0 ? 1.0 : -1.0;
    my $externalForceStrength = abs($force);
    
    # 输出当前状态信息
    my $logMsg = sprintf("步骤: %d, 时间: %.3f ps, 力大小: %.4f, 力方向Z: %.1f", 
                        $step, $time, $externalForceStrength, $externalForceZ);
    print "$logMsg\n";
    
    # 执行断键操作
    my $bonds = $doc->UnitCell->Bonds;
    my $brokenBonds = 0;
    foreach my $bond (@$bonds) {
        if ($bond->Length > $breakBondLength) {
            $bond->Delete;
            $brokenBonds++;
        }
    }
    print "断开了 $brokenBonds 个键\n";
    
    # 几何优化以弛豫能量
    print "执行几何优化...\n";
    my $optResults = Modules->Forcite->GeometryOptimization->Run($doc, Settings(
        Quality => 'Fine',
        CurrentForcefield => 'COMPASSIII',
        ChargeAssignment => 'Use current',
        MaxIterations => 500
    ));
    
    # 设置和运行NVT动力学模拟
    print "执行NVT动力学模拟...\n";
    
    # 在每次运行前尝试强制关闭可能存在的轨迹文档
    eval {
        if (exists $Documents{"Current.xtd"}) {
            $Documents{"Current.xtd"}->Close(1);
            print "已关闭轨迹文件\n";
        }
    };
    
    # 使用简化方法运行动力学模拟，minimize轨迹文件的影响
    my $results = Modules->Forcite->Dynamics->Run($doc, Settings(
        CurrentForcefield => "COMPASSIII",
        Quality => "Fine",
        Ensemble3D => "NVT",
        Temperature => $temperature,
        NumberOfSteps => int($interval / $timeStep),
        Thermostat => 'NHL',
        TimeStep => $timeStep * 1000, # 转换为fs
        TrajectoryFrequency => int($interval / $timeStep), # 设置为最大值，只记录最后一帧
        ExternalForceSet => "MySet",
        ExternalForceStrength => $externalForceStrength,
        ExternalForceZ => $externalForceZ,
        CounterExternalForce => "No"
    ));
    
    # 确保结果正确保存到当前文档
    $doc->UpdateView();
    
    # 保存当前结构的快照
    my $snapshot = $doc->SaveAs("Step_${step}.xsd");
    $snapshot->Close;
    
    # 更新时间
    $currentTime += $interval;
}

# 保存最终结构
$doc->SaveAs("Final_Structure.xsd");

print "超声场模拟完成!\n";
