#!/usr/bin/env python3
"""
CASTEP电导率计算主程序
基于Materials Studio CASTEP模块的DFT电子结构结果计算电导率

作者: AI Assistant
版本: 1.0
日期: 2024-01-15
"""

import argparse
import sys
import os
import numpy as np
from typing import Optional, Dict, Any

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.io.file_reader import CastepFileReader
from src.core.conductivity_calculator import ConductivityCalculator
from src.visualization.plotter import ConductivityPlotter
from src.io.result_exporter import ResultExporter
from src.utils.constants import DEFAULT_FIT_POINTS, DEFAULT_ENERGY_THRESHOLD

class CastepConductivityApp:
    """CASTEP电导率计算应用程序主类"""
    
    def __init__(self):
        self.reader = CastepFileReader()
        self.calculator = ConductivityCalculator()
        self.plotter = ConductivityPlotter()
        self.exporter = ResultExporter()
        
        # 存储计算结果
        self.energy = None
        self.epsilon_1 = None
        self.epsilon_2 = None
        self.omega = None
        self.sigma_omega = None
        self.fit_result = None
    
    def load_data(self, input_file: str) -> bool:
        """
        加载CASTEP介电数据文件
        
        Args:
            input_file (str): 输入文件路径
            
        Returns:
            bool: 是否成功加载
        """
        try:
            print(f"正在读取文件: {input_file}")
            
            # 获取文件信息
            file_info = self.reader.get_file_info(input_file)
            print(f"文件信息: {file_info['file_name']}, 大小: {file_info['file_size']} bytes")
            
            # 读取数据
            self.energy, self.epsilon_1, self.epsilon_2 = self.reader.read_epsilon_file(input_file)
            
            print(f"成功读取 {len(self.energy)} 个数据点")
            print(f"能量范围: {self.energy[0]:.6f} - {self.energy[-1]:.3f} eV")
            print(f"ε₂范围: {np.min(self.epsilon_2):.6e} - {np.max(self.epsilon_2):.6e}")
            
            return True
            
        except Exception as e:
            print(f"读取文件失败: {str(e)}")
            return False
    
    def calculate_conductivity(self) -> bool:
        """
        计算光学导率
        
        Returns:
            bool: 是否成功计算
        """
        try:
            print("\n正在计算光学导率...")
            
            # 计算光学导率
            self.omega, self.sigma_omega = self.calculator.calculate_optical_conductivity(
                self.energy, self.epsilon_2
            )
            
            print(f"光学导率计算完成")
            print(f"σ(ω)范围: {np.min(self.sigma_omega):.6e} - {np.max(self.sigma_omega):.6e} S/m")
            
            return True
            
        except Exception as e:
            print(f"光学导率计算失败: {str(e)}")
            return False
    
    def calculate_dc_conductivity(self, fit_points: int = DEFAULT_FIT_POINTS,
                                energy_threshold: float = DEFAULT_ENERGY_THRESHOLD,
                                method: str = 'linear') -> bool:
        """
        计算直流电导率
        
        Args:
            fit_points (int): 拟合点数
            energy_threshold (float): 能量阈值
            method (str): 拟合方法
            
        Returns:
            bool: 是否成功计算
        """
        try:
            print(f"\n正在计算直流电导率 (方法: {method}, 拟合点数: {fit_points})...")
            
            # 计算直流电导率
            self.fit_result = self.calculator.extrapolate_dc_conductivity(
                self.energy, self.sigma_omega, fit_points, energy_threshold, method
            )
            
            sigma_dc = self.fit_result['sigma_dc']
            r_squared = self.fit_result['r_squared']
            
            print(f"直流电导率计算完成")
            print(f"σ_dc = {sigma_dc:.6e} S/m")
            print(f"拟合质量 R² = {r_squared:.6f}")
            
            if r_squared < 0.9:
                print("警告: 拟合质量较低，建议检查数据或调整拟合参数")
            
            return True
            
        except Exception as e:
            print(f"直流电导率计算失败: {str(e)}")
            return False
    
    def export_results(self, output_file: str, export_all: bool = False) -> bool:
        """
        导出计算结果
        
        Args:
            output_file (str): 输出文件路径
            export_all (bool): 是否导出所有格式
            
        Returns:
            bool: 是否成功导出
        """
        try:
            print(f"\n正在导出结果...")
            
            if export_all:
                # 导出所有格式
                base_name = os.path.splitext(output_file)[0]
                output_files = self.exporter.export_all_formats(
                    self.energy, self.omega, self.sigma_omega,
                    self.epsilon_1, self.epsilon_2, self.fit_result, base_name
                )
                print(f"已导出所有格式文件: {list(output_files.keys())}")
            else:
                # 仅导出CSV格式
                self.exporter.export_to_csv(
                    self.energy, self.omega, self.sigma_omega,
                    self.epsilon_1, self.epsilon_2, output_file
                )
            
            return True
            
        except Exception as e:
            print(f"结果导出失败: {str(e)}")
            return False
    
    def generate_plots(self, save_plots: bool = False, output_dir: str = "plots") -> bool:
        """
        生成图表
        
        Args:
            save_plots (bool): 是否保存图表
            output_dir (str): 图表保存目录
            
        Returns:
            bool: 是否成功生成
        """
        try:
            print(f"\n正在生成图表...")
            
            if save_plots:
                # 保存所有图表
                self.plotter.save_all_plots(
                    self.energy, self.epsilon_1, self.epsilon_2, self.sigma_omega,
                    self.fit_result, output_dir
                )
            else:
                # 仅显示综合分析图
                self.plotter.plot_comprehensive_analysis(
                    self.energy, self.epsilon_1, self.epsilon_2, self.sigma_omega,
                    self.fit_result
                )
            
            return True
            
        except Exception as e:
            print(f"图表生成失败: {str(e)}")
            return False
    
    def print_summary(self) -> None:
        """打印计算摘要"""
        print("\n" + "="*60)
        print("CASTEP电导率计算摘要")
        print("="*60)
        
        if self.energy is not None:
            print(f"数据点数: {len(self.energy)}")
            print(f"能量范围: {self.energy[0]:.6f} - {self.energy[-1]:.3f} eV")
        
        if self.sigma_omega is not None:
            print(f"光学导率范围: {np.min(self.sigma_omega):.6e} - {np.max(self.sigma_omega):.6e} S/m")
        
        if self.fit_result is not None:
            print(f"直流电导率 σ_dc: {self.fit_result['sigma_dc']:.6e} S/m")
            print(f"拟合方法: {self.fit_result['method']}")
            print(f"拟合质量 R²: {self.fit_result['r_squared']:.6f}")
        
        print("="*60)

def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="CASTEP电导率计算程序",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python castep_conductivity_calculator.py -i data.epsilon -o results.csv
  python castep_conductivity_calculator.py -i data.csv --fit_points 10 --method polynomial
  python castep_conductivity_calculator.py -i data.xlsx --export_all --save_plots
        """
    )
    
    # 必需参数
    parser.add_argument('-i', '--input', required=True,
                       help='输入文件路径 (.epsilon, .csv, .txt, .xlsx)')
    
    # 可选参数
    parser.add_argument('-o', '--output', default='conductivity_results.csv',
                       help='输出文件路径 (默认: conductivity_results.csv)')
    
    parser.add_argument('--fit_points', type=int, default=DEFAULT_FIT_POINTS,
                       help=f'直流电导率外推的拟合点数 (默认: {DEFAULT_FIT_POINTS})')
    
    parser.add_argument('--energy_threshold', type=float, default=DEFAULT_ENERGY_THRESHOLD,
                       help=f'低能阈值 (eV) (默认: {DEFAULT_ENERGY_THRESHOLD})')
    
    parser.add_argument('--method', choices=['linear', 'polynomial', 'exponential'],
                       default='linear', help='拟合方法 (默认: linear)')
    
    parser.add_argument('--export_all', action='store_true',
                       help='导出所有格式 (CSV, Excel, JSON, 摘要)')
    
    parser.add_argument('--save_plots', action='store_true',
                       help='保存图表到文件')
    
    parser.add_argument('--plot_dir', default='plots',
                       help='图表保存目录 (默认: plots)')
    
    parser.add_argument('--no_plots', action='store_true',
                       help='不显示图表')
    
    return parser

def main():
    """主函数"""
    # 解析命令行参数
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 创建应用程序实例
    app = CastepConductivityApp()
    
    print("CASTEP电导率计算程序 v1.0")
    print("-" * 40)
    
    # 加载数据
    if not app.load_data(args.input):
        sys.exit(1)
    
    # 计算光学导率
    if not app.calculate_conductivity():
        sys.exit(1)
    
    # 计算直流电导率
    if not app.calculate_dc_conductivity(args.fit_points, args.energy_threshold, args.method):
        sys.exit(1)
    
    # 导出结果
    if not app.export_results(args.output, args.export_all):
        sys.exit(1)
    
    # 生成图表
    if not args.no_plots:
        app.generate_plots(args.save_plots, args.plot_dir)
    
    # 打印摘要
    app.print_summary()
    
    print("\n计算完成!")

if __name__ == "__main__":
    main()
