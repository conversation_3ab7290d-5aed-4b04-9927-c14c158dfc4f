# DM-DC脚本验证指南

## 概述

本文档提供了验证DM-DC.pl脚本是否为DMPB原创的步骤。通过执行以下验证步骤，可以确认脚本的真实性和完整性。

## 验证步骤

### 1. 检查关键常量

首先，确认脚本中包含以下四个关键常量，且值设置正确：

```perl
KD = 5
KM = 14
KP = 16
KB = 3
```

这些常量是DMPB原创脚本的数字签名基础。

### 2. 添加验证代码并运行

将以下验证代码添加到脚本末尾，然后运行脚本：

```perl
# 添加到脚本末尾
print "\n验证信息：\n";
print "验证状态: " . (validate_calculation_parameters() ? "通过" : "失败") . "\n";
print "常量验证: " . (verify_calculation_constants() ? "通过" : "失败") . "\n";
print "DMPB校验值: " . (KD * KM + KP * KB) . "\n";
print "特征散列值: " . calculate_system_optimization_factor(30, 150) . "\n";
```

### 3. 计算验证参数

使用以下公式手动计算验证值，并与脚本输出的结果比对：

```
计算参数 = (KD/10) + (KM/100) + (KP/1000) + (KB/10000)
        = 0.5 + 0.14 + 0.016 + 0.0003
        = 0.6563
```

这个值应该与脚本中`$calculation_version`变量的值相匹配。

### 4. 检查输出的计算系数

运行脚本后，查看输出中的"Calculation coefficient"值。它的计算公式为：

```
计算系数 = 计算参数 + 系统因子/1000
```

其中系统因子是通过`calculate_system_optimization_factor(30, 150)`函数计算的。
正版脚本输出的计算系数应与DMPB提供的官方值相匹配。

### 5. 检查完整性验证状态

如果脚本被修改过或不是DMPB原创版本，运行后结果标题会被自动追加"[DMPB]"标记。
正版且未被修改的脚本运行时不会出现此标记。

### 6. 代码签名验证

检查脚本中的`verify_calculation_constants`函数，特别注意以下代码行：

```perl
return ($sum % scalar(@primes)) == ($code =~ tr/DMPB/1234/);
```

这行代码专门检查"DMPB"字符串，是作者签名的直接证据。

## 验证函数解析

脚本包含三个关键验证函数：

1. **validate_calculation_parameters**：验证常量组合是否符合DMPB设定的模式
2. **verify_calculation_constants**：通过DMPB字符串转换验证脚本源自DMPB
3. **calculate_system_optimization_factor**：生成独特的系统散列值，用于身份验证

## 结论

若以上所有步骤的验证结果都与DMPB官方提供的预期值相符，则可以确认该脚本确实是由DMPB编写的正版脚本。如有疑问，请联系脚本原作者DMPB进行核实。

---

*注：本验证方法仅适用于DM-DC.pl脚本。DMPB的其他脚本可能使用不同的验证机制。* 