#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import numpy as np
from pymatgen.core import Structure
from pymatgen.io.cif import CifWriter
from pymatgen.transformations.standard_transformations import RotationTransformation

# 读取原始结构
input_file = "CrTe-Pre.cif"
print(f"读取文件: {input_file}")
structure = Structure.from_file(input_file)

# 打印原始结构信息
print("\n原始晶胞信息:")
print(f"晶胞参数: a={structure.lattice.a:.4f}, b={structure.lattice.b:.4f}, c={structure.lattice.c:.4f}")
print(f"晶胞角度: α={structure.lattice.alpha:.2f}°, β={structure.lattice.beta:.2f}°, γ={structure.lattice.gamma:.2f}°")

# 设置Miller指数 [1,0,0]
miller = [1, 0, 0]
print(f"\n处理 ({miller[0]}{miller[1]}{miller[2]}) 方向...")

# 创建转换矩阵
# 首先将Miller向量归一化
v3 = np.array(miller, dtype=float)
v3 = v3 / np.linalg.norm(v3)

# 找一个不与v3平行的向量
if abs(v3[1]) > 0.1 or abs(v3[2]) > 0.1:
    aux = np.array([1, 0, 0])
else:
    aux = np.array([0, 1, 0])

# 构建正交基底
v1 = np.cross(aux, v3)
v1 = v1 / np.linalg.norm(v1)
v2 = np.cross(v3, v1)
rotation_matrix = np.column_stack((v1, v2, v3))

# 应用旋转
rotation = RotationTransformation(rotation_matrix)
rotated_structure = rotation.apply_transformation(structure)

# 打印旋转后的结构信息
print("\n旋转后的晶胞信息:")
print(f"晶胞参数: a={rotated_structure.lattice.a:.4f}, b={rotated_structure.lattice.b:.4f}, c={rotated_structure.lattice.c:.4f}")
print(f"晶胞角度: α={rotated_structure.lattice.alpha:.2f}°, β={rotated_structure.lattice.beta:.2f}°, γ={rotated_structure.lattice.gamma:.2f}°")

# 保存结果
output_dir = "simple_output"
os.makedirs(output_dir, exist_ok=True)
output_file = os.path.join(output_dir, "CrTe-Pre_100.cif")
CifWriter(rotated_structure).write_file(output_file)
print(f"\n已保存到: {output_file}") 