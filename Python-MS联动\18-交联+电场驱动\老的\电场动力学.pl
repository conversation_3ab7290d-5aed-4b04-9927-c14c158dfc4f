#!perl

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);

my $doc = $Documents{"Original.xsd"};

my $results = Modules->Forcite->Dynamics->Run($doc, Settings(
	"3DPeriodicElectrostaticSummationMethod" => "Atom based", 
	CurrentForcefield => "COMPASSIII", 
	Ensemble3D => "NVE", 
	TimeStep => 0.1,
	Temperature => 300, 
	NumberOfSteps => 50000, 
	TrajectoryFrequency => 500, 
	Thermostat => "NHL", 
	Barostat => "Andersen", 
	EnergyDeviation => 5e+013, 
	WriteVelocities => "No",
	ElectricFieldX => 0,
	ElectricFieldY => 0,
	ElectricFieldZ => 1,
	ElectricFieldStrength => 10));
my $outTrajectory = $results->Trajectory;
