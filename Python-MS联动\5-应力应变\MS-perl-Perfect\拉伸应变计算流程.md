# MS-perl应力应变分析脚本运行流程

这个脚本是在Materials Studio环境中运行的Perl脚本，用于对分子系统施加递增应变并分析产生的应力响应。以下是该脚本的详细运行流程：

## 初始化阶段
1. 导入必要的Perl模块和Materials Studio模块
2. 设置一系列用户可配置参数，包括输入/输出设置、应变设置、力场设置、分子动力学设置等
3. 打印脚本基本信息和配置参数
4. 打开指定的输入文档（默认为"0.xsd"）
5. 创建研究表格用于存储分析结果数据
6. 创建总结轨迹文档用于保存每个周期的最后一帧

## 平衡阶段
1. 初始化Forcite模块并配置相关参数（力场、时间步长、求和方法等）
2. 运行初始平衡分子动力学模拟
3. 将平衡后的最后一帧复制为应变分析的起始结构
4. 记录要应变的轴（默认为Z轴/LengthC）的初始长度
5. 将初始状态的应力数据记录到研究表格中

## 应变循环阶段
对于每一步应变（1到$numberStrains，默认30步）：
1. 根据应变方向（拉伸或压缩）计算新的轴长度
2. 应用应变到当前结构上
3. **处理固定原子集**（新增功能）：
   - 如果启用了固定原子处理功能（$handle_fixed_atoms="Yes"）
   - 检查是否存在指定的固定原子集（默认为"Fixed_Atoms"）
   - 对固定原子集中的原子应用与单元格相同比例的位移
   - 根据应变轴（LengthA/B/C）只调整相应方向的坐标
   - 确保调整后的原子仍然保持固定状态
4. 在新的应变状态下运行分子动力学模拟
5. 复制最后一帧到新文档
6. 删除超出键长限制（默认1.76Å）的键，模拟断键行为
7. 将该帧添加到总结轨迹中
8. 获取并保存应力张量数据到研究表格
9. 根据设置保留或删除中间文件

## 分析阶段
1. 计算弹性模量（如果启用）
   - 通过线性回归找出应力-应变曲线的最佳线性区域
   - 计算该区域的斜率作为弹性模量
   - 将计算结果添加到研究表格
2. 从轨迹文档中提取并验证应力数据
3. 使用指定的最大键长（默认1.15Å）重新计算键
4. 执行最终清理，根据用户设置保留或删除中间文件
5. 保存最终的研究表格和轨迹文档

整个过程实现了材料在应变下的力学响应分析，可以获得应力-应变曲线和弹性模量，帮助研究材料的力学性能。脚本还支持处理固定原子集，确保这些固定原子随着应变同步移动，这对于模拟具有特定界面或边界约束的材料特别有用。
