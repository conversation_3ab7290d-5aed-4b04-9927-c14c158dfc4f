#!perl
use strict;
use warnings;
use MaterialsScript qw(:all);

#  Get the current activate document.
#my $doc = MSi::Documents->ActiveDocument or die "No activate file found!";
#my $doc = Documents->import("cruCIIRcell.xsd")  or die "No activate file found!";

my $doc = $Documents{"./CIIR_NVE24cores.xsd"};  #cruCIIRcell-noperi.xsd"}; #

if (!$doc) {die "No sach file found!";}

my $mol =$doc->UnitCell  # Molecule;     # Molecule must be get rather than doc

my %processedAtoms;          # recording the atoms processed

# $doc->Symmetry='P1';
# $doc->CalculateSymmetry;
# my @all_bonds = $mol->DisplayRange->Bonds;
my @all_bonds = @{$mol->Bonds};

print my $N_all_bond = @all_bonds. " total bonds. \n";        #used to debug

# Set the parameter.
my $distance_threshold = 3.0;    # Max distance allow to link (A).
my $sulfur_bond_length = 1.8;    # S-C bond length (A)

#  Save all of the double bonds and atoms they linked.

my @double_bonds = grep { $_->BondOrder == 2 } @all_bonds; 
#my @double_bonds = grep { $_->BondType eq 'Double' } @all_bonds;   #another expression.

print  my $N_doub_bond = @double_bonds. " double bonds.\n";         #used to debug

my %processed_pairs;                # Record the treated atom pairs.

#  main program
foreach my $i (0 .. $#double_bonds) {
    my $bond1 = $double_bonds[$i];
    my $a1 = $bond1->Atom1;
    my $a2 = $bond1->Atom2;

#                                              ####### .################

#print  $a1." and " .$a2. " connected by double bonds.\n";         #used to debug
    
# Only non-S-bond will be treated. C=C/N=N (modify if necessary)
    next if $a1->ElementSymbol eq 'S' || $a2->ElementSymbol eq 'S';
    
    foreach my $j ($i+1 .. $#double_bonds) {
        my $bond2 = $double_bonds[$j];
        my $b1 = $bond2->Atom1;
        my $b2 = $bond2->Atom2;
#        my ($b1, $b2) = $bond2->Atoms;
        
# ignor if bond link to S.
        next if $b1->ElementSymbol eq 'S' || $b2->ElementSymbol eq 'S';
        
# Check all the atom pairs.
        foreach my $atomA ($a1, $a2) {
            foreach my $atomB ($b1, $b2) {
# ignor if atom pair has be treated.
                my $pair_id = join '-', sort ($atomA->ID, $atomB->ID);       # meaning??
                next if exists $processed_pairs{$pair_id};
                
#  calculate the distance of atom pair.
                my $dist = distance_between($atomA, $atomB);
                next unless $dist <= $distance_threshold;
                
#  Record the atom pair has be treated.
                $processed_pairs{$pair_id} = 1;
                
#  Link.                   #Add judgement and selection of a1,a2,b1,b2 to link. Avoive two near atom on the same chain linking. #######
                create_sulfur_bridge($atomA, $atomB);
            }
        }
    }
}

#  Created S-bridge.                      # between ($atomA, $atomB)
sub create_sulfur_bridge {
    my ($atomA, $atomB) = @_;
    
#  Change the double bond to single bond.
#    $_->BondOrder=1 for find_parent_bonds($atomA, $atomB);
    $_->BondType='Single' for find_parent_bonds($atomA, $atomB);
    
#  Calculate S point should to be set at.(Can be modified.)
    my ($x1, $y1, $z1) = ($atomA->X,$atomA->Y,$atomA->Z);    
    my ($x2, $y2, $z2) = ($atomB->X,$atomB->Y,$atomB->Z);    
    
#  Calculate milddle point(Can be modified.)
    my $s_x = ($x1*1 + $x2*1) / 2;
    my $s_y = ($y1*1 + $y2*1) / 2;
    my $s_z = ($z1*1 + $z2*1) / 2;
    
#  Created S atom.
    my $sulfur = $doc->CreateAtom("S", Point(X => $s_x, Y => $s_y, Z => $s_z));
    $sulfur->Style= "Ball and Stick";
 ####  treat the atoms be changed to single bond atoms  ######  
    
    
#  Created new bond.(length auto-adjusted)
    $doc->CreateBond($atomA, $sulfur, 1);  # Single bond.
    $doc->CreateBond($sulfur, $atomB, 1);
    
#    print "Between atom ".$atomA->serverAtomIndex." and ".$atomB->serverAtomIndex.", S-bridge were created !\n";
}

#  Find the double bond link to the atom.      # between ($atomA, $atomB),used by create_sulfur_bridge()
sub find_parent_bonds {
    my @atoms = @_;
    
#    print " @atoms \n";                       #used to debug
       
    my @bonds;
    
    foreach my $atom (@atoms) {
        foreach my $bond (@double_bonds) {
             my $a = $bond->Atom1;
             my $b = $bond->Atom2;
             
print "Atom(a,b,atom): $a  ,$b , $atom \n";                  #used to debug

             my $dis_a_at = distance_between($a, $atom);
             my $dis_b_at = distance_between($b, $atom);
             
print "dis_a_at,dis_b_at=$dis_a_at, $dis_b_at \n";                  #used to debug
             
             push @bonds, $bond if $dis_a_at < 0.1 || $dis_b_at < 0.1;

#            push @bonds, $bond if $a==$atom   || $b==$atom ;
#            push @bonds, $bond if $a->serverAtomIndex == $atom->serverAtomIndex || $b->serverAtomIndex == $atom->serverAtomIndex;
        }
    }
#    print " @bonds \n";       #used to debug
    
    return @bonds;
}

#  Calculate the distance between atoms.
sub distance_between {

    my ($atma, $atmb) = @_;
    my ($x1, $y1, $z1) = ($atma->X,$atma->Y,$atma->Z);
    my ($x2, $y2, $z2) = ($atmb->X,$atmb->Y,$atmb->Z);
    
#print  "atoma(x,y,z)[ $atma ]= $x1, $y1, $z1 \n";         #used to debug
#print  "atomb(x,y,z)=".$x2.", " .$y2. ", ".$z2.".\n";         #used to debug

    my $dist_2_atm  =sqrt( ($x1-$x2)**2 + ($y1-$y2)**2 + ($z1-$z2)**2 );
#print  "Distance[ $atma -- $atmb ]= $dist_2_atm \n";         #used to debug
 
    return sqrt( ($x1-$x2)**2 + ($y1-$y2)**2 + ($z1-$z2)**2 );
}

print "Linking complete!, please check it. \n";