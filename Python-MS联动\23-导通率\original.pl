#!perl

use strict;
use warnings;
use MaterialsScript qw(:all);

# 导通率(Percolation Rate)计算脚本
# 基于Materials Studio的分子动力学模拟轨迹
# 计算导电填料之间的导通率A = 导通的构型数/总构型数
# 当两个导电填料之间的距离小于隧穿距离TD时，认为它们导通

# 配置参数
my $inputFileName = "Target";  # 轨迹文件基本名称(.xtd)
my $tunnelDistance = 3.0;      # 隧穿距离TD(Å)，当两个导电填料之间距离小于此值时认为导通
my $timeInterval = 0.01;       # 每个构型的输出时间间隔(ps)
my $totalFrames = 5000;        # 需要统计的总构型数
my $conductiveFiller = "C";    # 导电填料原子类型(默认为碳原子，可根据需要修改)

# 初始化输出文档
my $resultsTable = Documents->New("$inputFileName"."_Conductivity.std");  # 创建结果表格
my $logFile = Documents->New("$inputFileName"."_Conductivity_Log.txt");   # 创建日志文件

# 记录标题信息
$logFile->Append("导通率计算脚本\n");
$logFile->Append("隧穿距离(TD): $tunnelDistance Å\n");
$logFile->Append("每个构型的时间间隔: $timeInterval ps\n");
$logFile->Append("总构型数: $totalFrames\n");
$logFile->Append("导电填料类型: $conductiveFiller\n");
$logFile->Append("===========================================\n\n");
$logFile->Save;

# 设置结果表格标题
$resultsTable->ColumnHeading(0) = "帧序号";
$resultsTable->ColumnHeading(1) = "时间(ps)";
$resultsTable->ColumnHeading(2) = "导通状态";
$resultsTable->ColumnHeading(3) = "团簇数";
$resultsTable->ColumnHeading(4) = "最大团簇尺寸";

# 检查轨迹文件是否存在
if (!defined($Documents{"$inputFileName.xtd"})) {
    $logFile->Append("错误: 未找到输入轨迹文件 '$inputFileName.xtd'\n");
    $logFile->Append("请检查文件名并确保轨迹文件已正确导入\n");
    $logFile->Save;
    die "错误: 未找到输入轨迹文件 '$inputFileName.xtd'，进程终止";
}

# 打开轨迹文件
my $trajectoryDoc = $Documents{"$inputFileName.xtd"};
my $trajectory = $trajectoryDoc->Trajectory;

# 验证轨迹有效
if (!defined($trajectory)) {
    $logFile->Append("错误: 轨迹对象无效\n");
    $logFile->Save;
    die "错误: 轨迹对象无效，进程终止";
}

# 记录轨迹信息
my $totalAvailableFrames = $trajectory->NumFrames;
$logFile->Append("轨迹信息:\n");
$logFile->Append("  总帧数: $totalAvailableFrames\n");
$logFile->Append("  将分析的帧数: " . ($totalFrames > $totalAvailableFrames ? $totalAvailableFrames : $totalFrames) . "\n\n");
$logFile->Save;

# 限制要分析的帧数
$totalFrames = $totalAvailableFrames if $totalFrames > $totalAvailableFrames;

# 统计变量
my $conductiveFrames = 0;     # 导通的构型数
my %frameResults;             # 存储每个帧的结果

# 遍历轨迹的每一帧
for (my $frameIndex = 0; $frameIndex < $totalFrames; $frameIndex++) {
    # 设置当前帧
    $trajectory->CurrentFrame = $frameIndex;
    my $currentTime = $frameIndex * $timeInterval;
    
    # 记录处理状态
    if ($frameIndex % 100 == 0) {
        $logFile->Append("正在处理帧 $frameIndex (时间: $currentTime ps)...\n");
        $logFile->Save;
    }
    
    # 获取当前帧的3D模型
    my $currentStructure = $trajectoryDoc->CurrentUnitCell;
    
    # 获取所有导电填料原子
    my @fillerAtoms = $currentStructure->Atoms($conductiveFiller);
    my $totalFillers = scalar(@fillerAtoms);
    
    if ($totalFillers == 0) {
        $logFile->Append("警告: 帧 $frameIndex 中未找到导电填料原子\n");
        $logFile->Save;
        next;
    }
    
    # 创建序号映射 - 将每个填料分配从1到N的唯一序号
    my %fillerIndices;
    for (my $i = 0; $i < $totalFillers; $i++) {
        $fillerIndices{$fillerAtoms[$i]->ID} = $i + 1; # 从1开始编号
    }
    
    # 创建团簇映射表 - 初始时每个填料都是独立团簇
    my %clusterMap;
    for (my $i = 0; $i < $totalFillers; $i++) {
        $clusterMap{$i + 1} = $i + 1; # 初始时每个填料的团簇ID等于其自身序号
    }
    
    # 实现查找函数 - 查找填料所属的团簇
    sub findCluster {
        my $id = shift;
        if ($clusterMap{$id} != $id) {
            $clusterMap{$id} = findCluster($clusterMap{$id});
        }
        return $clusterMap{$id};
    }
    
    # 实现合并函数 - 合并两个团簇
    sub unionClusters {
        my ($id1, $id2) = @_;
        my $root1 = findCluster($id1);
        my $root2 = findCluster($id2);
        
        if ($root1 == $root2) {
            return; # 已经在同一团簇中
        }
        
        # 合并两个团簇，取较小的ID作为新团簇ID
        if ($root1 < $root2) {
            $clusterMap{$root2} = $root1;
        } else {
            $clusterMap{$root1} = $root2;
        }
    }
    
    # 检查填料间距并形成团簇
    for (my $i = 0; $i < $totalFillers; $i++) {
        for (my $j = $i + 1; $j < $totalFillers; $j++) {
            my $distance = $fillerAtoms[$i]->Distance($fillerAtoms[$j]);
            
            if ($distance < $tunnelDistance) {
                # 如果距离小于隧穿距离，合并团簇
                unionClusters($i + 1, $j + 1);
            }
        }
    }
    
    # 计算最终团簇
    my %finalClusters;
    for (my $i = 1; $i <= $totalFillers; $i++) {
        my $clusterId = findCluster($i);
        $finalClusters{$clusterId} = [] unless exists $finalClusters{$clusterId};
        push @{$finalClusters{$clusterId}}, $i;
    }
    
    # 计算团簇数量和最大团簇尺寸
    my $numClusters = scalar(keys %finalClusters);
    my $maxClusterSize = 0;
    
    foreach my $cluster (values %finalClusters) {
        my $size = scalar(@$cluster);
        $maxClusterSize = $size if $size > $maxClusterSize;
    }
    
    # 判断是否导通 (如果最大团簇占总填料的比例超过一定阈值，如50%)
    my $isPercolated = ($maxClusterSize / $totalFillers > 0.5) ? 1 : 0;
    $conductiveFrames += $isPercolated;
    
    # 将结果保存到表格
    $resultsTable->Cell($frameIndex, 0) = $frameIndex;
    $resultsTable->Cell($frameIndex, 1) = $currentTime;
    $resultsTable->Cell($frameIndex, 2) = $isPercolated;
    $resultsTable->Cell($frameIndex, 3) = $numClusters;
    $resultsTable->Cell($frameIndex, 4) = $maxClusterSize;
    
    # 存储帧结果
    $frameResults{$frameIndex} = {
        'time' => $currentTime,
        'isPercolated' => $isPercolated,
        'numClusters' => $numClusters,
        'maxClusterSize' => $maxClusterSize
    };
}

# 计算导通率 A = 导通的构型数/总构型数
my $conductivityA = $conductiveFrames / $totalFrames;

# 添加导通率结果到日志
$logFile->Append("\n===========================================\n");
$logFile->Append("导通率计算结果:\n");
$logFile->Append("  总构型数: $totalFrames\n");
$logFile->Append("  导通的构型数: $conductiveFrames\n");
$logFile->Append("  导通率 A = $conductivityA\n");
$logFile->Append("===========================================\n");
$logFile->Save;

# 添加导通率结果到表格
$resultsTable->Cell($totalFrames + 1, 0) = "导通率 A";
$resultsTable->Cell($totalFrames + 1, 1) = $conductivityA;

# 输出完成信息
$logFile->Append("\n计算完成\n");
$logFile->Save;

print "导通率计算完成，结果已保存到 $inputFileName"."_Conductivity.std 文件\n";
