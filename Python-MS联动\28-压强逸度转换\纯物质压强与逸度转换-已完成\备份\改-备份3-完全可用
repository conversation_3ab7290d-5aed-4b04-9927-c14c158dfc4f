#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
纯物质压强与逸度转换计算器
作者: 端木鹏博（合作：<EMAIL>）
功能: 使用Peng-Robinson状态方程计算纯物质的逸度和逸度系数
"""

import numpy as np
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import ttk, messagebox, font, filedialog
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib
import sys
import os
import platform
import csv
import base64
from io import BytesIO
import datetime

# 设置matplotlib后端
matplotlib.use("TkAgg")

# 配置中文字体支持
if platform.system() == "Windows":
    # Windows系统字体配置
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    
    # 检查系统中文字体是否可用
    available_fonts = matplotlib.font_manager.findSystemFonts(fontpaths=None, fontext='ttf')
    chinese_font_found = False
    for font_path in available_fonts:
        if 'simhei' in font_path.lower() or 'msyh' in font_path.lower() or 'simsun' in font_path.lower():
            chinese_font_found = True
            plt.rcParams['font.sans-serif'].insert(0, os.path.basename(font_path).split('.')[0])
            break
    
    if not chinese_font_found:
        print("警告: 未找到中文字体文件，图表中文可能无法正确显示")
        
elif platform.system() == "Linux":
    # Linux系统字体配置
    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'AR PL UMing CN', 'AR PL UKai CN']
    plt.rcParams['axes.unicode_minus'] = False
    
elif platform.system() == "Darwin":
    # macOS系统字体配置
    plt.rcParams['font.sans-serif'] = ['PingFang SC', 'STHeiti Light', 'Heiti SC']
    plt.rcParams['axes.unicode_minus'] = False

# 常量定义
R = 8.3144598  # 气体常数，J/(mol·K)
OMEGA_A = 0.45723553  # PR方程参数
OMEGA_B = 0.07779607  # PR方程参数
ROOT_SELECTION_TOL = 1e-10  # 数值计算容差

# 物质参数库
SUBSTANCE_PROPERTIES = {
    "co2": {
        "name": "二氧化碳",
        "Tc": 304.1,
        "Pc": 7.38e6,
        "omega": 0.239
    },
    "ch4": {
        "name": "甲烷",
        "Tc": 190.6,
        "Pc": 4.6e6,
        "omega": 0.011
    },
    "n2": {
        "name": "氮气",
        "Tc": 126.2,
        "Pc": 3.4e6,
        "omega": 0.039
    },
    "o2": {
        "name": "氧气",
        "Tc": 154.6,
        "Pc": 5.05e6,
        "omega": 0.022
    },
    "h2o": {
        "name": "水",
        "Tc": 647.1,
        "Pc": 22.06e6,
        "omega": 0.344
    },
    "nh3": {
        "name": "氨气",
        "Tc": 405.5,
        "Pc": 11.28e6,
        "omega": 0.253
    },
    "c2h6": {
        "name": "乙烷",
        "Tc": 305.3,
        "Pc": 4.87e6,
        "omega": 0.099
    },
    "c3h8": {
        "name": "丙烷",
        "Tc": 369.8,
        "Pc": 4.25e6,
        "omega": 0.152
    }
}


class PR_FugacityCalculator:
    """使用Peng-Robinson状态方程计算纯物质的逸度和逸度系数"""
    
    def __init__(self):
        """初始化计算器"""
        pass
    
    @staticmethod
    def calculate_kappa(omega):
        """计算kappa参数"""
        if omega <= 0.491:
            return 0.37464 + 1.54226 * omega - 0.26992 * omega * omega
        else:
            return 0.379642 + 1.48503 * omega - 0.164423 * omega * omega + 0.016666 * omega * omega * omega
    
    @staticmethod
    def calculate_alpha(T, Tc, omega):
        """计算alpha参数"""
        Tr = T / Tc  # 约化温度
        kappa = PR_FugacityCalculator.calculate_kappa(omega)
        
        sqrt_Tr = np.sqrt(max(Tr, 1e-10))
        term = 1.0 + kappa * (1.0 - sqrt_Tr)
        return term * term  # [1+k(1-Tr^0.5)]²
    
    @staticmethod
    def calculate_AB(Tc, Pc):
        """计算a和b参数"""
        b = OMEGA_B * R * Tc / Pc
        a = OMEGA_A * (R * Tc) ** 2 / Pc
        
        return a, b
    
    @staticmethod
    def calculate_AT(T, Tc, Pc, omega):
        """计算温度相关的a(T)参数"""
        a, _ = PR_FugacityCalculator.calculate_AB(Tc, Pc)
        alpha = PR_FugacityCalculator.calculate_alpha(T, Tc, omega)
        return a * alpha
    
    @staticmethod
    def select_proper_root(roots, P, T, Tc, Pc, B):
        """从多个实根中选择热力学上最合理的根"""
        # 筛选实根
        real_roots = [root for root in roots if abs(root.imag) < ROOT_SELECTION_TOL]
        real_roots = [root.real for root in real_roots]
        
        if len(real_roots) == 0:
            raise ValueError(f"没有找到实根，无法计算压缩因子Z。压力={P} Pa，温度={T} K")
        
        # 如果只有一个实根，直接返回
        if len(real_roots) == 1:
            return real_roots[0]
        
        # 检查温度与临界温度的关系，判断是否为超临界状态
        Tr = T / Tc
        Pr = P / Pc
        
        # 接近临界点时的特殊处理
        if abs(Tr - 1) < 0.05 and abs(Pr - 1) < 0.05:
            # 接近临界点时，直接返回中间值的根
            real_roots.sort()
            return real_roots[len(real_roots) // 2]
        
        # 计算吉布斯自由能来选择最稳定的相
        min_G = float('inf')
        best_root = None
        
        for Z in real_roots:
            if Z <= B:  # 排除物理不合理的根 (Z必须>B)
                continue
            
            # 近似计算吉布斯自由能
            G_deviation = abs(Z - 1.0) + abs(1.0 - B/Z)
            
            if G_deviation < min_G:
                min_G = G_deviation
                best_root = Z
        
        # 如果没有找到有效根，则使用备选策略
        if best_root is None:
            if T > Tc:  # 超临界温度，通常选择最大的实根
                return max(real_roots)
            else:  # 亚临界温度
                # 亚临界温度时，根据压力选择不同策略
                real_roots.sort()
                if P > Pc:  # 高压，倾向选择最小的根（液相）
                    return real_roots[0]
                else:  # 低压，倾向选择最大的根（气相）
                    return real_roots[-1]
        
        return best_root
    
    @staticmethod
    def calculate_Z(P, T, Tc, Pc, omega):
        """计算压缩因子Z"""
        # 计算PR方程参数
        aT = PR_FugacityCalculator.calculate_AT(T, Tc, Pc, omega)
        _, b = PR_FugacityCalculator.calculate_AB(Tc, Pc)
        
        # 计算无量纲参数
        A = aT * P / (R * T) ** 2
        B = b * P / (R * T)
        
        # 求解三次方程: Z^3 - (1-B)Z^2 + (A-3B^2-2B)Z - (AB-B^2-B^3) = 0
        coeffs = [
            1.0,  # Z^3
            -(1.0 - B),  # Z^2
            (A - 3.0*B*B - 2.0*B),  # Z^1
            -(A*B - B*B - B*B*B)  # Z^0
        ]
        
        # 使用numpy求解
        roots = np.roots(coeffs)
        
        # 选择合适的根
        return PR_FugacityCalculator.select_proper_root(roots, P, T, Tc, Pc, B)
    
    @staticmethod
    def calculate_fugacity_coefficient(pressure, temperature, Tc, Pc, omega):
        """计算逸度系数"""
        # 确保输入参数有效
        if pressure <= 0 or temperature <= 0 or Tc <= 0 or Pc <= 0:
            raise ValueError("压力、温度、临界温度和临界压力必须为正值")
        
        # 计算PR方程参数
        aT = PR_FugacityCalculator.calculate_AT(temperature, Tc, Pc, omega)
        _, b = PR_FugacityCalculator.calculate_AB(Tc, Pc)
        
        # 计算无量纲参数
        A = aT * pressure / (R * temperature) ** 2
        B = b * pressure / (R * temperature)
        
        # 计算压缩因子
        Z = PR_FugacityCalculator.calculate_Z(pressure, temperature, Tc, Pc, omega)
        
        # 计算辅助参数
        sqrt2 = np.sqrt(2.0)
        
        # 计算逸度系数，采用更稳定的数值方法
        # 避免Z-B接近0导致的数值不稳定
        Z_minus_B = max(Z - B, 1e-10)
        
        # 计算ln(phi)的三个项
        term1 = Z - 1.0
        term2 = np.log(Z_minus_B)
        
        # 计算第三项，避免数值不稳定
        if abs(B) < 1e-10:
            term3 = 0.0
        else:
            # 计算分母，避免接近0的情况
            denominator1 = max(Z + (1.0 + sqrt2) * B, 1e-10)
            denominator2 = max(Z + (1.0 - sqrt2) * B, 1e-10)
            
            # 避免对负数取对数
            if denominator1 <= 0 or denominator2 <= 0:
                # 当分母可能为负时，使用近似公式
                term3 = A / (2.0 * sqrt2 * B) * (2.0 * B / (Z + B))
            else:
                term3 = A / (2.0 * sqrt2 * B) * np.log(denominator1 / denominator2)
        
        # 计算ln(phi)并返回phi
        ln_phi = term1 - term2 - term3
        return np.exp(ln_phi)
    
    @staticmethod
    def calculate_fugacity(pressure, temperature, Tc, Pc, omega):
        """计算逸度"""
        phi = PR_FugacityCalculator.calculate_fugacity_coefficient(pressure, temperature, Tc, Pc, omega)
        return pressure * phi


class FugacityCalculatorGUI:
    """PR方程逸度计算器的图形用户界面"""
    
    def __init__(self, root):
        """初始化GUI"""
        self.root = root
        self.root.title("纯物质压强与逸度转换计算器 - 材料模拟路漫漫＆端木鹏博")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # 配置GUI中文字体
        self.configure_fonts()
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建输入和结果框架
        self.create_input_frame()
        self.create_result_frame()
        
        # 创建图表框架
        self.create_chart_frame()
        
        # 图表对象
        self.fugacity_fig = None
        self.fugacity_ax = None
        self.phi_fig = None
        self.phi_ax = None
        
        # 初始化图表
        self.init_charts()
    
    def configure_fonts(self):
        """配置中文字体"""
        # 检查系统可用字体
        available_fonts = [f.lower() for f in font.families()]
        
        # 尝试找到合适的中文字体
        chinese_fonts = ["微软雅黑", "microsoft yahei", "simhei", "simsun", "nsimsun", "fangsong", "kaiti"]
        self.default_font = None
        
        for font_name in chinese_fonts:
            if any(font_name.lower() in f for f in available_fonts):
                self.default_font = font_name
                break
        
        if not self.default_font:
            # 如果没有找到中文字体，使用默认字体
            self.default_font = "TkDefaultFont"
            print("警告: 未找到合适的中文字体，使用系统默认字体")
        
        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TLabel", font=(self.default_font, 10))
        self.style.configure("TButton", font=(self.default_font, 10))
        self.style.configure("TCombobox", font=(self.default_font, 10))
        self.style.configure("TEntry", font=(self.default_font, 10))
    
    def create_input_frame(self):
        """创建输入框架"""
        input_frame = ttk.LabelFrame(self.main_frame, text="输入参数", padding="10")
        input_frame.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        
        # 物质选择
        ttk.Label(input_frame, text="选择物质:").grid(row=0, column=0, sticky="w", pady=5)
        self.substance_var = tk.StringVar()
        substance_combo = ttk.Combobox(input_frame, textvariable=self.substance_var, state="readonly", width=20)
        substance_combo['values'] = [SUBSTANCE_PROPERTIES[key]["name"] for key in SUBSTANCE_PROPERTIES]
        substance_combo.current(0)  # 默认选择第一个物质
        substance_combo.grid(row=0, column=1, sticky="ew", pady=5)
        substance_combo.bind("<<ComboboxSelected>>", self.on_substance_selected)
        
        # 自定义物质选项
        self.custom_var = tk.BooleanVar()
        custom_check = ttk.Checkbutton(input_frame, text="自定义物质", variable=self.custom_var, command=self.toggle_custom_substance)
        custom_check.grid(row=1, column=0, columnspan=2, sticky="w", pady=5)
        
        # 自定义物质框架
        self.custom_frame = ttk.Frame(input_frame, padding="5")
        self.custom_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=5)
        self.custom_frame.grid_remove()  # 默认隐藏
        
        # 自定义物质参数
        ttk.Label(self.custom_frame, text="物质名称:").grid(row=0, column=0, sticky="w", pady=2)
        self.custom_name_var = tk.StringVar(value="自定义物质")
        ttk.Entry(self.custom_frame, textvariable=self.custom_name_var, width=20).grid(row=0, column=1, sticky="ew", pady=2)
        
        ttk.Label(self.custom_frame, text="临界温度 Tc (K):").grid(row=1, column=0, sticky="w", pady=2)
        self.custom_tc_var = tk.DoubleVar(value=300.0)
        ttk.Entry(self.custom_frame, textvariable=self.custom_tc_var, width=20).grid(row=1, column=1, sticky="ew", pady=2)
        
        ttk.Label(self.custom_frame, text="临界压强 Pc (MPa):").grid(row=2, column=0, sticky="w", pady=2)
        self.custom_pc_var = tk.DoubleVar(value=5.0)
        ttk.Entry(self.custom_frame, textvariable=self.custom_pc_var, width=20).grid(row=2, column=1, sticky="ew", pady=2)
        
        ttk.Label(self.custom_frame, text="偏心因子 ω:").grid(row=3, column=0, sticky="w", pady=2)
        self.custom_omega_var = tk.DoubleVar(value=0.1)
        ttk.Entry(self.custom_frame, textvariable=self.custom_omega_var, width=20).grid(row=3, column=1, sticky="ew", pady=2)
        
        # 温度和压强输入
        ttk.Label(input_frame, text="温度 (K):").grid(row=3, column=0, sticky="w", pady=5)
        self.temperature_var = tk.DoubleVar(value=298.15)
        ttk.Entry(input_frame, textvariable=self.temperature_var, width=20).grid(row=3, column=1, sticky="ew", pady=5)
        
        ttk.Label(input_frame, text="压强 (MPa):").grid(row=4, column=0, sticky="w", pady=5)
        self.pressure_var = tk.DoubleVar(value=1.0)
        ttk.Entry(input_frame, textvariable=self.pressure_var, width=20).grid(row=4, column=1, sticky="ew", pady=5)
        
        # 计算按钮
        calculate_btn = ttk.Button(input_frame, text="计算逸度", command=self.calculate_fugacity)
        calculate_btn.grid(row=5, column=0, columnspan=2, sticky="ew", pady=10)
        
        # 绘图选项
        self.plot_var = tk.BooleanVar()
        plot_check = ttk.Checkbutton(input_frame, text="绘制压强-逸度关系图", variable=self.plot_var, command=self.toggle_plot_options)
        plot_check.grid(row=6, column=0, columnspan=2, sticky="w", pady=5)
        
        # 绘图参数框架
        self.plot_frame = ttk.Frame(input_frame, padding="5")
        self.plot_frame.grid(row=7, column=0, columnspan=2, sticky="ew", pady=5)
        self.plot_frame.grid_remove()  # 默认隐藏
        
        # 绘图参数 - 修复重复默认值问题
        ttk.Label(self.plot_frame, text="最小压强 (MPa):").grid(row=0, column=0, sticky="w", pady=2)
        self.p_min_var = tk.DoubleVar(value=0.1)
        ttk.Entry(self.plot_frame, textvariable=self.p_min_var, width=20).grid(row=0, column=1, sticky="ew", pady=2)
        
        ttk.Label(self.plot_frame, text="最大压强 (MPa):").grid(row=1, column=0, sticky="w", pady=2)
        self.p_max_var = tk.DoubleVar(value=10.0)
        ttk.Entry(self.plot_frame, textvariable=self.p_max_var, width=20).grid(row=1, column=1, sticky="ew", pady=2)
        
        ttk.Label(self.plot_frame, text="采样点数量:").grid(row=2, column=0, sticky="w", pady=2)
        self.p_points_var = tk.IntVar(value=50)
        ttk.Entry(self.plot_frame, textvariable=self.p_points_var, width=20).grid(row=2, column=1, sticky="ew", pady=2)
        
        plot_btn = ttk.Button(self.plot_frame, text="绘制图表", command=self.draw_plot)
        plot_btn.grid(row=3, column=0, columnspan=2, sticky="ew", pady=5)
    
    def create_result_frame(self):
        """创建结果框架"""
        result_frame = ttk.LabelFrame(self.main_frame, text="计算结果", padding="10")
        result_frame.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)
        
        # 基本信息
        ttk.Label(result_frame, text="基本信息", font=(self.default_font, 10, "bold")).grid(row=0, column=0, sticky="w", pady=5)
        self.basic_info_text = tk.Text(result_frame, height=8, width=40, font=(self.default_font, 10))
        self.basic_info_text.grid(row=1, column=0, sticky="nsew", pady=5)
        self.basic_info_text.config(state=tk.DISABLED)
        
        # 计算结果
        ttk.Label(result_frame, text="计算结果", font=(self.default_font, 10, "bold")).grid(row=2, column=0, sticky="w", pady=5)
        self.result_text = tk.Text(result_frame, height=5, width=40, font=(self.default_font, 10))
        self.result_text.grid(row=3, column=0, sticky="nsew", pady=5)
        self.result_text.config(state=tk.DISABLED)
        
        # 导出按钮框架
        export_frame = ttk.Frame(result_frame, padding="5")
        export_frame.grid(row=4, column=0, sticky="ew", pady=10)
        
        # 导出CSV按钮
        self.export_csv_btn = ttk.Button(export_frame, text="导出为CSV", command=self.export_to_csv, state=tk.DISABLED)
        self.export_csv_btn.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        
        # 导出HTML按钮
        self.export_html_btn = ttk.Button(export_frame, text="导出为HTML", command=self.export_to_html, state=tk.DISABLED)
        self.export_html_btn.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
    
    def create_chart_frame(self):
        """创建图表框架"""
        chart_frame = ttk.LabelFrame(self.main_frame, text="压强-逸度关系图", padding="10")
        chart_frame.grid(row=1, column=0, columnspan=2, sticky="nsew", padx=5, pady=5)
        
        # 创建绘图区域
        self.chart_canvas_frame = ttk.Frame(chart_frame)
        self.chart_canvas_frame.pack(fill=tk.BOTH, expand=True)
    
    def init_charts(self):
        """初始化图表"""
        # 创建图表布局
        self.fig, (self.fugacity_ax, self.phi_ax) = plt.subplots(1, 2, figsize=(12, 5))
        self.fig.tight_layout(pad=3.0)
        
        # 添加标题和标签
        self.fugacity_ax.set_title("压强-逸度关系")
        self.fugacity_ax.set_xlabel("压强 (MPa)")
        self.fugacity_ax.set_ylabel("逸度 (MPa)")
        self.fugacity_ax.grid(True)
        
        self.phi_ax.set_title("压强-逸度系数关系")
        self.phi_ax.set_xlabel("压强 (MPa)")
        self.phi_ax.set_ylabel("逸度系数 φ")
        self.phi_ax.grid(True)
        
        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.chart_canvas_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    def toggle_custom_substance(self):
        """切换自定义物质显示状态"""
        if self.custom_var.get():
            self.custom_frame.grid()
        else:
            self.custom_frame.grid_remove()
    
    def toggle_plot_options(self):
        """切换绘图选项显示状态"""
        if self.plot_var.get():
            self.plot_frame.grid()
        else:
            self.plot_frame.grid_remove()
    
    def on_substance_selected(self, event):
        """物质选择变化时的回调"""
        # 如果选择了预定义物质，禁用自定义物质
        self.custom_var.set(False)
        self.toggle_custom_substance()
    
    def get_substance_properties(self):
        """获取物质参数"""
        if self.custom_var.get():
            try:
                name = self.custom_name_var.get() or "自定义物质"
                Tc = self.custom_tc_var.get()
                Pc = self.custom_pc_var.get() * 1e6  # 转换为Pa
                omega = self.custom_omega_var.get()
                
                if Tc <= 0 or Pc <= 0 or omega < 0:
                    messagebox.showerror("输入错误", "物质参数必须为正值")
                    return None
                
                return {
                    "name": name,
                    "Tc": Tc,
                    "Pc": Pc,
                    "omega": omega
                }
            except tk.TclError:
                messagebox.showerror("输入错误", "请输入有效的数值")
                return None
        else:
            # 获取选择的物质名称
            substance_name = self.substance_var.get()
            # 找到对应的键
            substance_key = None
            for key, props in SUBSTANCE_PROPERTIES.items():
                if props["name"] == substance_name:
                    substance_key = key
                    break
            
            if substance_key:
                return SUBSTANCE_PROPERTIES[substance_key]
            else:
                messagebox.showerror("错误", "找不到选定的物质")
                return None
    
    def calculate_fugacity(self):
        """计算逸度和逸度系数"""
        try:
            # 获取物质参数
            substance_props = self.get_substance_properties()
            if not substance_props:
                return
            
            # 获取温度和压强
            try:
                temperature = self.temperature_var.get()
                pressure = self.pressure_var.get() * 1e6  # 转换为Pa
                
                if temperature <= 0 or pressure <= 0:
                    messagebox.showerror("输入错误", "温度和压强必须为正值")
                    return
            except tk.TclError:
                messagebox.showerror("输入错误", "请确保输入有效的温度和压强数值")
                return
            
            # 计算逸度系数和逸度
            phi = PR_FugacityCalculator.calculate_fugacity_coefficient(
                pressure, temperature, substance_props["Tc"], substance_props["Pc"], substance_props["omega"]
            )
            fugacity = PR_FugacityCalculator.calculate_fugacity(
                pressure, temperature, substance_props["Tc"], substance_props["Pc"], substance_props["omega"]
            )
            
            # 显示基本信息
            self.basic_info_text.config(state=tk.NORMAL)
            self.basic_info_text.delete(1.0, tk.END)
            self.basic_info_text.insert(tk.END, f"物质: {substance_props['name']}\n")
            self.basic_info_text.insert(tk.END, f"临界温度 (Tc): {substance_props['Tc']:.2f} K\n")
            self.basic_info_text.insert(tk.END, f"临界压强 (Pc): {substance_props['Pc']/1e6:.4f} MPa\n")
            self.basic_info_text.insert(tk.END, f"偏心因子 (ω): {substance_props['omega']:.4f}\n")
            self.basic_info_text.insert(tk.END, f"温度: {temperature:.2f} K\n")
            self.basic_info_text.insert(tk.END, f"压强: {pressure/1e6:.4f} MPa\n")
            self.basic_info_text.config(state=tk.DISABLED)
            
            # 显示计算结果
            self.result_text.config(state=tk.NORMAL)
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, f"逸度系数 (φ): {phi:.6f}\n")
            self.result_text.insert(tk.END, f"逸度 (f): {fugacity/1e6:.6f} MPa\n")
            self.result_text.config(state=tk.DISABLED)
            
            # 如果选择了绘图，自动绘制图表
            if self.plot_var.get():
                self.draw_plot()
            
        except Exception as e:
            messagebox.showerror("计算错误", str(e))
            import traceback
            traceback.print_exc()  # 打印详细错误堆栈到控制台
    
    def draw_plot(self):
        """绘制压强-逸度关系图"""
        try:
            # 打印调试信息
            print("==== 开始绘图 ====")
            print(f"绘图状态: {self.plot_var.get()}")
            
            # 获取物质参数
            substance_props = self.get_substance_properties()
            if not substance_props:
                print("未获取到物质参数")
                return
            
            print(f"物质: {substance_props['name']}")
            
            # 获取温度和绘图参数
            try:
                temperature = self.temperature_var.get()
                print(f"温度: {temperature} K")
                
                # 打印绘图变量的值类型和值
                print(f"p_min_var 类型: {type(self.p_min_var)}, 值: {self.p_min_var.get()}")
                print(f"p_max_var 类型: {type(self.p_max_var)}, 值: {self.p_max_var.get()}")
                print(f"p_points_var 类型: {type(self.p_points_var)}, 值: {self.p_points_var.get()}")
                
                p_min = self.p_min_var.get() * 1e6  # 转换为Pa
                p_max = self.p_max_var.get() * 1e6  # 转换为Pa
                p_points = self.p_points_var.get()
                
                print(f"最小压强: {p_min/1e6} MPa, 最大压强: {p_max/1e6} MPa, 采样点数: {p_points}")
            except tk.TclError as e:
                print(f"TclError: {e}")
                messagebox.showerror("输入错误", "请确保所有输入字段都包含有效的数值")
                return
            except Exception as e:
                print(f"获取绘图参数错误: {e}")
                messagebox.showerror("输入错误", f"获取绘图参数时出错: {e}")
                return
            
            # 详细验证每个参数
            error_msg = []
            if temperature <= 0:
                error_msg.append("温度必须大于0 K")
            if p_min <= 0:
                error_msg.append("最小压强必须大于0 MPa")
            if p_max <= 0:
                error_msg.append("最大压强必须大于0 MPa")
            if p_min >= p_max:
                error_msg.append("最小压强必须小于最大压强")
            
            if error_msg:
                print(f"参数验证错误: {error_msg}")
                messagebox.showerror("输入错误", "\n".join(error_msg))
                return
            
            print("参数验证通过，开始计算...")
            
            # 确保采样点数量至少为2，这是numpy.linspace的最小要求
            p_points = max(2, p_points)
            
            # 计算压强-逸度数据点
            pressures = np.linspace(p_min, p_max, p_points)
            fugacities = []
            phi_values = []
            ideal_fugacities = []
            
            for P in pressures:
                try:
                    phi = PR_FugacityCalculator.calculate_fugacity_coefficient(
                        P, temperature, substance_props["Tc"], substance_props["Pc"], substance_props["omega"]
                    )
                    f = P * phi
                    
                    fugacities.append(f / 1e6)  # 转换为MPa
                    phi_values.append(phi)
                    ideal_fugacities.append(P / 1e6)  # 理想气体逸度等于压强
                except Exception as e:
                    print(f"在压强 {P/1e6} MPa 时计算出错: {e}")
                    fugacities.append(None)
                    phi_values.append(None)
                    ideal_fugacities.append(P / 1e6)
            
            # 转换为MPa
            pressures = pressures / 1e6
            
            # 保存数据用于导出
            self.plot_data = {
                "substance": substance_props["name"],
                "temperature": temperature,
                "pressures": pressures,
                "fugacities": fugacities,
                "phi_values": phi_values,
                "ideal_fugacities": ideal_fugacities,
                # 添加更多物质参数信息
                "Tc": substance_props["Tc"],
                "Pc": substance_props["Pc"] / 1e6,  # 转换为MPa
                "omega": substance_props["omega"],
                "R": R,  # 添加气体常数
                "OMEGA_A": OMEGA_A,  # PR方程参数
                "OMEGA_B": OMEGA_B   # PR方程参数
            }
            
            # 清除旧图
            self.fugacity_ax.clear()
            self.phi_ax.clear()
            
            # 绘制逸度-压强图
            self.fugacity_ax.plot(pressures, fugacities, 'b-', label='PR状态方程')
            self.fugacity_ax.plot(pressures, ideal_fugacities, 'r--', label='理想气体(f=P)')
            self.fugacity_ax.set_title(f"温度 {temperature} K 下的压强-逸度关系\n({substance_props['name']})")
            self.fugacity_ax.set_xlabel("压强 (MPa)")
            self.fugacity_ax.set_ylabel("逸度 (MPa)")
            self.fugacity_ax.legend()
            self.fugacity_ax.grid(True)
            
            # 绘制逸度系数-压强图
            self.phi_ax.plot(pressures, phi_values, 'g-')
            self.phi_ax.set_title(f"温度 {temperature} K 下的压强-逸度系数关系")
            self.phi_ax.set_xlabel("压强 (MPa)")
            self.phi_ax.set_ylabel("逸度系数 φ")
            self.phi_ax.grid(True)
            
            # 更新画布
            self.fig.tight_layout()
            self.canvas.draw()
            
            # 启用导出按钮
            self.export_csv_btn.config(state=tk.NORMAL)
            self.export_html_btn.config(state=tk.NORMAL)
            
            print("绘图完成")
            
        except Exception as e:
            print(f"绘图总体错误: {e}")
            messagebox.showerror("绘图错误", str(e))
            import traceback
            traceback.print_exc()  # 打印详细错误堆栈到控制台
    
    def export_to_csv(self):
        """导出数据为CSV格式"""
        try:
            if not hasattr(self, 'plot_data'):
                messagebox.showwarning("导出错误", "没有可用的数据，请先计算和绘制图表")
                return
            
            # 打开文件选择对话框
            default_filename = f"PR_fugacity_{self.plot_data['substance']}_{self.plot_data['temperature']}K.csv"
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV 文件", "*.csv")],
                initialfile=default_filename
            )
            
            if not file_path:
                return  # 用户取消了导出
            
            # 专业术语解释
            term_explanations = {
                "临界温度 (Tc)": "物质从气相变为液相时不再区分的最高温度，超过此温度时，无论压力多大，气体都不能液化",
                "临界压强 (Pc)": "临界温度下的饱和蒸汽压力，是物质从气相变为液相时不再区分的最高压力",
                "偏心因子 (ω)": "描述分子非球形性的参数，表示分子形状偏离球形的程度",
                "约化温度 (T/Tc)": "实际温度与临界温度的比值，用于表示系统距离临界点的程度",
                "气体常数 (R)": "理想气体状态方程中的普适常数，等于8.3144598 J/(mol·K)",
                "参数 OMEGA_A": "PR方程中的参数A，用于考虑分子间吸引力的影响",
                "参数 OMEGA_B": "PR方程中的参数B，用于考虑分子体积效应的影响",
                "压强 (MPa)": "单位面积上的垂直压力，MPa表示兆帕",
                "逸度 (MPa)": "表征非理想气体逃逸倾向的热力学量，可视为修正后的有效压力",
                "逸度系数": "逸度与压强的比值，表示气体偏离理想气体行为的程度",
                "理想气体逸度 (MPa)": "理想气体状态下的逸度，等于压强"
            }
            
            # 写入CSV文件，使用带BOM的UTF-8编码解决Excel打开中文乱码问题
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入标题和基本信息
                writer.writerow(['压强与逸度转换计算结果'])
                writer.writerow(['作者: 端木鹏博（合作：<EMAIL>）'])
                
                # 物质基本信息
                writer.writerow(['===== 物质信息 ====='])
                writer.writerow([f'物质: {self.plot_data["substance"]}'])
                writer.writerow([f'临界温度 (Tc): {self.plot_data["Tc"]:.4f} K', term_explanations["临界温度 (Tc)"]])
                writer.writerow([f'临界压强 (Pc): {self.plot_data["Pc"]:.6f} MPa', term_explanations["临界压强 (Pc)"]])
                writer.writerow([f'偏心因子 (ω): {self.plot_data["omega"]:.6f}', term_explanations["偏心因子 (ω)"]])
                
                # 计算参数
                writer.writerow(['===== 计算参数 ====='])
                writer.writerow([f'温度: {self.plot_data["temperature"]:.4f} K', "系统的实际温度"])
                writer.writerow([f'约化温度 (T/Tc): {self.plot_data["temperature"]/self.plot_data["Tc"]:.4f}', term_explanations["约化温度 (T/Tc)"]])
                
                # PR方程参数
                writer.writerow(['===== PR方程参数 ====='])
                writer.writerow([f'气体常数 (R): {self.plot_data["R"]:.8f} J/(mol·K)', term_explanations["气体常数 (R)"]])
                writer.writerow([f'参数 OMEGA_A: {self.plot_data["OMEGA_A"]:.8f}', term_explanations["参数 OMEGA_A"]])
                writer.writerow([f'参数 OMEGA_B: {self.plot_data["OMEGA_B"]:.8f}', term_explanations["参数 OMEGA_B"]])
                
                # 导出信息
                writer.writerow(['===== 导出信息 ====='])
                writer.writerow([f'导出时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                writer.writerow([f'数据点数量: {len(self.plot_data["pressures"])}'])
                writer.writerow([])  # 空行
                
                # 写入数据表头和表头解释
                writer.writerow(['压强 (MPa)', '逸度 (MPa)', '逸度系数', '理想气体逸度 (MPa)'])
                writer.writerow([term_explanations["压强 (MPa)"], term_explanations["逸度 (MPa)"], 
                                term_explanations["逸度系数"], term_explanations["理想气体逸度 (MPa)"]])
                writer.writerow([])  # 空行
                
                # 写入数据
                for i in range(len(self.plot_data["pressures"])):
                    writer.writerow([
                        self.plot_data["pressures"][i],
                        self.plot_data["fugacities"][i],
                        self.plot_data["phi_values"][i],
                        self.plot_data["ideal_fugacities"][i]
                    ])
            
            messagebox.showinfo("导出成功", f"数据已成功导出到:\n{file_path}")
            
        except Exception as e:
            messagebox.showerror("导出错误", f"导出CSV时出错：{str(e)}")
            import traceback
            traceback.print_exc()
    
    def export_to_html(self):
        """导出数据和图表为HTML格式"""
        try:
            if not hasattr(self, 'plot_data'):
                messagebox.showwarning("导出错误", "没有可用的数据，请先计算和绘制图表")
                return
            
            # 打开文件选择对话框
            default_filename = f"PR_fugacity_{self.plot_data['substance']}_{self.plot_data['temperature']}K.html"
            file_path = filedialog.asksaveasfilename(
                defaultextension=".html",
                filetypes=[("HTML 文件", "*.html")],
                initialfile=default_filename
            )
            
            if not file_path:
                return  # 用户取消了导出
            
            # 保存当前图表为图片
            buffer = BytesIO()
            self.fig.savefig(buffer, format='png', dpi=100)
            buffer.seek(0)
            chart_img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            buffer.close()
            
            # 计算约化温度
            reduced_temp = self.plot_data["temperature"] / self.plot_data["Tc"]
            
            # 专业术语解释
            term_explanations = {
                "临界温度": "物质从气相变为液相时不再区分的最高温度，超过此温度时，无论压力多大，气体都不能液化",
                "临界压强": "临界温度下的饱和蒸汽压力，是物质从气相变为液相时不再区分的最高压力",
                "偏心因子": "描述分子非球形性的参数，表示分子形状偏离球形的程度",
                "约化温度": "实际温度与临界温度的比值，用于表示系统距离临界点的程度",
                "气体常数": "理想气体状态方程中的普适常数，等于8.3144598 J/(mol·K)",
                "PR参数 OMEGA_A": "PR方程中的参数A，用于考虑分子间吸引力的影响",
                "PR参数 OMEGA_B": "PR方程中的参数B，用于考虑分子体积效应的影响"
            }
            
            # 生成HTML内容
            html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PR方程逸度计算结果 - {self.plot_data["substance"]}</title>
    <style>
        body {{
            font-family: Arial, "Microsoft YaHei", sans-serif;
            margin: 20px;
            background-color: #f9f9f9;
            color: #333;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        .chart-container {{
            text-align: center;
            margin: 20px 0;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        th, td {{
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
        }}
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .info {{
            margin: 20px 0;
            padding: 15px;
            background-color: #f1f8ff;
            border-left: 4px solid #2196F3;
            border-radius: 3px;
        }}
        .parameter-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }}
        .parameter-table th, .parameter-table td {{
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
        }}
        .parameter-table th {{
            width: 30%;
            background-color: #f5f5f5;
        }}
        .explanation {{
            width: 40%;
            color: #555;
            font-style: italic;
        }}
        .section {{
            margin: 25px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }}
        .footer {{
            margin-top: 30px;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }}
        .author {{
            font-weight: bold;
            color: #2c3e50;
        }}
        .data-explanation {{
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 0.9em;
        }}
        .data-explanation h3 {{
            margin-top: 0;
        }}
        .data-explanation p {{
            margin: 5px 0;
        }}
        .term {{
            font-weight: bold;
            color: #2c3e50;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>PR方程逸度计算结果</h1>
        
        <div class="info">
            <p class="author">作者: 端木鹏博（合作：<EMAIL>）</p>
        </div>
        
        <div class="section">
            <h2>物质参数</h2>
            <table class="parameter-table">
                <tr>
                    <th>物质名称</th>
                    <td>{self.plot_data["substance"]}</td>
                    <td class="explanation"></td>
                </tr>
                <tr>
                    <th>临界温度 (Tc)</th>
                    <td>{self.plot_data["Tc"]:.4f} K</td>
                    <td class="explanation">{term_explanations["临界温度"]}</td>
                </tr>
                <tr>
                    <th>临界压强 (Pc)</th>
                    <td>{self.plot_data["Pc"]:.6f} MPa</td>
                    <td class="explanation">{term_explanations["临界压强"]}</td>
                </tr>
                <tr>
                    <th>偏心因子 (ω)</th>
                    <td>{self.plot_data["omega"]:.6f}</td>
                    <td class="explanation">{term_explanations["偏心因子"]}</td>
                </tr>
            </table>
        </div>
        
        <div class="section">
            <h2>计算参数</h2>
            <table class="parameter-table">
                <tr>
                    <th>温度 (T)</th>
                    <td>{self.plot_data["temperature"]:.4f} K</td>
                    <td class="explanation">系统的实际温度</td>
                </tr>
                <tr>
                    <th>约化温度 (T/Tc)</th>
                    <td>{reduced_temp:.4f}</td>
                    <td class="explanation">{term_explanations["约化温度"]}</td>
                </tr>
                <tr>
                    <th>气体常数 (R)</th>
                    <td>{self.plot_data["R"]:.8f} J/(mol·K)</td>
                    <td class="explanation">{term_explanations["气体常数"]}</td>
                </tr>
                <tr>
                    <th>PR参数 OMEGA_A</th>
                    <td>{self.plot_data["OMEGA_A"]:.8f}</td>
                    <td class="explanation">{term_explanations["PR参数 OMEGA_A"]}</td>
                </tr>
                <tr>
                    <th>PR参数 OMEGA_B</th>
                    <td>{self.plot_data["OMEGA_B"]:.8f}</td>
                    <td class="explanation">{term_explanations["PR参数 OMEGA_B"]}</td>
                </tr>
                <tr>
                    <th>数据点数量</th>
                    <td>{len(self.plot_data["pressures"])}</td>
                    <td class="explanation">计算和绘图中使用的压强-逸度数据点数量</td>
                </tr>
                <tr>
                    <th>导出时间</th>
                    <td>{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</td>
                    <td class="explanation">计算结果导出的日期和时间</td>
                </tr>
            </table>
        </div>
        
        <div class="chart-container">
            <h2>压强-逸度关系图</h2>
            <img src="data:image/png;base64,{chart_img_base64}" alt="压强-逸度关系图" style="max-width:100%;">
        </div>
        
        <div class="data-explanation">
            <h3>数据说明</h3>
            <p><span class="term">压强 (MPa)</span>: 单位面积上的垂直压力，MPa表示兆帕</p>
            <p><span class="term">逸度 (MPa)</span>: 表征非理想气体逃逸倾向的热力学量，可视为修正后的有效压力</p>
            <p><span class="term">逸度系数</span>: 逸度与压强的比值，表示气体偏离理想气体行为的程度</p>
            <p><span class="term">理想气体逸度 (MPa)</span>: 理想气体状态下的逸度，等于压强</p>
        </div>
        
        <h2>数据表</h2>
        <table>
            <thead>
                <tr>
                    <th>压强 (MPa)</th>
                    <th>逸度 (MPa)</th>
                    <th>逸度系数</th>
                    <th>理想气体逸度 (MPa)</th>
                </tr>
            </thead>
            <tbody>
"""
            
            # 添加数据行
            for i in range(len(self.plot_data["pressures"])):
                pressure = self.plot_data["pressures"][i]
                fugacity = self.plot_data["fugacities"][i]
                phi = self.plot_data["phi_values"][i]
                ideal_fugacity = self.plot_data["ideal_fugacities"][i]
                
                html_content += f"""                <tr>
                    <td>{pressure:.6f}</td>
                    <td>{fugacity:.6f}</td>
                    <td>{phi:.6f}</td>
                    <td>{ideal_fugacity:.6f}</td>
                </tr>
"""
            
            # 完成HTML
            html_content += """            </tbody>
        </table>
        
        <div class="footer">
            <p>纯物质压强与逸度转换计算器 - 端木鹏博（合作：<EMAIL>）</p>
        </div>
    </div>
</body>
</html>
"""
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            messagebox.showinfo("导出成功", f"数据和图表已成功导出到:\n{file_path}")
            
        except Exception as e:
            messagebox.showerror("导出错误", f"导出HTML时出错：{str(e)}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    # 在Windows系统上设置环境变量，防止中文乱码
    if platform.system() == "Windows":
        # 设置环境变量，解决matplotlib中文显示问题
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        # 解决中文路径问题
        if sys.version_info[0] == 3:
            try:
                sys.stdout.reconfigure(encoding='utf-8')
            except AttributeError:
                # Python 3.6及以下版本兼容
                pass
    
    root = tk.Tk()
    app = FugacityCalculatorGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
