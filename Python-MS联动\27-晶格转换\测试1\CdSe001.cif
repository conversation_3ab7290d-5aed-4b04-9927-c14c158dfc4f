# generated using pymatgen
data_CdSe
_symmetry_space_group_name_H-M   'P 1'
_cell_length_a   6.14054240
_cell_length_b   6.14054240
_cell_length_c   6.14054240
_cell_angle_alpha   90.00000000
_cell_angle_beta   90.00000000
_cell_angle_gamma   90.00000000
_symmetry_Int_Tables_number   1
_chemical_formula_structural   CdSe
_chemical_formula_sum   'Cd4 Se4'
_cell_volume   231.53689388
_cell_formula_units_Z   4
loop_
 _symmetry_equiv_pos_site_id
 _symmetry_equiv_pos_as_xyz
  1  'x, y, z'
loop_
 _atom_type_symbol
 _atom_type_oxidation_number
  Cd2+  2.0
  Se2-  -2.0
loop_
 _atom_site_type_symbol
 _atom_site_label
 _atom_site_symmetry_multiplicity
 _atom_site_fract_x
 _atom_site_fract_y
 _atom_site_fract_z
 _atom_site_occupancy
  Cd2+  Cd0  1  0.00000000  0.00000000  0.00000000  1
  Cd2+  Cd1  1  0.00000000  0.50000000  0.50000000  1
  Cd2+  Cd2  1  0.50000000  0.00000000  0.50000000  1
  Cd2+  Cd3  1  0.50000000  0.50000000  0.00000000  1
  Se2-  Se4  1  0.75000000  0.75000000  0.75000000  1
  Se2-  Se5  1  0.75000000  0.25000000  0.25000000  1
  Se2-  Se6  1  0.25000000  0.75000000  0.25000000  1
  Se2-  Se7  1  0.25000000  0.25000000  0.75000000  1
