##################################################################################################################
# perl                                                                                                           #
#                                                                                                                #
# Author: DMPB                                                                                                   #
# Version: 3.0                                                                                                   #
# Tested on: Materials Studio 2020                                                                               #
#                                                                                                                #
# Required modules: Materials Visualizer                                                                         #
# This script aims to calculate the Free Volume Fraction (FFV) of molecular structures from trajectory files.    #
# It uses the Connolly surface method for volume calculations. The script processes frames within a trajectory   #
# file and saves results to a study table including occupied volume, surface area, total volume, FFV percentage, #
# and density data.                                                                                              #
#                                                                                                                #
# Date: 2023-06-15                                                                                               #
#                                                                                                                #
#                                                                                                                #
##################################################################################################################

use strict;
use warnings;
use MaterialsScript qw(:all);

#################################################################################################################
#                                         USER CONFIGURATION                                                    #
#################################################################################################################
# Basic file settings
my $trajectoryFile = "PE.xtd";         # Trajectory file
my $resultsFile = "FFV_Results.std";    # Results file

# Trajectory frame settings
my $startFrameNumber = 1;               # Start frame
my $endFrameNumber = 10;                # End frame (-1 means use all frames)
my $frameInterval = 1;                  # Frame interval

# Connolly calculation parameters
my $meshGridSize = 0.4;                 # Grid spacing (Å)
my $vanDerWaalsScaleFactor = 1.0;       # Van der Waals radius scale factor
my $probeRadius = 1.0;                  # Probe radius for Connolly surface
my $isosurfaceValue = 0;                # Isosurface value
#################################################################################################################
#                                         END OF CONFIGURATION                                                  #
#################################################################################################################

# Create output study table
my $resultsTable = Documents->New($resultsFile);
my $dataSheet = $resultsTable->ActiveSheet;

# Set column headings
$dataSheet->ColumnHeading(0) = "Filename";
$dataSheet->ColumnHeading(1) = "Frame";
$dataSheet->ColumnHeading(2) = "Time (ps)";
$dataSheet->ColumnHeading(3) = "Occupied Volume (A3)";
$dataSheet->ColumnHeading(4) = "Surface Area (A2)";
$dataSheet->ColumnHeading(5) = "Cell Total Volume (A3)";
$dataSheet->ColumnHeading(6) = "Free Volume Fraction (%)";
$dataSheet->ColumnHeading(7) = "Density (g/cm3)";

# Files to process
my @filesToProcess = ($trajectoryFile);

my $totalRowCount = 0;

# Process each file
foreach my $currentFile (@filesToProcess) {
    eval {
        print "\n==========================================\n";
        print "Processing file: $currentFile\n";
        print "==========================================\n";
        
        # Open document
        my $document;
        eval {
            $document = $Documents{$currentFile};
            if (!$document) {
                $document = Documents->Open($currentFile);
            }
        };
        if ($@) {
            warn "Unable to open file $currentFile: $@\n";
            next;
        }
        
        # Get trajectory information
        my $totalFrames = $document->Trajectory->NumFrames;
        print "This file contains $totalFrames frames\n";
        
        # If end frame is -1, set to last frame
        if ($endFrameNumber == -1) {
            $endFrameNumber = $totalFrames;
        } elsif ($endFrameNumber > $totalFrames) {
            $endFrameNumber = $totalFrames;
            warn "End frame adjusted to maximum frame count: $totalFrames\n";
        }
        
        # Ensure start frame is valid
        if ($startFrameNumber < 1) {
            $startFrameNumber = 1;
            warn "Start frame adjusted to: 1\n";
        }
        
        # Create progress information
        my $totalFramesToProcess = int(($endFrameNumber - $startFrameNumber) / $frameInterval) + 1;
        my $processedCount = 0;
        
        # Iterate through specified frames
        for (my $frameIndex = $startFrameNumber; $frameIndex <= $endFrameNumber; $frameIndex += $frameInterval) {
            # Update progress
            $processedCount++;
            my $progressPercent = int($processedCount / $totalFramesToProcess * 100);
            print "Processing frame $frameIndex ($processedCount/$totalFramesToProcess), $progressPercent% complete...\n";
            
            eval {
                # Create a temporary document copy
                my $tempDocument = Documents->New("$currentFile"."_$frameIndex.xsd");
                
                # Set current frame in the trajectory document
                $document->Trajectory->CurrentFrame = $frameIndex;
                
                # Get frame time
                my $frameTime = 0;
                eval {
                    $frameTime = $document->Trajectory->FrameTime;
                };
                
                # Copy the current frame to the temporary document
                $tempDocument->CopyFrom($document);
                
                # Get total volume from the temporary document
                my $cellVolume = $tempDocument->Lattice3D->CellVolume;
                
                # Calculate occupied volume using Connolly surface method on the temporary document
                my $volumeField = Tools->AtomVolumesSurfaces->Connolly->Calculate($tempDocument, Settings(
                    GridInterval => $meshGridSize,
                    ConnollyRadius => $probeRadius,
                    VDWScaleFactor => $vanDerWaalsScaleFactor));
                $volumeField->Style = "None";
                my $surfaceObject = $volumeField->CreateIsosurface([
                    IsoValue => $isosurfaceValue, 
                    HasFlippedNormals => "NO"]);
                
                # Get occupied volume and surface area
                my $occupiedVolume = $surfaceObject->EnclosedVolume;
                my $surfaceArea = $surfaceObject->SurfaceArea;
                
                # Calculate free volume fraction (percentage)
                my $freeVolume = $cellVolume - $occupiedVolume;
                my $ffvPercent = ($freeVolume / $cellVolume) * 100;
                
                # Get density
                my $density = $tempDocument->SymmetrySystem->Density;
                
                # Record data to study table
                $dataSheet->Cell($totalRowCount, 0) = $currentFile;
                $dataSheet->Cell($totalRowCount, 1) = $frameIndex;
                $dataSheet->Cell($totalRowCount, 2) = $frameTime;
                $dataSheet->Cell($totalRowCount, 3) = $occupiedVolume;
                $dataSheet->Cell($totalRowCount, 4) = $surfaceArea;
                $dataSheet->Cell($totalRowCount, 5) = $cellVolume;
                $dataSheet->Cell($totalRowCount, 6) = $ffvPercent;
                $dataSheet->Cell($totalRowCount, 7) = $density;
                
                print "  Frame $frameIndex: FFV = " . sprintf("%.2f", $ffvPercent) . "%, Density = " . sprintf("%.4f", $density) . " g/cm³\n";
                
                # Delete field and isosurface to free memory
                $surfaceObject->Delete();
                $volumeField->Delete();
                
                # Always discard temporary document after use
                $tempDocument->Discard();
                
                $totalRowCount++;
            };
            if ($@) {
                warn "Error processing frame $frameIndex: $@\n";
            }
        }
    };
    if ($@) {
        warn "Error processing file $currentFile: $@\n";
    }
}

# Save study table
$resultsTable->Save();

# Print summary
print "\n==========================================\n";
print "Calculation complete, results saved to $resultsFile\n";
print "Processed " . scalar(@filesToProcess) . " files, " . $totalRowCount . " frames\n";
print "==========================================\n";