{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 导入包"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["D:\\softstudy\\softinstall\\Anaconda3\\lib\\importlib\\_bootstrap.py:219: RuntimeWarning: numpy.ufunc size changed, may indicate binary incompatibility. Expected 192 from C header, got 216 from PyObject\n", "  return f(*args, **kwds)\n", "D:\\softstudy\\softinstall\\Anaconda3\\lib\\importlib\\_bootstrap.py:219: RuntimeWarning: numpy.ufunc size changed, may indicate binary incompatibility. Expected 192 from C header, got 216 from PyObject\n", "  return f(*args, **kwds)\n", "D:\\softstudy\\softinstall\\Anaconda3\\lib\\importlib\\_bootstrap.py:219: RuntimeWarning: numpy.ufunc size changed, may indicate binary incompatibility. Expected 192 from C header, got 216 from PyObject\n", "  return f(*args, **kwds)\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "from sklearn.linear_model import LinearRegression  #线性回归\n", "from sklearn.neighbors import KNeighborsRegressor  #K近邻回归\n", "from sklearn.neighbors import KNeighborsClassifier#K近邻分类\n", "from sklearn.tree import DecisionTreeRegressor  #决策树回归\n", "from sklearn.tree import DecisionTreeClassifier  #决策树分类\n", "from sklearn.ensemble import RandomForestRegressor  #随机森林回归\n", "import xgboost as xgb#xgboost模型\n", "\n", "from sklearn.svm import SVR  #支持向量回归\n", "from sklearn.svm import SVC  #支持向量分类\n", "from sklearn.neural_network import MLPClassifier#神经网络多层感知器分类\n", "from sklearn.neural_network import MLPClassifier#神经网络多层感知器回归\n", "\n", "from sklearn.model_selection import GridSearchCV#网格搜索+交叉验证\n", "from sklearn.model_selection import KFold, LeaveOneOut #K交叉，留一法\n", "\n", "from sklearn.model_selection import train_test_split  # 切分数据\n", "from sklearn.metrics import mean_squared_error,r2_score,mean_absolute_error  #部分回归评价指标\n", "from sklearn.metrics import accuracy_score,precision_score,recall_score,f1_score  #部分分类评价指标\n", "from sklearn.metrics import confusion_matrix#混淆矩阵\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 读取数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = pd.read_csv('filename.csv')#csv格式\n", "data = pd.read_excel('filename.xlsx')#excel格式"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_rows',None)#完全显示表格\n", "pd.set_option('display.max_columns',None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 指定输入和输出"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["X = data.iloc[:,1:-1]#行：所有，列：第2列到倒数第二例\n", "Y = data.iloc[:,-1]#行：所有，列：最后一列"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 查看数据信息"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.head(5)\n", "data.tail(5)\n", "data.info()\n", "data.describe()\n", "data.isnull().sum()#查看缺失值"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 缺失值处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#1 删除缺失值所在的行\n", "data2 = data.dropna()\n", "#2.平均数，众数，中位数填充\n", "data['EN'] = data['EN'].fillna(data['EN'].mean())#均值填充\n", "data['mass'] = data['mass'].fillna(data['mass'].mode()[0])#众数()\n", "data['Density'] = data['Density'].fillna(data['Density'].median())#中位数"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 特征处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#特征相关性\n", "train_corr = data2.corr()\n", "import seaborn as sns\n", "ax = plt.subplots(figsize=(20,16))\n", "ax = sns.heatmap(train_corr,annot = True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#PCA\n", "from sklearn.decomposition import PCA\n", "X_reduced = PCA(n_components=0.9).fit_transform(X)#保留90%的特征信息\n", "X_reduced = PCA(n_components=20).fit_transform(X)#降到20维\n", "X_reduced.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#数据标准化\n", "from sklearn.preprocessing import StandardScaler#标准化\n", "X_SS = StandardScaler().fit_transform(X2)\n", "#区间缩放法\n", "from sklearn.preprocessing import MinMaxScaler\n", "X_MMS = MinMaxScaler().fit_transform(X2)\n", "#归一化\n", "from sklearn.preprocessing import Normalizer\n", "X_N = Normalizer().fit_transform(X2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 划分数据集"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "x_train,x_test,y_train,y_test = train_test_split(X,Y,test_size = 0.2,random_state = 0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 模型训练"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.fit(x_train,y_train)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 预测"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y_pred = model.predict(x_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 交叉验证"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Kfold\n", "kf = KFold(n_splits=5)\n", "for k, (train_index, test_index) in enumerate(kf.split(X)):\n", "    train_data,test_data,train_target,test_target = X.iloc[train_index],X<PERSON>iloc[test_index],Y.iloc[train_index],Y.iloc[test_index]\n", "    model.fit(train_data, train_target)\n", "    MSE_test = mean_squared_error(test_target, model.predict(test_data))\n", "    print(k, \" 折\", \"NN test MSE:   \", MSE_test)\n", "   "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#留一法\n", "loo = LeaveOneOut()\n", "num = 100\n", "for k, (train_index, test_index) in enumerate(loo.split(X)):\n", "    train_data, test_data, train_target, test_target = X.iloc[\n", "        train_index], <PERSON><PERSON>iloc[test_index], Y<PERSON>iloc[train_index], Y.iloc[\n", "            test_index]\n", "    \n", "    model.fit(train_data, train_target)\n", "    score_test = mean_squared_error(test_target, model.predict(test_data))\n", "    print(k, \" 个\", \"MLPRegressor test MSE:   \", score_test, '\\n')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 模型参数调节"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#简单调节一个参数\n", "for i in range(3,21):\n", "    clf = KNeighborsClassifier(n_neighbors=i) \n", "    clf.fit(x_train,y_train)\n", "    score = accuracy_score(y_test, clf.predict(x_test))\n", "    print(\"KNeighborsRegressor:\"+str(i)+'  '+'score', score)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#GridSearchCV\n", "SVM = SVR()\n", "from sklearn.model_selection import KFold\n", "param_grid = {'kernel':['linear','rbf','sigmoid'],\n", "              'gamma':[0.001,0.01,0.1,0.5,0.7,1,2],\n", "              'C':[0.01,0.1,0.2,0.3,0.4,1],\n", "             }\n", "cross_Valid = KFold(n_splits=10, shuffle= True)\n", "SVM_PCE_op = GridSearchCV(estimator = SVM, param_grid = param_grid, cv = cross_Valid,verbose=2 ,n_jobs = -1)\n", "SVR_model =SVM_PCE_op.fit(x_train,y_train)\n", "print(SVR_model .best_params_)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 模型评估"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#R2,<PERSON><PERSON>,RMS<PERSON>（回归）\n", "R2 = r2_score(y_pred,y_test)\n", "MSE = mean_squared_error(y_pred,y_test)\n", "np.sqrt(mean_squared_error(y_test,y_pred))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#分类\n", "accuracy_score(y_test,y_pred)\n", "precision_score(y_test,y_pred)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 混淆矩阵\n", "C2= confusion_matrix(y_test, y_pred, labels=[0, 1])\n", "print(C2) #打印出来看看"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 混淆矩阵画图\n", "sns.set(font_scale=1.5)#修改字体比例\n", "f,ax=plt.subplots()\n", "sns.heatmap(C2,annot=True,cmap=\"YlGnBu_r\") #画热力图\n", "\n", "ax.set_title('confusion matrix') #标题\n", "ax.set_xlabel('predict') #x轴\n", "ax.set_ylabel('true') #y轴"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 随机森林筛选特征重要性"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.ensemble import RandomForestRegressor\n", "RF = RandomForestRegressor() #\n", "RF.fit(X, Y)\n", "importances = RF.feature_importances_"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column = data.columns.tolist()[2:]#可以使用tolist()函数转化为list\n", "len(column)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["column = data.columns.tolist()[2:]\n", "plt.figure(dpi=300)\n", "fig = plt.figure(figsize=(10, 6)) \n", "# plt.rcParams['font.sans-serif'] = ['SimHei']\n", "# plt.rcParams['axes.unicode_minus'] = False\n", "plt.bar(column, importances,width = 0.35) # 横放条形图函数 barh\n", "#plt.title('随机森林筛选特征重要性')\n", "plt.xticks(fontproperties = 'Times New Roman',size = 0.5 ,rotation = 45)\n", "plt.xlabel('Features',size = 25)\n", "plt.ylabel('Importances',size = 25)\n", "plt.tick_params(labelsize=20) \n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 简单可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#回归可视化\n", "plt.figure(figsize=(5,5))\n", "#plt.scatter(y_train,y_trainpred,marker = 's',color = 'white',edgecolors = 'b',label = 'training')\n", "plt.scatter(y_train,y_trainpred,marker = 's',color = 'b',label = 'training')\n", "plt.scatter(y_test,y_pred,color = 'red',label = 'test')\n", "\n", "plt.plot([-2,0],[-2,0],\"b--\",lw=3)\n", "plt.xlabel('ΔE​ DFT(ev)',size = 15)\n", "plt.ylabel('ΔE​ model(ev)',size=15)\n", "plt.tick_params(labelsize=15)\n", "plt.annotate('RMSE\\n 0.14',xy=(-1.0,-0.25))\n", "plt.legend()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#分类可视化\n", "plt.scatter(range(0,21),y_pred,c='red',s =200,label='predicted')\n", "plt.scatter(range(0,21),y_test,c='blue',label='experiment')\n", "plt.legend()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}