<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE REGISTRY SYSTEM "System/Registry.dtd">
<REGISTRY>
	<ITEMS>
		<ITEM NAME="cmdUser14" TEXT="DM-Hbons_Set" TOOLTIP="Run user script" STATUS="This script provides comprehensive hydrogen bond analysis between two sets in a molecular structure. " CATEGORY="RunScriptOnScopeOnServer" IMAGE="ScriptingPlugin,1" CATEGORYDATA="library://Hbonds-Sets/DM_Hbonds-Sets.pl" ENABLEMENTPROCESSOR="UserMenu:3dAtomisticTrajectoryDocument" DOCUMENTUSAGE="ActiveDocument" HELP="None" TYPE="Command" IMPORTANCE="Medium" ACCELERATOR="" LOCKACCESS="No">
			<OPTIONS>
				<OPTION NAME="Start_Frame" DATATYPE="Integer" DEFAULTVALUE="1"/>
				<OPTION NAME="End_Frame" DATATYPE="Integer" DEFAULTVALUE="1000"/>
				<OPTION NAME="First_Set_Name" DATATYPE="String" DEFAULTVALUE="Target1"/>
				<OPTION NAME="Second_Set_Name" DATATYPE="String" DEFAULTVALUE="Target2"/>
				<OPTION NAME="Max_H_A_Distance" DATATYPE="Float" DEFAULTVALUE="2.5"/>
				<OPTION NAME="Min_D_H_A_Angle" DATATYPE="Float" DEFAULTVALUE="90"/>
				<OPTION NAME="Output_Geometry_Table" DATATYPE="String" DEFAULTVALUE="No"/>
			</OPTIONS>
		</ITEM>
	</ITEMS>
	<MENUS>
		<MENU NAME="menuContainer" OWNER="Container">
			<POPUP NAME="User" LOCATION="Default" POSITIONAL="No">
				<MENUITEM NAME="cmdUser14"/>
			</POPUP>
		</MENU>
	</MENUS>
	<TOOLBARS/>
	<LOCATIONS>
		<LOCATION NAME="Hbonds-Sets">
			<PATH>
E:\\MS2024\\Perl-zhibo-202505_Files\\Documents\\Third-Mul-Sets			</PATH>
		</LOCATION>
	</LOCATIONS>
</REGISTRY>
