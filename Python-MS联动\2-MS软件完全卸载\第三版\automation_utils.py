import os
import time
import subprocess
import pyautogui
import cv2
import numpy as np
from PIL import ImageGrab

# 全局变量
SCREENSHOT_DIR = None

def init(screenshot_dir):
    """初始化自动化工具"""
    global SCREENSHOT_DIR
    SCREENSHOT_DIR = screenshot_dir
    
    # 设置pyautogui安全特性
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.5

def take_screenshot(region=None):
    """获取屏幕截图"""
    if region:
        screenshot = ImageGrab.grab(bbox=region)
    else:
        screenshot = ImageGrab.grab()
    return np.array(screenshot)

def save_debug_screenshot(name="debug"):
    """保存调试截图"""
    if not SCREENSHOT_DIR:
        raise ValueError("未设置截图保存目录，请先调用init()方法")
        
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    filename = os.path.join(SCREENSHOT_DIR, f"{name}_{timestamp}.png")
    screenshot = ImageGrab.grab()
    screenshot.save(filename)
    return filename

def locate_on_screen(template_path, confidence=0.8, timeout=10, region=None, log_callback=None):
    """在屏幕上查找图像，带有超时机制"""
    if not os.path.exists(template_path):
        if log_callback:
            log_callback(f"模板图像不存在: {template_path}")
        return None
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            # 使用pyautogui的位置查找
            location = pyautogui.locateOnScreen(template_path, confidence=confidence, region=region)
            if location:
                return location
            
            # 尝试使用OpenCV进行更精确的匹配
            screenshot = take_screenshot(region)
            template = cv2.imread(template_path)
            
            # 转换颜色空间
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            
            # 执行模板匹配
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= confidence:
                h, w = template_gray.shape
                # 计算匹配区域
                if region:
                    x_offset, y_offset = region[0], region[1]
                else:
                    x_offset, y_offset = 0, 0
                
                return (max_loc[0] + x_offset, max_loc[1] + y_offset, w, h)
        except Exception as e:
            if log_callback:
                log_callback(f"查找图像时出错: {e}")
        
        time.sleep(0.5)
    
    if log_callback:
        log_callback(f"超时: 未能在屏幕上找到图像 {template_path}")
    return None

def click_if_exists(template_path, confidence=0.8, timeout=10, region=None, log_callback=None):
    """如果图像存在，则点击它"""
    location = locate_on_screen(template_path, confidence, timeout, region, log_callback)
    if location:
        center_x = location[0] + location[2] // 2
        center_y = location[1] + location[3] // 2
        pyautogui.click(center_x, center_y)
        if log_callback:
            log_callback(f"点击了坐标: ({center_x}, {center_y})")
        return True
    return False

def capture_template(name, prompt=None, log_callback=None):
    """捕获界面元素的模板图像"""
    if not SCREENSHOT_DIR:
        raise ValueError("未设置截图保存目录，请先调用init()方法")
    
    template_path = os.path.join(SCREENSHOT_DIR, f"{name}.png")
    
    if prompt and log_callback:
        log_callback(prompt)
    
    # 等待用户将鼠标放在目标位置
    input("准备好后按Enter...")
    
    # 获取鼠标位置
    x, y = pyautogui.position()
    if log_callback:
        log_callback(f"捕获位置: ({x}, {y})")
    
    # 捕获鼠标周围区域的屏幕截图
    region = (x - 50, y - 25, 100, 50)  # 调整区域大小以适应目标元素
    screenshot = ImageGrab.grab(bbox=region)
    screenshot.save(template_path)
    
    if log_callback:
        log_callback(f"已保存模板: {template_path}")
    
    return template_path

def open_application(app_path, window_title=None, log_callback=None):
    """打开应用程序"""
    if not os.path.exists(app_path):
        if log_callback:
            log_callback(f"应用程序不存在: {app_path}")
        return False
    
    if log_callback:
        log_callback(f"正在打开应用程序: {app_path}")
    
    # 检查是否已运行
    app_name = os.path.basename(app_path)
    try:
        output = subprocess.check_output(f'tasklist /FI "IMAGENAME eq {app_name}"', shell=True).decode()
        if app_name in output:
            if log_callback:
                log_callback(f"{app_name} 已在运行")
            
            # 尝试激活窗口
            if window_title:
                try:
                    subprocess.run(f'powershell "(New-Object -ComObject WScript.Shell).AppActivate(\'{window_title}\')"', shell=True)
                    if log_callback:
                        log_callback(f"已激活窗口: {window_title}")
                except:
                    pass
            return True
    except:
        pass
    
    # 启动应用程序
    try:
        subprocess.Popen(app_path)
        if log_callback:
            log_callback(f"等待应用程序启动...")
        time.sleep(3)  # 等待启动
        return True
    except Exception as e:
        if log_callback:
            log_callback(f"启动应用程序时出错: {e}")
        return False

def type_text(text, delay=0.1):
    """输入文本"""
    pyautogui.typewrite(text, interval=delay)

def press_key(key):
    """按下键盘按键"""
    pyautogui.press(key)

def hotkey(*keys):
    """组合键"""
    pyautogui.hotkey(*keys)

def wait_for_image(template_path, confidence=0.8, timeout=30, region=None, log_callback=None):
    """等待图像出现"""
    if log_callback:
        log_callback(f"等待图像出现: {template_path}")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        location = locate_on_screen(template_path, confidence, 0.5, region)
        if location:
            if log_callback:
                log_callback(f"图像已出现: {template_path}")
            return location
        time.sleep(0.5)
    
    if log_callback:
        log_callback(f"超时: 未能找到图像 {template_path}")
    return None 