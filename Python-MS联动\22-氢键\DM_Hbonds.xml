<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE REGISTRY SYSTEM "System/Registry.dtd">
<REGISTRY>
	<ITEMS>
		<ITEM NAME="cmdUser12" TEXT="DM-Hbond" TOOLTIP="Run user script" STATUS="This script automates the process of hydrogen bond analysis in a given molecular structure over all trajectory  frames." CATEGORY="RunScriptOnScopeOnServer" IMAGE="ScriptingPlugin,1" CATEGORYDATA="library://Hbond/DM_Hbonds.pl" ENABLEMENTPROCESSOR="UserMenu:3dAtomisticTrajectoryDocument" DOCUMENTUSAGE="ActiveDocument" HELP="None" TYPE="Command" IMPORTANCE="Medium" ACCELERATOR="" LOCKACCESS="No">
			<OPTIONS>
				<OPTION NAME="Output_Bond_Lengths" DATATYPE="String" DEFAULTVALUE="No"/>
				<OPTION NAME="Start_Frame" DATATYPE="Integer" DEFAULTVALUE="1"/>
				<OPTION NAME="End_Frame" DATATYPE="Integer" DEFAULTVALUE="1000"/>
			</OPTIONS>
		</ITEM>
	</ITEMS>
	<MENUS>
		<MENU NAME="menuContainer" OWNER="Container">
			<POPUP NAME="User" LOCATION="Default" POSITIONAL="No">
				<MENUITEM NAME="cmdUser12"/>
			</POPUP>
		</MENU>
	</MENUS>
	<TOOLBARS/>
	<LOCATIONS>
		<LOCATION NAME="Hbond">
			<PATH>
E:\\MS2024\\Perl-zhibo-202505_Files\\Documents\\Second-Mul-Xtd			</PATH>
		</LOCATION>
	</LOCATIONS>
</REGISTRY>
