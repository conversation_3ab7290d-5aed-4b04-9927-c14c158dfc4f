#!/usr/bin/perl
use strict;
use warnings;
use MaterialsScript;

# 获取当前文档
my $doc = Documents->Active;

# 检查是否有活动文档
unless ($doc) {
    die "错误：没有活动的文档。请先打开一个结构文件。\n";
}

# 检查文档类型
if ($doc->Type ne "3DAtomistic") {
    die "错误：当前文档不是原子结构文档\n";
}

# 获取当前结构
my $structure = $doc->Structure;    

# 设置氢键计算参数
my $maxDist = 3.5;  # 可改为从用户输入获取
my $minAngle = 120;

my $hbonds = $structure->CalculateHydrogenBonds(
    MaxDistance => $maxDist,
    MinAngle => $minAngle,
    MaxAngle => 180
);

# 输出氢键分析结果
print "总氢键数：" . scalar(@$hbonds) . "\n\n";

# 输出每个氢键的详细信息
foreach my $hbond (@$hbonds) {
    print "氢键详情：\n";
    print "供体原子：" . $hbond->DonorAtom->Name . "\n";
    print "受体原子：" . $hbond->AcceptorAtom->Name . "\n";
    print "距离：" . sprintf("%.2f", $hbond->Distance) . " 埃\n";
    print "角度：" . sprintf("%.2f", $hbond->Angle) . " 度\n";
    print "-------------------\n";
}

# 在文件末尾添加结果保存
open my $fh, '>', 'hbonds_results.txt' or die $!;
print $fh "总氢键数：" . scalar(@$hbonds) . "\n";
close $fh;
print "结果已保存到 hbonds_results.txt\n";

# 例如您提供的氢键计算脚本，我可以：
# - 调整氢键判断参数（距离、角度）
# - 添加结果输出格式
# - 增加文件保存功能 

# 当前脚本可扩展功能：
sub 高级分析 {
    # 1. 动态轨迹分析
    my $trajectory = $doc->Trajectory;
    my $frameCount = 0;
    while (my $frame = $trajectory->NextFrame) {
        $frameCount++;
        my $hbonds = $frame->CalculateHydrogenBonds;
        print "第 $frameCount 帧氢键数: ".@$hbonds."\n";
    }
    
    # 2. 氢键存活时间统计
    my %hbondLifetimes;
    # ...添加时间相关统计代码...
} 