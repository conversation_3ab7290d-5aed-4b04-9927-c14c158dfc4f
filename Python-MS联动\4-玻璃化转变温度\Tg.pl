#!perl

use strict;
use Getopt::Long;
use MaterialsScript qw(:all);

# Author: DMPB
# Version: 3.0
# Materials Studio Version: 2020+
# Modules: Materials Visualizer, Forcite Plus, COMPASS

# This enhanced script performs NPT dynamics simulations at multiple temperatures to estimate 
# the glass transition temperature (Tg) of polymer structures. It systematically collects data on 
# density, energy, and conformational changes across a specified temperature range.
#
# The script operates by:
# 1. Processing a trajectory file frame by frame
# 2. Running temperature-dependent NPT equilibration for each structure
# 3. Monitoring density convergence with robust error handling
# 4. Performing production runs to gather averaged property data
# 5. Optionally tracking backbone torsion angle changes (frozen below Tg)
# 6. Compiling all results in a comprehensive study table with statistical analysis
#
# For accurate Tg determination, it's recommended to run multiple independent simulations
# and calculate the average value to minimize statistical error.

###################################################################################################

# Update History:
# Date: 2025-04-17
# Revised by: DMPB
# Compatible with: Materials Studio 2020 and newer versions
# Revision details: 
# (1) Implemented comprehensive error trapping to resolve "Can't call method Trajectory on undefined value" error
# (2) Added extensive validation checks throughout the execution pipeline
# (3) Enhanced logging system with detailed diagnostics for troubleshooting
# (4) Restructured code for improved maintenance and readability
# (5) Incorporated graceful failure handling and recovery mechanisms
# (6) Optimized statistical analysis methods for better Tg estimation
# (7) Simplified variable names for better understanding by beginners

# CONFIGURATION SECTION - MODIFY VARIABLES BELOW

my $inputFileName = "Target";        # Base filename for the trajectory file (.xtd extension)

# Glass transition temperature (Tg) simulation parameters
my $highestTemperature = 300;        # Starting temperature for temperature sweep (K)
my $lowestTemperature = 100;         # Final temperature for temperature sweep (K)
my $temperatureStepSize = 10;        # Temperature decrement per simulation step (K)

# Initial equilibration settings
# Set to 0 to skip initial equilibration and proceed directly to temperature sweep
my $numTemperatureCycles = 1;        # Number of heating/cooling cycles during initial equilibration

# Simulation physics parameters
my $simulationPressure = 0.0001;     # Applied pressure during NPT simulation (GPa)
my $timeStepSize = 1;                # MD integration time step (femtoseconds)
my $vdwCalculationMethod = "Atom based";  # Method for van der Waals interactions calculation
my $electrostaticMethod = "PPPM";    # Method for electrostatic interactions (PPPM = Particle-Particle Particle-Mesh)
my $forceFieldName = "COMPASSII";    # Force field selection - Options: COMPASSII (recommended), COMPASS, Dreiding, etc.

# Convergence criteria and simulation control
my $densityConvergenceTolerance = 0.02;  # Maximum density fluctuation for convergence criterion
my $equilibrationTime = 20.0;        # Base equilibration duration (picoseconds) - scales with temperature
my $snapshotFrequencyEquil = 1000;   # Trajectory sampling frequency (steps) during equilibration
my $averagingBlockSize = 4;          # Number of frames to average for density convergence checking
my $maxEquilibrationAttempts = 20;   # Maximum number of equilibration restarts before proceeding

# Production run and torsion analysis configuration
my $enableTorsionAnalysis = 0;       # Toggle torsion analysis (1=enabled, 0=disabled)
my $productionRunTime = 50;          # Production run duration for data collection (picoseconds)
my $snapshotFrequencyProd = 5000;    # Trajectory sampling frequency (steps) during production
my $torsionAngleWindow = 30;         # Angular window for torsion state classification (degrees)

# END OF CONFIGURATION SECTION

# Initialize essential output documents
my $resultsTable = Documents->New("$inputFileName.std");  # Study table for numerical data collection
my $logFile = Documents->New("$inputFileName"."_summary.txt");  # Log file for execution tracking

# Unit conversion constant - critical for proper time step calculation
use constant FEMTO_TO_PICO => 1000; # Conversion factor: 1 ps = 1000 fs

# Global hash table to store torsion angle data across frames
# Structure: %torsionStates{torsion_name} = [array of torsion states]
my %torsionStates;
    
#################################################################################
# Initialize Forcite module with optimized calculation parameters
# This configuration balances accuracy and computational efficiency

my $forciteModule = Modules->Forcite;

# Apply comprehensive simulation settings
$forciteModule->ChangeSettings([    
    # Calculation quality setting - affects accuracy vs. speed
    Quality                               => "Medium",
    
    # Long-range interaction calculation methods
    '3DPeriodicElectrostaticSummationMethod'     => $electrostaticMethod,
    '****************************'               => $vdwCalculationMethod,
    
    # Force field configuration
    CurrentForcefield                     => $forceFieldName,
    
    # Molecular dynamics parameters
    TimeStep                              => $timeStepSize, 
    TrajectoryRestart                     => 'false',  # Start new trajectory
    AppendTrajectory                      => 'false',  # Don't append to existing trajectory
    WriteLevel                            => "Silent", # Minimize unnecessary output
    
    # Thermodynamic ensemble configuration
    Ensemble3D                            => "NPT",    # Constant pressure and temperature
    Thermostat                            => "Nose",   # Temperature control algorithm
    Barostat                              => "Andersen", # Pressure control algorithm
]);

###################################################################################
# EXECUTION PIPELINE BEGINS HERE - TRAJECTORY PROCESSING

# Input validation - verify trajectory file existence before proceeding
if (!defined($Documents{"$inputFileName.xtd"})) {
    $logFile->Append("CRITICAL ERROR: Input trajectory file '$inputFileName.xtd' not found in workspace.\n");
    $logFile->Append("Please check filename and ensure the trajectory is correctly imported.\n");
    $logFile->Save;
    die "ERROR: Input trajectory file '$inputFileName.xtd' not found. Process terminated.";
}

# Extract individual frames from the trajectory for separate processing
# This allows independent Tg calculation for each structural snapshot
$logFile->Append("Beginning trajectory extraction process for file: $inputFileName.xtd\n");
$logFile->Save;

my @extractedFrames = extractFramesFromTrajectory($inputFileName);

# Validate successful frame extraction
if (scalar(@extractedFrames) == 0) {
    $logFile->Append("CRITICAL ERROR: Failed to extract any frames from trajectory.\n");
    $logFile->Append("Possible causes: corrupt file, empty trajectory, or insufficient permissions.\n");
    $logFile->Save;
    die "ERROR: Frame extraction failed. No frames available for processing.";
}

# Log successful extraction
$logFile->Append("SUCCESS: Extracted " . scalar(@extractedFrames) . " frames from trajectory file.\n");
$logFile->Append("Frame identifiers: " . join(", ", @extractedFrames) . "\n");
$logFile->Save;

# Initialize main processing loop - iterate through each extracted frame
foreach my $currentFrame (@extractedFrames) {
    $logFile->Append("\n===========================================================\n");
    $logFile->Append("PROCESSING FRAME: $currentFrame\n");
    $logFile->Append("===========================================================\n");
    $logFile->Save;

    # Create a new tab in the study table for this frame's results
    my $frameResultSheet;
    eval { $frameResultSheet = $resultsTable->InsertSheet; };
    if ($@) {
        $logFile->Append("ERROR: Failed to create study table sheet for $currentFrame: $@\n");
        $logFile->Append("Skipping this frame and continuing with next available frame\n");
        $logFile->Save;
        next; # Skip to next frame
    }
    
    # Configure sheet and set up column headers
    $frameResultSheet->Title = "$currentFrame";
    $frameResultSheet->ColumnHeading(0) = "Temperature (K)";
    $frameResultSheet->ColumnHeading(1) = "Density (g/cm³)";
    $frameResultSheet->ColumnHeading(2) = "Total Energy (kcal/mol)";
    $frameResultSheet->ColumnHeading(3) = "Torsion Mobility (%)";
    $frameResultSheet->ColumnHeading(4) = "Status";

    # Verify frame document exists
    if (!defined($Documents{"$currentFrame.xsd"})) {
        $logFile->Append("ERROR: Required structure file $currentFrame.xsd not found\n");
        $logFile->Append("This may indicate file system access issues or earlier extraction failure\n");
        $logFile->Save;
        next; # Skip to next frame
    }

    # Load molecular structure for this frame
    my $molecularStructure = $Documents{"$currentFrame.xsd"};
    $logFile->Append("Loaded structure from $currentFrame.xsd\n");

    # Run initial equilibration to prepare structure
    $logFile->Append("Beginning initial structure equilibration phase...\n");
    $logFile->Save;
    
    my $preparedTrajectory;
    eval { $preparedTrajectory = prepareInitialStructure($molecularStructure, $numTemperatureCycles); };
    
    if ($@ || !defined($preparedTrajectory)) {
        $logFile->Append("CRITICAL ERROR: Initial equilibration failed for structure $currentFrame\n");
        $logFile->Append("Error details: " . ($@ || "Equilibration returned undefined trajectory") . "\n");
        $logFile->Append("Skipping this structure and continuing with next frame\n");
        $logFile->Save;
        next; # Skip to next frame
    }
    
    $logFile->Append("Initial equilibration completed successfully\n");
    $logFile->Save;
                            
    #####################################################################
    # Temperature sweep - core analysis for Tg determination
    #####################################################################
    
    my $resultRowCounter = 0;  # Row index for data storage
    
    # Temperature loop - descending from high to low temperature
    for (my $currentTemperature = $highestTemperature; $currentTemperature >= $lowestTemperature; 
         $currentTemperature -= $temperatureStepSize) {
        
        $logFile->Append("\n==================================================\n");
        $logFile->Append("TEMPERATURE POINT: $currentTemperature K\n");
        $logFile->Append("==================================================\n");
        $logFile->Save;
    
        # Initialize variables for this temperature point
        my $averageDensityValue = undef;
        my $densityConvergenceStatus = "failed";  # Default assumption - equilibration needed
        my $totalEnergyValue = undef;
        my $firstDynamicsRun = undef;
        my $simulationTrajectory = undef;
        
        # Calculate temperature-dependent equilibration duration
        # Lower temperatures require longer equilibration times
        my $scaledEquilibrationTime = $equilibrationTime * $highestTemperature / $currentTemperature;
        my $equilibrationStepCount = int($scaledEquilibrationTime * FEMTO_TO_PICO / $timeStepSize);
        my $productionStepCount = int($productionRunTime * FEMTO_TO_PICO / $timeStepSize);
        
        $logFile->Append("EQUILIBRATION: $scaledEquilibrationTime ps ($equilibrationStepCount steps)\n");
        $logFile->Append("PRODUCTION: $productionRunTime ps ($productionStepCount steps)\n");
        
        # Create new document for this temperature point
        $logFile->Append("Creating structure for temperature point...\n");
        
        my $temperatureStructure;
        eval {
            # Create new document for this temperature with unique name
            $temperatureStructure = Documents->New("$currentFrame"."_$currentTemperature.xsd");
            
            # Use last frame from previous temperature point or initial equilibration
            $preparedTrajectory->Trajectory->CurrentFrame = $preparedTrajectory->Trajectory->EndFrame;
            $temperatureStructure->CopyFrom($preparedTrajectory);
        };
        
        if ($@ || !defined($temperatureStructure)) {
            $logFile->Append("ERROR: Failed to create temperature point structure: " . 
                            ($@ || "Document creation failed") . "\n");
            $logFile->Append("Skipping temperature point $currentTemperature K\n");
            $logFile->Save;
            next; # Skip to next temperature
        }

        # Close previous trajectory to free resources
        eval { $preparedTrajectory->Close; };
        if ($@) {
            $logFile->Append("WARNING: Failed to close previous trajectory: $@\n");
            $logFile->Append("Continuing despite potential resource leak\n");
        }
        
        $logFile->Append("Beginning NPT equilibration at $currentTemperature K...\n");
        $logFile->Save;
        
        # Configure Forcite for equilibration at this temperature
        $forciteModule->ChangeSettings([    
            Pressure                => $simulationPressure,
            Temperature             => $currentTemperature,
            InitialVelocities       => "Current",  # Continue from previous state
            NumberOfSteps           => $equilibrationStepCount,
            TrajectoryFrequency     => $snapshotFrequencyEquil,
        ]);
    
        # Execute first equilibration cycle with error trapping
        eval {$firstDynamicsRun = $forciteModule->Dynamics->Run($temperatureStructure)};
        
        if ($@ || !defined($firstDynamicsRun)) {
            # Handle equilibration failure
            $logFile->Append("\n!!!!!!!!!!!!!!!!!!! ERROR !!!!!!!!!!!!!!!!!!!\n");
            $logFile->Append("Initial NPT equilibration failed at $currentTemperature K\n");
            $logFile->Append("Error details: " . ($@ || "Dynamics run returned undefined result") . "\n");
            $logFile->Append("This may indicate system instability at this temperature\n");
            $logFile->Save;
            
            # Try to clean up resources
            eval { $temperatureStructure->Close; };
            
            # Record failure in study table
            $frameResultSheet->Cell($resultRowCounter, 0) = $currentTemperature;
            $frameResultSheet->Cell($resultRowCounter, 4) = "ERROR: Equilibration failed";
            
            ++$resultRowCounter;
            next; # Skip to next temperature
        }
        
        # First cycle succeeded - prepare for density analysis
        eval { $temperatureStructure->Close; };  # Close document to free resources
        $simulationTrajectory = $firstDynamicsRun->Trajectory;
        $totalEnergyValue = $firstDynamicsRun->{'TotalEnergy'};
        
        $logFile->Append("Initial equilibration cycle completed\n");
        $logFile->Append("Total energy: $totalEnergyValue kcal/mol\n");
        $logFile->Append("Checking density convergence...\n");
        $logFile->Save;
    
        #########################################################
        # Density convergence analysis
        #########################################################
        
        # Verify trajectory is accessible
        if (!defined($simulationTrajectory) || !defined($simulationTrajectory->Trajectory)) {
            $logFile->Append("ERROR: Cannot access equilibration trajectory\n");
            $logFile->Save;
            
            # Record error in study table
            $frameResultSheet->Cell($resultRowCounter, 0) = $currentTemperature;
            $frameResultSheet->Cell($resultRowCounter, 4) = "ERROR: Invalid trajectory";
            
            ++$resultRowCounter;
            next; # Skip to next temperature
        }
        
        # Set up frame range for density analysis
        my $frameCount = $simulationTrajectory->Trajectory->NumFrames;
        my $firstFrame = 1;
        
        # Analyze density convergence
        eval { ($densityConvergenceStatus, $averageDensityValue) = 
               checkDensityConvergence($simulationTrajectory, $firstFrame, $frameCount); };
        if ($@) {
            $logFile->Append("ERROR during density convergence check: $@\n");
            $logFile->Save;
            $densityConvergenceStatus = "failed";
        } else {
            $logFile->Append("Density convergence status: $densityConvergenceStatus\n");
            $logFile->Append(sprintf("Average density: %.4f g/cm³\n", $averageDensityValue));
            $logFile->Save;
        }
        
        #########################################################
        # Additional equilibration cycles if needed
        #########################################################
        
        my $equilibrationAttemptCount = 1;  # Counter for NPT restart attempts
        
        # Continue equilibration until density converges or max restarts reached
        while ($densityConvergenceStatus eq "failed" && $equilibrationAttemptCount <= $maxEquilibrationAttempts) {
            $logFile->Append("\nDensity not converged - starting additional NPT cycle #$equilibrationAttemptCount\n");
            $logFile->Append("Duration: $equilibrationTime ps\n");
            $logFile->Save;
            
            # Run additional NPT cycle
            my $cycleResults;
            eval {
                $cycleResults = $forciteModule->Dynamics->Run($simulationTrajectory, [    
                    TrajectoryRestart     => 'true',   # Continue from previous state
                    AppendTrajectory      => 'true',   # Add to existing trajectory
                ]);
            };
    
            if ($@ || !defined($cycleResults)) {
                # Handle failure in additional equilibration cycle
                $logFile->Append("\n!!!!!!!!!!!!!!!!!!! ERROR !!!!!!!!!!!!!!!!!!!\n");
                $logFile->Append("NPT restart cycle #$equilibrationAttemptCount failed\n");
                $logFile->Append("Error: " . ($@ || "Dynamics run returned undefined result") . "\n");
                $logFile->Save;
                last; # Exit equilibration loop on failure
            } 
            
            # Update tracking variables for next analysis
            $totalEnergyValue = $cycleResults->{'TotalEnergy'};
            $firstFrame = $frameCount;  # Start from end of previous analysis
            $frameCount = $simulationTrajectory->Trajectory->NumFrames;  # Update to new end
            
            $logFile->Append("Cycle completed - analyzing new frames $firstFrame to $frameCount\n");
            $logFile->Append("Total energy: $totalEnergyValue kcal/mol\n");
            
            # Re-check density convergence with new frames
            eval { ($densityConvergenceStatus, $averageDensityValue) = 
                   checkDensityConvergence($simulationTrajectory, $firstFrame, $frameCount); };
            if ($@) {
                $logFile->Append("ERROR during density convergence check: $@\n");
                $logFile->Save;
                $densityConvergenceStatus = "failed";
            } else {
                $logFile->Append("Density convergence status: $densityConvergenceStatus\n");
                $logFile->Append(sprintf("Average density: %.4f g/cm³\n", $averageDensityValue));
            }
            
            # Increment restart counter
            ++$equilibrationAttemptCount;
    
            # Check for maximum restart limit
            if ($equilibrationAttemptCount > $maxEquilibrationAttempts && $densityConvergenceStatus eq "failed") {
                $logFile->Append("WARNING: Maximum NPT cycles ($maxEquilibrationAttempts) reached without density convergence\n");
                $logFile->Append("Proceeding to production phase despite non-convergence\n");
                $logFile->Save;
                last; # Exit restart loop after max attempts
            }
        }
    
        $logFile->Append("\nEquilibration phase complete - final status: $densityConvergenceStatus\n");
        $logFile->Save;
    
        #########################################################
        # Production run and property analysis
        #########################################################
        
        my $torsionMobilityPercent = -1;  # Default for torsion mobility measurement
        
        # Skip production if equilibration trajectory is invalid
        if (!defined($simulationTrajectory) || !defined($simulationTrajectory->Trajectory)) {
            $logFile->Append("ERROR: Cannot access equilibration trajectory for production run\n");
            $logFile->Append("Skipping production phase at this temperature\n");
            $logFile->Save;
            
            # Record error in study table
            $frameResultSheet->Cell($resultRowCounter, 0) = $currentTemperature;
            $frameResultSheet->Cell($resultRowCounter, 4) = "ERROR: Invalid trajectory";
            
            ++$resultRowCounter;
            next; # Skip to next temperature
        }
        
        # Begin production phase for property collection
        $logFile->Append("\nStarting production phase ($productionRunTime ps)...\n");
        $logFile->Save;
            
        # Store current frame count for analysis range
        my $prodStartFrame = $simulationTrajectory->Trajectory->NumFrames;
        my $productionResult;
            
        # Execute production run
        eval {
            $productionResult = $forciteModule->Dynamics->Run($simulationTrajectory, [    
                TrajectoryRestart     => 'true',
                AppendTrajectory      => 'true',
                NumberOfSteps         => $productionStepCount,
                TrajectoryFrequency   => $snapshotFrequencyProd,
            ]);
        };
        
        if ($@ || !defined($productionResult)) {
            $logFile->Append("ERROR: Production run failed: " . ($@ || "Dynamics returned undefined result") . "\n");
            $logFile->Append("No property data will be available for this temperature point\n");
            $logFile->Save;
            
            # Record partial data in study table
            $frameResultSheet->Cell($resultRowCounter, 0) = $currentTemperature;
            $frameResultSheet->Cell($resultRowCounter, 4) = "ERROR: Production failed";
            
            ++$resultRowCounter;
            next; # Skip to next temperature
        }
            
        # Get ending frame for production analysis
        my $prodEndFrame = $simulationTrajectory->Trajectory->NumFrames;
        $logFile->Append("Production completed: analyzing frames $prodStartFrame to $prodEndFrame\n");
            
        # Perform torsion mobility analysis if enabled
        my $torsionCount = 0;
        eval { $torsionCount = $simulationTrajectory->UnitCell->Torsions->Count; };
        
        if ($@ || $torsionCount == 0) {
            $logFile->Append("No torsion angles defined - skipping mobility analysis\n");
            $torsionMobilityPercent = 0;
        } elsif ($enableTorsionAnalysis == 1) {
            $logFile->Append("Analyzing torsion mobility...\n");
            eval { $torsionMobilityPercent = 
                   measureTorsionMobility($simulationTrajectory, $prodStartFrame, $prodEndFrame); };
            if ($@) {
                $logFile->Append("ERROR in torsion analysis: $@\n");
                $logFile->Save;
                $torsionMobilityPercent = -1;
            } else {
                $logFile->Append(sprintf("Torsion mobility: %.2f%%\n", $torsionMobilityPercent));
            }
        } else {
            $logFile->Append("Torsion analysis disabled in configuration\n");
            $torsionMobilityPercent = -1;
        }
        
        # Calculate average properties from production run
        my $prodAvgDensity = 0;
        my $prodAvgEnergy = 0;
        
        eval { 
            ($prodAvgDensity, $prodAvgEnergy) = 
                calculateAverageProperties($simulationTrajectory, $prodStartFrame, $prodEndFrame); 
        };
        if ($@) {
            $logFile->Append("ERROR in property averaging: $@\n");
            $logFile->Save;
        } else {
            $logFile->Append(sprintf("Production average density: %.4f g/cm³\n", $prodAvgDensity));
            $logFile->Append(sprintf("Production average energy: %.2f kcal/mol\n", $prodAvgEnergy));
        }
        
        # Record results in study table
        $frameResultSheet->Cell($resultRowCounter, 0) = $currentTemperature;
        $frameResultSheet->Cell($resultRowCounter, 1) = $prodAvgDensity;
        $frameResultSheet->Cell($resultRowCounter, 2) = $prodAvgEnergy;
        $frameResultSheet->Cell($resultRowCounter, 3) = $torsionMobilityPercent;
        
        # Add status information
        if ($densityConvergenceStatus eq "failed") {
            $frameResultSheet->Cell($resultRowCounter, 4) = "WARNING: Density not fully converged";    
        } else {
            $frameResultSheet->Cell($resultRowCounter, 4) = "SUCCESS";
        }
        
        # Increment row counter for next temperature point
        ++$resultRowCounter;
        
        # Save equilibration trajectory for next temperature point
        $preparedTrajectory = $simulationTrajectory;    
        
        $logFile->Append("\nTemperature point $currentTemperature K completed\n");
        $logFile->Save;
    }
    
    $logFile->Append("\nFrame $currentFrame processing completed\n");
    $logFile->Save;
}

# Calculate statistical averages across all analyzed frames
# This produces the final data for Tg determination
eval { calculateStatisticalSummary($resultsTable, 0); };
if ($@) {
    $logFile->Append("ERROR during statistical summary calculation: $@\n");
    $logFile->Save;
}

# Final report and instructions
$logFile->Append("\n==============================================================\n");
$logFile->Append("GLASS TRANSITION ANALYSIS COMPLETED\n");
$logFile->Append("==============================================================\n");
$logFile->Append("The study table contains temperature-dependent property data for all analyzed structures.\n");
$logFile->Append("The 'Averages' sheet provides statistical summary across all frames.\n\n");
$logFile->Append("To determine the glass transition temperature (Tg):\n");
$logFile->Append("1. Plot density vs. temperature from the Averages sheet\n");
$logFile->Append("2. Identify the inflection point where the slope changes significantly\n");
$logFile->Append("3. For precise Tg determination, fit separate linear regressions to the\n");
$logFile->Append("   high and low temperature regions, then find their intersection\n\n");
$logFile->Append("Additional indicators of Tg:\n");
$logFile->Append("- Significant changes in torsional mobility near the transition temperature\n");
$logFile->Append("- Changes in thermal expansion coefficient (slope of density vs. temperature)\n");
$logFile->Append("- Shifts in energy fluctuations across the transition region\n\n");
$logFile->Append("For publication-quality results, it is recommended to:\n");
$logFile->Append("1. Repeat this analysis at least 3 times with different initial conditions\n");
$logFile->Append("2. Average the obtained Tg values and report statistical uncertainty\n");
$logFile->Append("3. Validate with experimental data when available\n\n");
$logFile->Append("Calculation summary:\n");
$logFile->Append("- Analyzed " . scalar(@extractedFrames) . " independent structures\n");
$logFile->Append("- Temperature range: $highestTemperature K to $lowestTemperature K in $temperatureStepSize K steps\n");
$logFile->Append("- Force field: $forceFieldName\n\n");
$logFile->Append("Analysis completed on " . localtime() . "\n");
$logFile->Save;

###################################################################################
# SUBROUTINES - CORE FUNCTIONALITY IMPLEMENTATION
###################################################################################

sub extractFramesFromTrajectory {
    # Extracts individual frames from a trajectory file into separate documents
    #
    # Parameters:
    # $trajectoryName - Base name of the trajectory file (without extension)
    #
    # Returns:
    # Array of frame identifiers (strings) that can be used to access individual frame documents
    
    my $trajectoryName = shift;
    my @frameNamesList = ();
    
    $logFile->Append("TRAJECTORY EXTRACTION: Processing $trajectoryName.xtd\n");
    
    eval {
        # Access the trajectory document
        my $trajectoryDoc = $Documents{"$trajectoryName.xtd"};
        
        if (!defined($trajectoryDoc)) {
            die "Trajectory document object could not be accessed";
        }
        
        # Optional torsion identification if enabled
        if ($enableTorsionAnalysis == 1) {
            $logFile->Append("Torsion analysis enabled - identifying backbone atoms...\n");
            findBackboneAtoms($trajectoryDoc);
            $logFile->Append("Backbone identification complete\n");
        }

        $logFile->Save;
        
        # Get total frame count for progress reporting
        my $totalFrames = $trajectoryDoc->Trajectory->NumFrames;
        $logFile->Append("Found $totalFrames frames in trajectory\n");
        
        # Process each frame in the trajectory
        for (my $frameIndex = 1; $frameIndex <= $totalFrames; ++$frameIndex) {
            $logFile->Append("Extracting frame $frameIndex of $totalFrames...\n");
            
            # Position at current frame in the trajectory
            $trajectoryDoc->Trajectory->CurrentFrame = $frameIndex;
            
            # Create new document for this frame
            my $frameDocName = "$inputFileName"."$frameIndex.xsd";
            my $frameDoc = Documents->New($frameDocName);
            
            if (!defined($frameDoc)) {
                die "Failed to create new document for frame $frameIndex";
            }
            
            # Copy structural data from trajectory frame
            $frameDoc->CopyFrom($trajectoryDoc);
            $frameDoc->Close;
            
            # Store frame identifier for later processing
            push (@frameNamesList, "$inputFileName"."$frameIndex");
            
            $logFile->Append("Frame $frameIndex successfully extracted as $frameDocName\n");
        }
        
        $logFile->Append("Frame extraction complete: " . scalar(@frameNamesList) . " frames processed\n");
    };
    
    # Error handling for trajectory processing
    if ($@) {
        $logFile->Append("ERROR DURING TRAJECTORY EXTRACTION: $@\n");
        $logFile->Append("This may indicate a corrupted trajectory file or memory limitations\n");
        $logFile->Save;
    }
    
    return @frameNamesList;
}

sub prepareInitialStructure {
    # Performs initial structure relaxation and temperature cycling to prepare for Tg determination
    #
    # This function implements a three-phase preparation process:
    # 1. Geometry optimization to relieve unfavorable contacts
    # 2. Short NVE dynamics with reduced timestep to stabilize velocities
    # 3. Temperature cycling (annealing) to escape metastable states
    #
    # Parameters:
    # $structure - Document containing the structure to equilibrate
    # $cycles - Number of temperature cycles to perform
    #
    # Returns:
    # Trajectory object containing equilibrated structure, or undef on failure
    
    my $structure = shift;
    my $cycles = shift;
    
    $logFile->Append("\n=====================================================\n");
    $logFile->Append("INITIAL EQUILIBRATION: Structure preparation phase\n");
    $logFile->Append("=====================================================\n");
    
    # PHASE 1: Geometry optimization to remove high-energy contacts
    $logFile->Append("PHASE 1: Executing geometry optimization...\n");
    my $optimizationResults;
    eval {$optimizationResults = $forciteModule->GeometryOptimization->Run($structure);};
    
    if ($@) {
        $logFile->Append("ERROR: Geometry optimization failed with message:\n");
        $logFile->Append("$@\n");
        $logFile->Append("This may indicate highly strained initial geometry or force field incompatibility\n");
        $logFile->Save;
        return undef; # Signal failure to caller
    } 
    
    $logFile->Append("Geometry optimization completed successfully\n");
    $logFile->Append("Final energy: " . $optimizationResults->{'Energy'} . " kcal/mol\n");
    
    # PHASE 2: Short dynamics with reduced timestep to stabilize velocities
    $logFile->Append("\nPHASE 2: Executing short dynamics for velocity stabilization...\n");
    my $shortDynamics;
    eval {
        $shortDynamics = $forciteModule->Dynamics->Run($structure, ([    
            TimeStep => 0.5,           # Reduced timestep for stability
            NumberOfSteps => 1000,     # Short run (0.5 ps)
            Ensemble3D => "NVE",       # Microcanonical ensemble preserves energy
            Quality => "Coarse"        # Speed over accuracy for this phase
        ]));
    };
    
    if ($@ || !defined($shortDynamics)) {
        $logFile->Append("ERROR: Initial dynamics equilibration failed:\n");
        $logFile->Append(($@ || "Undefined return value from dynamics run") . "\n");
        $logFile->Append("Possible causes: unstable starting structure or timestep too large\n");
        $logFile->Save;
        return undef;
    }
    
    $logFile->Append("Short dynamics completed successfully\n");
    $logFile->Append("Final temperature: " . $shortDynamics->{'Temperature'} . " K\n");
    $logFile->Save;
    
    # PHASE 3: Temperature cycling (annealing) to escape local minima
    $logFile->Append("\nPHASE 3: Executing temperature cycling ($cycles cycles)...\n");
    
    # Track success/failure of temperature cycling
    my $cycleSuccess = 0;
    
    for (my $cycleIndex = 0; $cycleIndex < $cycles; ++$cycleIndex) {
        $logFile->Append("\nTemperature cycle " . ($cycleIndex + 1) . " of $cycles\n");
        $logFile->Append("----------------------------------------\n");
        
        # HEATING PHASE - gradually increase temperature
        $logFile->Append("Heating phase: $lowestTemperature K → $highestTemperature K\n");
        my $heatingSuccess = 1; # Track if heating completed successfully
        
        for (my $tempPoint = $lowestTemperature; $tempPoint <= $highestTemperature; 
             $tempPoint += $temperatureStepSize) {
            
            $logFile->Append("   Running at $tempPoint K... ");
            
            # Execute NPT dynamics at current temperature
            my $annealingCycle;
            eval {
                $annealingCycle = $forciteModule->Dynamics->Run($shortDynamics->Trajectory, ([ 
                    Ensemble3D => "NPT",
                    Pressure => 0.001,               # Low pressure to avoid artifacts
                    Temperature => $tempPoint,        # Current temperature in cycle
                    NumberOfSteps => 5000,           # 5 ps per temperature step
                    TrajectoryFrequency => 2500,     # Save 2 frames per temperature
                    TrajectoryRestart => 1,          # Continue from previous state
                    AppendTrajectory => 1,           # Add to existing trajectory
                    Quality => "Coarse"              # Speed over accuracy for annealing
                ]));
            };
            
            if ($@ || !defined($annealingCycle)) {
                $logFile->Append("FAILED\n");
                $logFile->Append("   Error: " . ($@ || "No valid return from dynamics") . "\n");
                $heatingSuccess = 0;
            } else {
                $logFile->Append("OK\n");
                # Update the trajectory reference to continue from this point
                $shortDynamics = $annealingCycle;
            }
        }
        
        # COOLING PHASE - gradually decrease temperature
        $logFile->Append("Cooling phase: $highestTemperature K → $lowestTemperature K\n");
        my $coolingSuccess = 1; # Track if cooling completed successfully
        
        for (my $tempPoint = $highestTemperature; $tempPoint >= $lowestTemperature; 
             $tempPoint -= $temperatureStepSize) {
            
            $logFile->Append("   Running at $tempPoint K... ");
            
            # Execute NPT dynamics at current temperature
            my $annealingCycle;
            eval {
                $annealingCycle = $forciteModule->Dynamics->Run($shortDynamics->Trajectory, ([ 
                    Ensemble3D => "NPT",
                    Pressure => 0.001,               # Low pressure to avoid artifacts
                    Temperature => $tempPoint,        # Current temperature in cycle
                    NumberOfSteps => 5000,           # 5 ps per temperature step
                    TrajectoryFrequency => 2500,     # Save 2 frames per temperature
                    TrajectoryRestart => 1,          # Continue from previous state
                    AppendTrajectory => 1,           # Add to existing trajectory
                    Quality => "Coarse"              # Speed over accuracy for annealing
                ]));
            };
            
            if ($@ || !defined($annealingCycle)) {
                $logFile->Append("FAILED\n");
                $logFile->Append("   Error: " . ($@ || "No valid return from dynamics") . "\n");
                $coolingSuccess = 0;
            } else {
                $logFile->Append("OK\n");
                # Update the trajectory reference to continue from this point
                $shortDynamics = $annealingCycle;
            }
        }
        
        # Report on this cycle's success
        if ($heatingSuccess && $coolingSuccess) {
            $logFile->Append("Cycle " . ($cycleIndex + 1) . " completed successfully\n");
            $cycleSuccess = 1;
        } else {
            $logFile->Append("Cycle " . ($cycleIndex + 1) . " completed with errors\n");
        }
    }
    
    # Final validation of trajectory
    if (!defined($shortDynamics) || !defined($shortDynamics->Trajectory)) {
        $logFile->Append("ERROR: Failed to obtain valid trajectory after temperature cycling\n");
        $logFile->Append("Temperature cycling phase produced unusable results\n");
        $logFile->Save;
        return undef;
    }
    
    # Final report on equilibration process
    if ($cycleSuccess) {
        $logFile->Append("\nINITIAL EQUILIBRATION COMPLETED SUCCESSFULLY\n");
        $logFile->Append("Final temperature: " . $shortDynamics->{'Temperature'} . " K\n");
        $logFile->Append("Total frames in trajectory: " . $shortDynamics->Trajectory->NumFrames . "\n");
    } else {
        $logFile->Append("\nINITIAL EQUILIBRATION COMPLETED WITH WARNINGS\n");
        $logFile->Append("Some temperature cycling steps failed, but obtained usable trajectory\n");
    }
    $logFile->Save;
    
    # Return the trajectory for subsequent processing
    return ($shortDynamics->Trajectory);
}

sub checkDensityConvergence {
    # Analyzes density fluctuations to determine system equilibration status
    #
    # This function implements the block-averaging technique to evaluate density convergence:
    # 1. Extracts density values from trajectory frames
    # 2. Groups values into blocks for statistical averaging
    # 3. Compares adjacent blocks to detect convergence
    # 4. Returns convergence status and average density value
    #
    # Parameters:
    # $trajectory - Trajectory object containing frames to analyze
    # $startFrameNum - First frame to include in analysis
    # $endFrameNum - Total number of frames to analyze
    #
    # Returns:
    # Tuple containing (convergence_status, average_density)
    # convergence_status: "passed" or "failed"
    # average_density: Average density value across analyzed frames (g/cm³)
    
    my ($trajectory, $startFrameNum, $endFrameNum) = @_;

    # Input validation - critical to prevent downstream errors
    if (!defined($trajectory) || !defined($startFrameNum) || !defined($endFrameNum)) {
        $logFile->Append("ERROR: Incomplete parameters passed to density convergence function\n");
        $logFile->Save;
        die "checkDensityConvergence: Missing required parameters";
    }
    
    # Verify frame range is valid
    if ($startFrameNum > $endFrameNum) {
        $logFile->Append("WARNING: Invalid frame range in density check ($startFrameNum > $endFrameNum)\n");
        $logFile->Save;
        return ("failed", 0); # Invalid frame range
    }

    # Initialize data collection array
    my @densityValues;
    $logFile->Append("DENSITY ANALYSIS: Collecting data from frames $startFrameNum to $endFrameNum\n");

    # Extract density values from each trajectory frame
    eval {
        for (my $frameNum = $startFrameNum; $frameNum <= $endFrameNum; ++$frameNum) {
            # Set current frame and extract density value
            $trajectory->Trajectory->CurrentFrame = int($frameNum);
            my $densityValue = $trajectory->SymmetrySystem->Density;
            
            # Validate density value before storing
            if (!defined($densityValue) || $densityValue <= 0) {
                die "Invalid density value found at frame $frameNum: " . 
                    (defined($densityValue) ? $densityValue : "undefined");
            }
            
            # Store valid density value
            push (@densityValues, $densityValue);
        }
    };
    
    # Handle any errors during density extraction
    if ($@) {
        $logFile->Append("ERROR: Failed to retrieve density values: $@\n");
        $logFile->Append("This may indicate trajectory corruption or calculation errors\n");
        $logFile->Save;
        return ("failed", 0);
    }

    # Check for sufficient data points
    my $dataPointCount = scalar(@densityValues);
    $logFile->Append("Collected $dataPointCount density points for analysis\n");
    
    if ($dataPointCount < 2) {
        $logFile->Append("WARNING: Insufficient data points ($dataPointCount) for convergence analysis\n");
        $logFile->Append("Minimum of 2 points required, returning failed status\n");
        $logFile->Save;
        return ("failed", 0);
    }
    
    # Initialize block averaging data structures
    my @blockAverages;
    
    # Compute block averages for density values
    # This technique reduces noise and helps identify true convergence
    eval {
        my $blockCounter = 0;
        my $densitySum = 0;
        
        for (my $i = 0; $i < $dataPointCount; ++$i) {
            # Add value to current block
            $densitySum += $densityValues[$i];
            $blockCounter++;
            
            # Once block is full, compute average and reset counters
            if ($blockCounter == $averagingBlockSize || $i == $dataPointCount - 1) {
                # Calculate and store block average
                my $blockAverage = $densitySum / $blockCounter;
                push(@blockAverages, $blockAverage);
                
                # Reset counters for next block
                $densitySum = 0;
                $blockCounter = 0;
            }
        }
    };
    
    if ($@) {
        $logFile->Append("ERROR: Failed to compute block averages: $@\n");
        $logFile->Save;
        return ("failed", 0);
    }
    
    # Need at least two blocks to assess convergence
    my $blockCount = scalar(@blockAverages);
    $logFile->Append("Computed $blockCount averaging blocks\n");
    
    if ($blockCount < 2) {
        $logFile->Append("WARNING: Need at least 2 blocks for convergence analysis\n");
        $logFile->Save;
        return ("failed", 0);
    }
    
    # Compute differences between adjacent blocks
    my @blockDifferences;
    
    for (my $blockIndex = 1; $blockIndex < $blockCount; ++$blockIndex) {
        my $difference = abs($blockAverages[$blockIndex] - $blockAverages[$blockIndex-1]);
        push(@blockDifferences, $difference);
        $logFile->Append(sprintf("Block %d to %d difference: %.4f g/cm³\n", 
                                $blockIndex-1, $blockIndex, $difference));
    }
    
    # Check if all differences are below tolerance threshold
    my $convergenceStatus = "passed";
    
    foreach my $difference (@blockDifferences) {
        if ($difference > $densityConvergenceTolerance) {
            $convergenceStatus = "failed";
            $logFile->Append(sprintf("Difference %.4f exceeds tolerance %.4f - convergence failed\n", 
                                    $difference, $densityConvergenceTolerance));
            last;
        }
    }
    
    # Compute overall average density
    my $totalDensity = 0;
    foreach my $blockAvg (@blockAverages) {
        $totalDensity += $blockAvg;
    }
    my $averageDensity = $totalDensity / $blockCount;
    
    # Log convergence status
    $logFile->Append(sprintf("CONVERGENCE STATUS: %s (Avg. Density: %.4f g/cm³)\n", 
                            $convergenceStatus, $averageDensity));
    
    return ($convergenceStatus, $averageDensity);
}

sub findBackboneAtoms {
    # Identifies backbone atoms in a polymer structure for torsion analysis
    #
    # This function identifies the main chain (backbone) atoms of polymers:
    # 1. Examines each molecule to find atoms marked as backbone atoms
    # 2. Creates appropriate torsion angle definitions between backbone atoms
    # 3. Handles branched polymers by tracking branch points
    #
    # Parameters:
    # $structure - Document containing the molecular structure to analyze
    #
    # Returns:
    # Boolean indicating if any backbone atoms were found
    
    my $structure = shift;
    
    if (!defined($structure)) {
        $logFile->Append("ERROR: Undefined structure in backbone identification\n");
        $logFile->Save;
        return 0;
    }
    
    my $atomCollection;
    eval { $atomCollection = $structure->UnitCell->Atoms; };
    if ($@) {
        $logFile->Append("ERROR: Failed to access atoms: $@\n");
        $logFile->Save;
        return 0;
    }
    
    # Array to store branch points for later processing
    my @branchPoints;
    
    # Store original atom names for restoration later
    my @originalAtomNames;
    
    # Get all molecules in structure
    my $moleculeCollection;
    eval { $moleculeCollection = $structure->UnitCell->Molecules; };
    if ($@) {
        $logFile->Append("ERROR: Failed to access molecules: $@\n");
        $logFile->Save;
        return 0;
    }
    
    # Process each molecule
    my $moleculeCount = 0;
    foreach my $molecule (@$moleculeCollection) {
        # Rename molecule for tracking
        eval { $molecule->Name = "$moleculeCount"; };
        ++$moleculeCount;
        
        # Process atoms within this molecule
        my $moleculeAtoms;
        eval { $moleculeAtoms = $molecule->Descendants->Atoms; };
        if ($@) {
            $logFile->Append("ERROR: Failed to access molecule atoms: $@\n");
            $logFile->Save;
            next;
        }

        # Mark backbone atoms with special name
        foreach my $atom (@$moleculeAtoms) {
            eval {
                push(@originalAtomNames, $atom->Name);
                if ($atom->IsBackboneAtom) {
                    $atom->Name = "backbone";
                }
            };
            if ($@) {
                $logFile->Append("ERROR: Failed processing atom: $@\n");
                $logFile->Save;
            }
        }
    }
    
    # Find molecules that contain backbone atoms
    my @backboneMolecules;
    
    foreach my $molecule (@$moleculeCollection) {
        my $moleculeAtoms;
        eval { $moleculeAtoms = $molecule->Descendants->Atoms; };
        if ($@) {
            $logFile->Append("ERROR: Failed to access molecule atoms for backbone check: $@\n");
            $logFile->Save;
            next;
        }
        
        # Check for at least one backbone atom
        foreach my $atom (@$moleculeAtoms) {
            eval {
                if ($atom->IsBackboneAtom) {
                    push(@backboneMolecules, $molecule->Name);
                    last;  # One backbone atom is enough to mark molecule
                }
            };
            if ($@) {
                $logFile->Append("ERROR: Failed checking backbone status: $@\n");
                $logFile->Save;
            }
        }
    }
    
    # Check if any backbone-containing molecules were found
    my $backboneMoleculeCount = scalar(@backboneMolecules);
    if ($backboneMoleculeCount < 1) {
        $logFile->Append("WARNING: No backbone molecules found in structure\n");
        $logFile->Save;
        return 0;
    }
    
    $logFile->Append("Found $backboneMoleculeCount molecules with backbone atoms\n");
    $logFile->Save;
    
    # Process each backbone-containing molecule to create torsion definitions
    foreach my $moleculeName (@backboneMolecules) {
        $logFile->Append("Processing backbone in molecule $moleculeName\n");
        
        # Get reference to molecule
        my $molecule;
        eval { $molecule = $structure->UnitCell->Molecules("$moleculeName"); };
        if ($@) {
            $logFile->Append("ERROR: Failed to access molecule $moleculeName: $@\n");
            $logFile->Save;
            next;
        }
        
        # Initialize torsion processing variables
        my $torsionCounter = 0;
        my @moleculeTorsions;
        my $processingComplete = 0;
        my $atomCounter = 0;
        
        # Get all backbone atoms in this molecule
        my @backboneAtoms;
        eval { @backboneAtoms = getAllBackboneAtoms($molecule); };
        if ($@) {
            $logFile->Append("ERROR: Failed to retrieve backbone atoms: $@\n");
            $logFile->Save;
            next;
        }
        
        my $backboneAtomCount = scalar(@backboneAtoms);
        $logFile->Append("Found $backboneAtomCount backbone atoms in molecule $moleculeName\n");
        
        # Insufficient atoms for torsion analysis
        if ($backboneAtomCount < 4) {
            $logFile->Append("WARNING: Not enough backbone atoms for torsion analysis\n");
            $logFile->Save;
            next;
        }
        
        # Find terminal atom to start walking the backbone
        my $atom1;
        eval { $atom1 = findTerminalAtom(@backboneAtoms); };
        if ($@ || !defined($atom1)) {
            $logFile->Append("ERROR: Failed to identify terminal atom: $@\n");
            $logFile->Save;
            next;
        }
        
        # Mark terminal atom with index
        eval { $atom1->Name = "$atomCounter"; };
        
        # Initialize atoms for first torsion
        my $atom2;
        my $atom3;
        my $atom4;
        my $isEndOfChain;
        
        # Create first set of atoms for torsion definition
        ++$atomCounter;
        eval { ($atom2, $isEndOfChain) = findNextBackboneAtom($atom1, $atomCounter, @branchPoints); };
        if ($@ || !defined($atom2)) {
            $logFile->Append("ERROR: Failed to find second atom: $@\n");
            $logFile->Save;
            next;
        }
        
        ++$atomCounter;
        eval { ($atom3, $isEndOfChain) = findNextBackboneAtom($atom2, $atomCounter, @branchPoints); };
        if ($@ || !defined($atom3)) {
            $logFile->Append("ERROR: Failed to find third atom: $@\n");
            $logFile->Save;
            next;
        }
        
        ++$atomCounter;
        eval { ($atom4, $isEndOfChain) = findNextBackboneAtom($atom3, $atomCounter, @branchPoints); };
        if ($@ || !defined($atom4)) {
            $logFile->Append("ERROR: Failed to find fourth atom: $@\n");
            $logFile->Save;
            next;
        }
        
        # Create first torsion definition
        my $torsion;
        eval { $torsion = $structure->CreateTorsion([$atom1, $atom2, $atom3, $atom4]); };
        if ($@ || !defined($torsion)) {
            $logFile->Append("ERROR: Failed to create torsion: $@\n");
            $logFile->Save;
            next;
        }
        
        # Name torsion and track it
        eval { $torsion->Name = "$moleculeName"."_$torsionCounter"; };
        ++$torsionCounter;
        $torsionStates{$torsion->Name} = ();
        
        # Continue creating torsions along the backbone chain
        while (!$processingComplete) {
            ++$atomCounter;
            
            # Shift atoms by one position and find next atom
            $atom1 = $atom2;
            $atom2 = $atom3;
            $atom3 = $atom4;
            
            eval { ($atom4, $isEndOfChain) = findNextBackboneAtom($atom3, $atomCounter, @branchPoints); };
            if ($@) {
                $logFile->Append("ERROR: Failed finding next backbone atom: $@\n");
                $logFile->Save;
                $isEndOfChain = 1;
            }
            
            # If chain continues, create next torsion
            if (!$isEndOfChain && defined($atom4)) {
                eval {
                    $torsion = $structure->CreateTorsion([$atom1, $atom2, $atom3, $atom4]);
                    $torsion->Name = "$moleculeName"."_$torsionCounter";
                };
                if ($@) {
                    $logFile->Append("ERROR: Failed creating next torsion: $@\n");
                    $logFile->Save;
                    last;
                }
                
                ++$torsionCounter;
                $torsionStates{$torsion->Name} = ();
            }
            # If chain ends but branch points exist, process branches
            elsif ($isEndOfChain) {
                my $branchCount = scalar(@branchPoints);
                
                if ($branchCount > 0) {
                    $logFile->Append("Found branch point, continuing with branch\n");
                    
                    # Get branch atom info
                    my $atom2Name = $branchPoints[0];
                    my $atom1Name = $atom2Name - 1;
                    
                    # Remove processed branch point
                    shift(@branchPoints);
                    
                    # Locate atoms by name
                    undef $atom1;
                    undef $atom2;
                    
                    foreach my $backboneAtom (@backboneAtoms) {
                        eval {
                            if ($backboneAtom->Name eq "$atom2Name") {
                                $atom2 = $backboneAtom;
                            } elsif ($backboneAtom->Name eq "$atom1Name") {
                                $atom1 = $backboneAtom;
                            }
                        };
                    }
                    
                    # Create first torsion of branch if atoms found
                    if (defined($atom1) && defined($atom2)) {
                        eval {
                            ($atom3, $isEndOfChain) = findNextBackboneAtom($atom2, $atomCounter, @branchPoints);
                            if (!$isEndOfChain && defined($atom3)) {
                                ($atom4, $isEndOfChain) = findNextBackboneAtom($atom3, $atomCounter, @branchPoints);
                                if (!$isEndOfChain && defined($atom4)) {
                                    $torsion = $structure->CreateTorsion([$atom1, $atom2, $atom3, $atom4]);
                                    $torsion->Name = "$moleculeName"."_$torsionCounter";
                                    ++$torsionCounter;
                                    $torsionStates{$torsion->Name} = ();
                                }
                            }
                        };
                        if ($@) {
                            $logFile->Append("ERROR: Failed creating branch torsion: $@\n");
                            $logFile->Save;
                        }
                    }
                } else {
                    # No more branches to process
                    $logFile->Append("No more branch points, backbone processing complete\n");
                    $processingComplete = 1;
                }
            } else {
                # Processing complete
                $processingComplete = 1;
            }
        }
        
        $logFile->Append("Created $torsionCounter torsion definitions for molecule $moleculeName\n");
    }
    
    # Restore original atom names
    my $atomIndex = 0;
    if (defined($atomCollection)) {
        foreach my $atom (@$atomCollection) {
            if ($atomIndex < scalar(@originalAtomNames)) {
                eval { $atom->Name = $originalAtomNames[$atomIndex]; };
            }
            ++$atomIndex;
        }
    }
    
    $logFile->Append("Backbone identification and torsion definition complete\n");
    $logFile->Save;
    
    return 1;  # Success
}

sub getAllBackboneAtoms {
    # Retrieves all atoms marked as backbone atoms from a molecule
    #
    # Parameters:
    # $molecule - Molecule object to search for backbone atoms
    #
    # Returns:
    # Array of atoms that are part of the molecular backbone
    
    my ($molecule) = @_;
    
    # Get all atoms in the molecule
    my $atoms;
    eval { $atoms = $molecule->Descendants->Atoms; };
    if ($@) {
        die "getAllBackboneAtoms: Failed to access molecule atoms: $@";
    }
    
    my @backboneAtomsList;
    
    # Filter atoms based on backbone flag
    foreach my $atom (@$atoms) {
        eval {
            if ($atom->IsBackboneAtom) {
                push(@backboneAtomsList, $atom);
            }
        };
        if ($@) {
            $logFile->Append("WARNING: Error checking backbone status: $@\n");
            $logFile->Save;
        }
    }

    return @backboneAtomsList;
}

sub findTerminalAtom {
    # Locates an endpoint (terminal atom) of a backbone chain
    #
    # This identifies atoms that have only one connection to another backbone atom,
    # which indicates it is at the end of a chain.
    #
    # Parameters:
    # @backboneAtoms - Array of backbone atoms to search
    #
    # Returns:
    # Terminal backbone atom, or undefined if none found
    
    my (@backboneAtoms) = @_;
    
    if (scalar(@backboneAtoms) == 0) {
        die "findTerminalAtom: No backbone atoms provided";
    }

    my $terminalAtom;

    # Check each backbone atom to find one with only one backbone connection
    foreach my $atom (@backboneAtoms) {
        my $connectedAtoms;
        eval { $connectedAtoms = $atom->AttachedAtoms; };
        if ($@) {
            $logFile->Append("ERROR: Failed to access atom connections: $@\n");
            $logFile->Save;
            next;
        }
        
        # Count connections to other backbone atoms
        my $backboneConnectionCount = 0;
        
        foreach my $connectedAtom (@$connectedAtoms) {
            eval {
                if ($connectedAtom->IsBackboneAtom) {
                    ++$backboneConnectionCount;
                }
            };
            if ($@) {
                $logFile->Append("ERROR: Failed checking connected atom backbone status: $@\n");
                $logFile->Save;
            }
        }

        # Atom with exactly one backbone connection is terminal
        if ($backboneConnectionCount == 1) {
            $terminalAtom = $atom;
            last;
        }
    }

    return $terminalAtom;
}

sub findNextBackboneAtom {
    # Locates the next backbone atom in a chain
    #
    # This walks along a backbone chain by finding connected backbone atoms
    # that haven't been processed yet (still named "backbone").
    #
    # Parameters:
    # $currentAtom - Current position in the backbone chain
    # $atomIndex - Index to assign to the next atom found
    # @branchPoints - Array to track potential branch points
    #
    # Returns:
    # Tuple containing (next_atom, end_of_chain_flag)
    # next_atom: Next backbone atom in the chain, or undefined if none
    # end_of_chain_flag: Boolean indicating if the chain ends at this point
    
    my ($currentAtom, $atomIndex, @branchPoints) = @_;
    
    if (!defined($currentAtom)) {
        die "findNextBackboneAtom: Current atom undefined";
    }
    
    # Get atoms connected to current atom
    my $connectedAtoms;
    eval { $connectedAtoms = $currentAtom->AttachedAtoms; };
    if ($@) {
        die "findNextBackboneAtom: Failed to access connected atoms: $@";
    }
    
    my $nextAtom;
    my $isChainEnd = 1;  # Default assumption - end of chain
    my $backboneAtomCount = 0;
    
    # Look for connected backbone atoms
    foreach my $connectedAtom (@$connectedAtoms) {
        eval {
            if ($connectedAtom->Name eq "backbone") {
                $nextAtom = $connectedAtom;
                ++$backboneAtomCount;
            }
        };
        if ($@) {
            $logFile->Append("ERROR: Failed checking connected atom name: $@\n");
            $logFile->Save;
        }
    }

    # Check for branch points (multiple backbone connections)
    if ($backboneAtomCount > 1) {
        # This is a branch point - add to tracking list
        push(@branchPoints, $currentAtom->Name);
        
        # Rename the next atom we're following
        eval { $nextAtom->Name = "$atomIndex"; };
        if ($@) {
            $logFile->Append("ERROR: Failed to rename atom at branch point: $@\n");
            $logFile->Save;
        }
        
        # We found a next atom, so chain continues
        $isChainEnd = 0;
    } else {
        # If one or zero backbone atoms found, check if we have a valid next atom
        eval { 
            if (defined($nextAtom)) {
                $nextAtom->Name = "$atomIndex";
                $isChainEnd = 0;  # Chain continues
            } else {
                $isChainEnd = 1;  # No next atom - end of chain
            }
        };
        if ($@) {
            $logFile->Append("ERROR: Failed processing next atom: $@\n");
            $logFile->Save;
            $isChainEnd = 1;  # Error case - treat as end of chain
        }
    }

    # Return next atom and chain status
    return ($nextAtom, $isChainEnd);
}

sub measureTorsionMobility {
    # Quantifies conformational changes by analyzing torsion angle transitions
    #
    # This function implements a key indicator for glass transition detection:
    # 1. Tracks torsion angle states across trajectory frames
    # 2. Detects conformational transitions between states (cis/trans/gauche+/gauche-)
    # 3. Calculates percentage of torsions showing conformational mobility
    #
    # The physical basis: Below Tg, backbone torsions become "frozen" in specific 
    # conformational states due to restricted molecular mobility
    #
    # Parameters:
    # $trajectory - Trajectory object containing production run frames
    # $startFrameIndex - First frame to include in analysis
    # $lastFrameIndex - Last frame to include in analysis
    #
    # Returns:
    # Percentage of torsions that changed conformational state during analysis period
    
    my ($trajectory, $startFrameIndex, $lastFrameIndex) = @_;

    # Validate input parameters
    if (!defined($trajectory) || !defined($startFrameIndex) || !defined($lastFrameIndex)) {
        $logFile->Append("ERROR: Invalid parameters passed to torsion analysis function\n");
        $logFile->Save;
        die "measureTorsionMobility: Missing required parameters";
    }
    
    # Initialize tracking variables
    my $mobilityPercentage = -1;  # Default return value for error cases
    my $frameCountToAnalyze = $lastFrameIndex - $startFrameIndex;
            
    $logFile->Append("\n==================================================\n");
    $logFile->Append("TORSION MOBILITY ANALYSIS: Processing $frameCountToAnalyze frames\n");
    $logFile->Append("==================================================\n");
    $logFile->Save;
    
    # Retrieve torsion definitions from structure
    my $torsionCollection;
    eval { $torsionCollection = $trajectory->UnitCell->Torsions; };
    if ($@) {
        $logFile->Append("ERROR: Failed to access torsion definitions: $@\n");
        $logFile->Save;
        die "measureTorsionMobility: Unable to retrieve torsions: $@";
    }
    
    # Verify torsions exist in the structure
    my $torsionCount = $torsionCollection->Count;
    if ($torsionCount == 0) {
        $logFile->Append("WARNING: No torsion angles defined in structure\n");
        $logFile->Append("This may indicate improper backbone atom identification\n");
        $logFile->Save;
        return 0; # No torsions to analyze
    }
    
    $logFile->Append("Found $torsionCount torsion angles for mobility analysis\n");
    
    # Process each frame in the trajectory segment
    $logFile->Append("Examining conformational states across frames...\n");
    
    my $processedFrameCount = 0;  # Counter for successful frames
    
    for (my $frameIndex = $startFrameIndex; $frameIndex <= $lastFrameIndex; ++$frameIndex) {
        # Position at current frame in trajectory
        eval { $trajectory->Trajectory->CurrentFrame = int($frameIndex); };
        if ($@) {
            $logFile->Append("ERROR: Failed to access frame $frameIndex: $@\n");
            $logFile->Save;
            next;  # Skip problematic frame
        }
    
        # Track progress periodically to avoid excessive logging
        if ($frameIndex % 5 == 0 || $frameIndex == $startFrameIndex || $frameIndex == $lastFrameIndex) {
            $logFile->Append("Analyzing torsions in frame $frameIndex\n");
        }
    
        # Process each torsion in the current frame
        foreach my $torsion (@$torsionCollection) {
            eval {
                # Classify torsion angle into conformational state category
                my $conformationalState = classifyTorsionAngle($torsion->Angle);
                
                # Store state in the global hash table by torsion name
                push(@{$torsionStates{$torsion->Name}}, $conformationalState);
            };
            if ($@) {
                $logFile->Append("WARNING: Error processing torsion: $@\n");
                $logFile->Save;
            }
        }
        
        $processedFrameCount++;
    }
    
    $logFile->Append("Successfully processed $processedFrameCount frames\n");
    $logFile->Save;
    
    # Analyze conformational transitions for each torsion
    $logFile->Append("Analyzing conformational mobility patterns...\n");
    
    my $mobileTorsionCount = 0;  # Count of torsions showing any transition
    my $analyzedTorsionCount = 0;  # Count of successfully analyzed torsions
    
    foreach my $torsionName (keys %torsionStates) {
        # Validate torsion data
        if (!exists $torsionStates{$torsionName} || ref($torsionStates{$torsionName}) ne 'ARRAY') {
            $logFile->Append("WARNING: Invalid data format for torsion $torsionName\n");
            next;  # Skip problematic torsion
        }
        
        my @torsionStateHistory = @{$torsionStates{$torsionName}};
        if (scalar(@torsionStateHistory) < 2) {
            $logFile->Append("WARNING: Insufficient state data for torsion $torsionName\n");
            next;  # Skip torsions with insufficient data
        }
    
        my $hasTransitioned = 0;  # Flag for state change detection
        my $initialState = $torsionStateHistory[0];  # Reference state (first frame)
        
        # Compare states across frames to detect transitions
        for (my $frameIndex = 1; $frameIndex < scalar(@torsionStateHistory); ++$frameIndex) {
            if ($torsionStateHistory[$frameIndex] ne $torsionStateHistory[$frameIndex-1]) {
                $hasTransitioned = 1;
                last;  # One transition is sufficient to establish mobility
            } 
        }
        
        # Track torsions showing mobility
        if ($hasTransitioned == 1) { 
            ++$mobileTorsionCount;
        }
        
        $analyzedTorsionCount++;
    }
    
    # Calculate mobility percentage based on analyzed torsions
    if ($analyzedTorsionCount > 0) {
        $mobilityPercentage = ($mobileTorsionCount / $torsionCount) * 100;
    } else {
        $logFile->Append("WARNING: No valid torsions could be analyzed\n");
        $mobilityPercentage = 0;
    }

    # Report results
    $logFile->Append(sprintf("RESULTS: %.2f%% of torsions showed conformational transitions\n", $mobilityPercentage));
    $logFile->Append("Total torsions: $torsionCount, Mobile torsions: $mobileTorsionCount\n");
    $logFile->Save;

    # Reset global hash table for next temperature point
    %torsionStates = ();
        
    return $mobilityPercentage;
}

sub classifyTorsionAngle {
    # Classifies torsion angles into standard conformational state categories
    #
    # This function implements the standard nomenclature for torsion angle classification:
    # - Cis (C): 0° ± window
    # - Trans (T): 180° ± window
    # - Gauche+ (GP): +60° ± window
    # - Gauche- (GM): -60° ± window
    # - Other (O): Any angle not matching the above categories
    #
    # Parameters:
    # $angle - Torsion angle value in degrees (-180 to +180 range)
    #
    # Returns:
    # Single-character state code: C, T, GP, GM, or O
    
    my $angle = shift;
    
    # Input validation
    if (!defined($angle)) {
        $logFile->Append("ERROR: Undefined angle passed to conformational state classifier\n");
        $logFile->Save;
        die "classifyTorsionAngle: Undefined angle value";
    }
    
    # Normalize angle to -180 to +180 range if needed
    if ($angle > 180) {
        $angle = $angle - 360;
    } elsif ($angle < -180) {
        $angle = $angle + 360;
    }
    
    # Define standard conformational states (canonical positions)
    use constant CIS => 0;        # 0 degrees (cis)
    use constant TRANS => 180;    # 180 degrees (trans)
    use constant GAUCHE_MINUS => -60;  # -60 degrees (gauche minus)
    use constant GAUCHE_PLUS => 60;    # +60 degrees (gauche plus)
    
    # Classify angle into conformational state based on angular windows
    my $conformationalState; # Variable to hold classification result
    
    # Check each conformational state against the current angle
    if (($angle > CIS - $torsionAngleWindow) && ($angle < CIS + $torsionAngleWindow)) {
        $conformationalState = "C";  # Cis conformation
    } elsif (($angle > GAUCHE_MINUS - $torsionAngleWindow) && ($angle < GAUCHE_MINUS + $torsionAngleWindow)) {
        $conformationalState = "GM"; # Gauche- conformation
    } elsif (($angle > GAUCHE_PLUS - $torsionAngleWindow) && ($angle < GAUCHE_PLUS + $torsionAngleWindow)) {
        $conformationalState = "GP"; # Gauche+ conformation
    } elsif (($angle < ((TRANS * -1) + $torsionAngleWindow)) || ($angle > TRANS - $torsionAngleWindow)) {
        # Special case for trans - need to handle wrap-around at ±180°
        $conformationalState = "T";  # Trans conformation
    } else {
        # If angle doesn't match any standard state, classify as Other
        $conformationalState = "O";  # Other (non-standard) conformation
    }
    
    return $conformationalState;
}

sub calculateAverageProperties {
    # Calculates statistical properties from production run trajectory
    #
    # This function implements ensemble property analysis for Tg determination:
    # 1. Extracts energy and density data from production trajectory
    # 2. Performs statistical analysis to obtain mean values and standard deviations
    # 3. Returns averaged properties for glass transition analysis
    #
    # Parameters:
    # $trajectory - Trajectory object containing production run frames
    # $startFrame - First frame to include in analysis
    # $endFrame - Last frame to include in analysis
    #
    # Returns:
    # Tuple containing (average_density, average_energy)
    
    my ($trajectory, $startFrame, $endFrame) = @_;
    
    # Input validation
    if (!defined($trajectory) || !defined($startFrame) || !defined($endFrame)) {
        $logFile->Append("ERROR: Missing required parameters for property averaging\n");
        $logFile->Save;
        die "calculateAverageProperties: Missing required parameters";
    }
    
    $logFile->Append("\n=================================================\n");
    $logFile->Append("PROPERTY ANALYSIS: Processing production trajectory\n");
    $logFile->Append("=================================================\n");
    $logFile->Append("Analyzing frames $startFrame to $endFrame\n");
    $logFile->Save;
    
    # Configure Forcite to analyze specific frame range
    eval { 
        $forciteModule->ChangeSettings([ 
            ActiveDocumentFrameRange => "$startFrame - $endFrame",
            WriteLevel => "Silent" # Minimize output for analysis phase
        ]); 
    };
    if ($@) {
        $logFile->Append("ERROR: Failed to configure analysis frame range: $@\n");
        $logFile->Save;
        die "calculateAverageProperties: Error setting frame range: $@";
    }
    
    # Extract properties from trajectory into study table
    my $analysisResults;
    $logFile->Append("Extracting thermodynamic properties...\n");
    eval { $analysisResults = $forciteModule->Analysis->ViewInStudyTable($trajectory); };
    if ($@) {
        $logFile->Append("ERROR: Failed to extract trajectory data: $@\n");
        $logFile->Append("This may indicate corrupt trajectory or calculation errors\n");
        $logFile->Save;
        die "calculateAverageProperties: Error analyzing trajectory: $@";
    }
    
    # Validate study table
    my $propertiesTable = $analysisResults->StudyTable;
    if (!defined($propertiesTable)) {
        $logFile->Append("ERROR: Unable to access study table results\n");
        $logFile->Save;
        die "calculateAverageProperties: Result study table undefined";
    }
    
    # Report data extraction status
    my $rowCount = $propertiesTable->RowCount;
    $logFile->Append("Successfully extracted $rowCount data points\n");
    
    # Initialize property arrays
    my @energyValues = ();
    my @densityValues = ();
    
    # Extract property values from study table
    $logFile->Append("Collecting energy and density values...\n");
    eval {
        for (my $row = 0; $row < $propertiesTable->RowCount; ++$row) {
            # Extract energy value (Hamiltonian column)
            my $energy = $propertiesTable->Cell($row, "Hamiltonian");
            if (defined($energy)) {
                push(@energyValues, $energy);
            }
            
            # Extract density value (Density column)
            my $density = $propertiesTable->Cell($row, "Density");
            if (defined($density)) {
                push(@densityValues, $density);
            }
        }
    };
    if ($@) {
        $logFile->Append("ERROR: Failed to extract property values: $@\n");
        $logFile->Save;
        die "calculateAverageProperties: Error extracting data from study table: $@";
    }
    
    # Clean up temporary study table to free memory
    eval { $propertiesTable->Delete; };
    if ($@) {
        $logFile->Append("WARNING: Failed to clean up temporary study table: $@\n");
        $logFile->Save;
    }
    
    # Validate extracted data
    my $energyCount = scalar(@energyValues);
    my $densityCount = scalar(@densityValues);
    $logFile->Append("Collected $energyCount energy values and $densityCount density values\n");
    
    if ($energyCount == 0 || $densityCount == 0) {
        $logFile->Append("WARNING: Insufficient data for statistical analysis\n");
        return (0, 0); # Default return values for error case
    }
    
    # Calculate statistical properties for extracted data
    $logFile->Append("Performing statistical analysis...\n");
    my ($averageEnergy, $energyStdDev) = calculateStatistics(@energyValues);
    my ($averageDensity, $densityStdDev) = calculateStatistics(@densityValues);
    
    # Report statistical results
    $logFile->Append(sprintf("DENSITY: Average = %.4f g/cm³, Std. Dev = %.4f g/cm³\n", 
                             $averageDensity, $densityStdDev));
    $logFile->Append(sprintf("ENERGY: Average = %.4f kcal/mol, Std. Dev = %.4f kcal/mol\n", 
                             $averageEnergy, $energyStdDev));
    
    # Calculate coefficient of variation (relative standard deviation)
    my $densityVariation = ($averageDensity > 0) ? ($densityStdDev / $averageDensity) * 100 : 0;
    my $energyVariation = ($averageEnergy != 0) ? ($energyStdDev / abs($averageEnergy)) * 100 : 0;
    
    $logFile->Append(sprintf("DENSITY VARIATION: %.2f%%\n", $densityVariation));
    $logFile->Append(sprintf("ENERGY VARIATION: %.2f%%\n", $energyVariation));
    
    # Provide interpretation guidance
    if ($densityVariation > 5.0) {
        $logFile->Append("WARNING: High density variation may indicate incomplete equilibration\n");
    }
    if ($energyVariation > 10.0) {
        $logFile->Append("WARNING: High energy variation may indicate unstable trajectory\n");
    }
    
    $logFile->Save;
    
    # Return statistical results for Tg analysis
    return ($averageDensity, $averageEnergy);
}

sub calculateStatistics {
    # Performs statistical analysis on numerical data arrays
    #
    # This function implements essential statistical calculations:
    # 1. Calculates arithmetic mean (average) of the data set
    # 2. Computes standard deviation using the Bessel-corrected formula (n-1 denominator)
    # 3. Handles edge cases (empty arrays, singleton arrays, etc.)
    #
    # Parameters:
    # @dataValues - Array of numerical values to analyze
    #
    # Returns:
    # Tuple containing (mean, standard_deviation)
    
    my @dataValues = @_;
    
    # Validate input array
    my $dataPointCount = scalar(@dataValues);
    if ($dataPointCount < 1) {
        $logFile->Append("WARNING: Empty data array passed to statistical analysis\n");
        $logFile->Save;
        return (0, 0); # Return zeros to avoid division by zero errors
    }
    
    # Calculate sum for mean computation
    my $sum = 0;
    my $validValueCount = 0;
    
    # Process each value, handling potential non-numeric elements
    foreach my $value (@dataValues) {
        # Skip undefined or non-numeric values
        if (!defined($value) || $value !~ /^-?\d+(?:\.\d+)?$/) {
            next;
        }
        
        $sum += $value;
        $validValueCount++;
    }
    
    # Handle case where all values were invalid
    if ($validValueCount == 0) {
        $logFile->Append("WARNING: No valid numerical values found in data array\n");
        $logFile->Save;
        return (0, 0);
    }
    
    # Calculate arithmetic mean
    my $mean = $sum / $validValueCount;
    
    # For single-value arrays, standard deviation is undefined (return 0)
    if ($validValueCount == 1) {
        return ($mean, 0);
    }
    
    # Calculate sum of squared deviations for standard deviation
    my $sumSquaredDeviations = 0;
    
    foreach my $value (@dataValues) {
        # Skip invalid values
        if (!defined($value) || $value !~ /^-?\d+(?:\.\d+)?$/) {
            next;
        }
        
        # Sum squared differences from mean
        my $deviation = $value - $mean;
        $sumSquaredDeviations += $deviation * $deviation;
    }
    
    # Calculate standard deviation with Bessel's correction (n-1 denominator)
    # This provides an unbiased estimator of population standard deviation
    my $standardDeviation = sqrt($sumSquaredDeviations / ($validValueCount - 1));
    
    return ($mean, $standardDeviation);
}

sub calculateStatisticalSummary {
    # Performs statistical analysis across multiple sheets in a study table
    #
    # This function implements cross-frame analysis for Tg determination:
    # 1. Creates a new "Averages" sheet in the study table
    # 2. Computes mean values for each temperature point across all structures
    # 3. Calculates statistical error metrics for each value
    # 4. Creates a comprehensive summary for Tg identification
    #
    # Parameters:
    # $resultsTable - Study table object containing individual frame sheets
    # @ignoreColumnIndices - Array of column indices to exclude from averaging
    #                        (typically includes temperature column)
    
    my ($resultsTable, @ignoreColumnIndices) = @_;
    
    # Validate input study table
    if (!defined($resultsTable)) {
        $logFile->Append("ERROR: Undefined study table passed to averaging function\n");
        $logFile->Save;
        die "calculateStatisticalSummary: Study table is undefined";
    }
    
    # Verify frame data exists in the study table
    if (scalar(@extractedFrames) == 0 || !defined($resultsTable->Sheets("$extractedFrames[0]"))) {
        $logFile->Append("ERROR: No valid frame sheets found in study table\n");
        $logFile->Save;
        return;
    }
    
    $logFile->Append("\n==================================================\n");
    $logFile->Append("FINAL ANALYSIS: Computing Tg statistical summary\n");
    $logFile->Append("==================================================\n");
    
    # Extract dimensions from first frame sheet
    my $temperaturePointCount = $resultsTable->Sheets("$extractedFrames[0]")->RowCount;
    my $dataColumnCount = $resultsTable->Sheets("$extractedFrames[0]")->ColumnCount;
    
    $logFile->Append("Found $temperaturePointCount temperature points and $dataColumnCount data columns\n");
    
    # Create new summary sheet for averages
    my $summarySheet;
    eval { $summarySheet = $resultsTable->InsertSheet; };
    if ($@) {
        $logFile->Append("ERROR: Failed to create average summary sheet: $@\n");
        $logFile->Save;
        return;
    }
    
    $summarySheet->Title = "Averages";
    $logFile->Append("Created 'Averages' summary sheet\n");
    
    # Initialize column counter for the average sheet
    # This may differ from source sheets due to error columns
    my $summaryColumnIndex = 0;
    
    # Process each column from the original data
    for (my $sourceColumnIndex = 0; $sourceColumnIndex < $dataColumnCount; ++$sourceColumnIndex) {
        # Determine if this column should be averaged or copied
        my $isIgnoredColumn = 0;
        
        foreach my $ignoredColumnIndex (@ignoreColumnIndices) {
            if ($sourceColumnIndex == $ignoredColumnIndex) {
                $logFile->Append("Column $sourceColumnIndex will be copied without averaging\n");
                $isIgnoredColumn = 1;
                last;
            }
        }
        
        # Set up column headers in the average sheet
        eval {
            # Copy original column heading
            my $columnName = $resultsTable->Sheets("$extractedFrames[0]")->ColumnHeading($sourceColumnIndex);
            $summarySheet->ColumnHeading($summaryColumnIndex) = $columnName;
            
            # Add error column heading for averaged data
            if ($isIgnoredColumn == 0) {
                $summarySheet->ColumnHeading($summaryColumnIndex+1) = "$columnName Uncertainty";
            }
        };
        if ($@) {
            $logFile->Append("WARNING: Error setting column headings: $@\n");
            $logFile->Save;
        }
        
        # Process each row (temperature point) in the data set
        for (my $rowIndex = 0; $rowIndex < $temperaturePointCount; ++$rowIndex) {
            # Handle differently based on whether column is ignored or averaged
            if ($isIgnoredColumn == 1) {
                # For ignored columns (e.g., temperature), simply copy values
                eval {
                    my $value = $resultsTable->Sheets("$extractedFrames[0]")->Cell($rowIndex, $sourceColumnIndex);
                    $summarySheet->Cell($rowIndex, $summaryColumnIndex) = sprintf("%.1f", $value);
                };
                if ($@) {
                    $logFile->Append("WARNING: Error copying ignored column value: $@\n");
                }
            } else {
                # For regular columns, compute average across all frames
                my @valuesToAverage = ();
                
                # Collect values from each frame sheet
                foreach my $sheetName (@extractedFrames) {
                    eval {
                        my $value = $resultsTable->Sheets("$sheetName")->Cell($rowIndex, $sourceColumnIndex);
                        if (defined($value) && $value =~ /^-?\d+(?:\.\d+)?$/) {
                            push(@valuesToAverage, $value);
                        }
                    };
                    if ($@) {
                        $logFile->Append("WARNING: Error retrieving value from $sheetName: $@\n");
                    }
                }
                
                # Verify sufficient data was collected
                my $valueCount = scalar(@valuesToAverage);
                if ($valueCount == 0) {
                    $logFile->Append("WARNING: No valid values found for row $rowIndex, column $sourceColumnIndex\n");
                    next;
                }
                
                # Calculate statistical metrics for this data point
                my ($mean, $stdDev) = calculateStatistics(@valuesToAverage);
                
                # Calculate absolute errors (maximum deviation from mean)
                my @absoluteDeviations = ();
                foreach my $value (@valuesToAverage) {
                    push(@absoluteDeviations, abs($value - $mean));
                }
                
                # Sort deviations to find maximum
                my @sortedDeviations = sort {$a <=> $b} @absoluteDeviations;
                my $maxDeviation = ($valueCount > 0) ? $sortedDeviations[-1] : 0;
                
                # Store results in average sheet
                eval {
                    # Store mean value with appropriate precision
                    $summarySheet->Cell($rowIndex, $summaryColumnIndex) = sprintf("%.3f", $mean);
                    
                    # Store uncertainty value
                    if ($valueCount > 1) {
                        # Use maximum deviation for error estimation
                        $summarySheet->Cell($rowIndex, $summaryColumnIndex + 1) = 
                            sprintf("%.4f", $maxDeviation);
                    } else {
                        # Cannot calculate error with only one data point
                        $summarySheet->Cell($rowIndex, $summaryColumnIndex + 1) = "N/A";
                    }
                };
                if ($@) {
                    $logFile->Append("WARNING: Error writing average values: $@\n");
                }
            }
        }
        
        # Increment column counter, accounting for error columns
        if ($isIgnoredColumn == 0) {
            $summaryColumnIndex += 2; # Value column + error column
        } else {
            $summaryColumnIndex += 1; # Just value column
        }
    }
    
    # Add Tg estimation summary to log
    $logFile->Append("\nAVERAGE CALCULATION COMPLETE\n");
    $logFile->Append("--------------------------------\n");
    $logFile->Append("To determine Tg, examine density vs. temperature plot in the Averages sheet.\n");
    $logFile->Append("The glass transition temperature is typically identified as the inflection point\n");
    $logFile->Append("where the slope of density vs. temperature changes significantly.\n");
    $logFile->Append("For rigorous Tg determination, fit separate linear regressions to the data\n");
    $logFile->Append("points above and below the transition region, then identify the intersection.\n");
    
    $logFile->Save;
}